# Libertum Microservices Architecture Documentation

## Overview

This document provides a comprehensive analysis of the Libertum Node.js microservices architecture, including service mappings, database configurations, gRPC communication patterns, Kafka message flows, and system diagrams.

## 1. Microservices Inventory

### 1.1 Backend-marketplace
- **Port**: 3000 (HTTP), 40001 (gRPC)
- **Database**: MongoDB (`mvp_db`)
- **Additional Storage**: Redis, Google Cloud Storage
- **Purpose**: Core marketplace service handling:
  - Asset/NFT management
  - Investment offerings and orders
  - Transaction processing
  - Dividend management
  - User authentication
  - DocuSign integration
  - Notification system

### 1.2 Backend-admin
- **Port**: 4000 (HTTP), 40002 (gRPC)
- **Database**: MongoDB (`mvp_db`)
- **Additional Storage**: Redis
- **Purpose**: Administrative service for:
  - User management and KYC approval
  - Offering approval and management
  - System administration
  - Email notifications via SendGrid

### 1.3 Backend-transfer-agent
- **Port**: 5000 (HTTP), 40001 (gRPC)
- **Database**: MongoDB (`mvp_db`)
- **Additional Storage**: Redis
- **Purpose**: Regulatory compliance service for:
  - Investor onboarding and KYC verification
  - Transaction monitoring and compliance
  - Document management and storage
  - Regulatory reporting

### 1.4 Backend-dividend-service
- **Port**: 3000 (HTTP)
- **Database**: MongoDB (`divident-db`)
- **Purpose**: Dividend processing service for:
  - Dividend calculation and distribution
  - Blockchain-based dividend processing
  - Dividend history management

### 1.5 Backend-event-service
- **Database**: MongoDB (`event-service`)
- **Purpose**: Blockchain event monitoring service for:
  - Smart contract event monitoring (Base/BSC chains)
  - Event formatting and publishing to Kafka
  - Blockchain data synchronization

### 1.6 backend-bonding-dex-service
- **Port**: 5000 (HTTP), 40003 (gRPC)
- **Database**: MongoDB (`bondingDex` or `libertum_bonding_dex`)
- **Purpose**: DeFi operations service for:
  - Token bonding curves
  - Staking and unstaking operations
  - Reward calculations
  - DEX functionality

## 2. Database Architecture

### 2.1 MongoDB Databases
- **mvp_db**: Shared by Backend-marketplace, Backend-admin, Backend-transfer-agent
- **divident-db**: Used by Backend-dividend-service
- **event-service**: Used by Backend-event-service
- **bondingDex**: Used by backend-bonding-dex-service

### 2.2 Redis Cache
- Used by Backend-marketplace, Backend-admin, Backend-transfer-agent
- Purposes: Session management, rate limiting, OTP storage, caching

## 3. gRPC Communication Patterns

### 3.1 gRPC Servers
| Service | Port | Host | Service Name |
|---------|------|------|--------------|
| Backend-marketplace | 40001 | 0.0.0.0 | UserGrpcService |
| Backend-admin | 40002 | 0.0.0.0 | AdminGrpcService |
| Backend-transfer-agent | 40001 | 0.0.0.0 | UserGrpcService |
| backend-bonding-dex-service | 40003 | 0.0.0.0 | BondingGrpcService |

### 3.2 gRPC Client Connections
- **Backend-marketplace → Backend-admin**: AdminGrpcService (Asset/NFT operations)
- **Backend-marketplace → Backend-transfer-agent**: UserGrpcService (User management)
- **Backend-marketplace → backend-bonding-dex-service**: BondingGrpcService (Bonding operations)
- **Backend-admin → backend-bonding-dex-service**: BondingGrpcService (Admin bonding operations)

### 3.3 gRPC Service Methods
**UserGrpcService:**
- `getUser`, `approveKyc`, `approveWhitelist`, `unblockUser`
- `approveIssuer`, `rejectOffering`, `rejectForceTransfer`
- `sendOffering`, `scheduleOffering`, `updateNftStatus`, `checkDividendStatus`

**BondingGrpcService:**
- Staking transaction management
- User staking summaries
- Token bonding operations

## 4. Kafka Message Flows

### 4.1 Kafka Configuration
- **Broker**: `localhost:9092`
- **Client ID**: Service-specific identifiers

### 4.2 Kafka Topics

#### 4.2.1 `cron-to-user` Topic
- **Producers**: Backend-event-service, Backend-transfer-agent
- **Consumer**: Backend-marketplace
- **Message Types**: 
  - `offering` - Offering updates
  - `fee` - Fee updates  
  - `ForceTransferred` - Force transfer events
  - `whitelist` - Whitelist updates
  - `DividendDistributed` - Dividend distribution events
  - `SaveTransfer` - Transfer save events
  - `order` - Order updates
  - `issuerAction` - Issuer actions
  - `user` - User updates
  - `NftCollection` - NFT collection updates
  - `offeringNav` - Offering NAV updates

#### 4.2.2 `user-to-admin` Topic
- **Producers**: Backend-marketplace, Backend-event-service
- **Consumer**: Backend-admin
- **Purpose**: Administrative events requiring admin attention

#### 4.2.3 `admin-to-transferagent` Topic
- **Producer**: Backend-admin
- **Consumer**: Backend-transfer-agent
- **Purpose**: Administrative actions for transfer agent processing

#### 4.2.4 `notifications` Topic
- **Producers**: Backend-marketplace, Backend-admin
- **Consumer**: External notification service
- **Purpose**: User notification events

### 4.3 Message Structure
```json
{
  "type": "offering|fee|ForceTransferred|whitelist|DividendDistributed|...",
  "event": "event_name",
  "_id": "document_id", 
  "value": { /* event-specific data */ }
}
```

## 5. External System Integrations

- **Blockchain**: Base and BSC networks for smart contract interactions
- **DocuSign**: Document signing integration via Backend-marketplace
- **Google Cloud Storage**: File storage via Backend-marketplace
- **SendGrid**: Email notifications via Backend-admin
- **Kafka**: Message broker for asynchronous communication
- **MongoDB**: Primary database system
- **Redis**: Caching and session management

## 6. Key Workflows

The system supports several critical workflows:

1. **User Registration and KYC**: Multi-service flow involving marketplace, admin, and transfer agent
2. **Asset Offering Creation**: Complex approval process with blockchain deployment
3. **Token Purchase**: Integration with bonding curves and blockchain transactions
4. **Dividend Distribution**: Automated dividend calculation and blockchain distribution
5. **Event Processing**: Real-time blockchain event monitoring and system updates

## 7. Architecture Characteristics

- **Communication**: Hybrid synchronous (gRPC) and asynchronous (Kafka) patterns
- **Data Consistency**: Event-driven architecture with eventual consistency
- **Scalability**: Microservices can be scaled independently
- **Resilience**: Kafka provides message durability and retry mechanisms
- **Compliance**: Dedicated transfer agent service for regulatory requirements

## 8. Assumptions

- Kafka is running locally on `localhost:9092`
- MongoDB instances are configured per service requirements
- Redis is available for caching services
- Blockchain networks (Base/BSC) are accessible
- External APIs (DocuSign, SendGrid, GCS) are properly configured

---

*This documentation reflects the current state of the Libertum microservices architecture as analyzed from the codebase.*
