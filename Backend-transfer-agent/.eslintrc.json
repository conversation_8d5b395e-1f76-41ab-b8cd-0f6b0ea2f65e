{
  "env": {
    "es2021": true,
    "node": true
  },
  "extends": ["airbnb-base"],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": "latest",
    "sourceType": "module"
  },
  "plugins": ["@typescript-eslint"],
  "rules": {
    "@typescript-eslint/no-explicit-any": "off", // Allow any type
    "quotes": ["error", "single"], // Enforce single quotes
    "semi": ["error", "always"], // Require semicolons
    "object-curly-spacing": ["error", "always"], // Spaces inside object curly braces
    "array-bracket-spacing": ["error", "never"], // No spaces inside array brackets
    "comma-dangle": ["error", "always-multiline"], // Require trailing commas where valid
    "no-multiple-empty-lines": ["error", { "max": 1 }], // No more than 1 empty line
    "eol-last": ["error", "always"], // Ensure newline at end of file
    "keyword-spacing": ["error", { "before": true, "after": true }], // Enforce spacing around keywords
    "space-infix-ops": ["error"], // Require spaces around operators

    "no-trailing-spaces": "error", // No trailing spaces
    "prefer-template": "error", // Use template literals instead of string concatenation
    "no-console": "warn", // Warn for console.log but don't error
    "no-debugger": "error", // Disallow debugger statements
    "prefer-const": "error", // Prefer const over let where possible
    "no-var": "error", // Disallow var in favor of let/const
    "import/no-unresolved": "off",
    "no-useless-constructor": "off",
    "no-unused-vars": "off",
    "@typescript-eslint/no-unused-vars": ["error"],
    "no-param-reassign": "off",
    "import/extensions": "off",
    "no-empty-function": "off",
    "import/no-extraneous-dependencies": "off",
    "import/prefer-default-export": "off",
    "func-names": "off",
    "consistent-return": "off",
    "arrow-body-style": "off",
    "class-methods-use-this": "off",
    "no-underscore-dangle": [
      "error",
      {
        "allow": ["_id", "_privateVar"],
        "allowAfterThis": true,
        "allowAfterSuper": true,
        "allowAfterThisConstructor": true
      }
    ],

    "max-params": ["error", 20], // Allow up to 20 parameters before forcing multiline (increase if needed)
    "object-curly-newline": ["error", { "consistent": true }], // Prevent newlines in object literals

    // "function-paren-newline": ["error", "never"], // Force all arguments on the same line
    "arrow-parens": ["error", "as-needed"], // Avoid parentheses for single arguments
    "brace-style": ["error", "1tbs", { "allowSingleLine": true }], // Allow single-line function bodies
    "curly": ["error", "multi-line", "consistent"], // Ensure `if` blocks stay consistent
    "multiline-ternary": ["error", "never"], // Disallow multiline ternary expressions
    "no-multi-spaces": ["error", { "ignoreEOLComments": false }], // Prevent excessive spaces
    "indent": ["error", 4, { "SwitchCase": 1 }], // Enforce 4-space indentation
    // "space-before-function-paren": ["error", "never"], // No space before function parentheses
    "max-len": ["error", { "code": 500, "ignoreComments": true, "ignoreStrings": true }], // Prevent line breaks
    "newline-per-chained-call": ["error", { "ignoreChainWithDepth": 2 }], // Allow method chains up to depth 2
    "padding-line-between-statements": ["error", { "blankLine": "always", "prev": "*", "next": "return" }]
  }
}
