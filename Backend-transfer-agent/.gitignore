# Dependency directories
node_modules/
jspm_packages/

# Specific files
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Environment files
.env
*.env

# Build directories
build/
dist/
out/
tmp/
sonar
sonar.js
.scannerwork/

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
lib/
vendor/
*/.sass-cache/
.npm/
.lock-wscript
.svn/
.idea/
.vscode/
*.swp

# OS generated files
.DS_Store
Thumbs.db

# Editor directories and files
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
.vscode/

# Miscellaneous
*.bak
*.orig
*.backup
*.merge
*.rej
*.~
*.       #
*.sonar

# Package manager configuration
package-lock.json
yarn.lock
.pnpm-lock.yaml
.lock

# Ignore GitHub metadata and workflows
.github/
.github/*
.workflows/
.workflows/*
.ci/
.ci/*
pipeline/
pipeline/*