import { loadSync } from '@grpc/proto-loader';
import { loadPackageDefinition, Server, ServerCredentials } from '@grpc/grpc-js';
import * as path from 'path';
import logger from '../helpers/logger.helper';

const PROTO_PATH = path.join(__dirname, '/proto/user.proto');
const PROTO_OPTIONS = {
  keepCase: true,
  longs: String,
  enums: String,
  arrays: true,
};

class Grpc {
  constructor() {}

  public async startServer() {
    const host = process.env.USER_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0';
    const port = process.env.USER_SERVICE_GRPC_PORT || '40001';

    const grpcPackageDefination = loadSync(PROTO_PATH, PROTO_OPTIONS);
    const proto: any = loadPackageDefinition(grpcPackageDefination);

    const userService = proto.user?.UserService?.service;

    if (!userService) {
      logger.error('UserService is not defined');
      return;
    }

    const server = new Server();

    server.addService(userService, {
      getUserDetail: async (call: any, callback: any) => {
        callback(null, { error: false, message: 'User detail fetched successfully.', data: null });
      },
    });

    server.bindAsync(`${host}:${port}`, ServerCredentials.createInsecure(), (error: any) => {
      if (error) return logger.error('GRPC:: Failed To Connect..!!');
      console.log(`GRPC has been started on - ${host}:${port}`);
      server.start();
    });
  }
}

export default Grpc;
