import CONFIG from '../../config/env';
import * as path from 'path';
import * as grpc from '@grpc/grpc-js';
import * as protoLoader from '@grpc/proto-loader';

const PROTO_PATH = path.join(__dirname, '../proto/user.proto');
const PROTO_OPTIONS = {
  keepCase: true,
  longs: String,
  enums: String,
  arrays: true,
};

class UserClient {
  public client: any;

  constructor() {
    this.connectUserClient();
  }

  public async connectUserClient() {
    const host = CONFIG.GRPC.USER_SERVICE_GRPC_CONTAINER_NAME;
    const port = CONFIG.GRPC.USER_SERVICE_GRPC_PORT;
    const isSsl = process.env.GRPC_SSL;

    const packageDefinition = protoLoader.loadSync(PROTO_PATH, PROTO_OPTIONS);
    const grpcObject: any = grpc.loadPackageDefinition(packageDefinition);

    // Access the UserService from the 'user' package
    const userGrpcService = grpcObject.user.UserGrpcService;

    this.client = new userGrpcService(`${host}:${port}`, isSsl === 'True' ? grpc.credentials.createSsl() : grpc.credentials.createInsecure());

    console.log(`User Service Client running at ${host}:${port}`);
  }
}

export default new UserClient();
