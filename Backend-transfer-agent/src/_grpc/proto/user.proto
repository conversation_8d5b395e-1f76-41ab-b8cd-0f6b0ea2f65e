syntax = "proto3";

package user;

// Define the UserGrpcService with RPC methods
service UserGrpcService {
  rpc getUser (GetUserRequest) returns (CommonResponse);
  rpc approveKyc (ApproveRequest) returns (CommonResponse);
  rpc approveWhitelist (ApproveWhitelistRequest) returns (CommonResponse);
  rpc unblockUser (UnblockRequest) returns (CommonResponse);
  rpc approveIssuer (ApproveIssuerRequest) returns (CommonResponse);
  rpc rejectOffering (RejectRequest) returns (CommonResponse);
  rpc rejectForceTransfer (rejectForceTransferRequest) returns (CommonResponse);
}

// Admin-related gRPC service
service AdminGrpcService {
  rpc getTransferAgent (TARequest) returns (CommonResponse);
  rpc getConvertList (getConvertListRequest) returns (CommonResponse);
  rpc getTransactionList (getTransactionRequest) returns (CommonResponse);
}

// General response message for all services
message CommonResponse {
  int32 status = 1; // Status code
  bool error = 2;   // Indicates if there was an error
  string message = 3; // Detailed message
  string data = 4; // Flexible data type for object or array
}

// Specific request messages
message GetUserRequest {
  string userId = 1; // The user ID to fetch
}

message ApproveRequest {
  string email = 1;
  string kycStatus = 2;
  string kycReason = 3;
}

message ApproveWhitelistRequest {
  string _id = 1;
  string taStatus = 2;
  string whiteListReason = 3;
}

message ApproveIssuerRequest {
  string email = 1;
  string issuerStatus = 2;
  string issuerReason = 3;
}

message UnblockRequest {
  string email = 1;
  string isActive = 2;
}

message RejectRequest {
  string id = 1;
  string status = 2;
}

message TARequest {
  string page = 1;
  string limit = 2;
  string sort = 3;
  string search = 4;
}

message getConvertListRequest {
  string page = 1;
  string limit = 2;
  string sort = 3;
  string search = 4;
  string status = 5;
}

message getTransactionRequest {
  string page = 1;
  string limit = 2;
  string sort = 3;
  string search = 4;
  string status = 5;
  string type = 6;
}

message rejectForceTransferRequest  {
  string _id= 1; // The user ID to fetch
  string status= 2;
  string reason= 3;
}

