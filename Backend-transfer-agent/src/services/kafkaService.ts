import { KafkaMessage, Consumer } from 'kafkajs';
import { kafkaHelperService } from '../helpers/kafka.helper';
import UserService from '../component/userAuthentications/service';
import logger from '../helpers/logger.helper';
class KafkaService {
  private consumers: Consumer | null = null;

  constructor() {
    this.initialize();
  }

  private async initialize() {
    try {
      this.consumers = await this.setupConsumer('admin-to-transferagent', this.handleUserMessage.bind(this));
    } catch (error) {
      logger.error('Error initializing Kafka consumer for transferagent:', error);
      throw error;
    }
  }

  private async setupConsumer(topic: string, messageHandler: (message: KafkaMessage, topic: string, partition: number, offset: string) => void): Promise<Consumer> {
    try {
      const consumer = await kafkaHelperService.createConsumer('admin-transferagent-group', topic, messageHandler);
      this.consumers = consumer;
      return consumer;
    } catch (error) {
      logger.error(`Error setting up consumer for topic ${topic}:`, error);
      throw error;
    }
  }

  private async handleUserMessage(message: KafkaMessage, topic: string, partition: number, offset: any) {
    try {
      let response: any;
      const body = JSON.parse(message.value.toString());
      const value = typeof body.value === 'string' ? JSON.parse(body.value) : body.value;
      const filter = { _id: value._id };
      delete value._id;
      switch (value.type) {
        case 'user':
          response = await UserService.updateUserDetails(value, filter);
          break;

        default:
          logger.warn(`Unrecognized or missing type`);
          return;
      }

      if (!response.error && this.consumers) {
        const nextOffset = (parseInt(offset) + 1).toString();
        await this.consumers.commitOffsets([{ topic, partition, offset: nextOffset }]);
        logger.info(`Committed offset ${offset} for partition ${partition} on topic ${topic}`);
      } else {
        logger.warn('Consumer is not initialized or operation failed.');
      }
    } catch (error) {
      logger.error('handleUserMessage:', error);
      throw error;
    }
  }

  public async sendMessageToUser(message: any) {
    try {
      console.log('sendMessageToAdmin============>>>>>>>', message);
      await kafkaHelperService.sendMessage('cron-to-user', [{ value: JSON.stringify(message) }]);
    } catch (error) {
      logger.error('Error sending message from admin to user:', error);
      throw error;
    }
  }
}

export default new KafkaService();
