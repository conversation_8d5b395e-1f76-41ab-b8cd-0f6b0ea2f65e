export const capitalizeString = (message: string): string => {
  const exceptions = [
    // Articles & Determiners
    'a',
    'an',
    'the',
    'this',
    'that',
    'these',
    'those',
    'each',
    'every',
    'either',
    'neither',
    'some',
    'any',
    'no',
    'many',
    'much',
    'more',
    'most',
    'little',
    'less',
    'least',
    'few',
    'fewer',
    'fewest',
    'all',
    'both',
    'several',
    'enough',
    'one',
    'two',
    'three',

    // Prepositions
    'about',
    'above',
    'across',
    'after',
    'against',
    'along',
    'among',
    'around',
    'as',
    'at',
    'before',
    'behind',
    'below',
    'beneath',
    'beside',
    'between',
    'beyond',
    'but',
    'by',
    'concerning',
    'considering',
    'despite',
    'down',
    'during',
    'except',
    'excluding',
    'following',
    'for',
    'from',
    'in',
    'inside',
    'into',
    'like',
    'near',
    'of',
    'off',
    'on',
    'onto',
    'opposite',
    'out',
    'outside',
    'over',
    'past',
    'per',
    'plus',
    'regarding',
    'round',
    'since',
    'than',
    'through',
    'throughout',
    'till',
    'to',
    'toward',
    'towards',
    'under',
    'underneath',
    'unlike',
    'until',
    'up',
    'upon',
    'versus',
    'via',
    'with',
    'within',
    'without',

    // Conjunctions
    'and',
    'but',
    'or',
    'nor',
    'for',
    'so',
    'yet',
    'although',
    'as',
    'because',
    'before',
    'even though',
    'if',
    'once',
    'since',
    'though',
    'unless',
    'until',
    'when',
    'where',
    'whereas',
    'whether',
    'while',
    'both',
    'either',
    'neither',
    'not only',
    'but also',
    'whether...or',

    // Common Verbs (Helping/Auxiliary & Modal)
    'am',
    'is',
    'are',
    'was',
    'were',
    'be',
    'being',
    'been',
    'have',
    'has',
    'had',
    'do',
    'does',
    'did',
    'can',
    'could',
    'may',
    'might',
    'must',
    'shall',
    'should',
    'will',
    'would',

    // Pronouns
    'I',
    'you',
    'he',
    'she',
    'it',
    'we',
    'they',
    'me',
    'him',
    'her',
    'us',
    'them',
    'my',
    'your',
    'his',
    'her',
    'its',
    'our',
    'their',
    'myself',
    'yourself',
    'himself',
    'herself',
    'itself',
    'ourselves',
    'yourselves',
    'themselves',
    'who',
    'whom',
    'whose',
    'which',
    'that',
    'anybody',
    'anyone',
    'anything',
    'everybody',
    'everyone',
    'everything',
    'nobody',
    'no one',
    'nothing',
    'somebody',
    'someone',
    'something',
    'this',
    'that',
    'these',
    'those',
    'who',
    'whom',
    'whose',
    'which',
    'what',

    // Common Adverbs (Function Words)
    'again',
    'almost',
    'already',
    'also',
    'always',
    'anyway',
    'anywhere',
    'back',
    'barely',
    'before',
    'besides',
    'down',
    'elsewhere',
    'ever',
    'everywhere',
    'forward',
    'hence',
    'here',
    'however',
    'indeed',
    'instead',
    'later',
    'meanwhile',
    'moreover',
    'much',
    'neither',
    'nevertheless',
    'nonetheless',
    'now',
    'nowhere',
    'often',
    'once',
    'perhaps',
    'rather',
    'seldom',
    'so',
    'sometimes',
    'somewhat',
    'somewhere',
    'still',
    'then',
    'therefore',
    'thus',
    'today',
    'tomorrow',
    'too',
    'twice',
    'usually',
    'yet',
    // Contractions
    "I'm",
    "you're",
    "he's",
    "they're",
    "isn't",
    "aren't",
    "wasn't",
    // Quantifiers

    'various',
    'numerous',

    // Interjections & Miscellaneous Function Words
    'oh',
    'ah',
    'alas',
    'well',
    'yes',
    'okay',
    // Additional Adverbs
    'henceforth',
    'thereafter',
    'heretofore',
    'notwithstanding',
    // Additional Auxiliary/Modal Verbs
    'ought to',
    'dare',
    'need',

    // 3. Additional Prepositions
    'amid',
    'amongst',
    'inside of',
    'outside of',

    // Additional Pronouns

    'none',
    'whichever',
    'wherever',
    'whenever',

    // Correlative Conjunctions
    'as...as',
    'just as',
    'rather than',
    'whether',
  ];

  return message
    .split(' ')
    .map((word, index) => {
      // Keep "KYC" and "OTP" always capitalized
      if (['kyc', 'otp', 'KYC', 'OTP'].includes(word.toLowerCase())) {
        return word.toUpperCase();
      }
      // Capitalize the first word and words that are not exceptions
      if (index === 0 || !exceptions.includes(word.toLowerCase())) {
        return word.charAt(0).toUpperCase() + word.slice(1);
      }

      return word;
    })
    .join(' ');
};
