import * as Jo<PERSON> from 'joi';
import { offeringStatusEnum, orderStatusEnum } from '../utils/common.interface';

export const options = {
  errors: {
    wrap: {
      label: '',
    },
  },
};

export const capitalize = (s: string) => {
  return s && s[0].toUpperCase() + s.slice(1);
};

export const paginationSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1).required(),
  limit: Joi.number().integer().min(1).default(10).required(),
  sort: Joi.object().pattern(Joi.string(), Joi.number().valid(1, -1)).default({ createdAt: -1 }).optional(),
  search: Joi.string().allow('').optional(),
});

const offeringStatusValues = Object.values(offeringStatusEnum);
export const offeringStatusSchema = Joi.string()
  .trim()
  .valid(...offeringStatusValues)
  .optional()
  .label('Status')
  .messages({
    'string.empty': '{#label} cannot be empty',
    'any.only': `{#label} must be one of the following: ${offeringStatusValues.join(', ')}`,
    'any.required': '{#label} is required',
  });
const orderValues = Object.values(orderStatusEnum);
export const transactionsTypeSchema = Joi.string()
  .trim()
  .valid(...orderValues)
  .optional()
  .label('Type')
  .messages({
    'string.empty': '{#label} cannot be empty',
    'any.only': `{#label} must be one of the following: ${orderValues.join(', ')}`,
    'any.required': '{#label} is required',
  });
