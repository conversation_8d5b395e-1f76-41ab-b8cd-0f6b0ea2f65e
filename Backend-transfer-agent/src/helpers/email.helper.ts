import * as sgMail from '@sendgrid/mail';
import config from '../config/env';
import * as ejs from 'ejs';
import * as path from 'path';
import { RES_MSG } from '../utils/responseUtils';
import logger from './logger.helper';
const sender = process.env.SENDER;
/**
 * EmailService class to handle sending emails using SendGrid.
 */
class EmailService {
  private sgMail: any;

  /**
   * Constructor to initialize the EmailService.
   * Sets up SendGrid with the API key from the configuration.
   */
  constructor() {
    // Set the SendGrid API key
    this.sgMail = sgMail.setApiKey(config.SENDGRID.API_KEY);
  }

  /**
   * Sends an email using SendGrid.
   *
   * @param to - Recipient email address
   * @param subject - Subject of the email
   * @param text - Plain text content of the email
   * @param html - HTML content of the email
   * @returns A promise that resolves when the email is sent or rejects with an error
   */
  public async sendMail(to: string, subject: string, text: string, html: string, type: string): Promise<boolean> {
    try {
      const emailArray = to.split(',').map((email) => email.trim());
      let msg;
      if (type == 'investor') {
        msg = {
          to: sender,
          bcc: emailArray,
          from: {
            email: config.SENDGRID.SENDER,
            name: 'Libertum', // Set the display name here
          },
          subject,
          text,
          html,
        };
      } else {
        msg = {
          to: emailArray,
          from: {
            email: config.SENDGRID.SENDER,
            name: 'Libertum', // Set the display name here
          },
          subject,
          text,
          html,
        };
      }
      // Send the email using SendGrid
      return this.sgMail
        .send(msg)
        .then(() => {
          logger.error('error in sendgrid', '************');
          return true;
        })
        .catch((error: any) => {
          logger.error('error in sendgrid');
          logger.error(error, 'error in SendGrid');
          return false;
        });
    } catch (error) {
      logger.error('error in sendgridto');

      logger.error(error, 'Error of sendMail');
      return false;
    }
  }

  /**
   * Sends an email using SendGrid.
   *
   * @param to - Recipient email address
   * @param templateName - Template name
   * @param Details
   * @returns A promise that resolves when the email is sent or rejects with an error
   */
  public async sendEmailTemplate(to: string, templateName: string, details: any): Promise<boolean> {
    try {
      let templatePath, subject, text;
      // const htmlDetails = await ejs.renderFile(templatePath, details, { async: true });
      if (templateName === 'reset-password') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.FORGOT_PASSWORD_SUBJECT;
        text = RES_MSG.EMAIL.FORGOT_PASSWORD_TEXT;
      } else if (templateName === 'verify-otp') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.OTP_SUBJECT;
        text = RES_MSG.EMAIL.OTP_TEXT;
      } else if (templateName === 'login') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.ADMIN_EMAIL;
        text = RES_MSG.EMAIL.ADMIN_EMAIL;
      } else if (templateName === 'transferAgent') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.TRANSFERAGENT;
        text = RES_MSG.EMAIL.TRANSFERAGENT;
      } else if (templateName === 'accountBlocked') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.ACCOUNT_BLOCKED;
        text = RES_MSG.EMAIL.ACCOUNT_BLOCKED;
      } else if (templateName === 'accountUnBlock') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.ACCONT_UNBLOCKED;
        text = RES_MSG.EMAIL.ACCONT_UNBLOCKED;
      } else if (templateName === 'investor') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = `Exclusive Invitation: Private Launch of ${details?.name}`;
        text = `Exclusive Invitation: Private Launch of ${details?.name}`;
      } else {
        logger.error(templateName, 'Error: no matching template found');
        return false;
      }

      const htmlDetails = await ejs.renderFile(templatePath, { detail: { ...details, baseUrl: config.API_HOST_URL } }, { async: true }); // r
      if (templateName === 'investor') {
        return await this.sendMail(to, subject, text, htmlDetails, 'investor');
      } else {
        return await this.sendMail(to, subject, text, htmlDetails, 'investor');
      }
    } catch (error: any) {
      logger.error(error, 'Error in sendEmailTemplate');
      return false;
    }
  }
}

// Export an instance of the EmailService class
export default new EmailService();
