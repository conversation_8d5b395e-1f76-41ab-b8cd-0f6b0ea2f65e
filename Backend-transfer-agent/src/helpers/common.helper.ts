/* eslint-disable @typescript-eslint/no-explicit-any */
import * as jwt from 'jsonwebtoken';
import * as bcrypt from 'bcrypt';
import { PromiseResolve } from '../utils/common.interface';
import CONFIG from '../config/env';
import RedisHelper from './redis.helper';
import CustomError from './customError.helper';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import { otpLength } from '../utils/constant';
import logger from './logger.helper';
import { disposableDomains } from '../utils/disposableDomains';
import emailHelper from './email.helper';
const { LOGIN_MAX_ATTEMPT } = CONFIG.REDIS;
import * as moment from 'moment';

const { AUTH_EXPIRE_TIME, TOKEN, REFRESH_EXPIRE_TIME, REFRESH_TOKEN } = CONFIG.JWT_AUTH;

export interface AuthTokenResponseType {
  iat: number;
  exp: number;
  aud: string;
  iss: string;
}

const CommonHelper = {
  /**
   * @returns {Promise <PromiseResolve>}
   * @memberof CommonHelper
   */
  async createJWTAuth(data: object | any, isRefreshToken: boolean = false, tokenType?: string, expiresIn: string = AUTH_EXPIRE_TIME): Promise<PromiseResolve> {
    try {
      const expiresIN = this.parseExpirationTime(expiresIn);
      const refReshExpiresIN = this.parseExpirationTime(REFRESH_EXPIRE_TIME);
      const payload = data;
      const options = {
        expiresIn: expiresIN,
        issuer: CONFIG.PROJECT.NAME,
        audience: data.email,
      };

      const refreshOptions = {
        expiresIn: refReshExpiresIN,
        issuer: CONFIG.PROJECT.NAME,
        audience: data.email,
      };
      const accessToken: string = jwt.sign(payload, TOKEN, options);
      let refreshToken: string;
      if (isRefreshToken) {
        refreshToken = jwt.sign(payload, REFRESH_TOKEN, refreshOptions);
      }

      const accessTokenKey = tokenType ? `${tokenType}:${data.email}` : `accessToken:${data.email}`;

      await RedisHelper.setString(accessTokenKey, accessToken, Number(this.convertToMilliseconds(expiresIn)));

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: { refreshToken, accessToken },
      };
    } catch (error: any) {
      logger.error(error, 'createJWTAuth Error--------------');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message,
      };
    }
  },

  /**
   * Function to Convert second to time string.
   * @param {string} seconds
   * @returns {Promise<string>}
   * @memberof CommonHelper
   */
  async convertSecondsToHMS(seconds: number): Promise<string> {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    let timeString = '';

    if (hours > 0) {
      timeString += `${hours} hour${hours !== 1 ? 's' : ''} `;
    }
    if (minutes > 0) {
      timeString += `${minutes} minute${minutes !== 1 ? 's' : ''} `;
    }
    if (remainingSeconds > 0 || (!hours && !minutes && remainingSeconds === 0)) {
      timeString += `${remainingSeconds} second${remainingSeconds !== 1 ? 's' : ''}`;
    }

    return timeString.trim();
  },

  /**
   * @param {string} expiration
   * @returns {Promise <PromiseResolve>}
   * @memberof CommonHelper
   */
  parseExpirationTime(expiration: string | number): number | string {
    if (typeof expiration === 'string' && /^\d+$/.test(expiration)) {
      return Number(expiration); // Convert numeric strings to number
    }
    return expiration; // Return the original string if it's like "1h", "30m", etc.
  },

  /**
   * @param {string} accessToken
   * @returns {Promise <PromiseResolve>}
   * @memberof CommonHelper
   */
  async isValidToken(accessToken: string, isAccessToken: boolean = true): Promise<PromiseResolve> {
    try {
      let secretToken: any = TOKEN;
      if (!isAccessToken) {
        secretToken = REFRESH_TOKEN;
      }
      const aud = jwt.verify(accessToken, secretToken) as AuthTokenResponseType;
      if (!aud) throw new CustomError(RES_MSG.COMMON.UNAUTHORIZED_ACCESS, RESPONSES.UN_AUTHORIZED);

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: aud,
      };
    } catch (error: any) {
      logger.error(error, ' invalidToken Error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  /**
   * @param {number} length
   * @returns {Promise <PromiseResolve>}
   * @memberof CommonHelper
   */
  async generateOTP(length: number = otpLength): Promise<PromiseResolve> {
    try {
      const characters = '0123456789';
      const charactersLength = characters.length;
      let otp: string = '';
      for (let i = 0; i < length; i++) {
        otp += characters.charAt(Math.floor(Math.random() * charactersLength));
      }

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: otp,
      };
    } catch (error: any) {
      logger.error(error, ' generateOTP Error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  /**
   * Format a given date to DD-MM-YYYY format.
   * @param {Date} date - The date to be formatted.
   * @returns {Promise<PromiseResolve>} The formatted date wrapped in a PromiseResolve object.
   * @memberof CommonHelper
   */
  async formatDates(date: Date | string): Promise<PromiseResolve> {
    try {
      const formattedDate = moment(date).format('DD-MM-YYYY');
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: formattedDate,
      };
    } catch (error: any) {
      logger.error(error, 'formatDates Error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  /**
   * Send otp on user email.
   * @param {string} key - userId for key.
   * @param {string} email - targeted email.
   * @param {string} otpType - otp type.
   * @returns {Promise<PromiseResolve>} The formatted date wrapped in a PromiseResolve object.
   * @memberof CommonHelper
   */
  async sendOTP(key: string, otpType: string, email: string, name: string): Promise<PromiseResolve> {
    try {
      const otpResult = await CommonHelper.generateOTP(otpLength);
      if (otpResult.error) {
        throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.BAD_REQUEST);
      }
      const otp = otpResult.data;
      const otpKey = `otp_${otpType}_${key}`;
      const setOtpResult = await RedisHelper.setString(otpKey, otp, CONFIG.REDIS.OTP_EXPIRY);
      if (!setOtpResult) {
        throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.BAD_REQUEST);
      }
      const emailDetails = {
        otp,
        name,
      };
      // sending otp
      const sendMailResult = await emailHelper.sendEmailTemplate(email, 'verify-otp', emailDetails);
      if (!sendMailResult) {
        throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.BAD_REQUEST);
      }
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.OTP_SUCCESS,
        data: otp,
      };
    } catch (error: any) {
      logger.error(error, 'sendOTP');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  /**
   * get otp.
   * @param {string} key - userId for key.
   * @param {string} otpType - otp type.
   * @returns {Promise<PromiseResolve>} PromiseResolve object.
   * @memberof CommonHelper
   */
  async getOTP(key: string, otpType: string): Promise<PromiseResolve> {
    try {
      const otpKey = `otp_${otpType}_${key}`;
      const storedOTP = await RedisHelper.getString(otpKey);
      if (storedOTP === null || storedOTP === '') {
        throw new CustomError(RES_MSG.ERROR_MSG.OTP_EXP, RESPONSES.BAD_REQUEST);
      }
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: { otp: storedOTP, key: otpKey },
      };
    } catch (error: any) {
      logger.error(error, 'getOTP error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  /**
   * @param {string} password
   * @param {string} comparePassword
   * @returns {Promise<PromiseResolve>}
   * @memberof CommonHelper
   */
  async verifyPassword(password: string, comparePassword?: string): Promise<PromiseResolve> {
    try {
      const validPassword: boolean = await bcrypt.compare(password, comparePassword);

      if (!validPassword) throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.NOTFOUND);

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
      };
    } catch (error) {
      logger.error(error, 'verifyPassword error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  /**
   * @param {string} key
   * @returns {Promise<PromiseResolve>}
   * @memberof CommonHelper
   */
  async userLock(key: string): Promise<PromiseResolve> {
    try {
      const lockKey: string = `locked:${key}`;
      const attemptsKey: string = `otp_attempts:${key}`;
      const { LOGIN_MAX_ATTEMPT, LOGIN_BLOCK_TIME } = CONFIG.REDIS;

      const attempts: number | null = await RedisHelper.incrementKey(attemptsKey, 0, LOGIN_BLOCK_TIME);
      if (attempts && attempts >= LOGIN_MAX_ATTEMPT) {
        await RedisHelper.setString(lockKey, 'locked', LOGIN_BLOCK_TIME);
        throw new CustomError(RES_MSG.USER.USER_LOCKED_TIME, RESPONSES.RESOURCE_LOCKED);
      }
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
      };
    } catch (error) {
      logger.error(error, 'userLock error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        data: LOGIN_MAX_ATTEMPT,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  /**
   * @param {string} key
   * @returns {Promise<PromiseResolve>}
   * @memberof CommonHelper
   */
  async isLocked(key: string): Promise<PromiseResolve> {
    try {
      const lockKey: string = `locked:${key}`;
      const isLoked = await RedisHelper.getString(lockKey);
      if (!isLoked) {
        return {
          status: RESPONSES.SUCCESS,
          error: true,
          message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
          data: isLoked,
        };
      }
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: isLoked,
      };
    } catch (error) {
      logger.error(error, 'userLock error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  /**
   * @param {string} email
   * @returns {Promise<PromiseResolve>}
   * @memberof CommonHelper
   */

  getDateRange(period: string) {
    let startDate;
    const endDate = moment();

    switch (period) {
      case '1Y':
        // startDate = moment().subtract(1, 'years');
        startDate = moment().add(1, 'months').subtract(1, 'years').startOf('month');
        // startDate =moment().startOf('year');
        break;
      case '2Y':
        startDate = moment().subtract(2, 'years');
        break;
      case '3Y':
        startDate = moment().subtract(2, 'years');
        break;
      case '1M':
        startDate = moment().subtract(1, 'month');
        break;
      case '15D':
        startDate = moment().subtract(15, 'days');
        break;
      case '7D':
        startDate = moment().subtract(7, 'days');
        break;
      case '1D':
        startDate = moment().subtract(1, 'day');
        break;
      default:
        startDate = moment().subtract(1, 'year'); // default to last 1 year if no valid period
    }

    return {
      startDate: startDate.unix(), // Convert startDate to timestamp
      endDate: endDate.unix(), // Convert endDate to timestamp
    };
  },

  async isValidEmail(email: string): Promise<PromiseResolve> {
    try {
      const domain = email.split('@')[1];
      const isInvalid = disposableDomains.includes(domain);
      if (isInvalid) {
        return {
          status: RESPONSES.BAD_REQUEST,
          error: true,
          message: RES_MSG.ERROR_MSG.INVALID_EMAIL,
        };
      }
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: {
          isInvalid,
        },
      };
    } catch (error) {
      logger.error(error, 'isValidEmail error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },
  /**
   * Function to remove duplicates based on two unique identifiers.
   * @param {Array} newItems
   * @param {Array} existingItems
   * @param {Array} newItems
   * @returns {Promise<string>}
   * @memberof CommonHelper
   */

  mergeWithoutDuplicates(newItems: any[], existingItems: any[] = [], keys: any[]): any[] {
    newItems.forEach((newItem) => {
      const existingIndex = existingItems.findIndex((existingItem) =>
        keys.every((key) => {
          const existingValue = key.split('.').reduce((o: any, k: string) => (o || {})[k], existingItem);
          const newValue = key.split('.').reduce((o: any, k: string) => (o || {})[k], newItem);
          return existingValue === newValue;
        }),
      );

      if (existingIndex > -1) {
        existingItems[existingIndex] = {
          ...existingItems[existingIndex],
          ...newItem,
        };
      } else {
        existingItems.push(newItem);
      }
    });

    // Return the updated existingItems array
    return existingItems;
  },

  /**
   * Function to convert time.
   * @param {string} timeString
   * @returns {Promise<string>}
   * @memberof CommonHelper
   */
  convertToMilliseconds(timeString: string | number): number {
    if (typeof timeString === 'number') {
      return timeString;
    }

    const regex = /^(\d+)(ms|s|m|hr)$/;
    const match = timeString.match(regex);

    if (!match) {
      throw new Error('Invalid time format');
    }

    const value = parseInt(match[1], 10);
    const unit = match[2];

    switch (unit) {
      case 's':
        return value;
      case 'm':
        return value * 60;
      case 'hr':
        return value * 60 * 60;
      default:
        throw new Error('Unknown time unit');
    }
  },

  /**
   * Function to escape special character.
   * @param {string} timeString
   * @returns {Promise<string>}
   * @memberof CommonHelper
   */
  escapeRegex(text: string): string {
    return text.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
  },
};

export default CommonHelper;
