import { Storage, Bucket } from '@google-cloud/storage';
import CONFIG from '../config/env';
import logger from '../helpers/logger.helper';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import { PromiseResolve } from '../utils/common.interface';
export interface PutCommandResponse {
  endPoint: string;
  keyName: string;
}

export class GoogleCloudHelper {
  private storage: Storage;
  private bucket: Bucket;

  constructor() {
    this.initClient();
  }

  private initClient() {
    try {
      if (!CONFIG.GOOGLE.PROJECT_ID || !CONFIG.GOOGLE.BUCKET_NAME) {
        throw new Error('Google Cloud keys are not set correctly');
      }
      this.storage = new Storage({
        projectId: CONFIG.GOOGLE.PROJECT_ID,
        keyFilename: './src/helpers/libertum-tokenhub-4ec39e8b8b97.json',
      });

      this.bucket = this.storage.bucket(CONFIG.GOOGLE.BUCKET_NAME);
    } catch (error: any) {
      logger.error(error, 'File Upload Init error');
    }
  }
  public async uploadFiles(userId: string, file: Express.Multer.File, documentType: string = 'othersDocs'): Promise<PromiseResolve | any> {
    try {
      const envFolder = CONFIG.ENVIRONMENT;
      const keyName = `${envFolder}/${userId}/${documentType}/${file.originalname}`;
      // const getFiles = await this.bucket.getFiles();
      const blob = this.bucket.file(keyName);

      const blobStream = blob.createWriteStream({
        resumable: false,
        public: true,
        metadata: {
          contentType: file.mimetype,
        },
      });

      return new Promise((resolve, reject) => {
        blobStream.on('error', (error: any) => {
          logger.error(error, 'Failed to upload file');
          if (error.code === 'EACCES') {
            logger.error('Permission denied while uploading to Google Cloud Storage');
          } else if (error.code === 'ENOTFOUND') {
            logger.error('Network error: Google Cloud Storage endpoint not found');
          }
          reject({
            status: error.status || RESPONSES.BAD_REQUEST,
            error: true,
            message: error.message || RES_MSG.COMMON.BAD_REQUEST,
          });
        });

        blobStream.on('finish', () => {
          const endPoint = `https://storage.googleapis.com/${CONFIG.GOOGLE.BUCKET_NAME}/${keyName}`;

          resolve({
            status: RESPONSES.SUCCESS,
            error: false,
            message: RES_MSG.SUCCESS_MSG.CREATE_SUCCESS,
            data: { url: endPoint, keyName: keyName },
          });
        });

        blobStream.end(file.buffer);
      });
    } catch (error: any) {
      logger.error(error, 'Failed to uploadFile');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  public async deleteFile(keyName: string): Promise<boolean> {
    try {
      await this.bucket.file(keyName).delete();
      return true;
    } catch (err: any) {
      logger.error(err, 'Failed to deleteFile');
      return false;
    }
  }
}

export default new GoogleCloudHelper();
