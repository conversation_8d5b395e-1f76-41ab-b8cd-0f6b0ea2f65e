import * as Sentry from '@sentry/node';

class CustomError extends Error {
  status: number;

  constructor(message: string, status: number, userId?: string) {
    super(message);
    this.status = status;
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);

    // Capture the error in Sentry with additional context
    Sentry.captureException(this, {
      level: 'error',
      user: userId ? { id: userId } : undefined,
      tags: {
        errorType: this.name,
        errorStatus: this.status,
      },
      extra: {
        message: this.message,
        stack: this.stack,
      },
    });
  }
}

export default CustomError;
