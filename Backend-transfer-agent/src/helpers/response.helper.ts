import express from 'express';
import { ResponsePayLoad, PromiseResolve } from '../utils/common.interface';
import { capitalizeString } from './messageHelper';

export class ResponseHandler {
  static success<T>(response: express.Response, responseData: ResponsePayLoad<T>): PromiseResolve {
    let { message } = responseData;
    const { status, data = null, error } = responseData;
    message = capitalizeString(message);
    response.status(status || 200).json({ message, status, data, error });
    return {
      message,
      status: status || 200,
      data,
      error,
    };
  }

  static error<T>(response: express.Response, responseData: ResponsePayLoad<T>): PromiseResolve {
    const { message, status, data = null, error } = responseData;
    response.status(status || 500).json({ message, status, data, error });
    return {
      message,
      status: status || 500,
      data,
      error,
    };
  }
}
