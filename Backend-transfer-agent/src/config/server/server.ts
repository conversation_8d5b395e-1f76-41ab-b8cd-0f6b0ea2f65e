import * as express from 'express';
import * as path from 'path';
import * as Middleware from '../middleware/middleware';
import * as Routes from '../../routes';
import RedisHelper from '../../helpers/redis.helper';
import Db from '../connection/connection'; // Adjust the import path as necessary
import logger from '../../helpers/logger.helper';
import kafkaService from '../../services/kafkaService';
import { initSentryMiddleware } from '../middleware/sentry.middleware';

/**
 * @constant {express.Application}
 */
const app: express.Application = express();

app.use(express.static('public'));

app.use('/ta/public', express.static(path.join(__dirname, '../../public')));

/**
 * @constructs express.Application Middleware
 */
Middleware.configure(app);

/**
 * @constructs express.Application Routes
 */
Routes.init(app);

/**
 * @constructs express.Application Error Handler
 */
Middleware.initErrorHandler(app);

/**
 * Function to start the server
 */
const startServer = async () => {
  try {
    // Establish database connection
    await Db.dbConnection();

    // Sets port 3000 to default or unless otherwise specified in the environment
    app.set('port', process.env.PORT || 7002);
    const port = app.get('port');

    app.listen(port, () => {
      logger.info(`Admin Service Server is running on http://localhost:${port}`);
    });

    // Connect to Redis with a slight delay
    setTimeout(() => {
      RedisHelper.connectRedis();
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const service = kafkaService;
    }, 200); // 200 milliseconds
  } catch (error) {
    logger.error('Failed to start the server', error);
  }
};

/**
 * Start the server
 */
startServer();

/**
 * Handle graceful shutdown
 */
process.on('SIGINT', async () => {
  await Db.dbDisconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await Db.dbDisconnect();
  process.exit(0);
});

/**
 * @exports {express.Application}
 * Initialize Sentry middleware
 */
initSentryMiddleware(app);

/**
 * @exports {express.Application}
 */
export default app;
