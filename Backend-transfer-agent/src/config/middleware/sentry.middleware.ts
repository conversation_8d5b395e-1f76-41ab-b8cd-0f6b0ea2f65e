import * as Sentry from '@sentry/node';
import { Application } from 'express';

export const initSentryMiddleware = (app: Application) => {
  // The request handler must be the first middleware on the app
  app.use((req, res, next) => {
    Sentry.setContext('request', {
      method: req.method,
      url: req.url,
      headers: req.headers,
    });
    next();
  });

  // Error handler
  app.use((err: any, req: any, res: any, next: any) => {
    Sentry.captureException(err);
    next(err);
  });
};
