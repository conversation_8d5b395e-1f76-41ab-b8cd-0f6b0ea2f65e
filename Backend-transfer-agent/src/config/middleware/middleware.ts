import * as bodyParser from 'body-parser';
import * as compression from 'compression';
import * as cookieParser from 'cookie-parser';
import * as cors from 'cors';
import * as express from 'express';
import helmet from 'helmet';
import { HttpError } from '../error/index';
import { sendHttpErrorModule } from '../error/sendHttpError';
import { RESPONSES } from '../../utils/responseUtils';
import { UserInfo } from '../../utils/common.interface';
import logger from '../../helpers/logger.helper';

declare module 'express' {
  export interface Request {
    userInfo: UserInfo;
  }
}

/**
 * @export
 * @param {express.Application} app
 */
export function configure(app: express.Application): void {
  app.set('trust proxy', 1);
  // express middleware
  app.use(
    bodyParser.urlencoded({
      extended: false,
    }),
  );
  app.use(bodyParser.json());
  // parse Cookie header and populate req.cookies with an object keyed by the cookie names.
  app.use(cookieParser());
  // returns the compression middleware
  app.use(compression());
  // helps you secure your Express apps by setting various HTTP headers
  app.use(
    helmet({
      contentSecurityPolicy: true, // Disable Helmet's CSP to use the manually configured one.
      hsts: {
        maxAge: 31536000, // One year in seconds
        includeSubDomains: true,
        preload: true,
      },
      referrerPolicy: { policy: 'same-origin' },
    }),
  );

  // providing a Connect/Express middleware that can be used to enable CORS with various options
  app.use(cors({ origin: '*' })); // This allows requests from any origin

  // custom errors
  app.use(sendHttpErrorModule);

  // cors
  app.use((req, res, next) => {
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With,' + ' Content-Type, Accept,' + ' Authorization,');
    res.header('Access-Control-Allow-Credentials', 'true');
    const allowedMethods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'];
    if (!allowedMethods.includes(req.method)) {
      return res.status(RESPONSES.METHOD_NOT_ALLOWED).send('Method Not Allowed');
    }
    next();
  });
}

interface CustomResponse extends express.Response {
  sendHttpError: (error: HttpError | Error, message?: string) => void;
}

/**
 * @export
 * @param {express.Application} app
 */
export function initErrorHandler(app: express.Application): void {
  app.use((error: Error, req: express.Request, res: CustomResponse) => {
    if (typeof error === 'number') {
      error = new HttpError(error); // next(404)
    }

    if (error instanceof HttpError) {
      res.sendHttpError(error);
    } else if (app.get('env') === 'development') {
      error = new HttpError(500, error.message);
      res.sendHttpError(error);
    } else {
      error = new HttpError(500);
      res.sendHttpError(error, error.message);
    }

    logger.error(error);
  });
}
