/* eslint-disable @typescript-eslint/no-explicit-any */
import { Request, Response, NextFunction } from 'express';
import { JoiValidationResult, PromiseResolve } from '../utils/common.interface';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import { ResponseHandler } from '../helpers/response.helper';
import CustomError from '../helpers/customError.helper';
import TransactionsValidation from '../component/transactions/validation';
import logger from '../helpers/logger.helper';

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function convertValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await TransactionsValidation.convertValidation(req.query as Record<string, any>);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'convertValidationReq Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function transactionsValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await TransactionsValidation.transactionsValidation(req.query as Record<string, any>);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'transactionsValidationReq Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}
