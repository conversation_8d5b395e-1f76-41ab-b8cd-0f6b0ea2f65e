import * as multer from 'multer';
import CONFIG from '../config/env';

const storage = multer.memoryStorage();
const imageFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  if (!file.originalname.match(/\.(JPG|jpg|jpeg|JPEG|png|PNG|PDF|pdf)$/)) {
    return cb(null, false);
  }
  cb(null, true);
};

const maxSize = { fileSize: Number(CONFIG.GOOGLE.MAX_SIZE) * 1024 * 1024 };

export const validateFiles = multer({ storage, fileFilter: imageFilter, limits: maxSize });
