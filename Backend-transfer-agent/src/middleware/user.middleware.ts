/* eslint-disable @typescript-eslint/no-explicit-any */
import { Request, Response, NextFunction } from 'express';
import { JoiValidationResult, PromiseResolve } from '../utils/common.interface';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import UserValidation from '../component/userAuthentications/validation';
import { ResponseHandler } from '../helpers/response.helper';
import logger from '../helpers/logger.helper';
import CustomError from '../helpers/customError.helper';

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function validateSignUpReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.singUpValidation(req.body);
    if (validateRequest.error) throw new Error(validateRequest.message);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'validateSignupReq Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function validateLoginReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.loginValidation(req.body);
    if (validateRequest.error) throw new Error(validateRequest.message);

    const requestPayload = {
      email: validateRequest.value.email,
      password: validateRequest.value.password,
    };
    req.body = requestPayload;
    next();
  } catch (error: any) {
    logger.error(error, 'validateLoginReq Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function validateVerificationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.verificationValidation(req.body);
    if (validateRequest.error) throw new Error(validateRequest.message);

    const requestPayload = {
      email: validateRequest.value.email,
      otp: validateRequest.value.otp,
      type: validateRequest.value.type,
    };
    req.body = requestPayload;
    next();
  } catch (error: any) {
    logger.error(error, 'validateLoginReq Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function validateEmailReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.emailValidation(req.body);
    if (validateRequest.error) throw new Error(validateRequest.message);

    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'validatePasswordReq Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function changePasswordReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.changePasswordValidation(req.body);
    if (validateRequest.error) throw new Error(validateRequest.message);
    const { password, oldPassword } = validateRequest.value;
    req.body = { oldPassword: oldPassword, password, email: req.body.email };
    next();
  } catch (error: any) {
    logger.error(error, 'changePassReq Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function approveIssuer(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.approveIssuervalidation(req.body);
    if (validateRequest.error) throw new Error(validateRequest.message);
    next();
  } catch (error: any) {
    logger.error(error, 'changePassReq Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

export async function createForceTransfer(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.createForceTransfer(req.body);
    if (validateRequest.error) throw new Error(validateRequest.message);
    next();
  } catch (error: any) {
    logger.error(error, 'changePassReq Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function approveUser(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.approveUservalidation(req.body);
    if (validateRequest.error) throw new Error(validateRequest.message);
    next();
  } catch (error: any) {
    logger.error(error, 'changePassReq Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

export async function approveWhiteList(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.approveWhiteListvalidation(req.body);
    if (validateRequest.error) throw new Error(validateRequest.message);
    next();
  } catch (error: any) {
    logger.error(error, 'changePassReq Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

export async function BlockTransferAgent(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.BlockTransferAgent(req.body);
    if (validateRequest.error) throw new Error(validateRequest.message);
    next();
  } catch (error: any) {
    logger.error(error, 'CreateTransferAgent Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

export async function CreateTransferAgent(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.CreateTransferAgent(req.body);
    if (validateRequest.error) throw new Error(validateRequest.message);
    next();
  } catch (error: any) {
    logger.error(error, 'CreateTransferAgent Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

export async function CreateSubAdmin(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.CreateSubAdmin(req.body);
    if (validateRequest.error) throw new Error(validateRequest.message);
    next();
  } catch (error: any) {
    logger.error(error, 'CreateSubAdmin Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

export async function UpdateSubAdmin(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.UpdateSubAdmin(req.body);
    if (validateRequest.error) throw new Error(validateRequest.message);
    next();
  } catch (error: any) {
    logger.error(error, 'CreateSubAdmin Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function validateResetPasswordReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.resetPasswordValidation(req.body);
    if (validateRequest.error) throw new Error(validateRequest.message);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'changePassReq Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}
/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function validateUpdateProfileReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.updateProfileValidation(req.body);
    if (validateRequest.error) throw new Error(validateRequest.message);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'validateUpdateProfileReq Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function validateUpdateKycReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.updateKycValidation(req.body);
    if (validateRequest.error) throw new Error(validateRequest.message);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'validateUpdateKycReq Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function tokenValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.verifyTokenValidation(req.body);
    if (validateRequest.error) throw new Error(validateRequest.message);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'tokenValidationReq Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function verify2FAReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.verify2FAValidation(req.body);
    if (validateRequest.error) throw new Error(validateRequest.message);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'tokenValidationReq Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function resendOtpValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.resendOtpValidation(req.body);
    if (validateRequest.error) throw new Error(validateRequest.message);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'resendOtpValidationReq Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function getUserProfileReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.getUserProfileValidation({ ...req.query, id: req.params.id });
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'getUserProfileReq Error');
    ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}
