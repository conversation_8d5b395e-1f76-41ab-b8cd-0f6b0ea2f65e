import * as Jo<PERSON> from 'joi';
import { Request, Response, NextFunction } from 'express';

// Define the Joi schema for user list validation
export const getUsersListValidationSchema = Joi.object({
  page: Joi.number().min(1).default(1),
  limit: Joi.number().min(1).max(100).default(10),
  sort: Joi.string().optional(),
  search: Joi.string().optional().allow(''),
  isActive: Joi.boolean().optional(),
  isDeleted: Joi.boolean().optional(),
  kycStatus: Joi.string().allow('', null).optional(),
  userType: Joi.string().optional(),
  countryCode: Joi.string().optional(),
  isKyc: Joi.boolean().optional(),
});
export const userByofferingIdSchema = Joi.object({
  id: Joi.string(),
});

// Middleware function to validate user list parameters
export const validateGetUsersList = (req: Request, res: Response, next: NextFunction) => {
  const { error } = getUsersListValidationSchema.validate(req.query); // Validate req.query

  if (error) {
    return res.status(400).json({
      status: 400,
      error: true,
      message: error.details[0].message,
    });
  }
  next(); // Proceed to the next middleware or route handler if validation is successful
};
export const userByofferingIdValidate = (req: Request, res: Response, next: NextFunction) => {
  const { error } = userByofferingIdSchema.validate(req.params); // Validate req.query

  if (error) {
    return res.status(400).json({
      status: 400,
      error: true,
      message: error.details[0].message,
    });
  }
  next(); // Proceed to the next middleware or route handler if validation is successful
};

export const issuerQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
  sort: Joi.string().default(JSON.stringify({ createdAt: -1 })),
  search: Joi.string().optional().allow(''),
  issuerStatus: Joi.string().optional().allow(''),
  userType: Joi.string().optional().allow(''),
  countryCode: Joi.string().optional().allow(''),
});
export const validateGetissuerUsersList = (req: Request, res: Response, next: NextFunction) => {
  const { error } = issuerQuerySchema.validate(req.query); // Validate req.query

  if (error) {
    return res.status(400).json({
      status: 400,
      error: true,
      message: error.details[0].message,
    });
  }
  next(); // Proceed to the next middleware or route handler if validation is successful
};
