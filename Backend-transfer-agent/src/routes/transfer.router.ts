import { Router } from 'express';
import TransferRequestController from '../component/transfer';
import { createForceTransfer } from '../middleware';

const router: Router = Router();

// Ensure the user is authenticated before accessing the routes
// router.use(jwtAuthenticated.isAuthenticated);

// router.use();
// Create a new transfer request with validation

/**
 * Route to fetch the list of force transfer requests.
 * @route GET /get-forcetransfer-list
 * @returns {Promise<Response>} A list of transfer requests
 */
router.get('/get-forcetransfer-list', TransferRequestController.getAllTransferRequests);

/**
 * Route to fetch the list of force transfer requests in CSV format.
 * @route GET /get-forcetransfer-list-csv
 * @returns {Promise<Response>} A CSV file of transfer requests
 */
router.get('/get-forcetransfer-list-csv', TransferRequestController.getAllTransferRequestsCsv);

/**
 * Route to create and save a new force transfer request.
 * @route POST /saveForcedTransfer
 * @middleware createForceTransfer - Validates the request body
 * @returns {Promise<Response>} The newly created transfer request
 */
router.post('/saveForcedTransfer', createForceTransfer, TransferRequestController.saveForcedRequest);

/**
 * Route to reject a force transfer request.
 * @route POST /rejectForcedTransfer
 * @returns {Promise<Response>} The result of the rejection operation
 */
router.post('/rejectForcedTransfer', TransferRequestController.rejectForceTransfer);
// router.get('/Transcation', TransferRequestController.getAllTransferRequests);

export default router;
