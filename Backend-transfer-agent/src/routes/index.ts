import * as express from 'express';
import AuthRouter from './auth.router';
import UserRouter from './user.router';
import TransferRouter from './transfer.router';
import transactionsRouter from './transactions.router';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import { ResponseHandler } from '../helpers/response.helper';
import * as jwtAuthenticated from '../config/middleware/jwtAuthenticated';
import * as swaggerUi from 'swagger-ui-express';
import { swaggerDefinition } from '../utils/swaggerDef';

/**
 * @export
 * @param {express.Application} app
 */
export function init(app: express.Application): void {
  const router: express.Router = express.Router();
  app.use((req, res, next) => {
    next();
  });
  /**
   * @description Auth Router
   * @constructs
   */
  app.use('/ta/v1', AuthRouter);
  app.use('/ta/v1/transfer', jwtAuthenticated.isAuthenticated, TransferRouter);

  /**
   * @description Authenticated user routes
   */

  //app.use('/ta/v1/auth', UserRouter);
  app.use('/ta/v1/auth', jwtAuthenticated.isAuthenticated, UserRouter);
  app.use('/ta/v1/auth', jwtAuthenticated.isAuthenticated, transactionsRouter);
  /**
   * @description Swagger Routes
   */
  app.use('/apis-docs', swaggerUi.serve, swaggerUi.setup(swaggerDefinition));

  /**
   * @constructs all routes
   */

  app.use(router);

  /**
   * @description if page not found
   * @constructs
   */
  app.use((req, res) => {
    ResponseHandler.error(res, {
      error: true,
      message: RES_MSG.ERROR_MSG.PAGE_NOT_FOUND,
      status: RESPONSES.NOTFOUND,
    });
  });
}
