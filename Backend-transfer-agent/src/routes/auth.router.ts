import { Router } from 'express';
import { AuthComponent } from '../component';
import { validateEmailReq, validateVerificationReq, validateLoginReq, resendOtpValidationReq, validateResetPasswordReq, verify2FAReq, tokenValidationReq } from '../middleware';
import authMiddleware from '../config/middleware/jwtTokenAuth';
const router: Router = Router();
/**
 * The auth router handles all authentication related routes
 */

router.get('/health-check', AuthComponent.healthCheck);
/**
 * Logs in a user. The user is required to provide a valid email and password.
 * @param {Request} req The request object
 * @param {Response} res The response object
 */
router.post('/login', validateLoginReq, AuthComponent.loginController);
/**
 * Creates a new password for the user.
 * @param {Request} req The request object
 * @param {Response} res The response object
 */
router.post('/cratePassword', validateLoginReq, AuthComponent.cratePassword);

/**
 * Verifies the user's email and password.
 * @param {Request} req The request object
 * @param {Response} res The response object
 */
router.post('/verify', validateVerificationReq, AuthComponent.verifyController);

/**
 * Resends the OTP to the user.
 * @param {Request} req The request object
 * @param {Response} res The response object
 */
router.post('/resend-otp', resendOtpValidationReq, AuthComponent.reSendOtpController);
/**
 * Sends a password reset OTP to the user.
 * @param {Request} req The request object
 * @param {Response} res The response object
 */
router.post('/forgot-password', validateEmailReq, AuthComponent.forgotPasswordController);
/**
 * Verifies the user's 2FA before allowing login.
 * @param {Request} req The request object
 * @param {Response} res The response object
 */
router.post('/verify-2faBeforelogin', authMiddleware, AuthComponent.verify2FALcoginController);
/**
 * Resets the user's password.
 * @param {Request} req The request object
 * @param {Response} res The response object
 */
router.post('/reset-password', validateResetPasswordReq, authMiddleware, AuthComponent.resetPasswordController);
/**
 * Verifies the user's 2FA after login.
 * @param {Request} req The request object
 * @param {Response} res The response object
 */
router.post('/verify-2fas', verify2FAReq, authMiddleware, AuthComponent.verifyLogin2FAController);
/**
 * Allows the user to reset 2FA.
 * @param {Request} req The request object
 * @param {Response} res The response object
 */
router.patch('/forgot-2fa', tokenValidationReq, authMiddleware, AuthComponent.forgot2FAController);
/**
 * Resets the user's 2FA.
 * @param {Request} req The request object
 * @param {Response} res The response object
 */
router.post('/reset2FA', tokenValidationReq, authMiddleware, AuthComponent.reSet2FA);

/**
 * @export {express.Router}
 */
export default router;
