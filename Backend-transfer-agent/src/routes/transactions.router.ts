import { Router } from 'express';
import * as jwtAuthenticated from '../config/middleware/jwtAuthenticated';
import { TransactionController } from '../component';
import { transactionsValidationReq } from '../middleware/transactions.middleware';

/**
 * @constant {express.Router}
 */

const router: Router = Router();
router.use(jwtAuthenticated.isAuthenticated);

router.get('/transactions/:offeringId', transactionsValidationReq, TransactionController.getTransactions);
router.get('/transactionsCsv/:offeringId', TransactionController.getTransactionsCsv);

/**
 * @export {express.Router}
 */

export default router;
