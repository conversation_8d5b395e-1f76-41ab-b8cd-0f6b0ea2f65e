import { Router } from 'express';
import { AuthComponent } from '../component';
import { userByofferingIdValidate, validateGetUsersList, validateGetissuerUsersList } from '../middleware/validateGetUsersList';
import { approveIssuer, approveUser, approveWhiteList, changePasswordReq, getUserProfileReq, tokenValidationReq } from '../middleware';

/**
 * @constant {express.Router}
 */
// getUserslist
const router: Router = Router();
router.get('/getUserslist', validateGetUsersList, AuthComponent.getUsersListController);
router.get('/getUsersListCsv', AuthComponent.getUsersListControllerCsv);
router.post('/approve', approveUser, AuthComponent.approve);
router.post('/approveIssfetchUserListcsvuer', approveIssuer, AuthComponent.approveIssuer);
router.get('/getUserdata', AuthComponent.getUserdata);
router.post('/unblock', AuthComponent.unblock);
router.patch('/change-password', changePasswordReq, AuthComponent.changePasswordController);
router.get('/enable-2fa', AuthComponent.enable2FAController);
router.post('/verify-2fa', AuthComponent.verify2FAController);
router.patch('/disable-2fa', tokenValidationReq, AuthComponent.disable2FAController);
router.get('/log-out', AuthComponent.logOutController);
router.get('/getissuer', validateGetissuerUsersList, AuthComponent.getIssuerController);
router.get('/getIssuerListCsv', AuthComponent.getIssuerListControllerCsv);
router.get('/getManageIssuerListCsv', AuthComponent.getManageIssuerListCsv);
router.get('/report', AuthComponent.singleOfferingReportController);
router.get('/whitelistDetails', AuthComponent.whitelistDetails);
router.get('/whitelistDetailsCsv', AuthComponent.whitelistDetailsCsv);
router.post('/approveWhitelist', approveWhiteList, AuthComponent.approveOffering);
router.get('/offeringList', AuthComponent.offeringDetails);
router.get('/userByofferingId/:id', userByofferingIdValidate, AuthComponent.userByOfferingId);
router.get('/dashboard', AuthComponent.dashboard);
router.get('/transcation', AuthComponent.transcationReportController);
router.get('/topHolders', AuthComponent.topHolders);
router.get('/user', AuthComponent.user);
router.get('/topInvestors', AuthComponent.topInvestors);
router.get('/taGraph', AuthComponent.offeringReportController);
router.get('/profile/:id', getUserProfileReq, AuthComponent.getUserProfileDetails);

/**
 * @export {express.Router}
 */

export default router;
