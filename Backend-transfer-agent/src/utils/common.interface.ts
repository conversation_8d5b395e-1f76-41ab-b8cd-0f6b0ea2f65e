/* eslint-disable @typescript-eslint/no-explicit-any */
export interface ResponsePayLoad<T = void> {
  message: string;
  status: number;
  data?: T;
  error: boolean;
}
export interface PromiseResolve {
  status: number;
  error: boolean;
  message: string;
  data?: any;
}
export const enum queueMessageTypeEnum {
  USER = 'user',
  OFFERING = 'offering',
  REQ_OFFERING = 'reqOffering',
  REQ_WHITELIST = 'reqWhitelist',
  ORDER = 'order',
  TRANSFER = 'ForceTransferred',
  SAVETRANSFER = 'SaveTransfer',
}

export enum orderStatusEnum {
  STARTED = 'STARTED',
  MINTED = 'MINTED',
  BURN = 'BURN',
  ALL = 'ALL',
  FREEZE = 'FREEZE',
  UNFREEZE = 'UNFREEZE',
  TRANSFER = 'TRANSFER',
  PENDING = 'PENDING',
  REDEEM = 'REDEEM',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  RESUBMIT = 'RESUBMIT',
  DELETED = 'DELETED',
  TRANSFER_FROM = 'TRANSFER_FROM',
  TRANSFER_TO = 'TRANSFER_TO',
  CONVERT = 'CONVERT',
}

export enum paymentTypeEnum {
  USDC = 'USDC',
  USDT = 'USDT',
  // BANK_TRANSFER = 'Bank Transfer',
}
export enum transferStatusEnum {
  STARTED = 'STARTED',
  MINTED = 'MINTED',
  BURN = 'BURN',
  FREEZE = 'FREEZE',
  UNFREEZE = 'UNFREEZE',
  TRANSFER = 'TRANSFER',
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  RESUBMIT = 'RESUBMIT',
  DELETED = 'DELETED',
}

export enum offeringStatusEnum {
  STARTED = 'STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  RESUBMIT = 'RESUBMIT',
}

export interface JoiValidationResult {
  error: boolean;
  value: any;
  message?: string;
  status?: number;
}

export interface UserInfo {
  userId: string;
  email: string;
  status: string;
  reason: string;
  userType: string;
  exp: number;
}

export enum UserType {
  Investor = 'investor',
  Institution = 'institution',
  Admin = 'admin',
  Subadmin = 'subadmin',
}

export enum sumSubKycStatusEnum {
  NOT_STARTED = 'NOT_STARTED',
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  DECLINED = 'DECLINED',
  HOLD = 'HOLD',
  PRECHECKRED = 'PRECHECKRED',
}
export enum transferAgentStatus {
  REQUESTSENT = 'REQUEST-SENT',
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  DECLINED = 'DECLINED',
}

export enum KycStatus {
  STARTED = 'STARTED',
  NOT_STARTED = 'NOT_STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  RESUBMIT = 'RESUBMIT',
}

export enum IssuerStatus {
  NOT_APPLIED = 'NOT_APPLIED',
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  BLOCKED = 'BLOCKED',
}

export enum offeringStatus {
  STARTED = 'STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  RESUBMIT = 'RESUBMIT',
}
export enum OperationType {
  READ = 0,
  READ_WRITE = 1,
  NONE = 2,
}

export enum AssetsType {
  RealEstate = 'Real Estate',
  PrivateEquity = 'Private Equity',
  TreasuryBonds = 'Treasury Bonds',
  ArtCollectibles = 'Art & Collectibles',
  MBS = 'MBS',
  Commodities = 'Commodities',
}

export enum ChainType {
  METAMASK_WALLET = 'MetamaskWallet',
  TRUST_WALLET = 'TrustWallet',
  WALLET_CONNECT = 'WalletConnect',
  FIREBLOCKS_WALLET = 'FireblocksWallet',
}

export const otpType = {
  LOGIN: 'login',
  FORGOT: 'forgot',
  TFA: 'tfa',
};

export enum DocumentTypes {
  FRONT_ID_CARD = 'frontId',
  BACK_ID_CARD = 'backId',
  NATIONAL_ID = 'proofOfResidence',
}

export interface IUserFilters {
  name?: string;
  email?: string;
  mobile?: string;
  isActive?: boolean;
  isDeleted?: boolean;
  kycStatus?: string;
  userType?: string;
  countryCode?: string;
  isKyc?: boolean;
}

export interface IPagination {
  page: number;
  limit: number;
  sort?: any;
  search?: any;
}

export interface IUserListResult {
  data: any[];
  currentPage: number;
  totalPages: number;
  totalCount: number;
  nextPage: number | null;
  previousPage: number | null;
}
