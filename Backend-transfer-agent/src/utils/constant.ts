export const otpLength = 6;
export const passwordPattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
export const namePattern = /^[A-Za-z\s]+$/;
export const otpPattern = /^[0-9]{6}$/;
export const maxPasswordHistory = 5;
export const offeringDocs = [
  { title: 'E-Signature document', name: 'eSign' },
  { title: 'Land registration', name: 'landRegistration' },
  { title: 'Title document', name: 'titleDocs' },
  { title: 'Bank approval', name: 'bankApproval' },
  { title: 'Encumbrance certificate', name: 'encumbranceCertificate' },
  { title: 'Actual site', name: 'actualSite' },
  { title: 'Property tax receipt', name: 'propertyTaxReceipt' },
  { title: 'Sales deed', name: 'salesDeed' },
  { title: 'Power of attorney', name: 'powerOfAttorney' },
  { title: 'Property under registered society', name: 'resisteredSociety' },
  { title: 'Token icon', name: 'tokenIcon' },
];
