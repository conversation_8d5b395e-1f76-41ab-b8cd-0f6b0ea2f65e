export const RESPONSES = {
  CONTINUE: 100,
  SWITCHING_PROTOCOLS: 101,
  PROCESSING: 102,
  EARLY_HINTS: 103,
  CREATED: 201,
  SUCCESS: 200,
  ACCEPTED: 202,
  NON_AUTHORITATIVE_INFORMATION: 203,
  NO_CONTENT: 204,
  RESET_CONTENT: 205,
  PARTIAL_CONTENT: 206,
  AMBIGUOUS: 300,
  MOVED_PERMANENTLY: 301,
  FOUND: 302,
  SEE_OTHER: 303,
  NOT_MODIFIED: 304,
  TEMPORARY_REDIRECT: 307,
  PERMANENT_REDIRECT: 308,
  BAD_REQUEST: 400,
  UN_AUTHORIZED: 401,
  PAYMENT_REQUIRED: 402,
  FORBIDDEN: 403,
  NOTFOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  NOT_ACCEPTABLE: 406,
  PROXY_AUTHENTICATION_REQUIRED: 407,
  REQUEST_TIMEOUT: 408,
  CONFLICT: 409,
  GONE: 410,
  LENGTH_REQUIRED: 411,
  PRECONDITION_FAILED: 412,
  PAYLOAD_TOO_LARGE: 413,
  URI_TOO_LONG: 414,
  UNSUPPORTED_MEDIA_TYPE: 415,
  REQUESTED_RANGE_NOT_SATISFIABLE: 416,
  EXPECTATION_FAILED: 417,
  I_AM_A_TEAPOT: 418,
  MISDIRECTED: 421,
  UNPROCESSABLE_ENTITY: 422,
  RESOURCE_LOCKED: 423,
  FAILED_DEPENDENCY: 424,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  NOT_IMPLEMENTED: 501,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
  HTTP_VERSION_NOT_SUPPORTED: 505,
};

export const RES_MSG = {
  COMMON: {
    BAD_REQUEST: 'The request is invalid. Please check parameters and ensure all required fields are filled out.',
    RECORD_FETCH: 'Fetch Record Successfully',
    SOMETHING_WRONG: 'An unexpected error occurred. Please try again later or contact support.',
    DUPLICATE_DATA: 'The data already exists in our system. Please verify your entries.',
    UNAUTHORIZED_ACCESS: 'Please log in with appropriate credentials or contact your administrator.',
    NO_USER: 'No user account matches the provided credentials. Please check your username/password.',
    UNAUTHORIZED_ACTION: 'You can not perform this action',
    NO_FOUND: 'No records found matching your search criteria. Try different keywords or filters.',
    ADMIN_BLOCK_USER: 'User has been blocked by Admin.',
    ADMIN_BLOCK_TRANSFERAGENT: 'Transfer agent has been blocked.',
    ADMIN_BLOCK_SUBADMIN: 'Subadmin  has been blocked.',
    ADMIN_UNBLOCK_USER: 'User has been successfully unblocked.',
    ADMIN_UNBLOCK_TRANSFERAGENT: 'Transfer agent has been successfully unblocked.',
    ADMIN_UNBLOCK_SUBADMIN: 'Subadmin  has been successfully unblocked.',
    BLOCK_USER: 'The user is already blocked and cannot perform further actions. Contact support if needed.',
    KYC_APPROVED: 'User KYC is already approved.',
    KYC_APPROVED_STEP: 'Documents for KYC approval are incomplete. Please provide all required documents.',
    INCOMPLETE_KYC: 'KYC verification steps are incomplete. Please complete all necessary steps.',
    ISSUER_APPROVED: 'The issuer request is already approved.',
    FORBIDDEN_ACCESS: 'Access to this resource is forbidden.',
    NO_OFFERING: 'No offerings found based on your search.',
  },

  USER: {
    USERS_FETCH: 'Users fetched successfully.',
    USERS_UPDATED: 'transfer request updated sucessfully',
    USER_FETCH: 'User fetched successfully.',
    OTP_SUCCESS: 'OTP verified successfully.',
    LOGIN_SUCCESS: 'You have logged in successfully. Welcome back!',
    PASSWORD_CREATED: 'Password created sucessfully',
    USER_LOCKED: 'Your account is locked due to multiple unsuccessful login attempts. Please try again later or contact support for assistance.',
    USER_LOCKED_TIME: 'Your account is locked. Please try again after 10 minutes.',
    USER_DATA_SAVED: 'User data has been saved successfully.',
    USER_CREATE_ERROR: 'An error occurred while creating the user. Please check the provided information and try again.',
    USER_DELETED: 'User has been deleted successfully.',
    USER_FETCH_ERROR: 'An error occurred while fetching the user. Please ensure the user ID is correct and try again.',
    USER_UPDATION_SUCCESS: 'User information updated successfully.',
    USER_ALREADY_EXIST: 'A user with this email already exists. Please use different credentials or contact support.',

    UNAUTHORIZE: 'You are not authorize to requested page',

    USER_NOT_EXIST: 'A user with this email not exists',

    PASSWORD: 'Password and confirm password are not same',
    WALLLET_ALREADY_EXIST: 'A user with this Wallet address already exists. Please use different credentials or contact support.',
    USER_ALREADY_VERIFIED: 'This user has already been verified.',
    USER_NOT_VERIFIED: 'An OTP has been sent to your email. Please verify your email to continue accessing your account.',
    USER_KYC_UPDATE_SUCCESS: 'KYC has been submitted successfully.',
  },

  TWO_FA: {
    CREATED: 'Two-factor authentication has been enabled.',
    FORGOT_2FA: "You can now re-enable Two-Factor Authentication to enhance your account's security",
    VERIFIED_SUCCESS: 'Two-factor authentication verified successfully.',
    DISABLE_SUCCESS: 'Two-factor authentication has been disabled successfully.',
    PENDING: 'Two-factor authentication is pending. Please verify.',
    TOKEN_EXPIRE: 'Session expired!',
    TOKEN_INVALID: 'The 2FA code you entered is incorrect. Please check the code and try again.',
    RESET_SUCCESS: 'Two-factor authentication has been disabled. You can enable it again if desired.',
  },

  ERROR_MSG: {
    EXPIRE_OTP: 'OTP is expired. Please try again',
    ALREADY_REJECTED: 'Token Transfer Request is already rejected',
    ALREADY_APPROVED: "Token Transfer Request is already approved, you can't reject it",
    PASSWORD_CHANGE_ERROR: 'An error occurred while changing your password.',
    LOGIN_ERROR: 'An error occurred during login.',
    INTERNAL_SERVER_ERROR: 'Internal Server error, please try again.',
    INVALID_CREDENTIALS: 'The credentials you provided are invalid.',
    FORGOT_CRED: 'Please verify the email address entered. If it is registered, an OTP will be sent to you.',
    INVALID_USER: 'The user account is invalid or does not exist.',
    INVALID_EMAIL: 'Disposable email addresses are not allowed.',
    INVALID_FILE: 'Invalid file type. Only JPG, PNG, and PDF files are allowed. Please upload a supported file format.',
    ALREADY_EXIST_ERROR: 'A user with this information already exists. Please use different credentials or contact support for assistance.',
    REGISTRATION_COMMON_ERROR: 'An error occurred during registration.',
    INVALID_OTP: 'The provided One-Time Password (OTP) is invalid. Please check the OTP and try again.',
    OTP_EXP: 'The OTP has expired. Please request a new OTP and try again.',
    USER_NOT_FOUND: 'No user found with the provided information. Please verify your details and try again.',
    PAGE_NOT_FOUND: 'The requested page was not found.',
    LOGIN_COMMON_ERROR: 'An error occurred during login.',
    INCORRECT_CURRENT_PASSWORD: 'The current password you entered is incorrect. Please verify and try again.',
    NEW_PASSWORD_SAME_AS_CURRENT_PASSWORD: 'The new password must be different from the current password.',
    PASSWORD_RECENTLY_USED: 'This password has been used recently. Please choose a different password to enhance your account security.',
    OTP_REQUIRED: 'One-Time Password (OTP) is required to proceed.',
    OTP_AND_SECRET_REQUIRED: 'Both OTP and secret key are required for authentication. Please provide both to continue.',
    USER_ID_REQUIRED: 'User ID is required to perform this action.',
    TRANSFORMATION_TO_USERDTO_FAILED: 'Failed to process user data.',
    VALIDATION_FAILED: 'Validation Failed.',
    DATA_FETCH_ERROR: 'An error occurred while fetching data. Please try again later or contact support for assistance.',
    USER_UPDATION_ERROR: 'An error occurred while updating the user information.',
    MOBILE_NO_EXIST: 'This mobile number is already associated with an existing account.',
    UPDATE_FAILED: 'Failed to update the offering. Please check the details and try again.',
  },

  SUCCESS_MSG: {
    REGISTER_SUCCESS: 'Congratulations! You have successfully registered. Please verify your email to continue using our services.',
    FETCH_ISSUER_DETAILS: 'Issuer details have been retrieved successfully.',
    DATA_SUCCESS: 'Data fetched successfully.',
    OFFERING_REJECT: 'Offering rejected sucessfully',
    ISSUER_LIST: 'Issuer list retrieved successfully.',
    EMAIL_VERIFIED_SUCCESS: 'Email verification was successful.',
    CREATE_SUCCESS: 'User created successfully.',
    OTP_SENT_SUCCESS: 'OTP has been sent successfully.',
    LOGOUT: 'You have been logged out successfully.',
    UPDATE_USER: 'User information updated successfully.',
    HEALTH_CHECK: 'System health is OK.',
    PASSWORD_RESET_SUCCESS: 'Password has been changed successfully.',
    DOCS_UPLOADED_SUCCESS: 'Documents uploaded successfully.',
    KYC_APPROVED: 'KYC has been approved successfully.',
    WHITELIST_APPROVED: 'Wallet has been approved successfully.',
    KYB_APPROVED: 'KYB has been approved successfully.',
    KYC_REJECTED: 'KYC has been rejected successfully.',
    WALLET_REJECTED: 'Wallet has been rejected successfully.',
    KYB_REJECTED: 'KYB has been rejected successfully.',
    NO_OFFERING: 'No offerings found based on your search.',
    INVESTOR_COUNT_SUCCESS: 'Count gets sucessfully',
  },

  EMAIL: {
    KYC_RECEIVED: 'KYC document received',
    FORGOT_PASSWORD_SUBJECT: 'Password Reset Request',
    FORGOT_PASSWORD_TEXT: 'You have requested a password reset. Please use the OTP provided below to reset your password. If you did not request this, please ignore this email.',
    OTP_TEXT: 'Your One-Time Password (OTP) for verification is:',
    OTP_SUBJECT: 'Your Verification Code (OTP) is Here',
    KYC_APPROVED: 'Congratulations! Your KYC (Know Your Customer) verification has been successfully approved.',
    KYC_REJECTED: 'Unfortunately, your KYC application has been rejected. Please review the submitted information and try again.',
    KYB_APPROVED: 'Congratulations! Your KYB (Know Your Business) verification has been successfully approved.',
    KYB_REJECTED: 'Unfortunately, your KYB application has been rejected. Please review the submitted information and resubmit.',
    ADMIN_EMAIL: 'Admin Login Successful: You have successfully logged in to the admin panel.',
    TRANSFERAGENT: 'You are invited to join the Libertum Platform as a Transfer Agent. Click here for more details.',
    SUBADMIN_INVITE: 'Welcome to Libertum Platform – Sub-Admin Access Invitation',
    ACCOUNT_BLOCKED: 'Account blocked notification',
    ACCONT_UNBLOCKED: 'Account unblocked notification',
  },
};
