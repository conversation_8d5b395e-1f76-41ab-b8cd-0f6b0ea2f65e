import mongoose, { Schema, Document, Types } from 'mongoose';
import { offeringStatusEnum, paymentTypeEnum } from '../../../utils/common.interface';

export interface IOrder extends Document {
  userId: Types.ObjectId | string;
  offeringId: Types.ObjectId | string;
  walletAddress: string;
  amount: number;
  status?: offeringStatusEnum;
  isMinted: boolean;
  feesInPercentage: number;
  isSettled: boolean;
  quantity: number;
  txHash: string;
  paymentMethod?: paymentTypeEnum;
}

export interface IUpdateOrder {
  _id?: string;
  userId?: Types.ObjectId | string;
  offeringId?: Types.ObjectId | string;
  amount?: number;
  status?: offeringStatusEnum;
  isMinted?: boolean;
  feesInPercentage?: number;
  isSettled?: boolean;
  quantity?: number;
  txHash?: string;
  paymentMethod?: paymentTypeEnum;
}

const order: Schema<IOrder> = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, ref: 'users', required: true },
    offeringId: { type: Schema.Types.ObjectId, ref: 'offerings', required: true },
    walletAddress: { type: String, required: true },
    amount: {
      type: Number,
      required: true,
      default: 0,
    },
    isMinted: {
      type: Boolean,
      required: true,
      default: false,
    },
    isSettled: {
      type: Boolean,
      required: true,
      default: false,
    },
    feesInPercentage: {
      type: Number,
      required: true,
      default: 1,
    },
    quantity: {
      type: Number,
      required: true,
      default: 0,
    },
    status: {
      type: String,
      enum: Object.values(offeringStatusEnum),
      default: offeringStatusEnum.PENDING,
    },
    paymentMethod: {
      type: String,
      enum: Object.values(paymentTypeEnum),
      default: paymentTypeEnum.USDT,
    },
    txHash: { type: String, required: false, default: null },
  },

  {
    timestamps: true,
    versionKey: false,
  },
);

const OrderSchema = mongoose.model<IOrder>('order', order);
export { OrderSchema };
