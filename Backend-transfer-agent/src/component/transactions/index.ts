import { Request, Response } from 'express';
import { PromiseResolve } from '../../utils/common.interface';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { ResponseHandler } from '../../helpers/response.helper';

import { Types } from 'mongoose';
import TransactionsService from './service';
import logger from '../../helpers/logger.helper';

class TransactionController {
  /**
   * Handles fetching transactions with filters and pagination.
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise<void>}
   */
  public getTransactions = async (req: Request, res: Response): Promise<PromiseResolve | void> => {
    try {
      const { page = 1, limit = 10, sort = '', search = '', type = '' } = req.query;
      const userId = new Types.ObjectId(req.userInfo.userId);
      const offeringId = new Types.ObjectId(req.params.offeringId);

      const filters = {
        ...(type && { status: type?.toString().toUpperCase() }),
        userId,
        offeringId,
      };

      const result: PromiseResolve = await TransactionsService.fetchTransactions(filters, {
        page: Number(page),
        limit: Number(limit),
        ...(search && { search: search }),
        ...(sort && { sort: sort }),
      });

      return ResponseHandler.success(res, {
        status: result.status || RESPONSES.SUCCESS,
        error: false,
        message: result.message || RES_MSG.USER.USERS_FETCH,
        data: result.data || [],
      });
    } catch (error) {
      logger.error(error, 'getTransactions Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };
  public getTransactionsCsv = async (req: Request, res: Response): Promise<PromiseResolve | void> => {
    try {
      const { type = '' } = req.query;
      const userId = new Types.ObjectId(req.userInfo.userId);
      const offeringId = new Types.ObjectId(req.params.offeringId);

      // Build filters
      const filters = {
        ...(type && { status: type?.toString().toUpperCase() }),
        userId,
        offeringId,
      };

      // Fetch the transactions without pagination and search
      const result: PromiseResolve = await TransactionsService.fetchTransactionsCsv(filters);

      // Handle successful response
      return ResponseHandler.success(res, {
        status: result.status || RESPONSES.SUCCESS,
        error: false,
        message: result.message || RES_MSG.USER.USERS_FETCH,
        data: result.data || [],
      });
    } catch (error) {
      // Log the error and send error response
      logger.error(error, 'getTransactions Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };
}

export default new TransactionController();
