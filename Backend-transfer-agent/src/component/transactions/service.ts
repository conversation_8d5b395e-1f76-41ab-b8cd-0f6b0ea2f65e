import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { PromiseResolve, IPagination } from '../../utils/common.interface';
import { ITransactionsService } from './interface';
import logger from '../../helpers/logger.helper';
import { OrderSchema } from '../userAuthentications/models/order.model';
import CommonHelper from '../../helpers/common.helper';

class TransactionsService implements ITransactionsService {
  /**
   * @param {EventLog} filters
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  fetchTransactions = async (filters: any, pagination: IPagination): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, search: escapedSearch } = pagination;
      const search = escapedSearch ? CommonHelper.escapeRegex(escapedSearch) : null;
      const skip = (page - 1) * limit;
      const match: any = {
        offeringId: filters.offeringId,
        status: { $nin: ['PENDING', 'APPROVED'] },
      };
      const sort = filters.status === 'MINTED' ? { orderMinted: -1 } : { updatedAt: -1 };

      // If filters.status is 'ALL', include MINTED, FREEZE, UNFREEZE
      if (filters.status === 'ALL') {
        match.orderType = { $in: ['MINTED', 'FREEZE', 'UNFREEZE', 'CONVERT', 'REJECTED'] }; // Include all these statuses
      } else if (filters.status === 'MINTED' || filters.status === 'REJECTED') {
        match.status = { $eq: filters.status.toUpperCase() };
      } else if (filters.status !== 'ALL') {
        match.orderType = filters.status; // Add the specific orderType filter
      }
      const pipeline: any = [
        { $match: match }, // Filter first to reduce unnecessary lookups
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'userDetails',
          },
        },
        { $unwind: { path: '$userDetails', preserveNullAndEmptyArrays: true } },
        {
          $set: {
            name: '$userDetails.name',
            email: '$userDetails.email',
            userImage: '$userDetails.userImage',
          },
        },
        ...(search
          ? [
              {
                $match: {
                  $or: [{ name: { $regex: search, $options: 'i' } }, { email: { $regex: search, $options: 'i' } }],
                },
              },
            ]
          : []),
        { $sort: sort },
        {
          $facet: {
            transactions: [{ $skip: skip }, { $limit: limit }],
            total: [{ $count: 'totalCount' }],
          },
        },
      ];

      const [result] = await OrderSchema.aggregate(pipeline);
      const { transactions = [], total = [] } = result || {};
      const totalCount = total[0]?.totalCount || 0;
      const totalPages = Math.ceil(totalCount / limit);
      const nextPage = page < totalPages ? page + 1 : null;
      const previousPage = page > 1 ? page - 1 : null;

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.COMMON.RECORD_FETCH,
        data: { transactions, currentPage: page, totalPages, totalCount, nextPage, previousPage },
      };
    } catch (error) {
      logger.error(error, 'fetchTransactions error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  fetchTransactionsCsv = async (filters: any): Promise<PromiseResolve> => {
    try {
      // Prepare the match object with the offeringId and other filters
      const match: any = {
        offeringId: filters.offeringId,
        status: { $nin: ['PENDING', 'APPROVED'] },
      };

      // If filters.status is 'ALL', include MINTED, FREEZE, UNFREEZE
      if (filters.status === 'ALL') {
        match.orderType = { $in: ['MINTED', 'FREEZE', 'UNFREEZE'] }; // Include all these statuses
      } else if (filters.status !== 'ALL') {
        match.orderType = filters.status; // Add the specific orderType filter
      }

      // Prepare the pipeline with the proper stages
      const pipeline: any = [
        { $match: match }, // Apply the filters first

        // Lookup user details
        {
          $lookup: {
            from: 'users', // Look up from the 'users' collection
            localField: 'userId',
            foreignField: '_id',
            as: 'userDetails',
          },
        },
        { $unwind: { path: '$userDetails', preserveNullAndEmptyArrays: true } },

        // Set additional fields from userDetails to the root document
        {
          $set: {
            name: '$userDetails.name',
            email: '$userDetails.email',
            userImage: '$userDetails.userImage',
          },
        },

        // Project the desired fields, excluding unnecessary ones
        {
          $project: {
            userDetails: 0, // Remove the userDetails object to keep only necessary fields
          },
        },

        // Default sorting by createdAt (descending)
        { $sort: { createdAt: -1 } },
      ];

      // Perform the aggregation with the properly structured pipeline
      const result = await OrderSchema.aggregate(pipeline);

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.COMMON.RECORD_FETCH,
        data: { transactions: result }, // Return the full set of transactions
      };
    } catch (error) {
      logger.error(error, 'fetchTransactions error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };
}

export default new TransactionsService();
