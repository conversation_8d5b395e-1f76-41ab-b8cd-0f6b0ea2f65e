import { FilterQuery } from 'mongoose';
import { IPagination, PromiseResolve } from '../../utils/common.interface';

export interface ITransactionsService {
  /**
   * @param {EventLog} searchDetails
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */

  fetchTransactions(searchDetails: FilterQuery<any>, pagination?: IPagination): Promise<PromiseResolve>;
}
