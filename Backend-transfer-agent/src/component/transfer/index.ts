import { Request, Response } from 'express';
import TransferRequestService from './service';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import { ResponseHandler } from '../../helpers/response.helper';

import CustomError from '../../helpers/customError.helper';

import mongoose from 'mongoose';

import { queueMessageTypeEnum, transferStatusEnum } from '../../utils/common.interface';
import TransferRequest from './models/transfer';

import userClient from '../../_grpc/clients/user.client';

import kafkaService from '../../services/kafkaService';
import { userSchema } from '../userAuthentications/models/user.model';
import { offeringSchema } from '../userAuthentications/models/offerings.model';
import logger from '../../helpers/logger.helper';

export default class TransferRequestController {
  /**
   * Retrieves a list of transfer requests.
   *
   * @param {Request} req - Express request object
   * @param {Response} res - Express response object
   * @returns {Promise<void>}
   *
   * The query parameters can be used to filter the list of transfer requests.
   * The parameters are:
   *
   * - page: The page number to fetch.
   * - limit: The number of documents to fetch per page.
   * - sort: The field to sort the list by.
   * - search: The search query to filter the list.
   * - offeringId: The id of the offering.
   * - isActive: A boolean indicating whether the user is active.
   * - isDeleted: A boolean indicating whether the user is deleted.
   * - kycStatus: The status of the user's KYC.
   * - userType: The type of user.
   * - countryCode: The country code of the user.
   * - isKyc: A boolean indicating whether the user has completed KYC.
   * - status: The status of the transfer request.
   */
  static getAllTransferRequests = async (req: Request, res: Response) => {
    try {
      const { page = 1, limit = 10, sort = JSON.stringify({ createdAt: -1 }), search = '', offeringId, isActive, isDeleted, countryCode = '', isKyc, status, transferBy, userType } = req.query;

      /**
       * Define the filters to be used when fetching the transfer requests.
       */
      const filters = {
        ...(isActive !== undefined && { isActive: isActive === 'true' }),
        ...(isDeleted !== undefined && { isDeleted: isDeleted === 'true' }),
        ...(userType && { userType }),
        ...(countryCode && { countryCode }),
        ...(isKyc !== undefined && { isKyc: isKyc === 'true' }),
        ...(offeringId && { offeringId }), // Add offeringId to the filters if it is provided
        ...(status && { status }), // Add offeringId to the filters if it is provided
        ...(transferBy && { transferBy }),
        // ...(transferBy && { isForceTransfer: transferBy }), // Add offeringId to the filters if it is provided
      };

      // if (transferBy) {
      //   if (transferBy === 'ta') {
      //     filters.TransferBy = true; // If transferBy is 'ta', filter for isForceTransfer === true
      //   } else if (transferBy === 'issuer') {
      //     filters.TransferBy = false; // If transferBy is 'issuer', filter for isForceTransfer === false
      //   }
      // }

      /**
       * Parse the sort criteria from the query parameters.
       */
      const sortCriteria = JSON.parse(sort as string);
      const projection = [
        'isForceTransfer',
        'TransferBy',
        'status',
        'newRegisteredEmailId',
        'updatedAt',
        'newRegisteredName',
        'newWalletAddress',
        'offeringId',
        'registeredEmailId',
        'registeredName',
        'remark',
        'securityName',
        'createdAt',
        'tokenQuantity',
        'userId',
        'txHash',
        'walletAddress',
      ];

      const userDetails = await TransferRequestService.getAllTransferRequests(filters, projection, {
        page: Number(page),
        limit: Number(limit),
        sort: sortCriteria,
        search,
      });

      return ResponseHandler.success(res, {
        status: 200,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
        data: userDetails.data,
      });
    } catch (error: any) {
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Retrieves a list of transfer requests in CSV format.
   *
   * @param {Request} req - Express request object
   * @param {Response} res - Express response object
   * @returns {Promise<void>}
   *
   * The query parameters can be used to filter the list of transfer requests.
   * The parameters are:
   *
   * - page: The page number to fetch.
   * - limit: The number of documents to fetch per page.
   * - sort: The field to sort the list by.
   * - search: The search query to filter the list.
   * - offeringId: The id of the offering.
   * - isActive: A boolean indicating whether the user is active.
   * - isDeleted: A boolean indicating whether the user is deleted.
   * - kycStatus: The status of the user's KYC.
   * - userType: The type of user.
   * - countryCode: The country code of the user.
   * - isKyc: A boolean indicating whether the user has completed KYC.
   * - status: The status of the transfer request.
   */
  static getAllTransferRequestsCsv = async (req: Request, res: Response) => {
    try {
      const { page = 1, limit = 10, sort = JSON.stringify({ createdAt: -1 }), search = '', offeringId, startDate = '', endDate = '' } = req.query;

      const filters: any = {
        ...(startDate && endDate
          ? {
              createdAt: {
                $gte: new Date(startDate as string),
                $lte: new Date(endDate as string),
              },
            }
          : startDate
            ? { createdAt: { $gte: new Date(startDate as string) } }
            : endDate
              ? { createdAt: { $lte: new Date(endDate as string) } }
              : {}),
        ...(offeringId && { offeringId }), // Add offeringId to the filters if it is provided
      };

      const sortCriteria = JSON.parse(sort as string);
      const projection = [
        'isForceTransfer',
        'status',
        'newRegisteredEmailId',
        'updatedAt',
        'newRegisteredName',
        'newWalletAddress',
        'offeringId',
        'registeredEmailId',
        'registeredName',
        'remark',
        'securityName',
        'createdAt',
        'tokenQuantity',
        'userId',
        'walletAddress',
      ];

      const userDetails = await TransferRequestService.getAllTransferRequests(filters, projection, {
        page: Number(page),
        limit: Number(limit),
        sort: sortCriteria,
        search,
      });

      return ResponseHandler.success(res, {
        status: 200,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
        data: userDetails.data,
      });
    } catch (error: any) {
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  static saveForcedRequest = async (req: Request, res: Response) => {
    try {
      const { userId } = req.userInfo;

      const { toUserId, fromUserId, offeringId, remark, tokenQuantity } = req.body;
      const user1 = await userSchema.findOne({ _id: new mongoose.Types.ObjectId(fromUserId) }).select('name wallets.address email');
      const user2 = await userSchema.findOne({ _id: new mongoose.Types.ObjectId(toUserId) }).select('name wallets.address email');
      const offering = await offeringSchema.findOne({ _id: new mongoose.Types.ObjectId(offeringId) }).select('projectDetails.offeringName');
      const updateData = {
        registeredName: user1.name,
        TranferBy: true,
        taId: userId,
        newRegisteredName: user2.name,
        walletAddress: user1.wallets[0].address,
        newWalletAddress: user2.wallets[0].address,
        offeringId,
        tokenQuantity,
        remark,
        securityName: offering.projectDetails.offeringName,
        registeredEmailId: user1.email,
        newRegisteredEmailId: user2.email,
        isForceTransfer: true,
        userId: userId,
      };
      const updateOfferingResp = await TransferRequest.create(updateData);
      const _id = updateOfferingResp?._id;

      const finalData = JSON.parse(JSON.stringify(updateData));
      await kafkaService.sendMessageToUser({
        value: {
          ...finalData,
          type: queueMessageTypeEnum.SAVETRANSFER,
          _id: _id,
        },
      });

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_UPDATED,
        data: { _id },
      });
    } catch (error: any) {
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };
  static rejectForceTransfer = async (req: Request, res: Response) => {
    try {
      const { transferId, reason } = req.body;
      const searchQuery = { _id: transferId };

      // Fetch the TransferRequest details
      const transferRequest: any = await TransferRequest.findOne(searchQuery);
      if (transferRequest.error) {
        throw new CustomError(transferRequest.message, transferRequest.status);
      }

      // Handle offering status validation
      const { status } = transferRequest;
      if (status === transferStatusEnum.REJECTED) {
        throw new CustomError(RES_MSG.ERROR_MSG.ALREADY_REJECTED, RESPONSES.BAD_REQUEST);
      }
      if (status === transferStatusEnum.APPROVED) {
        throw new CustomError(RES_MSG.ERROR_MSG.ALREADY_APPROVED, RESPONSES.BAD_REQUEST);
      }

      const updateData = {
        status: transferStatusEnum.REJECTED,
        reason: reason,
      };

      // gRPC Payload
      const payload = {
        _id: transferId,
        status: transferStatusEnum.REJECTED,
        reason: reason,
      };

      // gRPC Request
      userClient.client.rejectForceTransfer(payload, async (error: any, response: any) => {
        if (error) {
          logger.error('gRPC Error:', error);
          return ResponseHandler.error(res, {
            status: RESPONSES.INTERNAL_SERVER_ERROR,
            message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
            error: true,
          });
        }

        if (response?.error) {
          return ResponseHandler.error(res, {
            message: response?.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
            status: RESPONSES.BAD_REQUEST,
            error: true,
          });
        }

        // Update transfer request in DB
        await TransferRequest.findOneAndUpdate(
          { _id: transferId }, // Finding the document by its _id
          updateData, // The update data, which includes the status
          { new: true }, // Optionally, return the updated document instead of the original
        );

        // Success Response
        return ResponseHandler.success(res, {
          status: RESPONSES.SUCCESS,
          message: RES_MSG.SUCCESS_MSG.OFFERING_REJECT || RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
          error: false,
          data: null,
        });
      });
    } catch (error) {
      logger.error('Error in rejectForceTransfer:', error.message);
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };
}
