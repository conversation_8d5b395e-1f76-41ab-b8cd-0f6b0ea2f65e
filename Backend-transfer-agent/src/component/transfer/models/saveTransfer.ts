import mongoose, { Schema, Document, Types } from 'mongoose';
export interface ISaveTransferRequest extends Document {
  offeringId: Types.ObjectId;
  fromUserId: Types.ObjectId;
  toUserId: Types.ObjectId;
  tokenQuantity: string;
  uniqueId: string;
  remark: string;
}

export interface ISaveUpdateTransferRequest extends Document {
  offeringId: Types.ObjectId;
  fromUserId: Types.ObjectId;
  toUserId: Types.ObjectId;
  tokenQuantity: string;
  uniqueId: string;
  remark: string;
}

const SaveTransferRequestSchema: Schema = new Schema(
  {
    fromUserId: { type: Schema.Types.ObjectId, ref: 'users', required: false },
    toUserId: { type: Schema.Types.ObjectId, ref: 'users', required: false },
    offeringId: { type: Schema.Types.ObjectId, ref: 'offerings', required: false },

    registeredName: {
      type: String,
      required: false,
    },
    uniqueId: {
      type: String,
      unique: true,
    },
    remark: {
      type: String,
      required: false,
    },

    tokenQuantity: {
      type: Number,
      required: false,
      min: [0, 'Token quantity must be a positive number'],
    },
  },
  {
    timestamps: true,
  },
);

const SaveTransferRequest = mongoose.model<ISaveTransferRequest>('SaveTransferRequest', SaveTransferRequestSchema);

export default SaveTransferRequest;
