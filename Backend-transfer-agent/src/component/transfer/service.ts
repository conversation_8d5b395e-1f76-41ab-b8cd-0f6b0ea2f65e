import TransferRequest from './models/transfer';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import mongoose, { FilterQuery } from 'mongoose';

import { IPagination } from '../../utils/common.interface';
import { ITransferRequestService } from './interface';
import logger from '../../helpers/logger.helper';
import CommonHelper from '../../helpers/common.helper';

class TransferRequestService implements ITransferRequestService {
  getAllTransferRequests = async (filters: any, projection: any[], pagination: IPagination) => {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 }, search: escapedSearch } = pagination;
      const search = escapedSearch ? CommonHelper.escapeRegex(escapedSearch) : null;

      const skip = (page - 1) * limit;
      // Construct query filter
      const query: FilterQuery<any> = { email: { $ne: process.env.EMAIL } };
      if (filters?.status) query.status = filters.status;
      if (filters?.createdAt) query.createdAt = filters.createdAt;
      if (filters?.offeringId && mongoose.isValidObjectId(filters.offeringId)) {
        query.offeringId = new mongoose.Types.ObjectId(filters.offeringId);
      }
      if (filters?.transferBy === 'ta') {
        query.TranferBy = true;
      }
      if (filters?.transferBy === 'issuer') {
        query.TranferBy = false;
      }
      if (search) {
        query.$or = [{ newRegisteredEmailId: { $regex: search, $options: 'i' } }, { registeredEmailId: { $regex: search, $options: 'i' } }];
      }
      // Define aggregation pipeline
      const pipeline: any[] = [
        { $match: query },
        {
          $lookup: {
            from: 'offerings',
            localField: 'offeringId',
            foreignField: '_id',
            as: 'offeringDetails',
            pipeline: [
              {
                $lookup: {
                  from: 'users',
                  localField: 'userId',
                  foreignField: '_id',
                  as: 'userDetails',
                },
              },
            ],
          },
        },
        { $unwind: { path: '$offeringDetails', preserveNullAndEmptyArrays: true } },
        // ...(search
        //   ? [
        //       {
        //         $match: {
        //           'offeringDetails.projectDetails.tokenTicker': { $regex: search, $options: 'i' },
        //         },
        //       },
        //     ]
        //   : []),
        {
          $set: {
            tokenAddress: '$offeringDetails.tokenAddress',
            offeringName: '$offeringDetails.projectDetails.offeringName',
            tokenTicker: '$offeringDetails.projectDetails.tokenTicker',
            tokenDecimals: '$offeringDetails.projectDetails.tokenDecimals',
            icon: '$offeringDetails.overview.icon',
            cover: '$offeringDetails.overview.cover',
            logo: '$offeringDetails.overview.logo',
            userType: {
              $ifNull: [{ $arrayElemAt: ['$offeringDetails.userDetails.userType', 0] }, 'No user type'],
            },
          },
        },
        { $sort: sort },
        {
          $facet: {
            data: [{ $skip: skip }, { $limit: limit }],
            totalCount: [{ $count: 'count' }],
          },
        },
      ];
      // Fetch results
      const results = await TransferRequest.aggregate(pipeline);
      const users = results[0]?.data || [];
      const totalCount = results[0]?.totalCount[0]?.count || 0;

      return {
        data: {
          user: users,
          currentPage: page,
          totalPages: Math.ceil(totalCount / limit),
          totalCount,
          nextPage: page * limit < totalCount ? page + 1 : null,
          previousPage: page > 1 ? page - 1 : null,
        },
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      logger.error('Error fetching transfer requests:', error);
      throw new Error('Failed to fetch transfer requests');
    }
  };
}

export default new TransferRequestService();
