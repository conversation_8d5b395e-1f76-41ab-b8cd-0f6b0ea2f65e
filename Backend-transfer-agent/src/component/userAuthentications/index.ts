/* eslint-disable @typescript-eslint/no-unused-vars */
import { Request, Response } from 'express';
import { IssuerStatus, PromiseResolve, UserType, offeringStatusEnum } from '../../utils/common.interface';

import UserService from './service';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { IUserModel, userSchema } from './models/user.model';
import CustomError from '../../helpers/customError.helper';
import RedisHelper from '../../helpers/redis.helper';
import { ResponseHandler } from '../../helpers/response.helper';
import CommonHelper from '../../helpers/common.helper';
import * as geoip from 'geoip-lite';
import { authenticator } from 'otplib';
import { toDataURL } from 'qrcode';
import CONFIG from '../../config/env';
import logger from '../../helpers/logger.helper';
import { otpType } from '../../utils/common.interface';
import userClient from '../../_grpc/clients/user.client';
import mongoose, { Types } from 'mongoose';
import emailHelper from '../../helpers/email.helper';
import { whitelistSchema } from './models/whitelist.model';
import { offeringSchema } from './models/offerings.model';
import { OrderSchema } from './models/order.model';
const kycCount = process.env.KYCCOUNT;

/**
 * @exports
 * @param {IUserModel} req
 * @param {IUserModel} res
 * @returns {Promise<PromiseResolve>}
 */
export async function loginController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    let { email } = req.body;
    const { password } = req.body;
    email = email.toLowerCase();
    const userDetails: PromiseResolve = await UserService.fetchTaDetails({ email }, [], ['createdAt', 'updatedAt']);
    const name = userDetails?.data?.name;
    const walletAddress = userDetails?.data?.walletAddress;
    const user: IUserModel = userDetails?.data;

    if (userDetails?.error || !user?.password) {
      throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.BAD_REQUEST);
    }
    const verifyPassResp: PromiseResolve = await CommonHelper.verifyPassword(password, user.password);

    if (verifyPassResp.error) {
      throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, verifyPassResp.status);
    }
    if (!user.isActive) {
      throw new CustomError(RES_MSG.COMMON.ADMIN_BLOCK_USER, RESPONSES.FORBIDDEN);
    } else if (user.isOtpActive) {
      const otpResponse = await CommonHelper.sendOTP(user?._id, otpType?.LOGIN, user?.email, user?.name);
      if (otpResponse.error) throw new CustomError(otpResponse.message, otpResponse.status);

      return ResponseHandler.success(res, {
        status: userDetails.status || RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.OTP_SENT_SUCCESS,
        data: {
          isOtpActive: user.isOtpActive,
          walletAddress,
        },
      });
    } else if (user.is2FAActive) {
      //token for twofa verification
      const tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email }, false, 'twoFA', CONFIG.JWT_AUTH.JWT_2FA_EXPIRE);
      if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);
      return ResponseHandler.success(res, {
        status: userDetails.status || RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.TWO_FA.PENDING,
        data: {
          is2FAActive: user.is2FAActive,
          walletAddress,

          ...tokenResp.data,
        },
      });
    }
    const tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email, userType: user.userType }, true, 'accessToken', CONFIG.JWT_AUTH.JWT_2FA_EXPIRE);
    if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);

    const emailDetail = {
      name: name,
    };

    emailHelper.sendEmailTemplate(email, 'login', emailDetail);

    return ResponseHandler.success(res, {
      status: userDetails.status || RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.USER.LOGIN_SUCCESS,
      data: {
        ...tokenResp.data,
        walletAddress: walletAddress,
        //  isOtpActive: user.isOtpActive,
        is2FAActive: user.is2FAActive,
      },
    });
  } catch (error: any) {
    logger.error(error, 'loginController Error');

    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

export async function cratePassword(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { email, password } = req.body;
    const userDetails: PromiseResolve = await UserService.fetchTaDetails({ email }, [], ['createdAt', 'updatedAt']);
    const user: IUserModel = userDetails?.data;

    if (userDetails?.error) {
      throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.BAD_REQUEST);
    }

    const response = await UserService.updateUserDetails({ password }, { email });

    return ResponseHandler.success(res, {
      status: response.status || RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.USER.PASSWORD_CREATED,
      data: '',
    });
  } catch (error: any) {
    logger.error(error, 'createpassword Error');

    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {IUserModel} req
 * @returns {Promise<PromiseResolve>}
 */
export async function verifyController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { email, otp, type } = req.body;

    const userDetails: any = await UserService.fetchTaDetails({
      email,
      isActive: true,
    });

    if (userDetails.error) {
      throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
    }

    const user = userDetails.data;
    const storedOTP = await CommonHelper.getOTP(user._id, type);

    if (storedOTP.error) {
      throw new CustomError(storedOTP.message || RES_MSG.ERROR_MSG.INVALID_OTP, storedOTP.status || RESPONSES.BAD_REQUEST);
    } else if (storedOTP.data.otp === null || storedOTP.data.otp === '') {
      throw new CustomError(RES_MSG.ERROR_MSG.EXPIRE_OTP, RESPONSES.BAD_REQUEST);
    } else if (Number(storedOTP.data.otp) !== Number(otp)) {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const userLockResp: PromiseResolve = await CommonHelper.userLock(email);
      // if (userLockResp.error) throw new CustomError(userLockResp.message, userLockResp.status);
      // const lockedTimes = await CommonHelper.convertSecondsToHMS(LOGIN_BLOCK_TIME);
      // const errorMessage = `${RES_MSG.ERROR_MSG.INVALID_OTP} You have ${LOGIN_MAX_ATTEMPT - (1 + +userLockResp.data.totalAttemptsCounts)} more attempts before your account is locked for ${userLockResp.data.lockedTimes ? userLockResp.data.lockedTimes : lockedTimes}.`;
      throw new CustomError(RES_MSG.ERROR_MSG.INVALID_OTP, RESPONSES.BAD_REQUEST);
    }

    let tokenResp;
    switch (type) {
      // Verify during login
      case otpType.LOGIN:
        if (user.is2FAActive) {
          tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email }, false, 'twoFA', CONFIG.JWT_AUTH.JWT_2FA_EXPIRE);
          if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);
          await RedisHelper.deleteKey(storedOTP.data.key);

          return ResponseHandler.success(res, {
            status: userDetails.status || RESPONSES.SUCCESS,
            error: false,
            message: RES_MSG.TWO_FA.PENDING,
            data: {
              is2FAActive: user.is2FAActive,
              ...tokenResp.data,
            },
          });
        } else {
          const ip = req.headers['x-forwarded-for'] || req.ip;
          const geo = geoip.lookup(ip.toString());
          const time = new Date().toLocaleString();

          tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email, userType: user.userType }, true);
          if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);

          await RedisHelper.deleteKey(storedOTP.data.key);

          const emailDetail = { name: userDetails.name, ip, geo, time };
          emailHelper.sendEmailTemplate(email, 'login', emailDetail);
          return ResponseHandler.success(res, {
            status: userDetails.status || RESPONSES.SUCCESS,
            error: false,
            message: RES_MSG.USER.LOGIN_SUCCESS,
            data: {
              ...tokenResp.data,
              is2FAActive: user.is2FAActive,
            },
          });
        }

      case otpType.FORGOT:
        tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email }, false, 'forgetToken', CONFIG.JWT_AUTH.FORGOT_EXPIRE_TIME);
        if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);

        return ResponseHandler.success(res, {
          status: userDetails.status || RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.USER.OTP_SUCCESS,
          data: {
            userType: UserType.Admin,
            //  isOtpActive: user.isOtpActive,
            // walletAddress: walletAddress,
            accessToken: tokenResp.data.accessToken,
          },
        });

      default:
        throw new CustomError(RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, RESPONSES.INTERNAL_SERVER_ERROR);
    }
  } catch (error) {
    logger.error(error, 'verifyController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @exports
 * @param {IUserModel} req
 * @returns {Promise<PromiseResolve>}
 */
export async function reSendOtpController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { email, type } = req.body;
    const userLockResp: PromiseResolve = await CommonHelper.userLock(email);
    if (userLockResp.error) throw new CustomError(userLockResp.message, userLockResp.status);

    const userDetails: PromiseResolve = await UserService.fetchTaDetails({
      email,
    });
    if (type == 'forgot') {
      if (userDetails.error)
        return ResponseHandler.success(res, {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.ERROR_MSG.FORGOT_CRED,
        });
    }

    if (userDetails.error) throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);

    const otpResponse = await CommonHelper.sendOTP(userDetails.data._id, type, userDetails.data.email, userDetails.data.name);
    if (otpResponse.error) throw new CustomError(otpResponse.message, otpResponse.status);
    return ResponseHandler.success(res, {
      status: userDetails.status || RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.ERROR_MSG.FORGOT_CRED,
      data: null,
    });
  } catch (error) {
    logger.error(error, 'loginController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise < PromiseResolve >}
 */
export async function getUsersListController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const {
      page = 1,
      limit = 10,
      sort = JSON.stringify({ createdAt: -1 }),
      search = '',

      isActive,
      isDeleted,
      kycStatus = '',
      userType = '',
      countryCode = '',
      isKyc,
    } = req.query;

    const filters = {
      ...(isActive !== undefined && { isActive: isActive === 'true' }),
      ...(isDeleted !== undefined && { isDeleted: isDeleted === 'true' }),
      ...(kycStatus !== '' && kycStatus && { kycStatus }), // Only include kycStatus if it's not an empty string
      ...(userType && { userType }),
      ...(countryCode && { countryCode }),
      ...(isKyc !== undefined && { isKyc: isKyc === 'true' }),
    };

    const sortCriteria = JSON.parse(sort as string);
    const projection = ['name', 'email', 'userImage', 'kycStatus', 'mobile', 'isKyc', 'mainInformation.nationality', 'institutions.companyInformation', 'isActive', 'userType', 'countryCode', 'createdAt', 'sumSubKycStatus', 'levelName'];

    const userDetails = await UserService.fetchUserList(filters, projection, {
      page: Number(page),
      limit: Number(limit),
      sort: sortCriteria,
      search,
    });

    return ResponseHandler.success(res, {
      status: 200,
      error: false,
      message: RES_MSG.USER.USERS_FETCH,
      data: userDetails.data,
    });
  } catch (error) {
    logger.error(error, 'getUserslist Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function getUsersListControllerCsv(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { sort = JSON.stringify({ createdAt: -1 }), startDate = '', endDate = '', isKyc = '' } = req.query;

    // Initialize filters object
    const filters: any = {
      ...(startDate && endDate
        ? {
            createdAt: {
              $gte: new Date(startDate as string),
              $lte: new Date(endDate as string),
            },
          }
        : startDate
        ? { createdAt: { $gte: new Date(startDate as string) } }
        : endDate
        ? { createdAt: { $lte: new Date(endDate as string) } }
        : {}),
    };

    // Parse sort criteria
    // let sortCriteria = JSON.parse(sort as string);

    // Define the projection fields
    const projection = [
      'name',
      'email',
      'userImage',
      'kycStatus',
      'mobile',
      'isKyc',
      'mainInformation.nationality',
      'institutions.companyInformation',
      'isActive',
      'userType',
      'countryCode',
      'sumSubKycStatus',
      'levelName',
      'createdAt',

      'startDate',
      'endDate',
    ];

    // Fetch user list with the applied filters and sorting
    const userDetails = await UserService.fetchUserListcsv(filters, projection);

    return ResponseHandler.success(res, {
      status: 200,
      error: false,
      message: RES_MSG.USER.USERS_FETCH,
      data: userDetails.data,
    });
  } catch (error) {
    logger.error(error, 'getUserslist Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function getIssuerListControllerCsv(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { sort = JSON.stringify({ createdAt: -1 }), startDate = '', endDate = '', issuerStatus = '', isKyc = '', userType = '' } = req.query;

    // Initialize filters object
    const filters: any = {
      isKyc: true,
      ...(userType && { userType }),
      ...(issuerStatus
        ? { IssuerStatus }
        : {
            issuerStatus: { $ne: IssuerStatus.NOT_APPLIED },
          }),
      ...(startDate && endDate
        ? {
            createdAt: {
              $gte: new Date(startDate as string),
              $lte: new Date(endDate as string),
            },
          }
        : startDate
        ? { createdAt: { $gte: new Date(startDate as string) } }
        : endDate
        ? { createdAt: { $lte: new Date(endDate as string) } }
        : {}),
    };

    // Parse sort criteria
    // let sortCriteria = JSON.parse(sort as string);

    // Define the projection fields
    const projection = [
      'name',
      'email',
      'userImage',
      'kycStatus',
      'mobile',
      'isKyc',
      'mainInformation.nationality',
      'institutions.companyInformation',
      'isActive',
      'userType',
      'countryCode',
      'sumSubKycStatus',
      'levelName',
      'createdAt',
      'issuerStatus',
      'startDate',
      'endDate',
    ];

    // Fetch user list with the applied filters and sorting
    const userDetails = await UserService.fetchUserListcsv(filters, projection);

    return ResponseHandler.success(res, {
      status: 200,
      error: false,
      message: RES_MSG.USER.USERS_FETCH,
      data: userDetails.data,
    });
  } catch (error) {
    logger.error(error, 'getUserslist Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function getManageIssuerListCsv(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { page = 1, limit = 10, sort, search = '', issuerStatus = '', startDate, endDate } = req.query;

    const filters: any = {
      isKyc: true,
      isIssuer: true,
      ...(issuerStatus && { issuerStatus }),
      ...(startDate && endDate
        ? {
            createdAt: {
              $gte: new Date(startDate as string),
              $lte: new Date(endDate as string),
            },
          }
        : startDate
        ? { createdAt: { $gte: new Date(startDate as string) } }
        : endDate
        ? { createdAt: { $lte: new Date(endDate as string) } }
        : {}),
    };

    const projection = ['name', 'email', '_id', 'wallets', 'userImage', 'offeringStatusCounts', 'createdAt', 'isActive', 'isKyc', 'isIssuer'];

    const userDetails = await UserService.fetchUserListWithOfferingscsv(filters, projection, {
      page: Number(page),
      limit: Number(limit),
      ...(search && { search: search }),
      ...(sort && { sort: JSON.parse(sort as string) }),
    });
    return ResponseHandler.success(res, {
      status: userDetails.status,
      error: false,
      message: RES_MSG?.SUCCESS_MSG.ISSUER_LIST,
      data: userDetails.data,
    });
  } catch (error: any) {
    logger.error(error, 'getIssuerOfferingController');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function singleOfferingReportController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const userId = new Types.ObjectId(req.userInfo.userId);
    const { offeringId, page = 1, limit = 10, sort = JSON.stringify({ createdAt: -1, userId: 1 }), search = '' }: any = req.query;
    const sortCriteria = JSON.parse(sort as string);
    // const offeringId = new Types.ObjectId(req.query.offeringId);
    const result: PromiseResolve = await UserService.singleOfferingReport(
      { offeringId },
      {
        page: Number(page),
        limit: Number(limit),
        sort: sortCriteria,
        search,
      },
    );
    if (result.error) {
      throw new CustomError(result.message, result.status);
    }
    return ResponseHandler.success(res, {
      status: result.status || RESPONSES.SUCCESS,
      error: false,
      message: result.message || RES_MSG.USER.USERS_FETCH,
      data: result.data || [],
    });
  } catch (error) {
    logger.error(error, 'singleOfferingReportController Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}
export async function topInvestors(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const userId = new Types.ObjectId(req.userInfo.userId);
    const { offeringId, page = 1, limit = 10, sort = JSON.stringify({ createdAt: -1 }), search = '' }: any = req.query;

    // Check if the user has permission for this offering
    const offering = await offeringSchema.findOne({
      _id: new Types.ObjectId(offeringId),
      'projectDetails.taId': userId,
    });

    if (!offering) {
      return ResponseHandler.error(res, {
        message: 'User ID does not match the TA ID in the offering.',
        status: RESPONSES.NOTFOUND,
        error: true,
      });
    }
    const pipeline: any[] = [
      {
        $match: {
          status: 'MINTED',
          offeringId: new Types.ObjectId(offeringId),
        },
      },
      {
        $addFields: {
          amount: { $toDouble: '$amount' }, // Convert amount from string to integer
        },
      },
      {
        $group: {
          _id: '$userId',
          maxAmount: { $max: '$amount' }, // Fixed missing closing quote for $max
          totalAmount: { $sum: '$amount' },
        },
      },
      { $sort: { maxAmount: -1 } },
      { $limit: 5 },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id', // Fixed the incorrect field name here ('_MMmm' to '_id')
          as: 'userData',
          pipeline: [
            {
              $project: {
                _id: 0,
                name: 1,
                email: 1,
                userImage: 1,
              },
            },
          ],
        },
      },
      { $unwind: '$userData' },
      {
        $group: {
          _id: null,
          topInvestors: {
            $push: {
              userId: '$_id',
              name: '$userData.name',
              email: '$userData.email',
              userImage: '$userData.userImage',
              maxAmount: '$maxAmount',
              totalAmount: '$totalAmount',
            },
          },
          totalInvested: { $sum: '$totalAmount' },
        },
      },
      {
        $addFields: {
          totalInvested: { $ifNull: ['$totalInvested', 1] }, // Ensure it's a number
          topInvestors: {
            $map: {
              input: '$topInvestors',
              as: 'investor',
              in: {
                $mergeObjects: [
                  '$$investor',
                  {
                    totalInvested: '$totalInvested',
                    percentageShare: {
                      $multiply: [
                        { $divide: ['$$investor.totalAmount', { $max: ['$totalInvested', 1] }] }, // Avoid division by 0
                        100,
                      ],
                    },
                  },
                ],
              },
            },
          },
        },
      },
      { $project: { topInvestors: 1 } },
    ];

    const result = await OrderSchema.aggregate(pipeline);
    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.USER.USERS_FETCH,
      data: { topInvestors: result[0]?.topInvestors || [] },
    });
  } catch (error: any) {
    logger.error(error, 'topInvestors Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function offeringReportController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const userId = new Types.ObjectId(req.userInfo.userId);
    const { offeringId, period } = req.query;
    const result: PromiseResolve = await UserService.offeringReport({ userId, offeringId, period });
    if (result.error) {
      throw new CustomError(result.message, result.status);
    }
    return ResponseHandler.success(res, {
      status: result.status || RESPONSES.SUCCESS,
      error: false,
      message: result.message || RES_MSG.USER.USERS_FETCH,
      data: result.data || [],
    });
  } catch (error) {
    logger.error(error, 'offeringReportController Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function topHolders(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const userId = new Types.ObjectId(req.userInfo.userId);
    const { offeringId, page = 1, limit = 10, sort = JSON.stringify({ createdAt: -1 }), search = '' }: any = req.query;
    const sortCriteria = JSON.parse(sort as string);
    // const offeringId = new Types.ObjectId(req.query.offeringId);
    const result: PromiseResolve = await UserService.topHolders(
      { offeringId },
      {
        page: Number(page),
        limit: Number(limit),
        sort: sortCriteria,
        search,
      },
    );
    if (result.error) {
      throw new CustomError(result.message, result.status);
    }
    return ResponseHandler.success(res, {
      status: result.status || RESPONSES.SUCCESS,
      error: false,
      message: result.message || RES_MSG.USER.USERS_FETCH,
      data: result.data || [],
    });
  } catch (error) {
    logger.error(error, 'singleOfferingReportController Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function transcationReportController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const userId = new Types.ObjectId(req.userInfo.userId);
    const { offeringId, page = 1, limit = 10, sort = JSON.stringify({ createdAt: -1 }), search = '' }: any = req.query;
    const sortCriteria = JSON.parse(sort as string);
    // const offeringId = new Types.ObjectId(req.query.offeringId);
    const result: PromiseResolve = await UserService.transcationReport(
      { offeringId },
      {
        page: Number(page),
        limit: Number(limit),
        sort: sortCriteria,
        search,
      },
    );
    if (result.error) {
      throw new CustomError(result.message, result.status);
    }
    return ResponseHandler.success(res, {
      status: result.status || RESPONSES.SUCCESS,
      error: false,
      message: result.message || RES_MSG.USER.USERS_FETCH,
      data: result.data || [],
    });
  } catch (error) {
    logger.error(error, 'singleOfferingReportController Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function userByOfferingId(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const result: any = await OrderSchema.aggregate([
      {
        $match: {
          offeringId: Types.ObjectId.createFromHexString(req.params.id),
          status: 'MINTED',
        },
      },
      {
        $lookup: {
          from: 'users', // Name of the users collection
          localField: 'userId', // Field in the OrderSchema to match
          foreignField: '_id', // Field in the users collection to match
          as: 'userData', // Name of the output array
        },
      },
      {
        $unwind: {
          path: '$userData', // Unwind the userData array
          preserveNullAndEmptyArrays: true, // Include orders without matching user
        },
      },
      {
        $project: {
          _id: 0, // Exclude the _id field from the result
          email: '$userData.email', // Include the email from userData
          userId: '$userData._id', // Include the user ID from userData
          name: '$userData.name', // Include the user ID from userData
          walletAddress: {
            address: { $arrayElemAt: ['$userData.wallets.address', 0] }, // First wallet address
            type: { $arrayElemAt: ['$userData.wallets.type', 0] }, // Wallet type
            isVerify: { $arrayElemAt: ['$userData.wallets.isVerify', 0] }, // Wallet verification status
          },
        },
      },
      {
        $group: {
          _id: '$email', // Group by email
          email: { $first: '$email' }, // Keep the first email
          userId: { $first: '$userId' }, // Keep the first userId
          name: { $first: '$name' }, // Keep the first name
          walletAddress: { $first: '$walletAddress' }, // Keep the first walletAddress
        },
      },
    ]);

    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.USER.USERS_FETCH,
      data: result, // Return the formatted result
    });
  } catch (error) {
    logger.error(error, 'offeringDetails Controller Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function offeringDetails(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const isBondingDeploy = req.query?.isBonding;
    const userId = new Types.ObjectId(req.userInfo.userId);

    const queryFilter: any = {
      'projectDetails.taId': userId, // Filter offerings where projectDetails.taId matches userId
      status: offeringStatusEnum.APPROVED,
      isNft: false,
    };

    if (isBondingDeploy !== undefined) {
      queryFilter.isBondingDeploy = isBondingDeploy;
    }

    const result: any = await offeringSchema
      .find(queryFilter, {
        _id: 1, // Select the _id field
        // 'projectDetails.offeringName': 1, // Select the offeringName field inside projectDetails
        'overview.icon': 1,
        'overview.cover': 1,
        // 'projectDetails.tokenTicker': 1,
        // 'projectDetails.taId': 1,
        projectDetails: 1,
        tokenAddress: 1,
        fundAddress: 1,
        erc20Address: 1,
        createdAt: 1,
      })
      .sort({ createdAt: -1 });
    if (!result || result.length === 0) {
      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: 'No offerings found', // Custom message when no results are found
        data: [],
      });
    }
    // Map the result to flatten the structure, moving offeringName to the root
    const formattedResult = result.map((item: any) => ({
      _id: item._id,
      offeringName: item.projectDetails ? item.projectDetails.offeringName : null, // Flatten offeringName to the root level
      icon: item.overview ? item.overview.icon : null,
      cover: item.overview ? item.overview.cover : null,
      tokenTicker: item.projectDetails ? item.projectDetails.tokenTicker : null,
      tokenAddress: item.tokenAddress,
      fundAddress: item.fundAddress,
      erc20Address: item.erc20Address,
      authorizedCountries: item.projectDetails.authorizedCountries,
      countries: item.projectDetails.authorizedCountries?.length,
    }));

    // Check if there is an error in the result
    if (!formattedResult || formattedResult.length === 0) {
      throw new CustomError('No offerings found', RESPONSES.BAD_REQUEST);
    }

    // Return the successful response with the formatted data
    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.USER.USERS_FETCH,
      data: formattedResult, // Return the formatted result
    });
  } catch (error) {
    logger.error(error, 'offeringDetails Controller Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}
/**
 * Handles the GET whitelist
 * @param req Express Request object
 * @param res Express Response object
 * @returns PromiseResolve - A promise containing the response data
 *
 * Fetches a list of whitelist records for a given offering.
 * The list is filtered based on the provided query parameters.
 * The query parameters include:
 * - page: The page number to fetch.
 * - limit: The number of documents to fetch per page.
 * - sort: The field to sort the list by.
 * - search: The search term to filter the list by.
 * - status: The status of the whitelist record.
 * - taStatus: The status of the TA.
 * - userId: The id of the user. (This is passed from the middleware)
 */
export async function whitelistDetails(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const userId = new Types.ObjectId(req.userInfo.userId);
    const { offeringId, page = 1, limit = 10, status = '', taStatus = '', sort = JSON.stringify({ createdAt: -1 }), search = '' }: any = req.query;
    const sortCriteria = JSON.parse(sort as string);
    // const offeringId = new Types.ObjectId(req.query.offeringId);
    const result: PromiseResolve = await UserService.singleOfferingWhitelistReport(
      { offeringId, status, taStatus, userId },

      {
        page: Number(page),
        limit: Number(limit),
        sort: sortCriteria,
        search,
      },
    );
    if (result.error) {
      throw new CustomError(result.message, result.status);
    }
    return ResponseHandler.success(res, {
      status: result.status || RESPONSES.SUCCESS,
      error: false,
      message: result.message || RES_MSG.USER.USERS_FETCH,
      data: result.data || [],
    });
  } catch (error) {
    logger.error(error, 'singleOfferingReportController Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}
/**
 * Handles the GET /user/whitelistDetailsCsv endpoint.
 * This function fetches and returns whitelist details in CSV format for a given offering.
 * @param req Express Request object
 * @param res Express Response object
 * @returns PromiseResolve - A promise containing the response data
 */
export async function whitelistDetailsCsv(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const userId = new Types.ObjectId(req.userInfo.userId);
    const { offeringId, page = 1, limit = 10, status = '', taStatus = '', startDate = '', endDate = '', sort = JSON.stringify({ createdAt: -1 }), search = '' }: any = req.query;

    // Initialize filters object
    const sortCriteria = JSON.parse(sort as string);
    // const offeringId = new Types.ObjectId(req.query.offeringId);
    const result: PromiseResolve = await UserService.singleOfferingWhitelistReportCsv(
      { offeringId, status, taStatus, startDate, endDate },

      {
        page: Number(page),
        limit: Number(limit),
        sort: sortCriteria,
        search,
      },
    );
    if (result.error) {
      throw new CustomError(result.message, result.status);
    }
    return ResponseHandler.success(res, {
      status: result.status || RESPONSES.SUCCESS,
      error: false,
      message: result.message || RES_MSG.USER.USERS_FETCH,
      data: result.data || [],
    });
  } catch (error) {
    logger.error(error, 'singleOfferingReportController Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

export async function user(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { sort = JSON.stringify({ createdAt: -1 }), startDate = '', endDate = '', issuerStatus = '', isKyc = '', userType = '' } = req.query;
    // Fetch the count of users where userType is 'investor'
    const investorCount = await userSchema.countDocuments({
      userType: { $ne: 'admin' },
    });
    const pendingInvestorCount = await userSchema.countDocuments({
      userType: { $ne: 'admin' },
      kycStatus: 'PENDING',
    });
    const filters: any = {
      isKyc: true,
      ...(userType && { userType }),
      ...(issuerStatus
        ? { IssuerStatus }
        : {
            issuerStatus: { $ne: IssuerStatus.NOT_APPLIED },
          }),
    };
    const issuerCount = await userSchema.countDocuments(filters);
    const pendingIssuerCount = await userSchema.countDocuments({
      isKyc: true,
      issuerStatus: 'PENDING',
    });

    return ResponseHandler.success(res, {
      status: 200,
      error: false,
      message: RES_MSG?.SUCCESS_MSG.INVESTOR_COUNT_SUCCESS,
      data: {
        investorCount,
        issuerCount,
        pendingIssuerCount,
        pendingInvestorCount,
      }, // Return the count of investors
    });
  } catch (error: any) {
    logger.error(error, 'user API Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise < PromiseResolve >}
 *
 * Fetches a list of all issuers and their details.
 * The list is filtered based on the provided query parameters.
 * The query parameters include:
 * - page: The page number to fetch.
 * - limit: The number of documents to fetch per page.
 * - sort: The field to sort the list by.
 * - issuerStatus: The status of the issuer.
 * - startDate: The start date of the filter.
 * - endDate: The end date of the filter.
 * - userId: The id of the user.
 */
export async function dashboard(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { page = 1, limit = 10, sort = JSON.stringify({ createdAt: -1 }), startDate, endDate, issuerStatus = '' } = req.query;
    const userId = new Types.ObjectId(req.userInfo.userId);
    // Add date filtering based on startDate and endDate
    const filters = {
      isKyc: true,
      isIssuer: true,
      ...(userId && { userId }),
      ...(issuerStatus && { issuerStatus }),
    };

    const sortCriteria = JSON.parse(sort as string);
    const projection = [
      'projectDetails.offeringName',
      'projectDetails.tokenTicker',
      'tokenAddress',
      'projectDetails.startDate',
      'projectDetails.endDate',
      'projectDetails.authorizedCountries',
      'projectDetails.taId',
      'createdAt',
      'createdBy',
      'status',
    ];

    const userDetails = await UserService.dashboardList(filters, projection, {
      page: Number(page),
      limit: Number(limit),
      sort: sortCriteria,
    });

    return ResponseHandler.success(res, {
      status: 200,
      error: false,
      message: RES_MSG?.SUCCESS_MSG.ISSUER_LIST,
      data: userDetails.data,
    });
  } catch (error: any) {
    logger.error(error, 'getIssuerList Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

/**
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise < PromiseResolve >}
 *
 * Fetches a list of all issuers and their details.
 * The list is filtered based on the provided query parameters.
 * The query parameters include:
 * - page: The page number to fetch.
 * - limit: The number of documents to fetch per page.
 * - sort: The field to sort the list by.
 * - issuerStatus: The status of the issuer.
 * - userType: The type of user.
 * - search: The search query to filter the list.
 * - countryCode: The country code to filter the list.
 */
export async function getIssuerController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { page = 1, limit = 10, sort = JSON.stringify({ createdAt: -1 }), issuerStatus = '', userType = '', search = '', countryCode = '' } = req.query;

    const filters = {
      isKyc: true,
      ...(issuerStatus
        ? { issuerStatus }
        : {
            issuerStatus: { $ne: IssuerStatus.NOT_APPLIED },
          }),
      ...(userType && { userType }),
      ...(countryCode && { countryCode }),
    };

    const sortCriteria = JSON.parse(sort as string);
    const projection = ['name', 'email', 'userImage', 'kycStatus', 'mobile', 'isIssuer', 'issuerStatus', 'issuerReason', 'mainInformation.nationality', 'isActive', 'userType', 'countryCode', 'institutions.companyInformation', 'sumSubKycStatus'];

    const userDetails = await UserService.fetchUserList(filters, projection, {
      page: Number(page),
      limit: Number(limit),
      sort: sortCriteria,
      search,
    });

    return ResponseHandler.success(res, {
      status: 200,
      error: false,
      message: RES_MSG?.SUCCESS_MSG.ISSUER_LIST,
      data: userDetails.data,
    });
  } catch (error: any) {
    logger.error(error, 'getIssuerList Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

/**
 * Approves or rejects an issuer based on the provided request body.
 * @export
 * @param {Request} req - The incoming request object containing email, issuerStatus, and issuerReason.
 * @param {Response} res - The response object to send the status of the operation.
 * @returns {Promise<PromiseResolve>} - A promise that resolves with the operation's result.
 *
 * Fetches the user details from the database and sends a gRPC request to approve or reject the issuer.
 * If the gRPC request succeeds, updates the user's issuer status in the database and returns a successful response.
 */
export async function approveIssuer(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { email, issuerStatus, issuerReason } = req.body;

    // Fetch specific user details
    const userDetails = await UserService.fetchTaDetails({ email }, ['name', 'email', 'kycStatus', 'isActive', 'issuerStatus']);

    if (userDetails?.data?.issuerStatus === issuerStatus) {
      throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.BAD_REQUEST);
    }

    const payload = { email, issuerStatus, issuerReason };

    // gRPC call to approve issuer
    userClient.client.approveIssuer(payload, async (error: any, response: any) => {
      if (error || response?.error) {
        const errorMessage = error?.message || response?.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR;
        const errorStatus = error?.status || RESPONSES.INTERNAL_SERVER_ERROR;

        logger.error('gRPC Error:', error || response);
        return ResponseHandler.error(res, {
          status: errorStatus,
          error: true,
          message: errorMessage,
        });
      }

      // Update user's issuer status
      const updatedUser = await userSchema.findOneAndUpdate(
        { email },
        {
          issuerStatus,
          isIssuer: true,
          issuerReason,
        },
        { new: true }, // Return updated document
      );

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.UPDATE_USER,
        data: issuerStatus,
      });
    });
  } catch (error: any) {
    logger.error(error, 'approveIssuer Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * Health check endpoint to verify the server is running.
 * @export
 * @param {Request} req - The request object.
 * @param {Response} res - The response object.
 * @returns {Promise<PromiseResolve>} - Returns a promise that resolves a successful health check message.
 */
export async function healthCheck(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    return ResponseHandler.success(res, {
      message: RES_MSG?.SUCCESS_MSG.HEALTH_CHECK,
      status: RESPONSES.SUCCESS,
      error: false,
      data: {
        timestamp: new Date(),
      },
    });
  } catch (error: any) {
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

/**
 * Fetches a user's data based on the provided userId query parameter.
 * @export
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise < PromiseResolve >}
 */
export async function getUserdata(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { userId }: any = req.query;
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return ResponseHandler.error(res, {
        message: 'invalid id',
        status: RESPONSES.BAD_REQUEST,
        error: true,
      });
    }

    const objectId = new mongoose.Types.ObjectId(userId);
    const user = await userSchema.findById(objectId).exec();
    if (!user) {
      return ResponseHandler.error(res, {
        message: RES_MSG.ERROR_MSG.USER_NOT_FOUND,
        status: 400,
        error: true,
      });
    }

    // Fetch user detail data based_id on userId
    // const userDetail = await UserDetailsSchema.findById(objectId).exec();

    // Combine or structure the data as needed
    const combinedData = {
      user: user?.toObject(),
      // details: userDetail ? userDetail.toObject() : null
    };
    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
      data: combinedData,
    });
  } catch (error: any) {
    logger.error(error, 'getUserslist Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}

/**
 * Forgot password controller
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @returns {Promise<PromiseResolve>} - Promise that resolves with a response
 */
export async function forgotPasswordController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { email } = req.body;
    const userDetails: PromiseResolve = await UserService.fetchTaDetails({
      email,
    });
    if (userDetails.error)
      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.ERROR_MSG.FORGOT_CRED,
      });
    const otpResponse = await CommonHelper.sendOTP(userDetails.data._id, otpType.FORGOT, userDetails.data.email, userDetails.data.name);
    if (otpResponse.error) throw new CustomError(otpResponse.message, otpResponse.status);
    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.ERROR_MSG.FORGOT_CRED,
    });
  } catch (error: any) {
    logger.error(error, 'Error in forgotPasswordController');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * Controller to handle password change requests.
 * Validates the provided passwords and updates the user's password in the system.
 *
 * @exports
 * @param {IUserModel} req - Express request object containing user info and new password details
 * @param {Response} res - Express response object
 * @returns {Promise<PromiseResolve>} - Promise resolving with the result of the operation
 */
export async function changePasswordController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const email = req.userInfo.email;
    const { password, oldPassword } = req.body;
    if (password == oldPassword) throw new CustomError(RES_MSG.ERROR_MSG.NEW_PASSWORD_SAME_AS_CURRENT_PASSWORD, RESPONSES.BAD_REQUEST);
    const userDetails: PromiseResolve = await UserService.fetchTaDetails({
      email,
    });
    if (userDetails.error) throw new CustomError(RES_MSG.COMMON.NO_USER, RESPONSES.UN_AUTHORIZED);

    const verifyPassResp: PromiseResolve = await CommonHelper.verifyPassword(oldPassword, userDetails.data.password);
    if (verifyPassResp.error) throw new CustomError(RES_MSG.ERROR_MSG.INCORRECT_CURRENT_PASSWORD, RESPONSES.BAD_REQUEST);
    const updateUserResp: PromiseResolve = await UserService.updateUserDetails({ password }, { email });
    if (!updateUserResp.error) {
      // Add the new password to the password history
      await RedisHelper.deleteKey(`accessToken:${email}`);
      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.PASSWORD_RESET_SUCCESS,
      });
    }
  } catch (error) {
    logger.error(error, 'changePasswordController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * Controller to handle password reset requests.
 * Validates the new password and updates it in the database.
 *
 * @exports
 * @param {Request} req - Express request object containing user info and new password
 * @param {Response} res - Express response object
 * @returns {Promise<PromiseResolve>} - Promise resolving with the result of the operation
 */
export async function resetPasswordController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { newPassword } = req.body;
    const { email } = req.body.user;
    const userDetails: PromiseResolve = await UserService.fetchTaDetails({ email }, ['email', '_id'], []);
    if (userDetails.error) throw new CustomError(RES_MSG.COMMON.NO_USER, RESPONSES.UN_AUTHORIZED);
    const updateUserResp = await UserService.updateUserDetails({ password: newPassword }, { email });
    if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);
    await RedisHelper.deleteKey(`forgetToken:${email}`);
    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.SUCCESS_MSG.PASSWORD_RESET_SUCCESS,
    });
  } catch (error: any) {
    logger.error(error, 'Error in resetPassword');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}
/**
 * Controller to handle logout requests.
 * Deletes the user's access token and 2FA key from Redis.
 *
 * @exports
 * @param {Request} req - Express request object containing user info
 * @param {Response} res - Express response object
 * @returns {Promise<PromiseResolve>} - Promise resolving with the result of the operation
 */
export async function logOutController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { email, userId } = req.userInfo;
    await RedisHelper.deleteKey(`accessToken:${email}`);
    await RedisHelper.deleteKey(`2FA_${userId}`);

    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.SUCCESS_MSG.LOGOUT,
    });
  } catch (error: any) {
    logger.error(error, 'logOut Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * Controller to enable 2FA for a user.
 *
 * @exports
 * @param {Request} req - Express request object containing user info
 * @param {Response} res - Express response object
 * @returns {Promise<PromiseResolve>} - Promise resolving with the result of the operation
 */
export async function enable2FAController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { userId, email } = req.userInfo;
    const secret = authenticator.generateSecret();
    const otpAuth = authenticator.keyuri(email, CONFIG.PROJECT.NAME, secret);
    const qrCode = await toDataURL(otpAuth);
    // await RedisHelper.setString(twoFactorSecretKey, secret, CONFIG.REDIS.OTP_EXPIRY)
    const userDetails: PromiseResolve = await UserService.fetchTaDetails({
      email,
    });
    if (userDetails.data.is2FAActive) throw new CustomError(RES_MSG.COMMON.BAD_REQUEST, RESPONSES.BAD_REQUEST);
    const updateUserResp = await UserService.updateUserDetails({ twoFASecret: secret }, { _id: userId });
    if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);
    return ResponseHandler.success(res, {
      status: RESPONSES.CREATED,
      error: false,
      message: RES_MSG.TWO_FA.CREATED,
      data: { qrCode, secret },
    });
  } catch (error) {
    logger.error(error, 'enable2FAController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * Handles the forgot 2FA process for a user by generating a new 2FA secret.
 * @exports
 * @param {Request} req - The request object containing the user information.
 * @param {Response} res - The response object for sending responses.
 * @returns {Promise<void>} - A promise that resolves when the operation is complete.
 */
export async function forgot2FAController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { userId } = req.body.user;
    const userDetails: PromiseResolve = await UserService.fetchTaDetails({ _id: userId }, [], ['kycDetails', 'wallets', 'isIssuer', 'issuerStatus', 'createdAt', 'updatedAt']);
    if (userDetails.error) throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
    const user = userDetails?.data;
    const secret = authenticator.generateSecret();
    // await RedisHelper.setString(twoFactorSecretKey, secret, CONFIG.REDIS.OTP_EXPIRY)
    const updateUserResp = await UserService.updateUserDetails({ twoFASecret: secret }, { _id: userId });
    if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);
    const otpAuth = authenticator.keyuri(user?.email, CONFIG.PROJECT.NAME, secret);
    const qrCode = await toDataURL(otpAuth);
    return ResponseHandler.success(res, {
      status: RESPONSES.CREATED,
      error: false,
      message: RES_MSG.TWO_FA.FORGOT_2FA,
      data: { qrCode, secret },
    });
  } catch (error) {
    logger.error(error, 'forgot2FAController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * Handles the forgot 2FA process for a user by generating a new 2FA secret.
 * @exports
 * @param {Request} req - The request object containing the user information.
 * @param {Response} res - The response object for sending responses.
 * @returns {Promise<void>} - A promise that resolves when the operation is complete.
 */
export async function reSet2FA(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { token } = req.body;

    const isTokenValid: PromiseResolve = await CommonHelper.isValidToken(token);
    if (isTokenValid.error) {
      throw new CustomError(RES_MSG.TWO_FA.TOKEN_EXPIRE, RESPONSES.UN_AUTHORIZED);
    }

    const { userId } = isTokenValid.data;

    const userDetails: PromiseResolve = await UserService.fetchTaDetails({ _id: userId }, [], ['kycDetails', 'wallets', 'isIssuer', 'issuerStatus', 'createdAt', 'updatedAt']);

    if (userDetails.error) {
      throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
    }

    const updateData = {
      is2FAActive: false,
      twoFASecret: '',
    };

    const updateUserResp = await UserService.updateUserDetails(updateData, {
      _id: userId,
    });

    if (updateUserResp.error) {
      throw new CustomError(updateUserResp.message, updateUserResp.status);
    }

    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.TWO_FA.RESET_SUCCESS,
      data: {},
    });
  } catch (error) {
    logger.error(error, 'reSet2FA Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * Handles the verification of a user's 2FA token when they log in to their account.
 * @exports
 * @param {Request} req - The request object containing the user's 2FA token.
 * @param {Response} res - The response object for sending responses.
 * @returns {Promise<void>} - A promise that resolves when the operation is complete.
 */
export async function verify2FAController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { token } = req.body;
    const { userId } = req.userInfo;
    const userDetails: PromiseResolve = await UserService.fetchTaDetails({ _id: userId }, [], ['kycDetails', 'wallets', 'isIssuer', 'issuerStatus', 'createdAt', 'updatedAt']);
    if (userDetails.error) throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
    const isValidTwoFAToken = authenticator.check(token, userDetails.data.twoFASecret);
    if (!isValidTwoFAToken) {
      throw new CustomError(RES_MSG.TWO_FA.TOKEN_INVALID, RESPONSES.BAD_REQUEST);
    }
    const updateUserResp = await UserService.updateUserDetails({ is2FAActive: true }, { _id: userId });
    if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);
    await RedisHelper.setString(`2FA_${userId}`, 'true', CONFIG.REDIS.REDIS_AUTH_EXPIRE);
    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.TWO_FA.VERIFIED_SUCCESS,
      data: null,
    });
  } catch (error) {
    logger.error(error, 'verify2FAController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * Verify 2FA code sent by the user
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<PromiseResolve>}
 */
export async function verify2FALcoginController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { code } = req.body;
    const { userId } = req.body.user;
    const userDetails: PromiseResolve = await UserService.fetchTaDetails({ _id: userId }, [], ['kycDetails', 'wallets', 'isIssuer', 'issuerStatus', 'createdAt', 'updatedAt']);
    if (userDetails.error) throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
    const isValidTwoFAToken = authenticator.check(code, userDetails.data.twoFASecret);
    if (!isValidTwoFAToken) {
      throw new CustomError(RES_MSG.TWO_FA.TOKEN_INVALID, RESPONSES.BAD_REQUEST);
    }
    const updateUserResp = await UserService.updateUserDetails({ is2FAActive: true }, { _id: userId });
    if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);
    await RedisHelper.setString(`2FA_${userId}`, 'true', CONFIG.REDIS.REDIS_AUTH_EXPIRE);
    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.TWO_FA.VERIFIED_SUCCESS,
      data: null,
    });
  } catch (error) {
    logger.error(error, 'verify2FAController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * Handles the disable 2FA process for a user by verifying the provided token.
 *
 * @param {Request} req - Express request object containing the user's token
 * @param {Response} res - Express response object
 * @returns {Promise<void>} - Promise resolving with the result of the operation
 */
export async function disable2FAController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { token } = req.body;
    const { userId, email } = req.userInfo;
    const userDetails: PromiseResolve = await UserService.fetchTaDetails({
      email,
    });
    if (userDetails.error) throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
    const isValidTwoFAToken = authenticator.check(token, userDetails.data.twoFASecret);
    if (!isValidTwoFAToken) {
      throw new CustomError(RES_MSG.TWO_FA.TOKEN_INVALID, RESPONSES.BAD_REQUEST);
    }
    const updateUserResp = await UserService.updateUserDetails({ is2FAActive: false }, { _id: userId });
    if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);
    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.TWO_FA.DISABLE_SUCCESS,
      data: null,
    });
  } catch (error) {
    logger.error(error, 'disable2FAController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * Handles the verification of a user's 2FA token when they log in to their account.
 *
 * @param {Request} req - Express request object containing the user's 2FA token and user details
 * @param {Response} res - Express response object
 * @returns {Promise<void>} - Promise resolving with the result of the operation
 */
export async function verifyLogin2FAController(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { token, otp } = req.body;
    const { userId } = req.body.user;
    const userDetails: PromiseResolve = await UserService.fetchTaDetails({ _id: userId }, [], ['kycDetails', 'wallets', 'isIssuer', 'issuerStatus', 'createdAt', 'updatedAt']);
    const user = userDetails.data;

    if (userDetails.error) throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
    const isValidTwoFAToken = authenticator.check(otp, user.twoFASecret);
    if (!isValidTwoFAToken) {
      throw new CustomError(RES_MSG.TWO_FA.TOKEN_INVALID, RESPONSES.BAD_REQUEST);
    }
    const updateUserResp = await UserService.updateUserDetails({ is2FAActive: true }, { _id: userId });
    if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);

    const tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email, userType: user.userType }, true);
    if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);
    await RedisHelper.setString(`2FA_${userId}`, 'true', CONFIG.REDIS.REDIS_AUTH_EXPIRE);
    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: RES_MSG.TWO_FA.VERIFIED_SUCCESS,
      data: { ...tokenResp.data },
    });
  } catch (error) {
    logger.error(error, 'verify2FAController Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * Approves or rejects a user's KYC based on the provided request body.
 * @export
 * @param {Request} req - The incoming request object containing email, kycStatus, and kycReason.
 * @param {Response} res - The response object to send the status of the operation.
 * @returns {Promise<PromiseResolve>} - A promise that resolves with the operation's result.
 */
export async function approve(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    let message: any;
    const { email, kycStatus, kycReason } = req.body;
    const payload = { email, kycStatus, kycReason };

    const userDetails = await UserService.fetchTaDetails({ email }, ['name', 'email', 'kycStatus', 'isActive', 'kycCount', 'userType'], []);

    if (userDetails.error) throw new CustomError(RES_MSG.COMMON.NO_USER, RESPONSES.UN_AUTHORIZED);

    const user = userDetails?.data;

    if (user?.isActive == false) {
      throw new CustomError(RES_MSG.COMMON.BLOCK_USER, RESPONSES.BAD_REQUEST);
    }

    if (user?.kycStatus === kycStatus) {
      throw new CustomError(`User is already ${kycStatus}`, RESPONSES.BAD_REQUEST);
    }

    // Prepare the update object with all conditions
    const updateData: any = {
      kycStatus: kycStatus,
      kycReason: kycReason,
      isKyc: false, // Default false, will update later based on gRPC response
    };

    if (kycStatus === 'REJECTED' && user?.kycCount === +kycCount - 1) {
      updateData.isActive = false; // Mark user inactive if rejected
      updateData.kycCount = kycCount;
    }

    // Call gRPC service
    userClient.client.approve(payload, async (error: any, response: any) => {
      if (error) {
        logger.error('gRPC Error:', error);
        return ResponseHandler.error(res, {
          status: RESPONSES.INTERNAL_SERVER_ERROR,
          error: true,
          message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        });
      }

      if (response.error) {
        return ResponseHandler.error(res, {
          message: response.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
          status: RESPONSES.BAD_REQUEST,
          error: true,
        });
      }

      // Update isKyc status based on gRPC response
      updateData.isKyc = response.error ? false : true;

      // Base emailDetail object
      const emailDetail: { name: string; kycReason?: string } = {
        name: user.name,
      };
      if (kycStatus === 'APPROVED') {
        if (user.userType == UserType.Investor) {
          message = RES_MSG.SUCCESS_MSG.KYC_APPROVED;
          emailHelper.sendEmailTemplate(email, 'KYCApproved', emailDetail);
        } else if (user.userType == UserType.Institution) {
          message = RES_MSG.SUCCESS_MSG.KYB_APPROVED;
          emailHelper.sendEmailTemplate(email, 'KYBApproved', emailDetail);
        }
      } else if (kycStatus === 'REJECTED') {
        if (user.userType == UserType.Investor) {
          message = RES_MSG.SUCCESS_MSG.KYC_REJECTED;
          emailDetail.kycReason = kycReason; // Conditionally add kycReason
          emailHelper.sendEmailTemplate(email, 'KYCRejected', emailDetail);
        } else if (user.userType == UserType.Institution) {
          message = RES_MSG.SUCCESS_MSG.KYB_REJECTED;
          emailDetail.kycReason = kycReason; // Conditionally add kycReason
          emailHelper.sendEmailTemplate(email, 'KYBRejected', emailDetail);
        }
      }

      // Single database update call
      const updatedUser = await userSchema.findOneAndUpdate({ email: email }, updateData, { new: true });

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: message,
        data: kycStatus,
      });
    });
  } catch (error: any) {
    logger.error(error, 'logOut Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

export async function approveOffering(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { _id, taStatus, whiteListReason } = req.body;
    const payload = { _id, taStatus, whiteListReason };
    const fetchuserId = await whitelistSchema.findOne({ _id: _id }, 'userId, taStatus');

    if (!fetchuserId) {
      throw new CustomError(RES_MSG.COMMON.NO_USER, RESPONSES.UN_AUTHORIZED);
    }

    const userId = fetchuserId.userId;
    const updatedUser = await userSchema.findOne({ _id: userId }, 'email, name');

    if (!updatedUser) {
      throw new CustomError(RES_MSG.COMMON.NO_USER, RESPONSES.UN_AUTHORIZED);
    }

    if (fetchuserId.taStatus === taStatus) {
      throw new CustomError(RES_MSG.COMMON.UNAUTHORIZED_ACTION, RESPONSES.BAD_REQUEST);
    }

    // Prepare the update object with all conditions
    const updateData: any = {
      status: taStatus,
      whiteListReason: whiteListReason,
    };

    // Await the gRPC service call
    await new Promise((resolve, reject) => {
      userClient.client.approveWhitelist(payload, (error: any, response: any) => {
        if (error) {
          logger.error('gRPC Error:', error);
          reject(new Error(error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR));
        } else if (response.error) {
          reject(new Error(response.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR));
        } else {
          resolve(response);
        }
      });
    });

    // Base emailDetail object
    const emailDetail = { name: updatedUser.name };

    let message: string;

    if (taStatus === 'APPROVED') {
      message = RES_MSG.SUCCESS_MSG.WHITELIST_APPROVED;
      emailHelper.sendEmailTemplate(updatedUser.email, 'taStatusApproved', emailDetail);
    } else if (taStatus === 'REJECTED') {
      message = RES_MSG.SUCCESS_MSG.WALLET_REJECTED;
      emailHelper.sendEmailTemplate(updatedUser.email, 'taStatusRejected', emailDetail);
    }

    // Update the whitelist entry
    await whitelistSchema.findOneAndUpdate({ _id: _id }, updateData, {
      new: true,
    });

    return ResponseHandler.success(res, {
      status: RESPONSES.SUCCESS,
      error: false,
      message: message,
      data: taStatus,
    });
  } catch (error: any) {
    logger.error(error, 'approveOffering Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * Unblocks or blocks a user based on the `isActive` status provided in the request body.
 *
 * @export
 * @param {Request} req - The request object containing user's email and the desired active status.
 * @param {Response} res - The response object used to send back the status of the operation.
 * @returns {Promise<PromiseResolve>} - A promise that resolves with the operation's result.
 */
export async function unblock(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { email, isActive } = req.body;

    // Fetch user details and return specific fields
    const userDetails = await UserService.fetchTaDetails({ email }, ['name', 'email', 'kycStatus', 'isActive']);

    if (!userDetails.data) {
      throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.BAD_REQUEST);
    }

    const currentIsActive = userDetails.data.isActive;
    if (String(currentIsActive) === String(isActive)) {
      throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.BAD_REQUEST);
    }

    const payload = {
      email,
      isActive,
      kycCount: 0,
    };

    // gRPC call to unblock the user
    userClient.client.unblock(payload, async (error: any, response: any) => {
      if (error) {
        logger.error('gRPC Error:', error);
        return ResponseHandler.error(res, {
          status: RESPONSES.INTERNAL_SERVER_ERROR,
          error: true,
          message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        });
      }

      if (response.error) {
        return ResponseHandler.error(res, {
          status: RESPONSES.BAD_REQUEST,
          error: true,
          message: response.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        });
      }

      // Update user's active status and reset kycCount if activated
      const updatedUser: PromiseResolve = await userSchema.findOneAndUpdate(
        { email },
        {
          isActive,
          ...(isActive === 'true' && { kycCount: 0 }), // Conditionally reset kycCount
        },
        { new: true }, // Return the updated document
      );

      const emailDetails = { name: userDetails.data.name };
      let message;

      if (isActive === 'true') {
        emailHelper.sendEmailTemplate(email, 'accountUnBlock', emailDetails);
        message = RES_MSG.COMMON.ADMIN_UNBLOCK_USER;
      } else {
        emailHelper.sendEmailTemplate(email, 'accountBlocked', emailDetails);
        await RedisHelper.deleteKey(`accessToken:${email}`);
        message = RES_MSG.COMMON.ADMIN_BLOCK_USER;
      }

      return ResponseHandler.success(res, {
        status: updatedUser.status || RESPONSES.SUCCESS,
        error: false,
        message,
        data: isActive,
      });
    });
  } catch (error: any) {
    logger.error(error, 'unblock Error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * get User Profile Details.
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<PromiseResolve>}
 */
export async function getUserProfileDetails(req: Request, res: Response): Promise<PromiseResolve> {
  try {
    const { id } = req.body;
    const userId = id ? new Types.ObjectId(id) : null;
    const searchQuery = { _id: userId };

    const { error, message, status = RESPONSES.SUCCESS, data: user } = await UserService.fetchUserDetails(searchQuery, [], ['password']);

    if (error) throw new CustomError(message, status);

    return ResponseHandler.success(res, {
      status,
      error: false,
      message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
      data: user,
    });
  } catch (error: any) {
    logger.error(error, 'getUserProfileDetailsController Error');
    return ResponseHandler.error(res, {
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
    });
  }
}
