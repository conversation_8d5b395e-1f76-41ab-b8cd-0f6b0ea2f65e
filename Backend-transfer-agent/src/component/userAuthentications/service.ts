import { IUserService } from './interface';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { PromiseResolve, IPagination, UserType } from '../../utils/common.interface';
import { IUserModel, IUpdateUserModel, userSchema, passwordSchema, PasswordModel } from './models/user.model';
import mongoose, { FilterQuery, Types } from 'mongoose';
import { maxPasswordHistory } from '../../utils/constant';
import logger from '../../helpers/logger.helper';
import * as bcrypt from 'bcrypt';
import { ITransferAgent, transferAgentSchema } from './models/transferagent.model';
import { OrderSchema } from './models/order.model';
import { whitelistSchema } from './models/whitelist.model';
import { IOffering, offeringSchema } from './models/offerings.model';
import TransferRequest from '../transfer/models/transfer';
import <PERSON>Helper from '../../helpers/common.helper';
import * as moment from 'moment';
import CustomError from '../../helpers/customError.helper';
import { calculate } from '../../helpers/bigMath';

class UserService implements IUserService {
  /**
   * Fetches a user's data based on the provided search query.
   *
   * @param {IUserModel} searchDetails - The search query to fetch the user data.
   * @param {string[]} fields - The fields to include in the response. If not provided, all fields are included.
   * @param {string[]} excludeFields - The fields to exclude from the response. If not provided, all fields are included.
   * @returns {Promise<PromiseResolve>} - A promise that resolves to a {@link PromiseResolve} object.
   * @memberof UserService
   */

  fetchTaDetails = async (searchDetails: FilterQuery<IUserModel>, fields?: string[], excludeFields?: string[]): Promise<PromiseResolve> => {
    try {
      const userDetailsQuery = transferAgentSchema.findOne(searchDetails);

      if (fields && fields.length > 0) {
        const fieldsString = fields.join(' ');
        userDetailsQuery.select(fieldsString);
      }

      if (excludeFields && excludeFields.length > 0) {
        const excludeFieldsString = excludeFields.map((field) => `-${field}`).join(' ');
        userDetailsQuery.select(excludeFieldsString);
      }

      const userDetails: ITransferAgent = await userDetailsQuery.exec();

      if (userDetails && userDetails.email) {
        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
          data: {
            ...userDetails.toObject(),
          },
        };
      }
      return {
        status: RESPONSES.NOTFOUND,
        error: true,
        message: RES_MSG.COMMON.NO_USER,
      };
    } catch (error) {
      logger.error(error, 'fetchTaDetails error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  /**
   * Updates a user's details in the database.
   *
   * @param {IUpdateUserModel} data - The updated user data.
   * @param {FilterQuery<IUserModel>} filter - The filter to identify the user to update.
   * @returns {Promise<PromiseResolve>} - A promise that resolves to a {@link PromiseResolve} object.
   * @memberof UserService
   */

  fetchUserDetails = async (searchDetails: FilterQuery<IUserModel>, fields?: string[], excludeFields?: string[]): Promise<PromiseResolve> => {
    try {
      const userDetailsQuery = userSchema.findOne(searchDetails);
      if (fields && fields.length > 0) {
        const fieldsString = fields.join(' ');
        userDetailsQuery.select(fieldsString);
      }

      if (excludeFields && excludeFields.length > 0) {
        const excludeFieldsString = excludeFields.map((field) => `-${field}`).join(' ');
        userDetailsQuery.select(excludeFieldsString);
      }

      const userDetails: IUserModel = await userDetailsQuery.exec();

      if (userDetails && userDetails.email) {
        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
          data: {
            ...userDetails.toObject(),
          },
        };
      }
      return {
        status: RESPONSES.NOTFOUND,
        error: true,
        message: RES_MSG.COMMON.NO_USER,
      };
    } catch (error) {
      logger.error(error, 'fetchUserDetails error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  /**
   * Updates a user's details in the database.
   *
   * @param {IUpdateUserModel} data - The updated user data.
   * @param {FilterQuery<IUserModel>} filter - The filter to identify the user to update.
   * @returns {Promise<PromiseResolve>} - A promise that resolves to a {@link PromiseResolve} object.
   * @memberof UserService
   */
  updateUserDetails = async (data: IUpdateUserModel, filter: FilterQuery<IUserModel>): Promise<PromiseResolve> => {
    try {
      if (data?.password) {
        const salt = await bcrypt.genSalt(10);
        data.password = await bcrypt.hash(data.password, salt);
      }

      const updateUserResp = await transferAgentSchema.findOneAndUpdate(filter, data, {
        new: true,
        runValidators: true,
        upsert: true, // Insert if the document doesn't exist
      });
      await this.addPassword(updateUserResp._id?.toString(), updateUserResp.password);
      if (updateUserResp) {
        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.USER.USER_UPDATION_SUCCESS,
          data: '',
        };
      }

      return {
        status: RESPONSES.BAD_REQUEST,
        error: true,
        message: RES_MSG.ERROR_MSG.USER_UPDATION_ERROR,
      };
    } catch (error) {
      logger.error(error, 'updateUserDetails error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  /**
   * @description Get the top holders of a specific offering
   * @param searchDetails Object containing the offeringId
   * @param pagination Object containing the page, limit, sort and search parameters
   * @returns PromiseResolve Object containing the top holders of the offering
   */
  singleOfferingReport = async (searchDetails: any, pagination: IPagination): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 }, search: escapedSearch } = pagination;
      const skip = (page - 1) * limit;
      const search = escapedSearch ? CommonHelper.escapeRegex(escapedSearch) : null;

      const offeringId = new mongoose.Types.ObjectId(searchDetails?.offeringId);

      const searchQuery: any = { status: 'MINTED', offeringId };

      const pipeline: any[] = [
        { $match: searchQuery },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'userData',
          },
        },
        {
          $lookup: {
            from: 'offerings',
            localField: 'offeringId',
            foreignField: '_id',
            as: 'offeringData',
          },
        },
        { $unwind: '$userData' },
        { $unwind: '$offeringData' },

        search
          ? {
              $match: {
                $or: [{ 'userData.name': { $regex: search, $options: 'i' } }, { 'userData.email': { $regex: search, $options: 'i' } }],
              },
            }
          : { $match: {} },

        {
          $project: {
            _id: 0,
            userId: '$userData._id',
            email: '$userData.email',
            name: '$userData.name',
            wallet: {
              type: { $arrayElemAt: ['$userData.wallets.type', 0] },
              address: { $arrayElemAt: ['$userData.wallets.address', 0] },
              isVerify: { $arrayElemAt: ['$userData.wallets.isVerify', 0] },
            },
            offeringName: '$offeringData.projectDetails.offeringName',
            offeringId: '$offeringData._id',
          },
        },
        {
          $group: {
            _id: { userId: '$userId', offeringId: '$offeringId' },
            userId: { $first: '$userId' },
            email: { $first: '$email' },
            name: { $first: '$name' },
            wallet: { $first: '$wallet' },
            offeringName: { $first: '$offeringName' },
            offeringId: { $first: '$offeringId' },
          },
        },
        { $sort: sort },
        { $skip: skip },
        { $limit: limit },
        {
          $group: {
            _id: null,
            offering: { $push: '$$ROOT' },
            totalInvestors: { $sum: 1 },
          },
        },
      ];

      const result = await OrderSchema.aggregate(pipeline);
      const data = result[0] || { offering: [], totalInvestors: 0 };
      const totalPages = Math.ceil(data.totalInvestors / limit);

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.COMMON.RECORD_FETCH,
        data: {
          offering: data.offering,
          totalInvestors: data.totalInvestors,
          currentPage: page,
          totalPages,
          totalCount: data.totalInvestors,
          nextPage: page < totalPages ? page + 1 : null,
          previousPage: page > 1 ? page - 1 : null,
        },
      };
    } catch (error) {
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  /**
   * @description Get the list of top holders for a specific offering
   * @param searchDetails {offeringId: string} - The offeringId to filter the results
   * @param pagination {page: number, limit: number, sort?: string[], search?: string} - The pagination details
   * @returns {PromiseResolve} - The response with the top holders data
   */
  topHolders = async (searchDetails: any, pagination: IPagination): Promise<PromiseResolve> => {
    try {
      const {
        page = 1,
        limit = 10,
        //  sort = { createdAt: -1 },
        search: escapedSearch,
      } = pagination;
      // const skip = (page - 1) * limit;
      // Build the search query
      const search = escapedSearch ? CommonHelper.escapeRegex(escapedSearch) : null;
      const offeringId = searchDetails?.offeringId;

      const searchQuery: FilterQuery<any> = {};

      if (search) {
        searchQuery.$or = [{ 'userData.email': { $regex: search, $options: 'i' } }, { 'userData.name': { $regex: search, $options: 'i' } }];
      }
      const pipeline: any[] = [
        {
          $match: {
            status: 'MINTED',
            offeringId: new mongoose.Types.ObjectId(offeringId),
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'userData',
          },
        },
        {
          $lookup: {
            from: 'offerings',
            localField: 'offeringId',
            foreignField: '_id',
            as: 'offeringData',
          },
        },
        {
          $unwind: {
            path: '$offeringData',
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $unwind: {
            path: '$userData',
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $match: {
            $expr: {
              $eq: ['$userId', '$offeringData.projectDetails.taid'],
            },
          },
        },
        {
          $lookup: {
            from: 'orders', // Lookup in the orders collection
            localField: 'offeringId',
            foreignField: 'offeringId',
            as: 'orderData',
          },
        },
        {
          $unwind: {
            path: '$orderData',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $group: {
            _id: {
              userId: '$userId',
              offeringId: '$offeringId',
            },
            email: { $first: '$userData.email' },
            name: { $first: '$userData.name' },
            totalAmount: {
              $sum: '$orderData.amount', // Sum the order amounts for this user-offering pair
            },
          },
        },
        {
          $project: {
            _id: 0,
            userId: '$_id.userId',
            offeringId: '$_id.offeringId',
            email: '$email',
            name: '$name',
            totalAmount: '$totalAmount',
          },
        },
      ];

      const order = await OrderSchema.aggregate(pipeline);

      const totalCount = order[0].totalCount || 0;
      const totalPages = Math.ceil(totalCount / limit);

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.COMMON.RECORD_FETCH,
        data: {
          offering: order[0].offering, // The fetched data
          totalInvestors: order[0]?.totalInvestors || 0,
          currentPage: page,
          totalPages: totalPages,
          totalCount: totalCount,
          nextPage: page < totalPages ? page + 1 : null,
          previousPage: page > 1 ? page - 1 : null,
        },
      };
    } catch (error) {
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  /**
   * Get the transaction report for a specific offering
   * @param searchDetails Object containing the offeringId
   * @param pagination Object containing the page, limit, sort and search parameters
   * @returns PromiseResolve Object containing the transaction report
   */
  transcationReport = async (searchDetails: any, pagination: IPagination): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, search: escapedSearch } = pagination;
      const search = escapedSearch ? CommonHelper.escapeRegex(escapedSearch) : null;
      const skip = (page - 1) * limit;
      // Build the search query
      const offeringId = searchDetails?.offeringId;

      const searchQuery: FilterQuery<any> = {};

      if (search) {
        searchQuery.$or = [{ 'userData.email': { $regex: search, $options: 'i' } }, { 'userData.name': { $regex: search, $options: 'i' } }];
      }
      const pipeline: any[] = [
        {
          $match: {
            // status: "MINTED",
            offeringId: new mongoose.Types.ObjectId(offeringId),
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'userData',
          },
        },
        {
          $lookup: {
            from: 'offerings',
            localField: 'offeringId',
            foreignField: '_id',
            as: 'offeringData',
          },
        },
        {
          $unwind: {
            path: '$offeringData',
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $unwind: {
            path: '$userData',
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $facet: {
            offering: [
              {
                $match: {
                  ...(search
                    ? {
                        $or: [
                          {
                            'userData.email': { $regex: search, $options: 'i' },
                          },
                          {
                            'userData.name': { $regex: search, $options: 'i' },
                          },
                        ],
                      }
                    : {}),
                },
              },
              {
                $project: {
                  _id: 0,
                  userId: '$userData._id',
                  email: '$userData.email',
                  name: '$userData.name',
                  walletaddress: {
                    type: {
                      $arrayElemAt: ['$userData.wallets.type', 0],
                    },
                    address: {
                      $arrayElemAt: ['$userData.wallets.address', 0],
                    },
                    isVerify: {
                      $arrayElemAt: ['$userData.wallets.isVerify', 0],
                    },
                  },
                  offeringName: '$offeringData.projectDetails.offeringName',
                  offeringId: '$offeringData._id',
                  tokenTicker: '$offeringData.projectDetails.tokenTicker',
                  status: 1, // Add the status from the order schema
                },
              },
              {
                $group: {
                  _id: {
                    userId: '$userId',
                    offeringId: '$offeringId',
                  },
                  userId: { $first: '$userId' },
                  email: { $first: '$email' },
                  name: { $first: '$name' },
                  walletaddress: {
                    $first: '$walletaddress',
                  },
                  offeringName: {
                    $first: '$offeringName',
                  },
                  tokenTicker: {
                    $first: '$tokenTicker',
                  },
                  offeringId: { $first: '$offeringId' },
                  status: { $first: '$status' }, // Group by status as well
                },
              },
              { $skip: skip },
              { $limit: limit },
            ],
            totalInvestorsData: [
              {
                $group: {
                  _id: '$userData.email',
                },
              },
              {
                $count: 'totalInvestors',
              },
            ],
          },
        },
        {
          $addFields: {
            totalCount: { $size: '$offering' },
            totalInvestors: {
              $arrayElemAt: ['$totalInvestorsData.totalInvestors', 0],
            },
          },
        },
      ];

      const order = await OrderSchema.aggregate(pipeline);

      const totalCount = order[0].totalCount || 0;
      const totalPages = Math.ceil(totalCount / limit);

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.COMMON.RECORD_FETCH,
        data: {
          offering: order[0].offering, // The fetched data
          totalInvestors: order[0]?.totalInvestors || 0,
          currentPage: page,
          totalPages: totalPages,
          totalCount: totalCount,
          nextPage: page < totalPages ? page + 1 : null,
          previousPage: page > 1 ? page - 1 : null,
        },
      };
    } catch (error) {
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  singleOfferingWhitelistReport = async (searchDetails: any, pagination: IPagination): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, search: escapedSearch } = pagination;
      const skip = (page - 1) * limit;
      const search = escapedSearch ? CommonHelper.escapeRegex(escapedSearch) : null;
      // Build the search query for the whitelist schema
      const offeringId = searchDetails?.offeringId;
      const userId = searchDetails?.userId.toLocaleString();
      const status = searchDetails?.status;
      const taStatus = searchDetails?.taStatus;

      const searchQuery: FilterQuery<any> = {
        offeringId: new mongoose.Types.ObjectId(offeringId),
      };

      // Add status filter if provided
      if (status !== '') {
        searchQuery.status = status;
      }
      if (taStatus !== '') {
        searchQuery.taStatus = taStatus;
      }

      const pipeline: any[] = [
        {
          $match: searchQuery, // Match the whitelist schema filters
        },
        {
          $lookup: {
            from: 'users', // Join with the users collection
            localField: 'userId',
            foreignField: '_id',
            as: 'userData',
            pipeline: [
              {
                // Apply the search query to the users' name and email
                $match: {
                  $or: [{ email: { $regex: search || '', $options: 'i' } }, { name: { $regex: search || '', $options: 'i' } }],
                },
              },
              {
                $project: {
                  _id: 1,
                  name: 1,
                  email: 1,
                  userImage: 1,
                  onchainID: 1,
                  countryCode: 1,
                  projectDetails: 1,
                  walletAddress: { $arrayElemAt: ['$wallets.address', 0] },
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'offerings',
            localField: 'offeringId',
            foreignField: '_id',
            as: 'offeringData',
            pipeline: [
              {
                $match: {
                  'projectDetails.taId': userId,
                },
              },
              {
                $project: {
                  _id: 0,
                  tokenAddress: 1,
                  projectDetails: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: '$userData', // Flatten userData array
        },
        {
          $unwind: '$offeringData', // Flatten offeringData array
        },
        {
          $addFields: {
            sortOrder: {
              $cond: {
                if: { $eq: ['$status', 'APPROVED'] }, // If status is "approved"
                then: 1, // Assign sort value 1
                else: 0, // Otherwise assign sort value 0
              },
            },
          },
        },
        {
          $sort: {
            createdAt: -1, // Sort by creation date
          },
        },
        { $skip: skip }, // Pagination: skip based on page and limit
        { $limit: limit }, // Pagination: limit the number of results
        {
          $project: {
            name: '$userData.name',
            email: '$userData.email',
            userImage: '$userData.userImage',
            onchainID: '$userData.onchainID',
            countryCode: '$userData.countryCode',
            userId: '$userData._id',
            tokenTicker: '$offeringData.projectDetails.tokenTicker',
            walletAddress: '$userData.walletAddress',
            status: 1,
            txHash: 1,
            tokenAddress: '$offeringData.tokenAddress',
            taId: '$offeringData.projectDetails.taId',
            taStatus: {
              $ifNull: ['$taStatus', 'NOT_STARTED'],
            },
            createdAt: 1,
          },
        },
      ];

      // Count the total number of matching documents
      const totalCount = await whitelistSchema.aggregate([{ $match: searchQuery }, { $count: 'total' }]);

      const totalRecords = totalCount.length > 0 ? totalCount[0].total : 0;
      const totalPages = Math.ceil(totalRecords / limit);

      // Fetch the paginated results
      const ans = await whitelistSchema.aggregate(pipeline);
      const tokenAddress = ans[0]?.tokenAddress; // Assuming all objects have the same tokenAddress

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.COMMON.RECORD_FETCH,
        data: {
          tokenAddress,
          whitelist: ans,
          currentPage: page,
          totalPages,
          totalCount: totalRecords,
          nextPage: page < totalPages ? page + 1 : null,
          previousPage: page > 1 ? page - 1 : null,
        },
      };
    } catch (error) {
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };
  singleOfferingWhitelistReportCsv = async (searchDetails: any, pagination: IPagination): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 } } = pagination;
      const skip = (page - 1) * limit;

      // Extract the offeringId and the date filters
      const offeringId = searchDetails?.offeringId;
      const startDate = searchDetails?.startDate;
      const endDate = searchDetails?.endDate;
      // Build the search query (only date-based filtering)
      const searchQuery: FilterQuery<any> = {
        offeringId: new mongoose.Types.ObjectId(offeringId),
      };

      // Apply date filters if provided
      if (startDate && endDate) {
        searchQuery.createdAt = {
          $gte: new Date(startDate),
          $lte: new Date(endDate),
        };
      } else if (startDate) {
        searchQuery.createdAt = { $gte: new Date(startDate) };
      } else if (endDate) {
        searchQuery.createdAt = { $lte: new Date(endDate) };
      }

      // Define the aggregation pipeline
      const pipeline: any[] = [
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'userData',
            pipeline: [
              {
                $project: {
                  _id: 0,
                  name: 1,
                  email: 1,
                  projectDetails: 1,
                  walletAddress: { $arrayElemAt: ['$wallets.address', 0] },
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'offerings',
            localField: 'offeringId',
            foreignField: '_id',
            as: 'offeringData',
            pipeline: [
              {
                $project: {
                  _id: 0,
                  tokenAddress: 1, // Ensure tokenAddress is included here
                  projectDetails: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: '$userData',
        },
        {
          $unwind: '$offeringData',
        },
        {
          $match: searchQuery, // Apply the date filters here
        },
        {
          $sort: sort,
        },
        {
          $skip: skip,
        },
        {
          $limit: limit,
        },
        {
          $project: {
            name: '$userData.name',
            email: '$userData.email',
            tokenTicker: '$offeringData.projectDetails.tokenTicker',
            walletAddress: '$userData.walletAddress',
            status: 1, // Include the status field from the main document
            tokenAddress: '$offeringData.tokenAddress',
            taStatus: {
              $ifNull: ['$taStatus', 'NOT_STARTED'], // Default taStatus to "NOT_STARTED" if missing
            },
            createdAt: 1, // Include the createdAt field from the main document
          },
        },
      ];

      // Count the total number of matching documents
      const totalCount = await whitelistSchema.aggregate([{ $match: searchQuery }, { $count: 'total' }]);

      const totalRecords = totalCount.length > 0 ? totalCount[0].total : 0;
      const totalPages = Math.ceil(totalRecords / limit);

      // Fetch the paginated results
      const ans = await whitelistSchema.aggregate(pipeline);

      // Extract tokenAddress (assuming all records have the same tokenAddress)
      const tokenAddress = ans[0]?.tokenAddress;

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.COMMON.RECORD_FETCH,
        data: {
          tokenAddress,
          whitelist: ans,
          currentPage: page,
          totalPages,
          totalCount: totalRecords,
          nextPage: page < totalPages ? page + 1 : null,
          previousPage: page > 1 ? page - 1 : null,
        },
      };
    } catch (error) {
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  offeringReport = async (searchDetails: any): Promise<PromiseResolve> => {
    try {
      const { offeringId, period, userId } = searchDetails;
      // Validate offeringId
      if (!mongoose.Types.ObjectId.isValid(offeringId)) {
        throw new CustomError('Invalid Offering ID', RESPONSES.BAD_REQUEST);
      }

      // Fetch the offering
      const offering = await offeringSchema.findOne(
        { _id: offeringId, 'projectDetails.taId': userId },
        { _id: 1, 'projectDetails.taId': 1 }, // Fetch only required fields
      );

      if (!offering) {
        throw new CustomError('You are not authorized for this offering', RESPONSES.BAD_REQUEST);
      }

      const { startDate, endDate } = CommonHelper.getDateRange(period);

      let groupBy: any;
      let timeFrames: any[] = [];
      if (['1Y', '2Y', '3Y'].includes(period)) {
        groupBy = { year: { $year: '$createdAt' }, month: { $month: '$createdAt' } };
        timeFrames = Array.from({ length: 12 }, (_, i) => ({
          month: i + 1,
          groupStartTime: moment.unix(startDate).startOf('month').add(i, 'months').toISOString(),
          groupEndTime: moment
            .unix(startDate)
            .startOf('month')
            .add(i + 1, 'months')
            .subtract(1, 'second')
            .toISOString(),
        }));
      } else if (['1M', '15D', '7D'].includes(period)) {
        const daysInRange = moment.unix(endDate).diff(moment.unix(startDate), 'days') + 1;
        groupBy = { year: { $year: '$createdAt' }, month: { $month: '$createdAt' }, day: { $dayOfMonth: '$createdAt' } };
        timeFrames = Array.from({ length: daysInRange }, (_, i) => {
          const date = moment.unix(startDate).add(i, 'days');
          return { day: date.date(), month: date.month() + 1, groupStartTime: date.startOf('day').toISOString(), groupEndTime: date.endOf('day').toISOString() };
        });
      } else if (period === '1D') {
        groupBy = { year: { $year: '$createdAt' }, month: { $month: '$createdAt' }, day: { $dayOfMonth: '$createdAt' }, hour: { $hour: '$createdAt' } };
        timeFrames = Array.from({ length: 24 }, (_, i) => ({
          hour: i,
          groupStartTime: moment.unix(startDate).startOf('day').add(i, 'hours').toISOString(),
          groupEndTime: moment
            .unix(startDate)
            .startOf('day')
            .add(i + 1, 'hours')
            .subtract(1, 'second')
            .toISOString(),
        }));
      } else {
        throw new CustomError('Invalid period specified', RESPONSES.BAD_REQUEST);
      }

      // Treat offeringId as an array
      const offeringIdArray = [offeringId]; // Convert offeringId to an array of one element

      const pipeline: any = [
        {
          $facet: {
            // Main data pipeline
            mainData: [
              {
                $match: {
                  offeringId: { $in: offeringIdArray.map((id: any) => new Types.ObjectId(id)) }, // Use offeringIdArray here
                  createdAt: { $gte: new Date(startDate * 1000), $lte: new Date(endDate * 1000) },
                  status: {
                    $in: ['MINTED'],
                  },
                },
              },
              {
                $addFields: {
                  quantity: { $toDecimal: '$quantity' },
                  amount: { $toDecimal: '$amount' },
                  feesInPercentage: { $toDecimal: '$feesInPercentage' },
                  price: { $toDecimal: '$price' },
                  wap: { $toDecimal: '$wap' },
                },
              },
              {
                $group: {
                  _id: groupBy,
                  offeringId: { $first: '$offeringId' },
                  currentQuantity: {
                    $sum: {
                      $switch: {
                        branches: [{ case: { $eq: ['$status', 'MINTED'] }, then: '$quantity' }],
                        default: 0,
                      },
                    },
                  },
                  totalAmount: {
                    $sum: {
                      $toDouble: {
                        $switch: {
                          branches: [{ case: { $eq: ['$status', 'MINTED'] }, then: '$amount' }],
                          default: 0,
                        },
                      },
                    },
                  },
                },
              },
              { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1, '_id.hour': 1 } },
            ],

            // Total Before Start Date pipeline
            totalBeforeStartDate: [
              {
                $match: {
                  offeringId: { $in: offeringIdArray.map((id: any) => new Types.ObjectId(id)) }, // Use offeringIdArray here
                  createdAt: { $lt: new Date(startDate * 1000) }, // This is the condition
                  status: {
                    $in: ['MINTED'],
                  },
                },
              },
              {
                $addFields: {
                  quantity: { $toDecimal: '$quantity' },
                  amount: { $toDecimal: '$amount' },
                  feesInPercentage: { $toDecimal: '$feesInPercentage' },
                  currentPrice: { $toDecimal: '$currentPrice' },
                  wap: { $toDecimal: '$wap' },
                },
              },
              {
                $group: {
                  _id: null,
                  currentQuantityBeforeStartDate: {
                    $sum: {
                      $switch: {
                        branches: [{ case: { $eq: ['$status', 'MINTED'] }, then: '$quantity' }],
                        default: 0,
                      },
                    },
                  },
                  totalAmountBeforeStartDate: {
                    $sum: {
                      $toDouble: {
                        $switch: {
                          branches: [{ case: { $eq: ['$status', 'MINTED'] }, then: '$amount' }],
                          default: 0,
                        },
                      },
                    },
                  },
                },
              },
            ],
          },
        },
        // Unwind totalBeforeStartDate to make it consistent
        {
          $unwind: {
            path: '$totalBeforeStartDate',
            preserveNullAndEmptyArrays: true, // Ensures null values are preserved
          },
        },
        {
          $project: {
            mainData: 1,
            cumulativeBalanceBeforeStartDate: {
              $cond: {
                if: { $eq: [{ $type: '$totalBeforeStartDate.totalAmountBeforeStartDate' }, 'string'] },
                then: { $toDouble: '$totalBeforeStartDate.totalAmountBeforeStartDate' }, // Ensure number conversion
                else: '$totalBeforeStartDate.totalAmountBeforeStartDate',
              },
            },
          },
        },
      ];

      const results = await OrderSchema.aggregate(pipeline);
      const { mainData, cumulativeBalanceBeforeStartDate } = results[0];
      const formattedResult = offeringIdArray.map((id: any) => {
        const groupedResults = mainData.filter((item: any) => item.offeringId.toString() === id);
        const chart: any = timeFrames.map((timeFrame) => {
          const match = groupedResults.find((item: any) => {
            const group = item._id;
            if (['1Y', '2Y', '3Y'].includes(period)) {
              return group.month === timeFrame.month;
            }
            if (['1M', '15D', '7D'].includes(period)) {
              return group.day === timeFrame.day && group.month === timeFrame.month;
            }
            if (period === '1D') {
              return group.hour === timeFrame.hour;
            }
            return false;
          });

          return {
            totalAmount: match ? match.totalAmount : 0,
            groupStartTime: timeFrame.groupStartTime,
            groupEndTime: timeFrame.groupEndTime,
            ...(period === '1Y' && { month: timeFrame.month }),
            ...(period === '1D' && { hour: timeFrame.hour }),
            ...(['1M', '15D', '7D'].includes(period) && { day: timeFrame.day, month: timeFrame.month }),
          };
        });

        const updatedChart: any = chart.reduce((acc: any[], item: any, index: number) => {
          const previousBalance = acc.length > 0 ? acc[acc.length - 1].cumulativeBalance || 0 : 0;
          if (index === 0) {
            item = {
              ...item,
              cumulativeBalance: cumulativeBalanceBeforeStartDate ? calculate('add', cumulativeBalanceBeforeStartDate, item.totalAmount) : item.totalAmount,
            };
          } else if (item.totalAmount > 0) {
            item = { ...item, cumulativeBalance: calculate('add', previousBalance, item.totalAmount) };
          } else if (previousBalance > 0) {
            item = { ...item, cumulativeBalance: previousBalance };
          } else {
            item = { ...item, cumulativeBalance: 0 };
          }

          // Convert cumulativeBalance to a number before pushing
          acc.push({ ...item, cumulativeBalance: Number(item.cumulativeBalance) });

          return acc;
        }, []);

        return { offeringId: id, chart: updatedChart };
      });

      return { status: RESPONSES.SUCCESS, error: false, message: 'Records fetched successfully', data: formattedResult };
    } catch (error) {
      logger.error(error);
      return { status: 500, error: true, message: error.message || 'Internal Server Error' };
    }
  };

  dashboardList = async (filters: any, projection: any[], pagination: IPagination): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 }, search: escapedSearch } = pagination;
      const skip = (page - 1) * limit;
      const search = escapedSearch ? CommonHelper.escapeRegex(escapedSearch) : null;
      let query: FilterQuery<any> = {};

      const addFilter = (key: keyof IOffering, value: any) => {
        if (typeof value !== 'undefined') query[key] = value;
      };
      query.status = { $ne: 'REJECTED' };
      query.status = { $ne: 'IN_PROGRESS' };
      if (search) {
        query.$or = [{ title: { $regex: search, $options: 'i' } }, { subTitle: { $regex: search, $options: 'i' } }];
      }

      addFilter('isActive', true);
      addFilter('status', filters.status);
      //  addFilter('userId', filters.userId);
      query = {
        ...query, // Existing query conditions
        'projectDetails.taId': filters.userId, // Match the taId with filters.userId
      };

      const totalCount = await offeringSchema.countDocuments(query).exec();
      const pendingCount = await offeringSchema
        .countDocuments({
          ...query, // Include all the existing filters in the query
          status: 'PENDING', // Ensure the status is 'PENDING'
        })
        .exec();
      const users = await offeringSchema.find(query).select(projection).sort(sort).skip(skip).exec();
      const offeringIds = users.map((offering) => offering._id);

      // Log the extracted offering IDs for debugging

      // Step 2: Query the `order` schema and count how many times each `userId` appears for those offering IDs
      const orderCountByUser = await OrderSchema.aggregate([
        {
          $match: {
            offeringId: { $in: offeringIds }, // Match only those orders related to the offerings from the above list
            status: 'MINTED',
          },
        },
        {
          $group: {
            _id: '$userId', // Group by userId
            offeringIds: { $addToSet: '$offeringId' }, // Collect distinct offeringIds for each user
          },
        },
        {
          $project: {
            _id: 1,
            count: { $size: '$offeringIds' }, // Count the number of unique offerings for each user
          },
        },
      ]);
      const whiteListCountByUser = await whitelistSchema.aggregate([
        {
          $match: {
            offeringId: { $in: offeringIds }, // Match only those orders related to the offerings from the above list
          },
        },
        {
          $group: {
            _id: '$userId', // Group by userId
            offeringIds: { $addToSet: '$offeringId' }, // Collect distinct offeringIds for each user
          },
        },
        {
          $project: {
            _id: 1,
            count: { $size: '$offeringIds' }, // Count the number of unique offerings for each user
          },
        },
      ]);

      const pendingwhiteListCountByUser = await whitelistSchema.aggregate([
        {
          $match: {
            offeringId: { $in: offeringIds }, // Match only those orders related to the offerings from the above list
            status: 'PENDING',
          },
        },
        {
          $group: {
            _id: '$userId', // Group by userId
            offeringIds: { $addToSet: '$offeringId' }, // Collect distinct offeringIds for each user
          },
        },
        {
          $project: {
            _id: 1,
            count: { $size: '$offeringIds' }, // Count the number of unique offerings for each user
          },
        },
      ]);
      const transferRequest = await TransferRequest.aggregate([
        {
          $match: {
            offeringId: { $in: offeringIds }, // Filter relevant offerings
            isForceTransfer: true,
          },
        },
        {
          $group: {
            _id: '$offeringId', // Group by offeringId
            userCount: { $sum: 1 }, // Count occurrences of userId for each offering
          },
        },
        {
          $group: {
            _id: null, // Group everything together to compute grand total
            offerings: { $push: { offeringId: '$_id', userCount: '$userCount' } }, // Store individual offering counts
            totalUserCount: { $sum: '$userCount' }, // Compute total userCount
          },
        },
        {
          $project: {
            _id: 0, // Remove _id
            offerings: 1, // Keep offering-wise breakdown
            totalUserCount: 1, // Include grand total user count
          },
        },
      ]);

      const pendingTransferRequest = await TransferRequest.aggregate([
        {
          $match: {
            offeringId: { $in: offeringIds }, // Filter relevant offerings
            status: 'PENDING',
          },
        },
        {
          $group: {
            _id: '$offeringId', // Group by offeringId
            userCount: { $sum: 1 }, // Count occurrences of userId for each offering
          },
        },
        {
          $group: {
            _id: null, // Group everything together to compute grand total
            offerings: { $push: { offeringId: '$_id', userCount: '$userCount' } }, // Store individual offering counts
            totalUserCount: { $sum: '$userCount' }, // Compute total user count
          },
        },
        {
          $project: {
            _id: 0, // Remove _id
            offerings: 1, // Keep offering-wise breakdown
            totalUserCount: 1, // Include grand total user count
          },
        },
      ]);

      const totalPages = Math.ceil(totalCount / limit);

      return {
        data: {
          offering: users,
          currentPage: page,
          totalInvestors: orderCountByUser.length,
          walletWhiteListCount: whiteListCountByUser.length,
          pendingWalletWhiteListCount: pendingwhiteListCountByUser.length,
          forceTransferRequt: transferRequest?.[0]?.totalUserCount || 0,
          pendingforceTransferRequtCount: pendingTransferRequest?.[0]?.totalUserCount || 0,
          totalPages,
          totalCount,
          pendingCount,
          nextPage: page < totalPages ? page + 1 : null,
          previousPage: page > 1 ? page - 1 : null,
        },
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  /**
   * @param {IUserModel} body
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */

  createUser = async (body: IUserModel): Promise<PromiseResolve> => {
    try {
      const createQuery: IUserModel = await userSchema.create(body);
      if (createQuery) {
        return {
          status: RESPONSES.CREATED,
          error: false,
          message: RES_MSG.SUCCESS_MSG.REGISTER_SUCCESS,
          data: createQuery,
        };
      }
      return {
        status: RESPONSES.BAD_REQUEST,
        error: true,
        message: RES_MSG.COMMON.SOMETHING_WRONG,
      };
    } catch (error) {
      logger.error(error, 'createUser error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  /**
   * @param {IUpdateUserModel} filters
   * @param {any[]} projection
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */

  fetchUserList = async (filters: IUpdateUserModel | any, projection: any[], pagination: IPagination): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 }, search: escapedSearch } = pagination;
      const skip = (page - 1) * limit;
      const search = escapedSearch ? CommonHelper.escapeRegex(escapedSearch) : null;
      const query: FilterQuery<any> = {};
      const addFilter = (key: keyof IUpdateUserModel, value: any) => {
        if (typeof value !== 'undefined') query[key] = value;
      };

      // const regexPattern = new RegExp(`${search}$`, 'i'); // Matches the end

      if (search) {
        /* query.$or = [
            { email: search },
            { name: search },
            { mobile: search },
        ]; */
        query.$or = [{ email: { $regex: search, $options: 'i' } }, { name: { $regex: search, $options: 'i' } }, { mobile: { $regex: search, $options: 'i' } }];
      }

      query.email = {
        $ne: process.env.EMAIL,
      };
      query.userType = {
        $ne: UserType.Admin,
      };

      addFilter('isActive', filters.isActive);
      addFilter('isEmailVerify', filters.isEmailVerify);
      addFilter('isMobileVerify', filters.isMobileVerify);
      addFilter('isSocialMedia', filters.isSocialMedia);
      addFilter('isDeleted', filters.isDeleted);
      addFilter('kycStatus', filters.kycStatus);
      addFilter('userType', filters.userType);
      addFilter('countryCode', filters.countryCode);
      addFilter('isKyc', filters.isKyc);
      addFilter('isIssuer', filters.isIssuer);
      addFilter('issuerStatus', filters.issuerStatus);

      const totalCount = await userSchema.countDocuments(query).exec();

      const users = await userSchema.find(query).select(projection).sort(sort).skip(skip).limit(limit).exec();

      const totalPages = Math.ceil(totalCount / limit);

      return {
        data: {
          user: users,
          currentPage: page,
          totalPages,
          totalCount,
          nextPage: page < totalPages ? page + 1 : null,
          previousPage: page > 1 ? page - 1 : null,
        },
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      throw new Error(`Error fetching users: ${error.message}`);
    }
  };

  fetchUserListcsv = async (filters: IUpdateUserModel, projection: any[]): Promise<PromiseResolve> => {
    try {
      const query: FilterQuery<any> = {};
      const addFilter = (key: keyof IUpdateUserModel, value: any) => {
        if (typeof value !== 'undefined') query[key] = value;
      };

      query.email = {
        $ne: process.env.EMAIL,
      };
      query.userType = {
        $ne: UserType.Admin,
      };

      addFilter('createdAt', filters.createdAt);
      addFilter('isKyc', filters.isKyc);
      addFilter('isIssuer', filters.isIssuer);
      addFilter('issuerStatus', filters.issuerStatus);

      const totalCount = await userSchema.countDocuments(query).exec();

      const users = await userSchema.find(query).select(projection).sort({ createdAt: -1 });

      return {
        data: {
          user: users,
          totalCount,
        },

        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      throw new Error(`Error fetching users: ${error.message}`);
    }
  };

  /**
   * @param {IUpdateUserModel} filters
   * @param {any[]} projection
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  fetchUserListWithOfferings = async (filters: IUpdateUserModel, projection: any[], pagination: IPagination): Promise<PromiseResolve> => {
    try {
      const { page, limit, sort, search: escapedSearch } = pagination;
      const skip = (page - 1) * limit;
      const search = escapedSearch ? CommonHelper.escapeRegex(escapedSearch) : null;
      const searchQuery: FilterQuery<any> = {};
      const addFilter = (key: keyof IUpdateUserModel, value: any) => {
        if (typeof value !== 'undefined') searchQuery[key] = value;
      };
      if (search) {
        searchQuery.$or = [{ name: { $regex: search, $options: 'i' } }, { email: { $regex: search, $options: 'i' } }];
      }
      const projectionObject = projection.reduce((acc: any, field: string) => ({ ...acc, [field]: 1 }), {});

      searchQuery.userType = {
        $ne: UserType.Admin,
      };
      addFilter('isKyc', filters.isKyc);
      addFilter('isIssuer', filters.isIssuer);
      addFilter('isActive', true);
      addFilter('isEmailVerify', true);
      addFilter('issuerStatus', filters.issuerStatus);

      const userWithOfferings = await userSchema.aggregate([
        {
          $match: searchQuery,
        },
        {
          $lookup: {
            from: 'offerings',
            localField: '_id',
            foreignField: 'userId',
            as: 'offerings',
          },
        },
        {
          $addFields: {
            offeringStatusCounts: {
              PENDING: {
                $size: {
                  $filter: {
                    input: '$offerings',
                    as: 'offering',
                    cond: { $eq: ['$$offering.status', 'PENDING'] },
                  },
                },
              },
              REJECTED: {
                $size: {
                  $filter: {
                    input: '$offerings',
                    as: 'offering',
                    cond: { $eq: ['$$offering.status', 'REJECTED'] },
                  },
                },
              },
              APPROVED: {
                $size: {
                  $filter: {
                    input: '$offerings',
                    as: 'offering',
                    cond: { $eq: ['$$offering.status', 'APPROVED'] },
                  },
                },
              },
              RESUBMIT: {
                $size: {
                  $filter: {
                    input: '$offerings',
                    as: 'offering',
                    cond: { $eq: ['$$offering.status', 'RESUBMIT'] },
                  },
                },
              },
            },
          },
        },
        {
          $project: {
            ...projectionObject,
            // offerings: 0,
          },
        },
        {
          $sort: sort ? sort : { createdAt: -1 },
        },
        {
          $skip: skip,
        },
        {
          $limit: limit,
        },
      ]);

      const totalCount = await userSchema.countDocuments(searchQuery).exec();
      const totalPages = Math.ceil(totalCount / limit);

      return {
        data: {
          users: userWithOfferings,
          currentPage: page,
          totalPages,
          totalCount,
          nextPage: page < totalPages ? page + 1 : null,
          previousPage: page > 1 ? page - 1 : null,
        },
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      logger.error(error, 'fetchUserListWithOfferings error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  fetchUserListWithOfferingscsv = async (filters: IUpdateUserModel, projection: any[], pagination: IPagination): Promise<PromiseResolve> => {
    try {
      const searchQuery: FilterQuery<any> = {};
      const addFilter = (key: keyof IUpdateUserModel, value: any) => {
        if (typeof value !== 'undefined') searchQuery[key] = value;
      };
      const projectionObject = projection.reduce((acc: any, field: string) => ({ ...acc, [field]: 1 }), {});

      searchQuery.userType = { $ne: UserType.Admin };
      addFilter('isKyc', filters.isKyc);
      addFilter('isIssuer', filters.isIssuer);
      addFilter('isActive', true);
      addFilter('createdAt', filters.createdAt);
      addFilter('isEmailVerify', true);

      const userWithOfferings = await userSchema.aggregate([
        { $match: searchQuery },
        { $sort: { createdAt: -1 } },
        {
          $lookup: {
            from: 'offerings',
            localField: '_id',
            foreignField: 'userId',
            as: 'offerings',
          },
        },
        {
          $addFields: {
            offeringStatusCounts: {
              PENDING: {
                $size: {
                  $filter: {
                    input: '$offerings',
                    as: 'offering',
                    cond: { $eq: ['$$offering.status', 'PENDING'] },
                  },
                },
              },
              REJECTED: {
                $size: {
                  $filter: {
                    input: '$offerings',
                    as: 'offering',
                    cond: { $eq: ['$$offering.status', 'REJECTED'] },
                  },
                },
              },
              APPROVED: {
                $size: {
                  $filter: {
                    input: '$offerings',
                    as: 'offering',
                    cond: { $eq: ['$$offering.status', 'APPROVED'] },
                  },
                },
              },
              RESUBMIT: {
                $size: {
                  $filter: {
                    input: '$offerings',
                    as: 'offering',
                    cond: { $eq: ['$$offering.status', 'RESUBMIT'] },
                  },
                },
              },
            },
          },
        },
        {
          $project: {
            ...projectionObject,
          },
        },
      ]);
      const totalCount = await userSchema.countDocuments(searchQuery).exec();

      return {
        data: {
          users: userWithOfferings,
          totalCount,
        },
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      logger.error(error, 'fetchUserListWithOfferings error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  /**
   * @param {string} _id
   * @param {string} password
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  addPassword = async (_id: string, password: string): Promise<PromiseResolve> => {
    try {
      const userPasswords = await passwordSchema.findOne({ _id });

      // If the user has a password history
      if (userPasswords) {
        // Check if the password is already used
        if (userPasswords.passwords.includes(password)) {
          return {
            status: RESPONSES.BAD_REQUEST,
            error: true,
            message: RES_MSG.ERROR_MSG.PASSWORD_RECENTLY_USED,
          };
        }

        // Add the new password and manage history length
        userPasswords.passwords.push(password);
        if (userPasswords.passwords.length > maxPasswordHistory) {
          userPasswords.passwords.shift(); // Remove oldest password
        }

        const updatedQuery = await userPasswords.save();
        return {
          status: RESPONSES.CREATED,
          error: false,
          message: RES_MSG.SUCCESS_MSG.REGISTER_SUCCESS,
          data: updatedQuery,
        };
      }

      // If no password history exists, create a new entry
      const createQuery = await passwordSchema.create({
        _id,
        passwords: [password],
      });

      return {
        status: RESPONSES.CREATED,
        error: false,
        message: RES_MSG.SUCCESS_MSG.REGISTER_SUCCESS,
        data: createQuery,
      };
    } catch (error: any) {
      logger.error(error, 'addPassword error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  /**
   * @param {PasswordModel} searchDetails
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  fetchRecentPasswords = async (_id: string): Promise<PromiseResolve> => {
    try {
      const recentPasswords: PasswordModel[] = await passwordSchema.findOne({
        _id,
      });

      if (recentPasswords) {
        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
          data: recentPasswords,
        };
      }
      return {
        status: RESPONSES.NOTFOUND,
        error: true,
        message: RES_MSG.COMMON.NO_USER,
      };
    } catch (error) {
      logger.error(error, 'fetchRecentPasswords error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };
}

export default new UserService();
