import mongoose, { Schema, Document, Types } from 'mongoose';
import { transferAgentStatus } from '../../../utils/common.interface';

export interface ITransferAgent extends Document {
  name: string;
  email: string;
  password: string;
  noOfOfferings: number;
  status: transferAgentStatus;
  isActive: boolean;
  twoFASecret?: string;
  is2FAActive?: boolean;
  isOtpActive?: boolean;
  walletAddress: string;
  offeringId: Types.ObjectId[]; // Array of ObjectIds to store offering IDs
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IUpdateTransferAgent {
  name: string;
  email: string;
  password: string;
  noOfOfferings: number;
  status: transferAgentStatus;
  isActive: boolean;
  is2FAActive?: boolean;
  isOtpActive?: boolean;
  twoFASecret?: string;
  walletAddress: string;
  offeringId: Types.ObjectId[]; // Array of ObjectIds to store offering IDs
  createdAt?: Date;
  updatedAt?: Date;
}

const transferAgent: Schema<ITransferAgent> = new Schema(
  {
    name: { type: String, required: true },
    email: { type: String, required: true, unique: true },
    password: {
      type: String,
      required: false,
    },
    isOtpActive: {
      type: Boolean,
      default: true,
    },
    is2FAActive: {
      type: Boolean,
      default: false,
    },
    twoFASecret: {
      type: String,
      default: null,
    },
    noOfOfferings: { type: Number, required: false, default: 0 },
    walletAddress: { type: String, required: false, unique: true },
    status: { type: String, enum: transferAgentStatus, default: transferAgentStatus.REQUESTSENT, required: true },
    isActive: { type: Boolean, default: true, required: true },
    offeringId: [{ type: String, ref: 'Offering', required: false, default: [] }], // Array of ObjectIds, referencing the "Offering" model
  },
  { versionKey: false, timestamps: true },
);

const transferAgentSchema = mongoose.model<ITransferAgent>('ITransferAgent', transferAgent);

export { transferAgentSchema };
