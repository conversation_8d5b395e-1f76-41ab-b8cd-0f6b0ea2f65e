import mongoose, { Schema, Document, Types } from 'mongoose';
import { orderStatusEnum, paymentTypeEnum } from '../../../utils/common.interface';

export interface IOrder extends Document {
  userId?: Types.ObjectId | string;
  offeringId?: Types.ObjectId | string;
  walletAddress?: string;
  amount?: number;
  status?: orderStatusEnum;
  isMinted?: boolean;
  feesInPercentage?: number;
  isSettled?: boolean;
  isFreezed?: boolean;
  price?: number;
  adminFeeAmount?: number;
  orderReceived?: Date;
  orderMinted?: Date;
  quantity?: number;
  txHash?: string;
  mintTxHash?: string;
  emailSent?: boolean;
  mintEmailSent?: boolean;
  paymentMethod?: paymentTypeEnum;
  orderType: string;
  principleAmount?: number;
  wap?: number;
  profit?: number;
  reason?: string;
}

export interface IUpdateOrder {
  _id?: string;
  userId?: Types.ObjectId | string;
  offeringId?: Types.ObjectId | string;
  amount?: number;
  price?: number;
  adminFeeAmount?: number;
  status?: orderStatusEnum;
  isMinted?: boolean;
  isFreezed?: boolean;
  feesInPercentage?: number;
  isSettled?: boolean;
  quantity?: number;
  orderReceived?: Date;
  orderMinted?: Date;
  txHash?: string;
  mintTxHash?: string;
  emailSent?: boolean;
  mintEmailSent?: boolean;
  paymentMethod?: paymentTypeEnum;
  orderType?: string;
  principleAmount?: number;
  wap?: number;
  profit?: number;
  reason?: string;
}

const order: Schema<IOrder> = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, ref: 'users', required: true },
    offeringId: { type: Schema.Types.ObjectId, ref: 'offerings', required: true },
    walletAddress: { type: String, required: false },
    amount: {
      type: Number,
      required: true,
      default: 0,
    },
    isMinted: {
      type: Boolean,
      required: true,
      default: false,
    },
    isSettled: {
      type: Boolean,
      required: true,
      default: false,
    },
    isFreezed: {
      type: Boolean,
      required: true,
      default: false,
    },
    feesInPercentage: {
      type: Number,
      required: true,
      default: 0,
    },
    quantity: {
      type: Number,
      required: true,
      default: 0,
    },
    price: {
      type: Number,
      required: true,
      default: 0,
    },
    adminFeeAmount: {
      type: Number,
      required: false,
      default: 0,
    },
    status: {
      type: String,
      enum: Object.values(orderStatusEnum),
      default: orderStatusEnum.PENDING,
    },
    emailSent: { type: Boolean, default: false },
    mintEmailSent: { type: Boolean, default: false },
    orderReceived: { type: Date, required: false },
    orderMinted: { type: Date, required: false },
    paymentMethod: {
      type: String,
      enum: Object.values(paymentTypeEnum),
      default: paymentTypeEnum.USDT,
    },
    txHash: { type: String, required: false, unique: false, sparse: true, default: null },
    mintTxHash: { type: String, required: false, unique: false, sparse: true, default: null },
    orderType: {
      type: String,
      required: false,
      default: orderStatusEnum.MINTED,
    },
    principleAmount: {
      type: Number,
      required: false,
      default: 0,
    },
    wap: {
      type: Number,
      required: false,
      default: 0,
    },
    profit: {
      type: Number,
      required: false,
      default: 0,
    },
    reason: {
      type: String,
      required: false,
    },
  },

  {
    timestamps: true,
    versionKey: false,
  },
);

const OrderSchema = mongoose.model<IOrder>('order', order);
export { OrderSchema };
