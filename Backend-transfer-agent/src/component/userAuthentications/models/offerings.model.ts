import mongoose, { Schema, Document, Types } from 'mongoose';
import { offeringStatusEnum } from '../../../utils/common.interface';

interface IAuthorizedCountry {
  name: string;
  isoCode: string;
  countryCode: string;
}

export interface IOffering extends Document {
  _id: Types.ObjectId | string;
  userId: Types.ObjectId;
  overview: {
    title: string;
    subTitle?: string;
    description: string;
    entityName: string;
    entityType: string;
    webUrl?: string;
    lineOfBusiness: string;
    sourceOfFunds: string;
    location: string;
    companyDescription: string;
    icon?: string;
    cover?: string;
    logo: string;
  };
  projectDetails: {
    assetType: string;
    blockChainType: string;
    offeringType: string;
    tokenStandard: string;
    offeringName: string;
    CUSIP: string;
    isAuthorized: boolean;
    authorizedCountries: IAuthorizedCountry[];
    startDate: Date;
    endDate: Date;
    minInvestment: number;
    maxInvestment: number;
    assetName: string;
    tokenTicker: string;
    tokenSupply: number;
    tokenDecimals: number;
    lockupMonths: number;
    holdTime: Date;
    maxTokenHolding: number;
    navLaunchPrice: number;
    latestNav: number;
    // aumSize: number;
    // spvValuation: number;
    propertyType: string;
    isTransferAgent: boolean;
    taId?: string;
    issuerId: string;
    issuerWallet: string;
    customFields: Array<{
      label: string;
      type: string;
      value: string;
    }>;
    isPrivate?: boolean;
    offeringMembers?: string[];
    propertySubtype?: string;
    yearBuilt?: number;
    lotSize?: number;
    occupancy?: number;
    projectedYield?: number;
    launchValuation?: number;
    previousValuation?: number;
    deRatio?: number;
  };
  documents: {
    assetType?: string;
    eSign?: string;
    pitchDeck?: string;
    confidentialInformationMemorendum?: string;
    landRegistration?: string;
    titleDocs?: string;
    bankApproval?: string;
    encumbranceCertificate?: string;
    propertyTaxReceipt?: string;
    articlesOfAssociation?: string;
    operatingAgreement?: string;
    taxAssignmentLetter?: string;
    certificateOfRegistration?: string;
    registerOfManagers?: string;
    customDocs: Array<{
      docsLabel: string;
      value: string;
    }>;
  };
  team: Array<{
    name: string;
    title: string;
    summary?: string;
    email?: string;
    url?: string;
    linkedInUrl?: string;
    twitterUrl?: string;
  }>;
  isFinalSubmission?: boolean;
  currentStep: number;
  isTokenDeploy?: boolean;
  isFundDeploy?: boolean;
  iserc20?: boolean;
  tokenAddress?: string;
  erc20Address?: string;
  identityRegistry?: string;
  fundAddress?: string;
  fee?: {
    escrowFee?: number;
    wrapFee?: number;
    dividendFee?: number;
    redemptionFee?: number;
  };
  deployedDate?: Date;
  isActive: boolean;
  isDelete?: boolean;
  status?: offeringStatusEnum;
  createdBy?: Types.ObjectId;
  template_id: string;
  envelopeId: string;
  offeringFeeStatus?: boolean;
  reason?: string;
  proposalHoldingPercentage?: [string];
}

export interface IUpdateOffering {
  // tokenAddress: any;
  template_id?: string;
  isTokenDeploy?: boolean;
  isFundDeploy?: boolean;
  tokenAddress?: string;
  erc20Address?: string;
  fee?: {
    escrowFee?: number;
    wrapFee?: number;
    dividendFee?: number;
    redemptionFee?: number;
  };
  iserc20?: boolean;
  fundAddress?: string;
  _id?: Types.ObjectId | string;
  userId?: Types.ObjectId;
  overview?: {
    title?: string;
    subTitle?: string;
    description?: string;
    entityName?: string;
    entityType?: string;
    webUrl?: string;
    lineOfBusiness?: string;
    sourceOfFunds?: string;
    location?: string;
    companyDescription?: string;
    icon?: string;
    cover?: string;
    logo?: string;
  };
  projectDetails?: {
    assetType?: string;
    blockChainType?: string;
    offeringType?: string;
    tokenStandard?: string;
    offeringName?: string;
    CUSIP?: string;
    isAuthorized?: boolean;
    authorizedCountries?: IAuthorizedCountry[];
    startDate?: Date;
    endDate?: Date;
    minInvestment?: number;
    maxInvestment?: number;
    assetName?: string;
    tokenTicker?: string;
    tokenSupply?: number;
    tokenDecimals?: number;
    lockupMonths?: number;
    holdTime?: Date;
    maxTokenHolding?: number;
    navLaunchPrice?: number;
    latestNav?: number;
    // aumSize?: number;
    // spvValuation?: number;
    propertyType?: string;
    isTransferAgent?: boolean;
    taId?: string;
    issuerId?: string;
    issuerWallet?: string;
    customFields?: Array<{
      label?: string;
      type?: string;
      value?: string;
    }>;
    isPrivate?: boolean;
    isDelete?: boolean;
    offeringMembers?: string[];
    propertySubtype?: string;
    yearBuilt?: number;
    lotSize?: number;
    occupancy?: number;
    projectedYield?: number;
    launchValuation?: number;
    previousValuation?: number;
    deRatio?: number;
  };
  documents?: {
    assetType?: string;
    eSign?: string;
    pitchDeck?: string;
    confidentialInformationMemorendum?: string;
    landRegistration?: string;
    titleDocs?: string;
    bankApproval?: string;
    encumbranceCertificate?: string;
    propertyTaxReceipt?: string;
    articlesOfAssociation?: string;
    operatingAgreement?: string;
    taxAssignmentLetter?: string;
    certificateOfRegistration?: string;
    registerOfManagers?: string;
    customDocs?: Array<{
      docsLabel?: string;
      value?: string;
    }>;
  };
  team?: Array<{
    name?: string;
    title?: string;
    summary?: string;
    email?: string;
    url?: string;
    linkedInUrl?: string;
    twitterUrl?: string;
  }>;
  isFinalSubmission?: boolean;
  currentStep?: number;
  deployedDate?: Date;
  isActive?: boolean;
  isDelete?: boolean;
  status?: offeringStatusEnum;
  createdBy?: Types.ObjectId;
  offeringId?: Types.ObjectId;
  period?: string;
  offeringFeeStatus?: boolean;
  reason?: string;
  proposalHoldingPercentage?: [string];
}

const offerings: Schema<IOffering> = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, ref: 'users', required: true },
    overview: {
      type: {
        title: { type: String, required: true },
        subTitle: { type: String },
        description: { type: String, required: true },
        entityName: { type: String, required: true },
        entityType: { type: String, required: true },
        webUrl: { type: String },
        lineOfBusiness: { type: String, required: true },
        sourceOfFunds: { type: String, required: true },
        location: { type: String, required: true },
        companyDescription: { type: String, required: true },
        icon: { type: String },
        cover: { type: String },
        logo: { type: String, required: true },
      },
      _id: false,
    },
    projectDetails: {
      type: {
        assetType: { type: String, required: true },
        blockChainType: { type: String, required: true },
        offeringType: { type: String, required: true },
        tokenStandard: { type: String, required: true },
        offeringName: { type: String, required: true },
        CUSIP: {
          type: String,
          required: false,
          unique: true,
          sparse: true, // This allows multiple documents with null values
        },
        isAuthorized: { type: Boolean, required: false },
        authorizedCountries: {
          type: [
            {
              name: { type: String, required: false },
              isoCode: { type: String, required: false },
              countryCode: { type: String, required: false },
            },
          ],
          required: false,
          default: [],
          _id: false,
        },
        startDate: { type: Date, required: false },
        endDate: { type: Date, required: false },
        minInvestment: { type: Number, required: true },
        maxInvestment: { type: Number, required: true },
        assetName: { type: String, required: true },
        tokenTicker: {
          type: String,
          required: false,
          unique: true,
          sparse: true,
        },
        tokenSupply: { type: Number, required: true },
        tokenDecimals: { type: Number, required: true },
        lockupMonths: { type: Number, required: false },
        holdTime: { type: Date, required: true },
        maxTokenHolding: { type: Number, required: true },
        navLaunchPrice: { type: Number, required: false },
        latestNav: { type: Number, required: false },
        // aumSize: { type: Number, required: true },
        // spvValuation: { type: Number, required: true },
        isTransferAgent: { type: Boolean, required: true },
        taId: { type: String, required: false },
        issuerId: { type: String, required: true },
        issuerWallet: { type: String, required: true },
        isPrivate: { type: Boolean, required: true },
        offeringMembers: { type: [String], required: false, default: [] },
        customFields: {
          type: [
            {
              label: { type: String, required: true },
              type: { type: String, required: true },
              value: { type: String, required: true },
            },
          ],
          _id: false,
          default: undefined,
        },
        propertyType: { type: String, required: false },
        propertySubtype: { type: String, trim: false, required: false },
        yearBuilt: {
          type: Number,
          required: false,
          min: 1,
          max: 9999,
        },
        lotSize: { type: Number, required: false, min: 1 },
        occupancy: {
          type: Number,
          required: false,
          min: 0,
          max: 100,
        },
        projectedYield: {
          type: Number,
          required: true,
          min: 0,
          max: 100,
        },
        launchValuation: { type: Number, required: false, min: 0 },
        previousValuation: { type: Number, required: false, min: 0 },
        deRatio: { type: Number, required: false, min: 0 },
      },
      _id: false,
      required: false,
    },
    documents: {
      type: {
        eSign: { type: String, required: false },
        pitchDeck: { type: String, required: false },
        confidentialInformationMemorendum: { type: String, required: false },
        landRegistration: { type: String, required: false },
        titleDocs: { type: String, required: false },
        bankApproval: { type: String, required: false },
        encumbranceCertificate: { type: String, required: false },
        propertyTaxReceipt: { type: String, required: false },
        articlesOfAssociation: { type: String, required: false },
        operatingAgreement: { type: String, required: false },
        taxAssignmentLetter: { type: String, required: false },
        certificateOfRegistration: { type: String, required: false },
        registerOfManagers: { type: String, required: false },
        customDocs: {
          type: [
            {
              docsLabel: { type: String, required: true },
              value: { type: String, required: true },
            },
          ],
          _id: false,
          default: undefined,
        },
      },
      _id: false,
      required: false,
    },
    team: {
      type: [
        {
          name: { type: String, required: true },
          title: { type: String, required: true },
          summary: { type: String, required: false },
          email: { type: String, required: false },
          url: { type: String, required: false },
          linkedInUrl: { type: String, required: false },
          twitterUrl: { type: String, required: false },
        },
      ],
      _id: false,
      default: undefined,
      required: false,
    },
    template_id: { type: String, required: false },
    fee: {
      escrowFee: { type: Number, required: false, default: 0 },
      wrapFee: { type: Number, required: false, default: 0 },
      dividendFee: { type: Number, required: false, default: 0 },
      redemptionFee: { type: Number, required: false, default: 0 },
    },
    tokenAddress: { type: String, required: false },

    isTokenDeploy: { type: Boolean, required: false, default: false },
    erc20Address: { type: String, required: false },
    iserc20: { type: Boolean, required: false, default: false },
    isFundDeploy: { type: Boolean, required: false, default: false },
    identityRegistry: { type: String, required: false },
    fundAddress: { type: String, required: false },
    deployedDate: { type: Date, required: false },
    currentStep: { type: Number, required: true, default: 0 },
    isActive: { type: Boolean, required: true, default: true },
    isDelete: { type: Boolean, required: true, default: false },
    isFinalSubmission: { type: Boolean, required: true, default: false },
    status: {
      type: String,
      enum: Object.values(offeringStatusEnum),
      default: offeringStatusEnum.IN_PROGRESS,
    },
    offeringFeeStatus: { type: Boolean, required: true, default: false },
    proposalHoldingPercentage: { type: [String], required: false },
    createdBy: { type: Schema.Types.ObjectId, ref: 'users', required: true },
    // deployedDate: { type: Date, required: false },
    reason: { type: String, required: false },
  },
  { versionKey: false, timestamps: true },
);

const offeringSchema = mongoose.model<IOffering>('offerings', offerings);

export { offeringSchema };
