import { FilterQuery } from 'mongoose';
import { IPagination, PromiseResolve } from '../../utils/common.interface';
import { IUserModel, IUpdateUserModel } from './models/user.model';
import { IUpdateOrder } from './models/order.model';
import { IUpdateWhitelist } from './models/whitelist.model';

export interface IUserService {
  /**
   * @param {IUserModel} body
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  createUser(body: any): Promise<PromiseResolve>;

  /**
   * @param {IUserModel} searchDetails
   * @param {IUserModel} fields
   * @param {IUserModel} excludeFields
   * @param {Boolean} isKyc
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  fetchTaDetails(searchDetails: FilterQuery<IUserModel>, fields?: string[], excludeFields?: string[], isKyc?: boolean): Promise<PromiseResolve>;

  /**
   * @param {IUserModel} searchDetails
   * @param {IUserModel} fields
   * @param {IUserModel} excludeFields
   * @param {Boolean} isKyc
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  fetchUserDetails(searchDetails: FilterQuery<IUserModel>, fields?: string[], excludeFields?: string[], isKyc?: boolean): Promise<PromiseResolve>;

  /**
   * @param {IUpdateUserModel} searchDetails
   * @param {Array} projection
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */

  fetchUserList(searchDetails: FilterQuery<IUpdateUserModel>, projection?: string[], pagination?: IPagination): Promise<PromiseResolve>;

  fetchUserListcsv(searchDetails: FilterQuery<IUpdateUserModel>, projection?: string[]): Promise<PromiseResolve>;

  /**
   * @param {IUpdateUserModel} searchDetails
   * @param {Array} projection
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */

  fetchUserListWithOfferings(searchDetails: FilterQuery<IUpdateUserModel>, projection?: string[], pagination?: IPagination): Promise<PromiseResolve>;

  fetchUserListWithOfferingscsv(searchDetails: FilterQuery<IUpdateUserModel>, projection?: string[], pagination?: IPagination): Promise<PromiseResolve>;

  /**
   * @param {IUpdateUserModel} body
   * @param {IUserModel} filter
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  updateUserDetails(body: IUpdateUserModel, filter: FilterQuery<IUserModel>): Promise<PromiseResolve>;

  /**
   * @param {string} _id
   * @param {string} password
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  addPassword(_id: string, password: string): Promise<PromiseResolve>;

  /**
   * @param {PasswordModel} _id
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */

  singleOfferingReport(searchDetails: FilterQuery<IUpdateOrder>, pagination?: IPagination): Promise<PromiseResolve>;
  topHolders(searchDetails: FilterQuery<IUpdateOrder>, pagination?: IPagination): Promise<PromiseResolve>;
  offeringReport(searchDetails: any): Promise<PromiseResolve>;
  transcationReport(searchDetails: FilterQuery<IUpdateOrder>, pagination?: IPagination): Promise<PromiseResolve>;
  singleOfferingWhitelistReport(searchDetails: FilterQuery<IUpdateWhitelist>, pagination?: IPagination): Promise<PromiseResolve>;
  singleOfferingWhitelistReportCsv(searchDetails: FilterQuery<IUpdateWhitelist>, pagination?: IPagination): Promise<PromiseResolve>;
  /**
   * @param {IUpdateOrder} searchDetails
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */

  fetchRecentPasswords(_id: string): Promise<PromiseResolve>;
  dashboardList(searchDetails: any, projection?: string[], pagination?: IPagination): Promise<PromiseResolve>;
}
