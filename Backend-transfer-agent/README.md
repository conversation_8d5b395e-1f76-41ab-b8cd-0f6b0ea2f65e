# Backend-transfer-agent

## 📌 Overview

The Backend-transfer-agent service is a specialized API for managing transfer agent operations within the Libertum Tokenization Platform. This service handles the critical regulatory and compliance functions required for digital asset transfers, investor onboarding, and securities administration. It serves as the primary interface for transfer agents to manage their portfolio of tokenized assets and investor relationships.

### Key Responsibilities

- **Transfer Agent Operations**: Complete transfer agent functionality including investor registry management
- **Offering Management**: Multi-step offering creation and management workflows for transfer agents
- **Investor Onboarding**: Comprehensive investor registration and KYC/AML processes
- **Transaction Processing**: Transfer agent oversight of asset transfers and transactions
- **Compliance Management**: Regulatory compliance tools and reporting capabilities
- **Document Management**: Secure document handling and storage for regulatory requirements
- **User Authentication**: Secure authentication system with multi-factor authentication support
- **gRPC Integration**: High-performance service-to-service communication with other platform components

## 🚀 Getting Started

### Prerequisites

- Node.js v18.0.0 or higher
- npm v8.0.0 or higher
- MongoDB (for data storage)
- Redis (for caching and session management)
- Google Cloud Storage (for document storage)
- Email service provider (SMTP)

### Installation

```bash
# Clone the repository
git clone https://github.com/Libertum-Project/Backend-transfer-agent.git
cd Backend-transfer-agent

# Install dependencies
npm install

# Setup environment variables
cp .env.example .env
# Edit .env with your configuration values
```

### Environment Configuration

```bash
# Server Configuration
PORT=4000
NODE_ENV=development

# Database Configuration
MONGO_URI=mongodb://localhost:27017/libertum_transfer_agent
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-jwt-secret-here
JWT_REFRESH_SECRET=your-refresh-secret-here
JWT_EXPIRY=24h

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Google Cloud Storage Configuration
GOOGLE_CLOUD_PROJECT_ID=your-project-id
GOOGLE_CLOUD_BUCKET=your-bucket-name
GOOGLE_CLOUD_KEY_FILE=path/to/service-account-key.json

# gRPC Configuration
GRPC_PORT=50051
GRPC_HOST=localhost

# Security Configuration
BCRYPT_ROUNDS=12
OTP_EXPIRY_MINUTES=10

# File Upload Configuration
MAX_FILE_SIZE=********
ALLOWED_FILE_TYPES=pdf,jpg,jpeg,png,docx

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### Running Locally

```bash
# Development mode (with auto-reload)
npm run dev

# Production mode
npm run build
npm start

# The service will be available at:
# - API: http://localhost:4000
# - Swagger UI: http://localhost:4000/api-docs
# - Health Check: http://localhost:4000/ta/v1/health-check
```

## 🔐 Authentication

The Backend-transfer-agent service uses JWT (JSON Web Tokens) for secure authentication:

### Login Process

1. Transfer agent provides email and password
2. System validates credentials and checks account status
3. If OTP is required, user receives OTP via email/SMS
4. User verifies OTP to complete authentication
5. System generates access and refresh tokens
6. User can access protected transfer agent endpoints

### Token Usage

```bash
# Include JWT token in requests
Authorization: Bearer <your-jwt-token>
Content-Type: application/json
```

### Available Auth Endpoints

- `POST /ta/v1/login` - Transfer agent login
- `POST /ta/v1/verify` - OTP verification
- `POST /ta/v1/resend-otp` - Resend OTP
- `POST /ta/v1/forgot-password` - Password reset
- `POST /ta/v1/reset-password` - Complete password reset

## 📡 API Documentation

**Swagger UI**: [http://localhost:4000/api-docs](http://localhost:4000/api-docs)

The API is organized into the following main categories:

### Authentication APIs

- Transfer agent login and authentication
- OTP verification and management
- Password reset functionality
- Session management

### User Management APIs

- Transfer agent profile management
- User registration and onboarding
- Account status management
- Permission and role management

### Offering Management APIs

- Multi-step offering creation workflow
- Offering documentation and compliance
- Offering status tracking and updates
- Investor access management

### Transaction Management APIs

- Transaction monitoring and processing
- Transfer history and audit trails
- Regulatory reporting
- Settlement oversight

### Document Management APIs

- Secure document upload and storage
- Document verification and validation
- Compliance document management
- Digital signature integration

## 📂 Directory Structure

```
Backend-transfer-agent/
├── src/
│   ├── _grpc/                    # gRPC service definitions
│   │   ├── clients/             # gRPC clients for service communication
│   │   ├── proto/               # Protocol buffer definitions
│   │   └── server.ts            # gRPC server setup
│   ├── component/               # Business logic components
│   │   ├── index.ts
│   │   ├── transactions/        # Transaction management
│   │   │   ├── index.ts
│   │   │   ├── interface.ts
│   │   │   ├── service.ts
│   │   │   ├── validation.ts
│   │   │   └── models/          # Transaction data models
│   │   ├── transfer/            # Transfer operations
│   │   │   ├── service.ts
│   │   │   ├── validation.ts
│   │   │   └── models/          # Transfer data models
│   │   └── userAuthentications/ # User authentication
│   │       ├── index.ts
│   │       ├── interface.ts
│   │       ├── service.ts
│   │       ├── validation.ts
│   │       └── models/          # User data models
│   ├── config/                  # Configuration files
│   │   ├── connection/          # Database connections
│   │   ├── env/                 # Environment configuration
│   │   ├── error/               # Error handling
│   │   ├── middleware/          # Express middleware
│   │   └── server/              # Server configuration
│   ├── helpers/                 # Utility functions
│   │   ├── bigMath.ts           # Precision mathematics
│   │   ├── common.helper.ts     # Common utilities
│   │   ├── customError.helper.ts # Error handling
│   │   ├── email.helper.ts      # Email operations
│   │   ├── jwt.helper.ts        # JWT utilities
│   │   ├── redis.helper.ts      # Redis operations
│   │   └── sms.helper.ts        # SMS operations
│   ├── middleware/              # Route middleware
│   │   ├── googleCloudMiddleWare.ts
│   │   ├── index.ts
│   │   ├── transactions.middleware.ts
│   │   └── auth.middleware.ts
│   ├── routes/                  # API route definitions
│   │   ├── auth.router.ts
│   │   ├── index.ts
│   │   ├── transactions.router.ts
│   │   ├── transfer.router.ts
│   │   └── user.router.ts
│   ├── services/                # Business services
│   │   └── kafkaService.ts
│   ├── utils/                   # Utility functions
│   │   ├── common.interface.ts
│   │   ├── constant.ts
│   │   ├── disposableDomains.ts
│   │   ├── emailTemplate/       # Email templates
│   │   └── responseUtils.ts
│   └── public/                  # Static assets
├── docker-compose.yaml          # Docker configuration
├── Dockerfile                   # Container setup
└── package.json                # Dependencies and scripts
```

## ⚙️ Environment Variables

| Key                       | Description                 | Example Value                                       |
| ------------------------- | --------------------------- | --------------------------------------------------- |
| `PORT`                    | Server port                 | `4000`                                              |
| `NODE_ENV`                | Environment mode            | `development`                                       |
| `MONGO_URI`               | MongoDB connection string   | `mongodb://localhost:27017/libertum_transfer_agent` |
| `REDIS_URL`               | Redis connection string     | `redis://localhost:6379`                            |
| `JWT_SECRET`              | JWT signing secret          | `your-jwt-secret-here`                              |
| `JWT_REFRESH_SECRET`      | JWT refresh token secret    | `your-refresh-secret-here`                          |
| `SMTP_HOST`               | Email server host           | `smtp.gmail.com`                                    |
| `SMTP_PORT`               | Email server port           | `587`                                               |
| `SMTP_USER`               | Email username              | `<EMAIL>`                              |
| `SMTP_PASS`               | Email password              | `your-app-password`                                 |
| `GOOGLE_CLOUD_PROJECT_ID` | Google Cloud project ID     | `your-project-id`                                   |
| `GOOGLE_CLOUD_BUCKET`     | Google Cloud storage bucket | `your-bucket-name`                                  |
| `GRPC_PORT`               | gRPC server port            | `50051`                                             |
| `BCRYPT_ROUNDS`           | Password hashing rounds     | `12`                                                |
| `MAX_FILE_SIZE`           | Maximum file upload size    | `********`                                          |
| `RATE_LIMIT_MAX_REQUESTS` | Rate limit max requests     | `100`                                               |

## 🧪 Running Tests

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run unit tests only
npm run test:unit

# Run integration tests only
npm run test:integration

# Run tests in watch mode
npm run test:watch

# Run specific test file
npm test -- --grep "Transfer Agent"
```

## 🛠️ Technologies Used

- **Framework**: Express.js (Node.js web framework)
- **Database**: MongoDB with Mongoose ODM
- **Caching**: Redis for session management and caching
- **Authentication**: JWT (JSON Web Tokens)
- **Documentation**: Swagger/OpenAPI 3.0
- **File Storage**: Google Cloud Storage
- **Email**: Nodemailer for email notifications
- **gRPC**: High-performance RPC for service communication
- **Validation**: Joi for request validation
- **Logging**: Winston for structured logging
- **Security**: Helmet, CORS, Rate Limiting
- **Message Queue**: Kafka for event processing
- **Docker**: Container support for deployment

## 🧑‍💻 Developer Notes

### Development Setup

1. Ensure MongoDB and Redis are running locally
2. Configure Google Cloud Storage credentials
3. Set up email SMTP configuration
4. Copy `.env.example` to `.env` and configure all variables
5. Run `npm run dev` for development mode
6. API documentation is available at `/api-docs`

### Testing API Endpoints

- Use Postman or any API client
- Import the Swagger specification from `/api-docs.json`
- Login with transfer agent credentials
- Include JWT token in Authorization header for protected routes

### Common Development Tasks

```bash
# Start development server with live reload
npm run dev

# Generate API documentation
npm run docs:generate

# Format code using Prettier
npm run format

# Lint code using ESLint
npm run lint

# Build for production
npm run build

# Run database migrations
npm run migrate

# Generate test data
npm run seed
```

### Transfer Agent Workflows

- Multi-step offering creation process
- Investor onboarding and KYC verification
- Transaction monitoring and compliance
- Document management and storage
- Regulatory reporting capabilities

### Debugging

- Logs are stored in the `logs/` directory
- Use `DEBUG=*` environment variable for detailed logs
- Check health endpoint: `GET /ta/v1/health-check`
- Monitor gRPC service status via health checks

## 📦 Build & Deploy

### Docker Deployment

```bash
# Build Docker image
docker build -t backend-transfer-agent .

# Run with Docker Compose
docker-compose up -d

# The service will be available at http://localhost:4000
```

### Production Deployment

```bash
# Install production dependencies
npm ci --only=production

# Build the application
npm run build

# Start the application
npm start
```

### Environment-Specific Configurations

- **Development**: Local databases, debug logging, test credentials
- **Staging**: Staging databases, limited email sending
- **Production**: Production databases, full email and SMS integration

## 🔧 API Endpoints Summary

| Category         | Endpoint                    | Method | Description                |
| ---------------- | --------------------------- | ------ | -------------------------- |
| **Health**       | `/ta/v1/health-check`       | GET    | Service health check       |
| **Auth**         | `/ta/v1/login`              | POST   | Transfer agent login       |
| **Auth**         | `/ta/v1/verify`             | POST   | OTP verification           |
| **Auth**         | `/ta/v1/forgot-password`    | POST   | Password reset             |
| **User**         | `/ta/v1/auth/profile`       | GET    | Get transfer agent profile |
| **User**         | `/ta/v1/auth/users`         | GET    | Get managed users          |
| **Offerings**    | `/ta/v1/auth/offering`      | PUT    | Create/update offering     |
| **Offerings**    | `/ta/v1/auth/offering/{id}` | GET    | Get offering details       |
| **Transactions** | `/ta/v1/auth/transactions`  | GET    | Get transaction history    |
| **Transfer**     | `/ta/v1/auth/transfer`      | POST   | Process asset transfer     |

## 🎯 Key Features

### Multi-Step Offering Creation

- Comprehensive offering workflow with 5 distinct steps
- Document upload and verification
- Compliance checks and validations
- Team and project details management

### Investor Management

- Complete investor registry
- KYC/AML compliance workflows
- Document verification and approval
- Communication and notification systems

### Transaction Processing

- Real-time transaction monitoring
- Compliance checks and validations
- Audit trail and reporting
- Integration with blockchain networks

### Regulatory Compliance

- SEC compliance reporting
- AML/KYC verification processes
- Document retention and management
- Audit trail capabilities

## 📞 Support

For technical support or questions about the Backend-transfer-agent service:

- **Email**: <EMAIL>
- **Documentation**: https://docs.libertum.com
- **GitHub Issues**: https://github.com/Libertum-Project/Backend-transfer-agent/issues

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/transfer-agent-enhancement`)
3. Commit your changes (`git commit -m 'Add transfer agent feature'`)
4. Push to the branch (`git push origin feature/transfer-agent-enhancement`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
