{"openapi": "3.0.0", "info": {"title": "Libertum Dividend Service API", "description": "## Overview\nThe Libertum Dividend Service is a specialized microservice for calculating and distributing dividends to token holders. This service provides comprehensive functionality for:\n- Token balance retrieval and validation\n- Dividend calculation based on token holdings\n- Proportional distribution calculations\n- Multi-token support with contract address validation\n\n## Key Features\n- **Balance Calculation**: Fetch token balances for multiple addresses\n- **Dividend Distribution**: Calculate proportional dividends based on holdings\n- **Contract Integration**: Support for multiple token contracts\n- **Validation**: Comprehensive input validation and error handling\n- **Scalability**: Designed for high-volume dividend calculations\n\n## Authentication\nThis service uses internal authentication for microservice communication.\n\n## Support\nFor technical support or questions, please contact:\n- Email: <EMAIL>\n- Documentation: https://docs.libertum.com\n", "version": "1.0.0", "contact": {"name": "Libertum Support", "email": "<EMAIL>", "url": "https://libertum.com"}, "license": {"name": "Proprietary", "url": "https://libertum.com/license"}}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}, {"url": "https://dividend-api.libertum.com", "description": "Production server"}, {"url": "https://stage-dividend-api.libertum.com", "description": "Staging server"}], "tags": [{"name": "User Balance", "description": "User balance and dividend calculation operations"}, {"name": "Health", "description": "System health and status endpoints"}], "paths": {"/dividend/health": {"get": {"summary": "Health Check", "description": "Check the health status of the Dividend Service.\n\n### Purpose\n- Verify service availability\n- Check database connectivity\n- Monitor system status\n\n### Response Details\n- Service status information\n- Timestamp of the check\n- Database connection status\n", "tags": ["Health"], "responses": {"200": {"description": "Service is healthy", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Dividend service is healthy"}, "status": {"type": "integer", "example": 200}, "timestamp": {"type": "string", "format": "date-time", "example": "2024-01-01T00:00:00.000Z"}, "data": {"type": "object", "properties": {"service": {"type": "string", "example": "dividend-service"}, "version": {"type": "string", "example": "1.0.0"}, "database": {"type": "string", "example": "connected"}}}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/dividend/user/balance": {"post": {"summary": "Calculate User Balances and Dividends", "description": "Calculates token balances and proportional dividends for a list of Ethereum addresses.\n\n### Features\n- Multi-address balance retrieval\n- Proportional dividend calculation\n- Contract address validation\n- Comprehensive error handling\n\n### Calculation Logic\n1. Fetch token balances for all provided addresses\n2. Calculate total token supply across addresses\n3. Determine proportional dividend for each address\n4. Validate calculations and return results\n\n### Use Cases\n- Dividend distribution planning\n- Token holder analytics\n- Reward calculations\n- Governance voting weight determination\n", "tags": ["User Balance"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["address", "contractaddress", "total_dividend_amount"], "properties": {"address": {"type": "array", "items": {"type": "string", "pattern": "^0x[a-fA-F0-9]{40}$"}, "minItems": 1, "maxItems": 1000, "example": ["******************************************", "0xabcdefabcdefabcdefabcdefabcdefabcdef123456"], "description": "Array of Ethereum addresses to fetch balances for.\n- Must be valid Ethereum addresses (42 characters starting with 0x)\n- Maximum 1000 addresses per request\n- Duplicate addresses will be deduplicated\n"}, "contractaddress": {"type": "string", "pattern": "^0x[a-fA-F0-9]{40}$", "example": "0xabcdefabcdefabcdefabcdefabcdefabcdef123456", "description": "The contract address of the token.\n- Must be a valid Ethereum contract address\n- Contract must implement ERC-20 standard\n"}, "total_dividend_amount": {"type": "number", "minimum": 0.01, "maximum": 1000000000, "example": 1000, "description": "The total dividend amount to distribute among addresses.\n- Must be a positive number\n- Maximum 1 billion tokens\n- Will be distributed proportionally based on token holdings\n"}}}, "examples": {"Basic Request": {"summary": "Basic dividend calculation", "value": {"address": ["******************************************", "0xabcdefabcdefabcdefabcdefabcdefabcdef123456"], "contractaddress": "0xabcdefabcdefabcdefabcdefabcdefabcdef123456", "total_dividend_amount": 1000}}, "Large Scale Request": {"summary": "Large scale dividend calculation", "value": {"address": ["******************************************", "0xabcdefabcdefabcdefabcdefabcdefabcdef123456", "******************************************"], "contractaddress": "0xabcdefabcdefabcdefabcdefabcdefabcdef123456", "total_dividend_amount": 50000}}}}}}, "responses": {"200": {"description": "Successful calculation of balances and dividends", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Balances and dividends calculated successfully"}, "status": {"type": "integer", "example": 200}, "data": {"type": "array", "items": {"type": "object", "properties": {"address": {"type": "string", "description": "Ethereum address", "example": "******************************************"}, "balance": {"type": "string", "description": "Token balance for the address in wei", "example": "100456789012345678901"}, "balanceFormatted": {"type": "string", "description": "Human-readable token balance", "example": "100.456789012345678901"}, "dividend": {"type": "string", "description": "Calculated dividend for the address", "example": "10.1234567890123456789"}, "percentage": {"type": "string", "description": "Percentage of total supply held", "example": "1.01234"}}}}, "metadata": {"type": "object", "properties": {"totalSupply": {"type": "string", "description": "Total token supply across all addresses", "example": "9876543210123456789012"}, "totalDividend": {"type": "string", "description": "Total dividend amount distributed", "example": "1000"}, "addressCount": {"type": "integer", "description": "Number of addresses processed", "example": 2}, "contractAddress": {"type": "string", "description": "Token contract address", "example": "0xabcdefabcdefabcdefabcdefabcdefabcdef123456"}}}}}, "examples": {"Success Response": {"summary": "Successful dividend calculation", "value": {"message": "Balances and dividends calculated successfully", "status": 200, "data": [{"address": "******************************************", "balance": "100456789012345678901", "balanceFormatted": "100.456789012345678901", "dividend": "10.1234567890123456789", "percentage": "1.01234"}, {"address": "0xabcdefabcdefabcdefabcdefabcdefabcdef123456", "balance": "200789012345678901234", "balanceFormatted": "200.789012345678901234", "dividend": "20.2468135780246913578", "percentage": "2.02468"}], "metadata": {"totalSupply": "9876543210123456789012", "totalDividend": "1000", "addressCount": 2, "contractAddress": "0xabcdefabcdefabcdefabcdefabcdefabcdef123456"}}}}}}}, "400": {"description": "Validation error in the request body", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"Invalid Address": {"summary": "Invalid Ethereum address format", "value": {"message": "Invalid address format", "status": 400, "error": "Address must be a valid Ethereum address (42 characters starting with 0x)", "data": null}}, "Invalid Contract": {"summary": "Invalid contract address", "value": {"message": "Invalid contract address", "status": 400, "error": "Contract address must be a valid Ethereum address", "data": null}}, "Invalid Amount": {"summary": "Invalid dividend amount", "value": {"message": "Invalid dividend amount", "status": 400, "error": "Total dividend amount must be a positive number between 0.01 and 1000000000", "data": null}}}}}}, "422": {"description": "Business logic validation error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"No Token Balance": {"summary": "No token balance found", "value": {"message": "No token balance found", "status": 422, "error": "None of the provided addresses have token balance for this contract", "data": null}}, "Contract Not Found": {"summary": "Token contract not found", "value": {"message": "Token contract not found", "status": 422, "error": "The specified contract address does not exist or is not a valid ERC-20 contract", "data": null}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"Server Error": {"summary": "Internal server error", "value": {"message": "Internal Server Error", "status": 500, "error": "An unexpected error occurred while processing your request", "data": null}}}}}}}}}}, "components": {"schemas": {"ErrorResponse": {"type": "object", "properties": {"message": {"type": "string", "description": "Error message", "example": "Validation error"}, "status": {"type": "integer", "description": "HTTP status code", "example": 400}, "error": {"type": "string", "description": "Detailed error description", "example": "Invalid address format"}, "data": {"type": "object", "nullable": true, "description": "Additional error data", "example": null}}, "required": ["message", "status", "error", "data"]}, "SuccessResponse": {"type": "object", "properties": {"message": {"type": "string", "description": "Success message"}, "status": {"type": "integer", "description": "HTTP status code", "example": 200}, "data": {"type": "object", "description": "Response data"}}, "required": ["message", "status", "data"]}}}}