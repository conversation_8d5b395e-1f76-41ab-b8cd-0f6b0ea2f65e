{"name": "dividend", "version": "1.0.0", "description": "", "main": "index.ts", "scripts": {"test": "npx jest --testPathPattern=src", "build": "rimraf build  && npx tsc --resolveJsonModule && cp ./swagger.json ./build/", "start": " npm run build && nodemon ./build/index.js", "dev": "npm run format && nodemon ./src/index.ts", "format": "npx prettier --write . './src/**/*.{ts,tsx,js,jsx,json,ejs}' && npx eslint --fix './src/**/*.{ts,tsx,js}'", "sonar": "dotenv -e .env -- sh -c 'sonar-scanner -Dsonar.host.url=\"$SONAR_HOST\" -Dsonar.token=\"$SONAR_TOKEN\"'"}, "author": "", "license": "ISC", "dependencies": {"@babel/preset-env": "^7.26.9", "@babel/preset-typescript": "^7.26.0", "@eslint/js": "^9.22.0", "axios": "^1.8.2", "big.js": "^6.2.2", "body-parser": "^1.20.3", "chai": "^5.2.0", "cors": "^2.8.5", "D": "^1.0.0", "decimal.js": "^10.5.0", "dotenv": "^16.4.7", "dotenv-cli": "^8.0.0", "express": "^4.21.2", "fs": "^0.0.1-security", "globals": "^16.0.0", "helmet": "^8.1.0", "ioredis": "^5.6.0", "jest": "^29.7.0", "joi": "^17.13.3", "js-yaml": "^4.1.0", "kafkajs": "^2.2.4", "logger": "^0.0.1", "mongoose": "^8.12.1", "node-cron": "^3.0.3", "nodemon": "^3.1.9", "path": "^0.12.7", "rimraf": "^6.0.1", "start": "^5.1.0", "superset": "^2.0.1", "supertest": "^7.0.0", "swagger-ui-express": "^5.0.1", "ts-node": "^10.9.2", "typescript": "^5.8.2", "web3": "^4.16.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "winston-mongodb": "^6.0.0"}, "devDependencies": {"@types/big.js": "^6.2.2", "@types/body-parser": "^1.19.5", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node-cron": "^3.0.11", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "eslint": "^8.57.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.31.0", "nodemon": "^3.1.9", "sonarqube-scanner": "^4.3.0", "ts-jest": "^29.2.6", "typescript-eslint": "^8.26.1"}}