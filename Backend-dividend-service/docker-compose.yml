version: '3.8' # Specify the Compose file format version

services:
  redis:
    image: redis:latest
    ports:
      - '6379:6379'
    networks:
      - app_network

  rabbitmq:
    image: rabbitmq:management
    ports:
      - '5672:5672'
      - '15672:15672'
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    networks:
      - app_network

  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - '2181:2181'
    networks:
      - app_network

  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: kafka
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092 # Advertised for other containers in the network
      KAFKA_LISTENERS: PLAINTEXT://localhost:9092 # Bind Kafka to all interfaces
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    ports:
      - '9092:9092'
    depends_on:
      - zookeeper
    networks:
      - app_network

  kafdrop:
    image: obsidiandynamics/kafdrop
    container_name: kafdrop
    ports:
      - '9000:9000'
    environment:
      KAFKA_BROKERCONNECT: localhost:9092
    depends_on:
      - kafka
    networks:
      - app_network

  mysql:
    image: mysql:5.7
    container_name: mysql_container_v2
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: my_database
      MYSQL_USER: my_user
      MYSQL_PASSWORD: my_password
    ports:
      - '3307:3306'
    networks:
      - app_network

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
    ports:
      - '8080:80'
    depends_on:
      - mysql
    networks:
      - app_network

networks:
  app_network:
    driver: bridge
