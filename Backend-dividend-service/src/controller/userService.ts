import Big from 'big.js';
import { Request, Response } from 'express';
import <PERSON><PERSON> from 'joi';
import UserBalanceService from '../helpers/baseScan';
import tokenAbi from '../abi/token';
import fundFactoryABI from '../abi/funcFactory';
import Web3 from 'web3';
import config from '../interfaces/config';
import logger from '../helpers/logger.helper';

interface TotalSupplyResponse {
  status: string;
  message: string;
  result: string;
}

class UserService {
  private readonly tokenInstance: any;
  private readonly fundFactory: any;

  constructor() {
    const web3 = new Web3(config.WEB3_PROVIDER_URL);
    this.tokenInstance = new web3.eth.Contract(tokenAbi, config.TOKEN_ADDRESS);
    this.fundFactory = new web3.eth.Contract(fundFactoryABI, config.FUND_FACTORY);
  }

  /**
   * Validates the request body using Joi schema.
   * @param body - The request body to validate.
   * @returns Validation result or error.
   */
  private validateRequestBody(body: any) {
    const schema = Joi.object({
      address: Joi.array()
        .items(Joi.string().pattern(/^0x[a-fA-F0-9]{40}$/))
        .min(1)
        .max(50)
        .required()
        .messages({
          'string.pattern.base': "'address' must contain valid Ethereum addresses ",
          'array.min': "'address' array must contain at least one address",
        }),
      contractaddress: Joi.string()
        .pattern(/^0x[a-fA-F0-9]{40}$/)
        .required()
        .messages({
          'string.pattern.base': "'contractaddress' must be a valid Ethereum address",
        }),
      total_dividend_amount: Joi.number().min(1).max(100000000000).required().messages({
        'number.min': "'total_dividend_amount' must be at least 1",
        'number.max': "'total_dividend_amount' must not exceed 100,000,000,000",
      }),
    });

    return schema.validate(body);
  }
  private validateRequestBodyTotalSupply(body: any) {
    const schema = Joi.object({
      contractaddress: Joi.string()
        .pattern(/^0x[a-fA-F0-9]{40}$/)
        .required()
        .messages({
          'string.pattern.base': "'contractaddress' must be a valid Ethereum address",
        }),
    });

    return schema.validate(body);
  }

  private validateYeildRequestBody(body: any) {
    const schema = Joi.object({
      contractaddress: Joi.array()
        .items(Joi.string().pattern(/^0x[a-fA-F0-9]{40}$/))
        .min(1)
        .max(50)
        .required()
        .messages({
          'string.pattern.base': "'address' must contain valid Ethereum addresses ",
          'array.min': "'address' array must contain at least one address",
        }),
      dividend_amount: Joi.array().items(Joi.number().min(0).max(1000000000000000)).min(1).max(50).required().messages({
        'number.min': "'total_dividend_amount' must be at least 1",
        'number.max': "'total_dividend_amount' must not exceed 100,000,000,000",
      }),
    });

    return schema.validate(body);
  }

  /**
   * Fetches user balances and calculates dividends.
   * @param req - The Express request object.
   * @param res - The Express response object.
   */
  /**
   * Fetches user balances and calculates dividends.
   */
  public async getContractTokenYeild(req: Request, res: Response): Promise<any> {
    try {
      // Validate request body
      const { error, value } = this.validateYeildRequestBody(req.body);
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const { contractaddress, dividend_amount } = value;

      if (!Array.isArray(contractaddress) || !Array.isArray(dividend_amount)) {
        return res.status(400).json({ error: 'contractaddress and dividend_amount must be arrays' });
      }

      if (contractaddress.length !== dividend_amount.length) {
        return res.status(400).json({ error: 'contractaddress and dividend_amount arrays must be of equal length' });
      }

      const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

      // Fetch token decimal
      const { decimal } = await this.getDecimal();
      console.info('Decimal:', decimal);

      const finalAns: any[] = [];

      // **Process in batches of 5**
      for (let i = 0; i < contractaddress.length; i += 5) {
        const contractChunk = contractaddress.slice(i, i + 5);
        const dividendChunk = dividend_amount.slice(i, i + 5);

        console.log(`Processing batch: ${contractChunk}`);

        const circulatingSupplyResults = await Promise.all(
          contractChunk.map(async (address) => {
            try {
              // Fetch token total supply
              console.log('address ->', address);
              const totalSupplyResponse = await UserBalanceService.getTotalSupply(address);

              // logger.info('without fund factory', totalSupplyResponse);
              const totalSupply11 = new Big(totalSupplyResponse?.result || 0);
              const totalSupplyValue = totalSupply11.toString();

              if (totalSupplyValue.toString() === '0') {
                return res.json({
                  status: 500,
                  data: null,
                  error: `Total Supply of ${contractaddress.toString()} is 0`,
                });
              }

              // const totalSupplyValue = await this.fundFactory.methods.getTokenTotalSupply(address).call();
              console.log('here ===>>>', totalSupplyValue, address);
              return new Big(totalSupplyValue).div(new Big(10).pow(decimal)).toString();
            } catch (err) {
              console.warn(`Error fetching circulating supply for ${address}:`, err);
              return null;
            }
          }),
        );

        contractChunk.forEach((address, index) => {
          const circulatingSupply = circulatingSupplyResults[index];
          const dividendAmount = dividendChunk[index];

          if (circulatingSupply && dividendAmount) {
            try {
              console.log(`Processing dividend for ${address}`);

              const circulatingVal = new Big(circulatingSupply as string);
              const dividendAmountVal = new Big(dividendAmount);

              if (dividendAmountVal.eq(0)) {
                console.warn(`Dividend amount is zero for ${address}`);
                return;
              }

              const dividendPerToken = dividendAmountVal.div(circulatingVal).toFixed(8);
              finalAns.push({ address, dividendAmount, dividendPerToken });
            } catch (err) {
              console.error(`Error calculating dividend for ${address}:`, err);
            }
          } else {
            console.warn(`Skipping ${address} due to missing supply or dividend amount`);
          }
        });

        // **Rate limit handling**
        if (i + 5 < contractaddress.length) {
          console.info(`Waiting 1 second before next batch...`);
          await delay(1000);
        }
      }

      console.info('Final Response:', finalAns);

      if (finalAns.length > 0) {
        return res.status(200).json({ status: 200, data: finalAns });
      } else {
        return res.status(500).json({ error: 'Failed to fetch user balances or all balances are zero' });
      }
    } catch (error: any) {
      console.error('❌ Error in getContractTokenYeild:', error);
      return res.status(500).json({ error: error.message || 'Internal Server Error' });
    }
  }

  /**
   * Fetches user balances and calculates dividends.
   * @param req - The Express request object.
   * @param res - The Express response object.
   */
  public async getUserBalance(req: Request, res: Response): Promise<void> {
    const responseSent = false;

    try {
      const { error, value } = this.validateRequestBody(req.body);
      if (error) {
        res.status(400).json({ error: error.details[0].message });
        return;
      }

      const { address, contractaddress, total_dividend_amount } = value;

      const { decimal } = await this.getDecimal();
      const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

      const totalSupplyResponse = await UserBalanceService.getTotalSupply(contractaddress);
      const totalSupplyRaw = new Big(totalSupplyResponse?.result || 0).toString();

      if (!totalSupplyRaw || totalSupplyRaw === '0') {
        res.status(500).json({
          error: `Total Supply of ${contractaddress.toString()} is 0 or undefined`,
          data: null,
        });
        return;
      }

      const totalSupply = new Big(totalSupplyRaw).div(new Big(10).pow(decimal));
      if (totalSupply.eq(0)) {
        res.status(200).json({
          error: true,
          message: `Circulating Supply of token is 0`,
        });
        return;
      }

      const results: { address: string; balance: string; dividend: string }[] = [];
      await delay(1000); // Initial delay for rate limiting

      for (let i = 0; i < address.length; i += 5) {
        const chunk = address.slice(i, i + 5);

        const balances = await Promise.allSettled(chunk.map((addr: string) => UserBalanceService.getUserBalance({ address: addr, contractaddress })));

        chunk.forEach((addr: any, idx: any) => {
          const result = balances[idx];
          if (result.status === 'fulfilled' && result.value?.result) {
            const balance = new Big(result.value.result).div(new Big(10).pow(decimal));
            const dividend = balance.div(totalSupply).times(new Big(total_dividend_amount)).toFixed(6);

            results.push({
              address: addr,
              balance: balance.toString(),
              dividend,
            });
          } else {
            logger.warn(`Failed to fetch balance for ${addr}: ${result.status === 'rejected' ? result.reason : 'Invalid result'}`);
          }
        });

        if (i + 5 < address.length) await delay(1000); // Delay between chunks
      }

      if (!responseSent) {
        if (results.length > 0) {
          res.status(200).json(results);
          return;
        } else {
          res.status(500).json({ error: 'Failed to fetch user balances' });
          return;
        }
      }
    } catch (error: any) {
      logger.error('Error in getUserBalance:', error);
      if (!responseSent) {
        res.status(500).json({ error: error.message || 'Internal Server Error' });
        return;
      }
    }
  }

  /**
   * Fetches token decimal value.
   * @returns Decimal value of the token.
   */
  private async getDecimal(): Promise<{ decimal: number }> {
    try {
      const decimal = await this.tokenInstance.methods.decimals().call();
      return { decimal: parseInt(decimal, 10) };
    } catch (error) {
      logger.error('Error fetching token decimals:', error);
      throw new Error('Failed to fetch token decimals');
    }
  }

  public async getTotalSupply(req: Request, res: Response): Promise<any> {
    try {
      const { error } = this.validateRequestBodyTotalSupply(req.query);
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const { contractaddress } = req.query;
      if (!contractaddress) {
        return res.status(400).json({ error: 'Contract address is required' });
      }

      const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));
      await delay(1000);

      // Fetch token total supply from the service
      const totalSupplyResponse: TotalSupplyResponse | null = await UserBalanceService.getTotalSupply(contractaddress);

      if (!totalSupplyResponse) {
        return res.status(500).json({ error: 'Failed to fetch total supply data from external API' });
      }

      console.info('totalSupplyResponse:', totalSupplyResponse);

      // Get the token decimal
      const { decimal }: any = await this.getDecimal();
      console.info('token Decimal:', decimal);

      // Ensure that result from the totalSupplyResponse is a valid number
      if (totalSupplyResponse.status !== '1' || !totalSupplyResponse.result) {
        throw new Error('Invalid response from total supply API');
      }

      // Calculate the actual total supply considering the decimals
      const totalSupplyRaw: any = totalSupplyResponse.result;
      const adjustedTotalSupply = totalSupplyRaw / Math.pow(10, decimal);

      return res.status(200).json({ totalSupply: adjustedTotalSupply });
    } catch (error) {
      logger.error('Error fetching token supply:', error);
      return res.status(500).json({ error: 'Failed to fetch token supply' });
    }
  }
}

export default new UserService();
