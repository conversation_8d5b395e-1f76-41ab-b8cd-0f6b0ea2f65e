import { Request, Response } from 'express';

class healthCheckService {
  public async health(req: Request, res: Response): Promise<void> {
    try {
      res.status(200).json({
        error: false,
        message: 'dividend service running !',
        port: 9000,
      });
    } catch (error) {
      console.error('Error during API call:', error);
      res.status(500).send('Internal Server Error');
    }
  }
}
export default new healthCheckService();
