import * as dotenv from 'dotenv';
dotenv.config();

const NODE_ENV: string = process.env.NODE_ENV || 'development';

const development: any = {
  BASE_API_KEY: process.env.BASE_API_KEY || '',
  BASE_URL: process.env.BASE_URL || '',
  TOKEN_ADDRESS: process.env.TOKEN_ADDRESS || '',
  WEB3_PROVIDER_URL: process.env.WEB3_PROVIDER_URL || '',
  FUND_FACTORY: process.env.FUND_FACTORY || '',
  PORT: process.env.PORT || '3000',

  NODE_ENV: process.env.NODE_ENV || 'development',
  PROJECT_NAME: process.env.PROJECT_NAME || '',
  LOG_LEVEL: process.env.LOG_LEVEL || 'debug',
};

const config: { [name: string]: any } = {
  development,
};

export default config[NODE_ENV];
