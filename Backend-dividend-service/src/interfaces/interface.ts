// request-data.interface.ts
export interface RequestData {
  address: string; // List of user wallet addresses (mandatory)
  contractaddress?: string; // Token contract address (optional)
  module?: 'account' | string; // Defaults to 'account' but allows extensibility
  action?: 'balancemulti' | string; // Defaults to 'balancemulti' but allows extensibility
  page?: number; // Pagination: Defaults to 1
  offset?: number; // Number of items per page: Defaults to 10
  tag?: 'latest' | 'earliest' | string; // Block tag (default: 'latest')
}

export interface ApiResponse<T = any> {
  status: string; // API response status, e.g., "1" for success
  message: string; // API response message
  result: T; // Generic result to allow type customization based on API response
}

export interface UserBalanceResult {
  address: string; // Wallet address
  balance: string; // Balance of the user in token or base currency
}

export interface totalSupply {
  amount: string; // Balance of the user in token or base currency
}

export interface UserBalanceResponse {
  status: string;
  message: string;
  result: UserBalanceResult;
}

export interface totalSupplyResponse {
  status: string;
  message: string;
  result: string;
}
