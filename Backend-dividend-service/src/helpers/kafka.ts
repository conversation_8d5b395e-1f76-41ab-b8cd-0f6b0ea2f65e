import { Ka<PERSON><PERSON>, Producer, Consumer, KafkaMessage } from 'kafkajs';
import CONF<PERSON> from './config';

class KafkaHelper {
  private kafka: Kafka;
  private producer: Producer;
  private consumers: Map<string, Consumer> = new Map();

  constructor(brokers: string[] = [CONFIG.KAFKA.BROKERS], clientId: string) {
    this.kafka = new Kafka({
      clientId,
      brokers,
      retry: {
        retries: 5,
        initialRetryTime: 300,
        factor: 0.2,
      },
      connectionTimeout: 10000,
    });
    this.producer = this.kafka.producer();
    this.connectProducer();
  }

  /**
   * Connect to Kafka broker.
   */
  async connectProducer(): Promise<void> {
    try {
      await this.producer.connect();
      console.info('Kafka producer connected successfully.');
    } catch (error) {
      console.error('Error connecting Kafka producer:', error);
      throw error;
    }
  }

  /**
   * Disconnect from Kafka broker.
   */
  async disconnectProducer(): Promise<void> {
    try {
      await this.producer.disconnect();
      console.info('Kafka producer disconnected successfully.');
    } catch (error) {
      console.error('Error disconnecting Kafka producer:', error);
      throw error;
    }
  }

  /**
   * Send a message to a Kafka topic.
   * @param topic - Kafka topic
   * @param messages - Array of messages to send
   */
  async sendMessage(topic: string, messages: KafkaMessage[] | any): Promise<void> {
    try {
      await this.producer.send({
        topic,
        messages,
      });
      console.info(`Message sent successfully to topic ${topic}.`);
    } catch (error) {
      console.error(`Error sending message to topic ${topic}:`, error);
      throw error;
    }
  }

  /**
   * Create a consumer and subscribe to a Kafka topic.
   * @param groupId - Kafka consumer group ID
   * @param topic - Kafka topic to subscribe to
   * @param eachMessageHandler - Callback to handle each received message
   */
  async createConsumer(groupId: string, topic: string, eachMessageHandler: (message: KafkaMessage, topic: string, partition: number, offset: string) => void): Promise<Consumer> {
    try {
      const consumer = this.kafka.consumer({ groupId });
      await consumer.connect();
      await consumer.subscribe({ topic, fromBeginning: true });

      await consumer.run({
        eachMessage: async ({ topic, partition, message }) => {
          eachMessageHandler(message, topic, partition, message.offset);
        },
      });

      this.consumers.set(groupId, consumer);
      console.info(`Consumer created and subscribed to topic ${topic} with group ID ${groupId}.`);
      return consumer;
    } catch (error) {
      console.error(`Error creating consumer for topic ${topic} with group ID ${groupId}:`, error);
      throw error;
    }
  }

  /**
   * Disconnect a consumer from Kafka broker.
   * @param groupId - Kafka consumer group ID
   */
  async disconnectConsumer(groupId: string): Promise<void> {
    const consumer = this.consumers.get(groupId);
    if (consumer) {
      try {
        await consumer.disconnect();
        this.consumers.delete(groupId);
        console.info(`Consumer with group ID ${groupId} disconnected successfully.`);
      } catch (error) {
        console.error(`Error disconnecting consumer with group ID ${groupId}:`, error);
        throw error;
      }
    } else {
      console.warn(`Consumer with group ID ${groupId} not found.`);
    }
  }
}

export const kafkaHelperService = new KafkaHelper([CONFIG.KAFKA.BROKERS], 'service-client');
