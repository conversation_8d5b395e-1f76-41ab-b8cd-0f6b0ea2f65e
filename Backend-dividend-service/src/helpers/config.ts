import * as dotenv from 'dotenv';
dotenv.config();

const NODE_ENV: string = process.env.NODE_ENV || 'development';

const development: any = {
  DATABASE: {
    MONGODB_HOST: process.env.MONGODB_HOST || 'localhost',
    MONGODB_USER: process.env.MONGODB_USER,
    MONGODB_PASSWORD: process.env.MONGODB_PASSWORD,
    MONGODB_PORT: Number(process.env.MONGODB_PORT) || 27017,
    MONGODB_DATABASE: process.env.MONGODB_DATABASE || 'mvp_db',
  },
  BASE_API_KEY: process.env.BASE_API_KEY || '',
  BASE_URL: process.env.BASE_URL || '',
  TOKEN_ADDRESS: process.env.TOKEN_ADDRESS || '',
  WEB3_PROVIDER_URL: process.env.WEB3_PROVIDER_URL || '',
  FUND_FACTORY: process.env.FUND_FACTORY || '',
};

const config: { [name: string]: any } = {
  development,
};

export default config[NODE_ENV];
