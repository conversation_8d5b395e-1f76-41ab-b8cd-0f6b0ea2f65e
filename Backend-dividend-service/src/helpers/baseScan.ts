import axios from 'axios';
import { UserBalanceResponse, totalSupplyResponse } from '../interfaces/interface';
import config from '../interfaces/config';

class UserBalanceService {
  private readonly apiKey: string;
  private readonly baseUrl: string;

  constructor() {
    this.apiKey = config.BASE_API_KEY;
    this.baseUrl = config.BASE_URL;
  }

  public async getUserBalance(requestData: any): Promise<UserBalanceResponse | null> {
    try {
      const { address, contractaddress = '', module = 'account', action = 'tokenBalance', page = 1, offset = 10, tag = 'latest' } = requestData;

      const response: any = await axios.get(this.baseUrl, {
        params: {
          module,
          action,
          contractaddress: contractaddress,
          address: address,
          tag,
          page,
          offset,
          apikey: this.apiKey,
        },
      });
      // console.log('balance response  :::\n\n', response.data);
      if (response.data.status === '1') {
        const userBalanceResponse: UserBalanceResponse = {
          status: response.data.status,
          message: response.data.message,
          result: response.data.result || [], // Assuming `result` contains the balances array
        };
        return userBalanceResponse;
      } else {
        console.error('API call failed:', response.data.message);
        return null;
      }
    } catch (error) {
      console.error('Error during API call:', error);
      return null;
    }
  }

  public async getTotalSupply(contractaddress: any): Promise<totalSupplyResponse | null> {
    try {
      const params = {
        contractaddress,
        module: 'stats',
        action: 'tokensupply',
        apikey: this.apiKey,
      };

      const response: any = await axios.get(this.baseUrl, {
        params,
      });
      console.log('total Supply :::::::\n\n', response.data);
      if (response.data.status === '1') {
        const totalSupply: totalSupplyResponse = {
          status: response.data.status,
          message: response.data.message,
          result: response.data.result || [], // Assuming `result` contains the balances array
        };
        return totalSupply;
      } else {
        console.error('API call failed:', response.data.message);
        return null;
      }
    } catch (error) {
      console.error('Error fetching token details:', error);
      throw new Error('Failed to fetch token details');
    }
  }
}

export default new UserBalanceService();
