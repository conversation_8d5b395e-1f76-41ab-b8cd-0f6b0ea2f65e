import swaggerUi from 'swagger-ui-express';
import { Express } from 'express';
import fs from 'fs';
import path from 'path';

let swaggerDocument: any;

// Load swagger.json file
try {
  const swaggerPath = path.join(__dirname, '../../swagger.json');
  const swaggerFile = fs.readFileSync(swaggerPath, 'utf8');
  swaggerDocument = JSON.parse(swaggerFile);
} catch (error) {
  console.error('Error loading swagger.json:', error);
  // Fallback minimal swagger document if json file is not found
  swaggerDocument = {
    openapi: '3.0.0',
    info: {
      title: 'Libertum Dividend Service API',
      version: '1.0.0',
      description: 'API documentation for the Dividend Service',
    },
    servers: [
      {
        url: 'http://localhost:3000',
        description: 'Development server',
      },
    ],
    paths: {},
  };
}

export const setupSwagger = (app: Express): void => {
  // Swagger UI with dynamic server URL
  app.use('/api-docs', swaggerUi.serve, (req, res, next) => {
    // Get the protocol and host from the request
    const protocol = req.protocol;
    const host = req.get('host');
    const baseUrl = `${protocol}://${host}`;

    // Create a dynamic swagger spec with the current host
    const dynamicSwaggerSpec = {
      ...swaggerDocument,
      servers: [
        {
          url: baseUrl,
          description: `Current server (${host})`,
        },
        ...swaggerDocument.servers,
      ],
    };

    // Setup Swagger UI with dynamic spec
    swaggerUi.setup(dynamicSwaggerSpec, {
      explorer: true,
      customSiteTitle: 'Libertum Dividend Service API',
      customCss: `
        .swagger-ui .topbar { display: none }
        .swagger-ui .info .title { color: #1f2937 }
        .swagger-ui .info .description { color: #4b5563 }
        .swagger-ui .scheme-container { background: #f9fafb; padding: 15px; border-radius: 5px; }
      `,
      customCssUrl: null,
      swaggerOptions: {
        persistAuthorization: true,
        displayRequestDuration: true,
        docExpansion: 'list',
        filter: true,
        showExtensions: true,
        showCommonExtensions: true,
        tryItOutEnabled: true,
      },
    })(req, res, next);
  });

  // Swagger JSON endpoint with dynamic server URL
  app.get('/api-docs.json', (req, res) => {
    // Get the protocol and host from the request
    const protocol = req.protocol;
    const host = req.get('host');
    const baseUrl = `${protocol}://${host}`;

    // Create a dynamic swagger spec with the current host
    const dynamicSwaggerSpec = {
      ...swaggerDocument,
      servers: [
        {
          url: baseUrl,
          description: `Current server (${host})`,
        },
        ...swaggerDocument.servers,
      ],
    };

    res.setHeader('Content-Type', 'application/json');
    res.send(dynamicSwaggerSpec);
  });

  // Add health check endpoint if not already present
  app.get('/dividend/health', (req, res) => {
    res.json({
      message: 'Dividend service is healthy',
      status: 200,
      timestamp: new Date().toISOString(),
      data: {
        service: 'dividend-service',
        version: '1.0.0',
        database: 'connected', // You can add actual database check here
      },
    });
  });

  console.log('✅ Swagger documentation is available at:');
  console.log('   - Swagger UI: http://localhost:3000/api-docs');
  console.log('   - Swagger JSON: http://localhost:3000/api-docs.json');
  console.log('   - Health Check: http://localhost:3000/dividend/health');
};

export default swaggerDocument;
