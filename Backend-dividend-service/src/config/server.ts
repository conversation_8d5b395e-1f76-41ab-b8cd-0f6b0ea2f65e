import express, { Application } from 'express';
import bodyParser from 'body-parser';
import cors from 'cors';
import helmet from 'helmet';
import http from 'http';
import dotenv from 'dotenv';
import router from '../router/router.controller';
import config from '../interfaces/config';
import { setupSwagger } from './swagger';

dotenv.config();

class Server {
  public app: Application;
  private server: http.Server | null = null;

  constructor() {
    this.app = express();

    // Middleware
    this.configureMiddleware();

    // Swagger documentation
    this.setupSwagger();

    // Routes
    this.initialiseRouter();

    // Start server
    this.startServer();
  }

  private configureMiddleware(): void {
    this.app.use(cors());
    this.app.use(helmet());
    this.app.use(bodyParser.json());
    this.app.use(bodyParser.urlencoded({ extended: false }));
  }

  private setupSwagger(): void {
    setupSwagger(this.app);
  }

  private initialiseRouter(): void {
    this.app.use('/dividend', router);
  }

  private startServer(): void {
    const PORT = config.PORT;
    this.server = http.createServer(this.app);
    this.server.listen(PORT, () => {
      console.log(`🚀 Dividend Service is running on port ${PORT}`);
      console.log(`📚 API Documentation: http://localhost:${PORT}/api-docs`);
      console.log(`💚 Health Check: http://localhost:${PORT}/dividend/health`);
    });
  }
}

export default Server;
