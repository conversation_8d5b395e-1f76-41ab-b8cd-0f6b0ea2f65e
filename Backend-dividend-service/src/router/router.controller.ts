import { Router } from 'express';
import userService from '../controller/userService';
import healthCheckService from '../controller/healthCheck';
const router = Router();

// Define your routes here
router.post('/cap-table', async (req, res) => {
  await userService.getUserBalance(req, res);
});

router.post('/yeild', async (req, res) => {
  await userService.getContractTokenYeild(req, res);
});

router.get('/totalSupply', async (req, res) => {
  await userService.getTotalSupply(req, res);
});

router.get('/health', async (req, res) => {
  await healthCheckService.health(req, res);
});

export default router;
