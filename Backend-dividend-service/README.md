# **Divident Backend**

This project provides the backend API service for calculating and distributing dividends based on user token balances. It integrates with Ethereum smart contracts, fetches token details, and computes dividend distribution based on token holdings.

---

## **Features**

- Fetches user token balances from Ethereum blockchain.
- Calculates dividends based on user balances, token decimals, and total supply.
- Supports Ethereum-based token contract interaction via Web3.
- API request validation using **Joi**.
- Rate-limited requests to external APIs to avoid throttling.

---

## **Prerequisites**

Ensure you have the following installed on your system:

- [Node.js](https://nodejs.org/) (v16.x or later)
- [npm](https://www.npmjs.com/) or [yarn](https://yarnpkg.com/)
- [MongoDB](https://www.mongodb.com/)
- A valid Ethereum node or provider URL (e.g., Infura or Alchemy).

---

## **Environment Variables**

Create a `.env` file in the root directory with the following variables:

\`\`\`env

# Server

PORT=3000

# Blockchain

WEB3_PROVIDER_URL=https://mainnet.infura.io/v3/YOUR_INFURA_PROJECT_ID
TOKEN_ADDRESS=0xYourTokenAddressHere

# External API

BASE_URL=https://api.etherscan.io/api
BASE_API_KEY=YourEtherscanAPIKey

# Database

MONGO_URI=mongodb://localhost:27017/divident-db

# Other Configs

NODE_ENV=development
\`\`\`

---

## **Installation**

1. Clone the repository:
   \`\`\`bash
   git clone https://github.com/Libertum-Project/Backend-dividend-service.git
   cd divident-backend
   \`\`\`

2. Install dependencies:
   \`\`\`bash
   npm install
   \`\`\`

3. Set up environment variables in `.env` as described above.

4. Start the development server:
   \`\`\`bash
   npm run dev
   \`\`\`

---

## **Contributing**

Contributions are welcome! Please fork this repository, create a feature branch, and submit a pull request.

---

## **License**

This project is licensed under the MIT License. See the LICENSE file for details.

---

## **Contact**

For any issues or queries, please reach out at [<EMAIL>].
