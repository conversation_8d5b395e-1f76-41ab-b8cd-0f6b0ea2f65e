# Backend-marketplace

## 📌 Overview

The Backend-marketplace service is the core marketplace API for the Libertum Tokenization Platform. This service provides the complete marketplace functionality for trading tokenized assets, managing user accounts, handling orders, facilitating dividends, and managing NFT collections. It serves as the primary interface for investors and institutions to interact with the tokenization platform.

### Key Responsibilities

- **Asset Trading**: Complete marketplace for buying and selling tokenized assets
- **User Authentication**: Comprehensive authentication and authorization system with 2FA
- **Order Management**: Order creation, matching, settlement, and lifecycle management
- **Dividend Distribution**: Automated dividend calculations and distributions to token holders
- **NFT Management**: Complete NFT marketplace with creation, trading, and collection management
- **Document Signing**: DocuSign integration for legal document execution
- **KYC/AML Compliance**: Full compliance workflows for user onboarding
- **Investment Offerings**: Access to and management of investment opportunities

## 🚀 Getting Started

### Prerequisites

- Node.js v18.0.0 or higher
- npm v8.0.0 or higher
- MongoDB (for data storage)
- Redis (for caching and sessions)
- DocuSign Developer Account
- Email service provider (SendGrid, Mailgun, etc.)

### Installation

```bash
# Clone the repository
git clone https://github.com/Libertum-Project/Backend-marketplace.git
cd Backend-marketplace

# Install dependencies
npm install

# Setup environment variables
cp .env.example .env
# Edit .env with your configuration values
```

### Environment Configuration

```bash
# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
MONGO_URI=mongodb://localhost:27017/libertum_marketplace
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-jwt-secret-here
JWT_REFRESH_SECRET=your-refresh-secret-here
JWT_EXPIRY=24h

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# DocuSign Configuration
DOCUSIGN_CLIENT_ID=your-docusign-client-id
DOCUSIGN_CLIENT_SECRET=your-docusign-client-secret
DOCUSIGN_REDIRECT_URI=your-redirect-uri
DOCUSIGN_BASE_URL=https://demo.docusign.net

# Blockchain Configuration
WEB3_PROVIDER_URL=https://mainnet.infura.io/v3/your-project-id
PRIVATE_KEY=your-private-key-here

# File Upload Configuration
GOOGLE_CLOUD_PROJECT_ID=your-project-id
GOOGLE_CLOUD_BUCKET=your-bucket-name
GOOGLE_CLOUD_KEY_FILE=path/to/service-account-key.json

# Two-Factor Authentication
TOTP_SECRET=your-totp-secret-here

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### Running Locally

```bash
# Development mode (with auto-reload)
npm run dev

# Production mode
npm run build
npm start

# The service will be available at:
# - API: http://localhost:3000
# - Swagger UI: http://localhost:3000/api-docs
# - Health Check: http://localhost:3000/health-check
```

## 🔐 Authentication

The Backend-marketplace service uses JWT (JSON Web Tokens) with optional Two-Factor Authentication:

### Login Process

1. User provides email and password
2. System validates credentials
3. If 2FA is enabled, user provides TOTP code
4. System generates access and refresh tokens
5. User accesses protected endpoints with Bearer token

### Token Usage

```bash
# Include JWT token in requests
Authorization: Bearer <your-jwt-token>
Content-Type: application/json
```

### Two-Factor Authentication

- Time-based One-Time Password (TOTP)
- QR code generation for authenticator apps
- Backup codes for account recovery
- Reset and disable 2FA functionality

### Available Auth Endpoints

- `POST /marketplace/v1/signup` - User registration
- `POST /marketplace/v1/login` - User login
- `POST /marketplace/v1/auth/verify-2fa` - 2FA verification
- `POST /marketplace/v1/auth/enable-2fa` - Enable 2FA
- `POST /marketplace/v1/auth/forgot-password` - Password reset

## 📡 API Documentation

**Swagger UI**: [http://localhost:3000/api-docs](http://localhost:3000/api-docs)

The API is organized into the following main categories:

### Authentication APIs

- User registration and login
- Two-factor authentication management
- Password reset and account recovery
- Session management and token refresh

### User Management APIs

- Profile management and updates
- KYC document upload and verification
- User preferences and settings
- Account status and permissions

### Offering APIs

- Browse investment offerings
- Offering details and documentation
- Investment requests and approvals
- Offering subscription management

### Order Management APIs

- Create buy/sell orders
- Order history and status tracking
- Order cancellation and modification
- Settlement and execution

### Dividend APIs

- Dividend history and calculations
- Automatic dividend distributions
- Dividend claim mechanisms
- Tax reporting and documentation

### NFT APIs

- NFT creation and minting
- NFT marketplace and trading
- Collection management
- Metadata and asset management

### DocuSign APIs

- Document template generation
- Embedded signing workflows
- Document status tracking
- Webhook event processing

## 📂 Directory Structure

```
Backend-marketplace/
├── src/
│   ├── _grpc/                    # gRPC service definitions
│   │   ├── clients/             # gRPC clients
│   │   ├── index.ts
│   │   └── proto/               # Protocol buffer definitions
│   ├── component/               # Business logic components
│   │   ├── dividends/          # Dividend management
│   │   ├── docuSign/           # DocuSign integration
│   │   ├── nft/                # NFT management
│   │   ├── notification/       # Notification system
│   │   ├── offerings/          # Investment offerings
│   │   ├── order/              # Order management
│   │   ├── transactions/       # Transaction processing
│   │   ├── transfer/           # Asset transfers
│   │   └── userAuthentications/ # User authentication
│   ├── config/                  # Configuration files
│   │   ├── connection/         # Database connections
│   │   ├── env/                # Environment configuration
│   │   ├── error/              # Error handling
│   │   ├── middleware/         # Express middleware
│   │   ├── security/           # Security configurations
│   │   └── server/             # Server setup
│   ├── helpers/                 # Utility functions
│   │   ├── logging/            # Logging utilities
│   │   ├── bigMath.ts          # Precision mathematics
│   │   ├── cloud.helper.ts     # Cloud storage operations
│   │   └── common.helper.ts    # Common utilities
│   ├── middleware/              # Route middleware
│   │   ├── dividend.middleware.ts
│   │   ├── docusign.middleware.ts
│   │   ├── fileValidation.middleware.ts
│   │   └── auth.middleware.ts
│   ├── routes/                  # API route definitions
│   │   ├── auth.router.ts
│   │   ├── dividends.router.ts
│   │   ├── docusign.router.ts
│   │   ├── nft.router.ts
│   │   ├── offerings.router.ts
│   │   └── order.router.ts
│   ├── service/                 # Business services
│   │   ├── cronHandler.ts
│   │   └── kafkaService.ts
│   ├── utils/                   # Utility functions
│   │   ├── abi/                # Smart contract ABIs
│   │   ├── emailTemplate/      # Email templates
│   │   ├── common.interface.ts
│   │   ├── constant.ts
│   │   └── disposableDomains.ts
│   └── public/                  # Static assets
├── docker-compose.yaml          # Docker configuration
├── Dockerfile                   # Container setup
└── package.json                # Dependencies and scripts
```

## ⚙️ Environment Variables

| Key                       | Description               | Example Value                                    |
| ------------------------- | ------------------------- | ------------------------------------------------ |
| `PORT`                    | Server port               | `3000`                                           |
| `NODE_ENV`                | Environment mode          | `development`                                    |
| `MONGO_URI`               | MongoDB connection string | `mongodb://localhost:27017/libertum_marketplace` |
| `REDIS_URL`               | Redis connection string   | `redis://localhost:6379`                         |
| `JWT_SECRET`              | JWT signing secret        | `your-jwt-secret-here`                           |
| `JWT_REFRESH_SECRET`      | JWT refresh token secret  | `your-refresh-secret-here`                       |
| `SMTP_HOST`               | Email server host         | `smtp.gmail.com`                                 |
| `SMTP_PORT`               | Email server port         | `587`                                            |
| `SMTP_USER`               | Email username            | `<EMAIL>`                           |
| `SMTP_PASS`               | Email password            | `your-app-password`                              |
| `DOCUSIGN_CLIENT_ID`      | DocuSign client ID        | `your-docusign-client-id`                        |
| `DOCUSIGN_CLIENT_SECRET`  | DocuSign client secret    | `your-docusign-client-secret`                    |
| `WEB3_PROVIDER_URL`       | Blockchain RPC URL        | `https://mainnet.infura.io/v3/...`               |
| `GOOGLE_CLOUD_PROJECT_ID` | Google Cloud project ID   | `your-project-id`                                |
| `TOTP_SECRET`             | TOTP secret for 2FA       | `your-totp-secret-here`                          |

## 🧪 Running Tests

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run unit tests only
npm run test:unit

# Run integration tests only
npm run test:integration

# Run tests in watch mode
npm run test:watch

# Run specific test file
npm test -- --grep "Order Management"
```

## 🛠️ Technologies Used

- **Framework**: Express.js (Node.js web framework)
- **Database**: MongoDB with Mongoose ODM
- **Caching**: Redis for session management and caching
- **Authentication**: JWT with optional TOTP 2FA
- **Documentation**: Swagger/OpenAPI 3.0
- **File Upload**: Multer with Google Cloud Storage
- **Email**: Nodemailer for email notifications
- **Document Signing**: DocuSign API integration
- **Blockchain**: Web3.js for smart contract interactions
- **Validation**: Joi for request validation
- **Logging**: Winston for structured logging
- **Security**: Helmet, CORS, Rate Limiting
- **Message Queue**: Kafka for event processing
- **Docker**: Container support for deployment

## 🧑‍💻 Developer Notes

### Development Setup

1. Ensure MongoDB and Redis are running locally
2. Set up DocuSign developer account and configure API keys
3. Configure Google Cloud Storage for file uploads
4. Copy `.env.example` to `.env` and configure all variables
5. Run `npm run dev` for development mode
6. API documentation is available at `/api-docs`

### Testing API Endpoints

- Use Postman or any API client
- Import the Swagger specification from `/api-docs.json`
- Register a new user or use test credentials
- Include JWT token in Authorization header for protected routes

### Common Development Tasks

```bash
# Start development server with live reload
npm run dev

# Generate new API documentation
npm run docs:generate

# Format code using Prettier
npm run format

# Lint code using ESLint
npm run lint

# Build for production
npm run build

# Run database migrations
npm run migrate

# Generate test data
npm run seed
```

### File Upload Configuration

- Configure Google Cloud Storage bucket
- Set up proper IAM permissions
- Supported file types: PDF, JPG, PNG, DOCX
- Maximum file size: 10MB per file
- Files are automatically scanned for malware

### Debugging

- Logs are stored in the `logs/` directory
- Use `DEBUG=*` environment variable for detailed logs
- Check health endpoint: `GET /health-check`
- Monitor application metrics via `/metrics` endpoint

## 📦 Build & Deploy

### Docker Deployment

```bash
# Build Docker image
docker build -t backend-marketplace .

# Run with Docker Compose
docker-compose up -d

# The service will be available at http://localhost:3000
```

### Production Deployment

```bash
# Install production dependencies
npm ci --only=production

# Build the application
npm run build

# Start the application
npm start
```

### Environment-Specific Configurations

- **Development**: Local databases, debug logging, test API keys
- **Staging**: Staging databases, DocuSign demo environment
- **Production**: Production databases, live DocuSign environment, error-only logging

## 🔧 API Endpoints Summary

| Category      | Endpoint                                          | Method | Description               |
| ------------- | ------------------------------------------------- | ------ | ------------------------- |
| **Health**    | `/health-check`                                   | GET    | Service health check      |
| **Auth**      | `/marketplace/v1/signup`                          | POST   | User registration         |
| **Auth**      | `/marketplace/v1/login`                           | POST   | User login                |
| **Auth**      | `/marketplace/v1/auth/verify-2fa`                 | POST   | 2FA verification          |
| **User**      | `/marketplace/v1/auth/profile`                    | GET    | Get user profile          |
| **User**      | `/marketplace/v1/auth/upload-docs`                | POST   | Upload KYC documents      |
| **Offerings** | `/marketplace/v1/auth/offering/all`               | GET    | Get all offerings         |
| **Offerings** | `/marketplace/v1/auth/offering/request`           | POST   | Request offering access   |
| **Orders**    | `/marketplace/v1/order/create`                    | POST   | Create new order          |
| **Orders**    | `/marketplace/v1/order`                           | GET    | Get user orders           |
| **Dividends** | `/marketplace/v1/auth/dividends`                  | GET    | Get dividend history      |
| **NFT**       | `/marketplace/v1/auth/nft/collections`            | GET    | Get NFT collections       |
| **DocuSign**  | `/marketplace/v1/auth/docusign/generate-template` | POST   | Generate signing template |

## 🎯 Key Features

### Order Management System

- Real-time order matching engine
- Support for market and limit orders
- Partial order filling capabilities
- Automatic settlement processing

### Dividend Distribution

- Automated dividend calculations
- Pro-rata distribution based on holdings
- Tax reporting and documentation
- Multiple dividend payment methods

### NFT Marketplace

- NFT creation and minting
- Collection management
- Secondary market trading
- Royalty distribution

### Document Management

- DocuSign integration for legal documents
- Document templates and workflows
- Electronic signature tracking
- Compliance audit trails

## 📞 Support

For technical support or questions about the Backend-marketplace service:

- **Email**: <EMAIL>
- **Documentation**: https://docs.libertum.com
- **GitHub Issues**: https://github.com/Libertum-Project/Backend-marketplace/issues

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/marketplace-enhancement`)
3. Commit your changes (`git commit -m 'Add marketplace feature'`)
4. Push to the branch (`git push origin feature/marketplace-enhancement`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
