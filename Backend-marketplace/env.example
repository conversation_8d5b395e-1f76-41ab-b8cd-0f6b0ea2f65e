# Application Configuration
NODE_ENV=development
PORT=3000
BASEURL=http://localhost:3000
API_HOST_URL=http://localhost:3000
FRONTEND_URL=http://localhost:3000
PROJECT_NAME=marketplace
LOG_LEVEL=info

# MongoDB Configuration
MONGODB_HOST=localhost
MONGODB_USER=your_mongodb_user
MONGODB_PASSWORD=your_mongodb_password
MONGODB_PORT=27017
MONGODB_DATABASE=mvp_db

# Redis Configuration
REDIS_HOST=localhost
LOGIN_MAX_ATTEMPT=5
LOGIN_BLOCK_TIME=30
OTP_EXPIRY=300
BLOCK_USER=3

# JWT Configuration
JWT_AUTH_SECRET=your_auth_secret
JWT_AUTH_EXPIRE=1h
JWT_REFRESH_SECRET=your_refresh_secret
JWT_REFRESH_EXPIRE=7d
JWT_FORGOT_EXPIRE=1h
JWT_2FA_EXPIRE=5m

# Email Configuration (SendGrid)
SENDGRID_API_KEY=your_sendgrid_api_key
SENDER=your_sender_email

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# Azure Storage Configuration
CONNECTION_STRING=your_azure_connection_string
ACCOUNT_NAME=your_azure_account_name
BUCKET_NAME=your_bucket_name
MAX_SIZE=10

# Kafka Configuration
KAFKA_BROKER=localhost
KAFKA_BROKER_PORT=9092

# gRPC Configuration
USER_SERVICE_GRPC_CONTAINER_NAME=0.0.0.0
USER_SERVICE_GRPC_PORT=40001
ADMIN_SERVICE_GRPC_CONTAINER_NAME=0.0.0.0
ADMIN_SERVICE_GRPC_PORT=40002
NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME=0.0.0.0
NOTIFICATION_SERVICE_GRPC_PORT=8001
GRPC_SSL=false

# SumSub Configuration
SUMSUB_SECRET_KEY=your_sumsub_secret_key
SUMSUB_TOKEN=your_sumsub_token

# Blockchain Configuration
RPC_URL=your_rpc_url
FUND_CONTRACT_ADDRESS=your_fund_contract_address
USDC_ADDRESS=your_usdc_address

# DocuSign Configuration
DOCUSIGN_CLIENT_ID=your_docusign_client_id
DOCUSIGN_IMPERSONATED_USER_ID=your_docusign_impersonated_user_id
DOCUSIGN_ACCOUNT_ID=your_docusign_account_id
DOCUSIGN_OAUTH_URL=your_docusign_oauth_url
DOCUSIGN_PRIVATE_KEY=your_docusign_private_key
DOCUSIGN_WEBHOOK_USERNAME=your_docusign_webhook_username
DOCUSIGN_WEBHOOK_PWD=your_docusign_webhook_password

# Google Configuration
GOOGLE_CLIENT_ID=your_google_client_id

# Sentry Configuration
SENTRY_DSN=your_sentry_dsn

# Cap Table Configuration
CAP_TABLE_URL=your_cap_table_url

# Authentication URLs
LOGIN_URL=/auth/login

SONAR_HOST=http://localhost:9000
SONAR_TOKEN=sqa_716ddb46061fca0cbe1c38a764771c67d84e1a60