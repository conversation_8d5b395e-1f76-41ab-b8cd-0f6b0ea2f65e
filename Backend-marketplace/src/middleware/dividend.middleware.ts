import { NextFunction, Request, Response } from 'express';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>R<PERSON>ult, PromiseResolve } from '../utils/common.interface';
import DividentsValidation from '../component/dividends/validation';
import CustomError from '../helpers/customError.helper';
import { ResponseHandler } from '../helpers/response.helper';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';

/**
 * Validate the request body for dividend creation
 * @param req - The request body
 * @param res - The response object
 * @param next - The next function in the middleware stack
 * @returns A promise that resolves if the request is valid or rejects if it is not
 */
export async function DividendValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await DividentsValidation.createDividend(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Middleware to validate the request query for fetching dividends.
 * @param req - The request object containing query parameters.
 * @param res - The response object for sending error responses.
 * @param next - The next middleware function in the stack.
 * @returns A promise that resolves if the request is valid or sends an error response if not.
 */
export async function DividendFetchValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await DividentsValidation.getDividendsValidation(req.query);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Middleware to validate the request query for fetching dividends by an investor.
 * @param req - The request object containing query parameters.
 * @param res - The response object for sending error responses.
 * @param next - The next middleware function in the stack.
 * @returns A promise that resolves if the request is valid or sends an error response if not.
 */
export async function DividendInvestorFetchValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await DividentsValidation.getDividendsInvestorValidation(req.query);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validate the request body for dividend calculation.
 * @param req - The request object containing the query parameters.
 * @param res - The response object for sending error responses.
 * @param next - The next middleware function in the stack.
 * @returns A promise that resolves if the request is valid or sends an error response if not.
 */
export async function DividendCalculateValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await DividentsValidation.calculate(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validate the request query parameters for fetching dividend history.
 * @param req - The request object containing the query parameters.
 * @param res - The response object for sending error responses.
 * @param next - The next middleware function in the stack.
 * @returns A promise that resolves if the request is valid or sends an error response if not.
 */
export async function DividendHistoryValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await DividentsValidation.history(req.query);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validate the request query parameters for fetching dividend history of an investor.
 * @param req - The request object containing the query parameters.
 * @param res - The response object for sending error responses.
 * @param next - The next middleware function in the stack.
 * @returns A promise that resolves if the request is valid or sends an error response if not.
 */
export async function investorDividendHistoryValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await DividentsValidation.getinvestorDividendsHistoryValidation(req.query);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
