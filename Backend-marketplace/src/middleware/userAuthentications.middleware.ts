/* eslint-disable no-restricted-syntax */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { Request, Response, NextFunction } from 'express';
import { JoiValidationResult, PromiseResolve, UserTypeEnum } from '../utils/common.interface';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import UserValidation from '../component/userAuthentications/validation';
import { ResponseHandler } from '../helpers/response.helper';
import logger from '../helpers/logging/logger.helper';
import CustomError from '../helpers/customError.helper';
import CommonHelper from '../helpers/common.helper';

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function validateSignUpReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.singUpValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'validateSignupReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function validateLoginReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.loginValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'validateLoginReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function socialLoginValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.socialLoginValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'socialLoginValidationReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function validateVerificationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.verificationValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);

    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'validateLoginReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function validateEmailReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.emailValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'validatePasswordReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function changePasswordReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.changePasswordValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    const { password, oldPassword } = validateRequest.value;
    req.body = { oldPassword, password, email: req.body.email };
    next();
  } catch (error: any) {
    logger.error(error, 'changePassReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function validateResetPasswordReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.resetPasswordValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'changePassReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function validateUpdateProfileReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.updateProfileValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'validateUpdateProfileReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function validateUpdateKycReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const { userType } = req.userInfo;

    const validateRequest: JoiValidationResult = userType === UserTypeEnum.Institution ? await UserValidation.updateKybValidation(req.body) : await UserValidation.updateKycValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);

    // kyc all date formats
    if (validateRequest.value?.mainInformation?.dob) {
      const formattedDate = await CommonHelper.formatDates(validateRequest.value.mainInformation?.dob);
      validateRequest.value.mainInformation.dob = formattedDate;
    }
    if (validateRequest.value?.mainInformation?.documentExpiration) {
      const formattedDate = await CommonHelper.formatDates(validateRequest.value.mainInformation?.documentExpiration);
      validateRequest.value.mainInformation.documentExpiration = formattedDate;
    }

    // kyb all date formats

    if (validateRequest.value.primaryContactInfo?.personalInformation?.dob) {
      const formattedDate = await CommonHelper.formatDates(validateRequest.value.primaryContactInfo?.personalInformation?.dob);
      validateRequest.value.primaryContactInfo.personalInformation.dob = formattedDate;
    }

    // Format dob for each beneficial owner in the array
    if (validateRequest.value?.beneficialOwners && Array.isArray(validateRequest.value.beneficialOwners)) {
      for (const owner of validateRequest.value.beneficialOwners) {
        if (owner?.personalInformation?.dob) {
          // eslint-disable-next-line no-await-in-loop
          const formattedDate = await CommonHelper.formatDates(owner.personalInformation.dob);
          owner.personalInformation.dob = formattedDate;
        }
      }
    }

    // Format dob for each managementInfo owner in the array
    if (validateRequest.value?.managementInfo && Array.isArray(validateRequest.value.managementInfo)) {
      for (const owner of validateRequest.value.managementInfo) {
        if (owner?.personalInformation?.dob) {
          // eslint-disable-next-line no-await-in-loop
          const formattedDate = await CommonHelper.formatDates(owner.personalInformation.dob);
          owner.personalInformation.dob = formattedDate;
        }
      }
    }

    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'validateUpdateKycReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function tokenValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.verifyTokenValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'tokenValidationReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function docsValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.docsValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'tokenValidationReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function verify2FAReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.verify2FAValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'tokenValidationReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function resendOtpValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.resendOtpValidation(req.body);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'resendOtpValidationReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function getUserPortfolio(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const { page, limit, search } = req.query;
    const isCsv = req.query.isCsv === 'true';
    // Convert the query params to numbers and validate
    const validateRequest: JoiValidationResult = await UserValidation.getUserPortfolio({
      page: page ? Number(page) : undefined,
      limit: limit ? Number(limit) : undefined,
      search: search ? String(search as string) : undefined,
      isCsv: isCsv ? Boolean(isCsv) : false,
    });
    if (validateRequest.error) {
      throw new CustomError(validateRequest.message, validateRequest.status);
    }

    // Replace req.query with the validated and possibly defaulted values
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'getPortfolio Error');

    return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function getProfileReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.getProfileValidation({ ...req.query });
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'changePassReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function getUserProfileReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.getUserProfileValidation({ ...req.query, id: req.params.id });
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'getUserProfileReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function transferAgentListValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await UserValidation.transferAgentListValidation(req.query as Record<string, any>);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'transferAgentListValidationReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
