import * as express from 'express';
import { rateLimit } from 'express-rate-limit';
import { ResponseHandler } from '../helpers/response.helper';
import { RESPONSES } from '../utils/responseUtils';

/**
 * Rate limiter middleware configuration
 * Limits requests based on user ID or IP address
 */
export const createRateLimiter = (options: {
  windowMs?: number; // Time window in milliseconds
  max?: number; // Maximum requests per window
  message?: string; // Custom error message
}) => {
  const {
    windowMs = 15 * 60 * 1000, // Default: 15 minutes
    max = 200, // Default: 200 requests per windowMs
    message = 'Too many requests, please try again later',
  } = options;

  return rateLimit({
    windowMs,
    max,
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers

    // Use user ID as the key if available, fallback to IP
    keyGenerator: (req: any) => {
      // If user is authenticated, use their ID
      if (req.user && req.user.id) {
        return req.user.id.toString();
      }

      // Fallback to IP address
      return req.ip || (req.headers['x-forwarded-for'] as string) || '127.0.0.1';
    },

    // Custom handler for when rate limit is exceeded
    handler: (req: any, res: express.Response<any, Record<string, any>>) => {
      ResponseHandler.error(res, {
        error: true,
        message,
        status: RESPONSES.TOO_MANY_REQUESTS,
      });
    },

    // Skip rate limiting for certain paths if needed
    skip: (req: { originalUrl: string | string[] }) => {
      // Don't rate limit health check endpoint
      return req.originalUrl.includes('/health-check');
    },
  });
};

/**
 * Configure different rate limiters based on route/endpoint importance
 */
export function configureRateLimiting(app: express.Application): void {
  // Default rate limiter for all routes
  const defaultLimiter = createRateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 1000, // 100 requests per 15 minutes
  });

  // Default limiter for all other routes
  app.use(defaultLimiter);
}
