import { JoiValidationResult } from '../utils/common.interface';
// import * as <PERSON><PERSON> from 'joi';
import { Request, Response, NextFunction } from 'express';
// import * as joiOptions from '../helpers/joiError.filter.helper';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import { ResponseHandler } from '../helpers/response.helper';
import logger from '../helpers/logging/logger.helper';
import CustomError from '../helpers/customError.helper';
import NotificationValidation from '../component/notification/validation';

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function getNotification(req: Request, res: Response, next: NextFunction): Promise<JoiValidationResult | void> {
  try {
    const validateRequest: JoiValidationResult = await NotificationValidation.getNotification(req.query);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'seen OfferingNotification Validation Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */

export async function seenNotification(req: Request, res: Response, next: NextFunction): Promise<JoiValidationResult | void> {
  try {
    const validateRequest: JoiValidationResult = await NotificationValidation.notificationSeenJoi(req.query);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'seen OfferingNotification Validation Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
