import { Request, Response, NextFunction } from 'express';
import logger from '../helpers/logging/logger.helper';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import { JoiValidationResult, PromiseResolve } from '../utils/common.interface';
import { TransferRequestValidation } from '../component/transfer/validation';
import CustomError from '../helpers/customError.helper';
import { ResponseHandler } from '../helpers/response.helper';

/**
 * Validate the request body for creating a transfer request
 * @param req - The request containing the transfer request data
 * @param res - The response object
 * @param next - The next function in the middleware stack
 * @returns A promise that resolves if the request is valid or rejects if it is not
 */
export async function createTransferRequestValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const transferData = req.body;

    // Validate transfer request data using the TransferRequestValidation schema
    const validateRequest: JoiValidationResult = await TransferRequestValidation.createTransferRequestValidation(transferData);

    if (validateRequest.error) {
      throw new CustomError(validateRequest.message, validateRequest.status);
    }
    // Replace req.body with the validated data
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'createTransferRequestValidationReq Error');

    return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Validate the request query for retrieving transfer requests
 * @param req - The request containing the query parameters
 * @param res - The response object
 * @param next - The next function in the middleware stack
 * @returns A promise that resolves if the request is valid or rejects if it is not
 */
export async function getTransferRequestsValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const { page, limit, search } = req.query;

    // Convert query params to numbers and validate
    const validateRequest: JoiValidationResult = await TransferRequestValidation.getTransferRequestsValidation({
      page: page ? Number(page) : undefined,
      limit: limit ? Number(limit) : undefined,
      search: search ? String(search as string) : undefined,
    });

    if (validateRequest.error) {
      throw new CustomError(validateRequest.message, validateRequest.status);
    }

    // Replace req.query with the validated and possibly defaulted values
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'getTransferRequestsValidationReq Error');

    return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
/**
 * Validate the request body for rejecting a transfer request
 * @param req - The request containing the query parameters
 * @param res - The response object
 * @param next - The next function in the middleware stack
 * @returns A promise that resolves if the request is valid or rejects if it is not
 */
export async function rejectTransferRequestsValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const { transferReqId, reason } = req.body;

    // Convert query params to numbers and validate
    const validateRequest: JoiValidationResult = await TransferRequestValidation.rejectTransferRequestsValidation({ reason: reason ? String(reason as string) : undefined, transferReqId: transferReqId ? String(transferReqId) : undefined });

    if (validateRequest.error) {
      throw new CustomError(validateRequest.message, validateRequest.status);
    }

    // Replace req.query with the validated and possibly defaulted values
    req.body = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'rejectTransferRequestsValidationReq Error');

    return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
/**
 * Middleware to validate transfer requests by offering ID.
 * @exports
 * @param {Request} req - The express request object containing query and params.
 * @param {Response} res - The express response object.
 * @param {NextFunction} next - The next middleware function.
 * @returns {Promise<PromiseResolve | void>} - Resolves if validation is successful, otherwise sends an error response.
 */
export async function getTransferRequestsByofferingValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const { page, limit, search, isCsv } = req.query;
    const { offeringId } = req.params;
    const isCsvBool = isCsv === 'true'; // Convert isCsv to boolean (true if 'true', otherwise false)
    // Convert query params to numbers and validate
    const validateRequest: JoiValidationResult = await TransferRequestValidation.getTransferRequestsFromOfferingIdValidation({
      page: page ? Number(page) : undefined,
      limit: limit ? Number(limit) : undefined,
      search: search ? String(search as string) : undefined,
      offeringId: String(offeringId),
      isCsv: isCsvBool, // Pass the boolean value
    });

    if (validateRequest.error) {
      throw new CustomError(validateRequest.message, validateRequest.status);
    }
    req.params = { offeringId: validateRequest.value.offeringId };
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'getTransferRequestsFromOfferingIdValidation Error');

    return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
