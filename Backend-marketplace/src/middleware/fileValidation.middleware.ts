import { Request, Response, NextFunction } from 'express';

/**
 * Middleware to validate uploaded image files.
 * Ensures a file is provided and that it is a valid JPEG, PNG, or GIF image.
 *
 * @param req - The request object containing the file to validate.
 * @param res - The response object to send error responses if validation fails.
 * @param next - The next middleware function in the stack.
 */
export function validateImageFiles(req: Request, res: Response, next: NextFunction) {
  const file = req.file;

  // Check if a file is provided
  if (!file) {
    return res.status(400).json({ error: 'No file provided' });
  }

  // Define allowed MIME types for images
  const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif'];

  // Validate the file's MIME type
  if (!allowedMimeTypes.includes(file.mimetype)) {
    return res.status(400).json({ error: 'Invalid file type. Only JPEG, PNG, and GIF are allowed.' });
  }

  // Proceed to the next middleware if validation passes
  next();
}
