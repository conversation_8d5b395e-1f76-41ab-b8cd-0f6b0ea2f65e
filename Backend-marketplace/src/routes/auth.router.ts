import { Router } from 'express';
import { authComponent } from '../component';
import { validateEmailReq, validateVerificationReq, validateLoginReq, validateSignUpReq, socialLoginValidationReq, resendOtpValidationReq, validateResetPasswordReq, verify2FAReq, tokenValidationReq } from '../middleware';

/**
 * @constant {express.Router}
 */

const router: Router = Router();
/**
 * Auth API
 * @description This module contains all the routes related to the user authentication
 */

/**
 * @description This route will create a new user
 * @param {object} req - The request object
 * @param {object} res - The response object
 * @param {function} next - The next middleware
 */
router.post('/signup', validateSignUpReq, authComponent.signUp);

/**
 * @description This route will login the user
 * @param {object} req - The request object
 * @param {object} res - The response object
 * @param {function} next - The next middleware
 */
router.post('/login', validateLoginReq, authComponent.login);

/**
 * @description This route will login the user with social media
 * @param {object} req - The request object
 * @param {object} res - The response object
 * @param {function} next - The next middleware
 */
router.post('/social-login', socialLoginValidationReq, authComponent.socialLogin);

/**
 * @description This route will verify the user
 * @param {object} req - The request object
 * @param {object} res - The response object
 * @param {function} next - The next middleware
 */
router.post('/verify', validateVerificationReq, authComponent.verify);

/**
 * @description This route will resend the otp
 * @param {object} req - The request object
 * @param {object} res - The response object
 * @param {function} next - The next middleware
 */
router.post('/resend-otp', resendOtpValidationReq, authComponent.reSendOtp);

/**
 * @description This route will send the password reset link
 * @param {object} req - The request object
 * @param {object} res - The response object
 * @param {function} next - The next middleware
 */
router.post('/forgot-password', validateEmailReq, authComponent.forgotPassword);

/**
 * @description This route will reset the password
 * @param {object} req - The request object
 * @param {object} res - The response object
 * @param {function} next - The next middleware
 */
router.post('/reset-password', validateResetPasswordReq, authComponent.resetPassword);

/**
 * @description This route will verify the 2FA
 * @param {object} req - The request object
 * @param {object} res - The response object
 * @param {function} next - The next middleware
 */
router.post('/verify-2fa', verify2FAReq, authComponent.verifyLogin2FA);

/**
 * @description This route will disable the 2FA
 * @param {object} req - The request object
 * @param {object} res - The response object
 * @param {function} next - The next middleware
 */
router.patch('/forgot-2fa', tokenValidationReq, authComponent.forgot2FA);

/**
 * @description Contact us
 * @param {object} req - The request object
 * @param {object} res - The response object
 * @param {function} next - The next middleware
 */
router.post('/contact-us', authComponent.contactUs);

/**
 * @export {express.Router}
 */
export default router;
