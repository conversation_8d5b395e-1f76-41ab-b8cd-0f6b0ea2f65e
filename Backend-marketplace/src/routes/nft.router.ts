import { Router } from 'express';
import * as jwtAuthenticated from '../config/middleware/jwtAuthenticated';
import { NftComponent } from '../component';
import { nftCollectionValidationReq, createNftCollectionValidationReq, createNftValidationReq } from '../middleware';

/**
 * @constant {express.Router}
 */

const router: Router = Router();
router.use(jwtAuthenticated.isAuthenticated);

/**
 * Route to get the list of all the collection.
 * @name getTransactions
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.get('/', jwtAuthenticated.isIssuerCompleted, nftCollectionValidationReq, NftComponent.getCollection);

/**
 * Route to get the list of all the collection.
 * @name getTransactions
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.post('/create-collection', jwtAuthenticated.isIssuerCompleted, createNftCollectionValidationReq, NftComponent.createNftCollection);

/**
 * Create nft.
 * @name getTransactions
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.post('/', jwtAuthenticated.isIssuerCompleted, createNftValidationReq, NftComponent.createNft);

/**
 * Get details of an nft
 * @param {Request} req
 * @param {Response} res
 */
router.get('/:id', NftComponent.getNftDetails);

/**
 * @export {express.Router}
 */

export default router;
