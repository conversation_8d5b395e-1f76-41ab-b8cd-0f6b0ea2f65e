import { Router } from 'express';
import * as jwtAuthenticated from '../config/middleware/jwtAuthenticated';
import TransferRequestController from '../component/transfer';
import { createTransferRequestValidationReq, getTransferRequestsByofferingValidationReq, getTransferRequestsValidationReq, rejectTransferRequestsValidationReq } from '../middleware/transfer.middleware';

const router: Router = Router();

// Ensure the user is authenticated before accessing the routes
router.use(jwtAuthenticated.isAuthenticated);

/**
 * @route POST /
 * @description Create a new transfer request
 * @access Public
 */
router.post('/', createTransferRequestValidationReq, TransferRequestController.createTransferRequest);

/**
 * @route GET /
 * @description Get all transfer requests
 * @access Public
 */
router.get('/', getTransferRequestsValidationReq, TransferRequestController.getAllTransferRequests);

/**
 * @route PUT /reject
 * @description Reject a transfer request
 * @access Public
 */
router.put('/reject', rejectTransferRequestsValidationReq, TransferRequestController.rejectTransferRequest);

/**
 * @route GET /:offeringId
 * @description Get all transfer requests by offering ID
 * @access Issuer (completed)
 */
router.get('/:offeringId', getTransferRequestsByofferingValidationReq, jwtAuthenticated.isIssuerCompleted, TransferRequestController.getAllTransferRequestsByOffering);

export default router;
