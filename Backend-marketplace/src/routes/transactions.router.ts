import { Router } from 'express';
import * as jwtAuthenticated from '../config/middleware/jwtAuthenticated';
import { transactionsValidationReq } from '../middleware/transactions.middleware';
import { TransactionController } from '../component';

/**
 * @constant {express.Router}
 */

const router: Router = Router();
router.use(jwtAuthenticated.isAuthenticated);

/**
 * Route to get the list of all the transactions for a specific offering.
 * @name getTransactions
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.get('/transactions/:offeringId', jwtAuthenticated.isIssuerCompleted, transactionsValidationReq, TransactionController.getTransactions);
router.get('/staking-transactions/:token', TransactionController.getStakingTransactions);
router.get('/staking-users/:token', TransactionController.getUserStakingSummary);
router.get('/staking-dividends/:token/:month', TransactionController.getStakingDividendList);
router.post('/staking-rewards/:token/:month', TransactionController.updateStakingRewardAmount);

/**
 * @export {express.Router}
 */

export default router;
