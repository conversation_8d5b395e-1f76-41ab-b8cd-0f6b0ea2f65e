import { Router } from 'express';
import { authComponent } from '../component';
import { changePasswordReq, docsValidationReq, getProfileReq, getUserPortfolio, getUserProfileReq, tokenValidationReq, transferAgentListValidationReq, validateUpdateKycReq, validateUpdateProfileReq } from '../middleware';
import * as file from '../middleware/googleCloud.middleware';
import * as jwtAuthenticated from '../config/middleware/jwtAuthenticated';

/**
 * @constant {express.Router}
 */

const router: Router = Router();
router.use(jwtAuthenticated.isAuthenticated);
/**
 * Route to get the current user's profile.
 * @name getProfile
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.get('/profile', getProfileReq, authComponent.getUserProfile);

/**
 * Route to get the details of a user by their user ID.
 * @name getUserProfileDetails
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.get('/profile/:id', getUserProfileReq, authComponent.getUserProfileDetails);

/**
 * Route to update the user's profile.
 * @name updateProfile
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.put('/profile', file.validateImageFiles, validateUpdateProfileReq, authComponent.updateProfile);
/**
 * Route to change the user's password.
 * @name changePassword
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.patch('/change-password', changePasswordReq, authComponent.changePassword);
/**
 * Route to enable 2FA.
 * @name enable2FA
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.get('/enable-2fa', authComponent.enable2FA);
/**
 * Route to verify 2FA.
 * @name verify2FA
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.post('/verify-2fa', tokenValidationReq, authComponent.verify2FA);
/**
 * Route to disable 2FA.
 * @name disable2FA
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.patch('/disable-2fa', tokenValidationReq, authComponent.disable2FA);
/**
 * Route to upload documents.
 * @name uploadDocs
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.post('/upload-docs', file.validateFiles, docsValidationReq, authComponent.uploadDocs);
/**
 * Route to update the user's KYC.
 * @name updateKyc
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.put('/kyc', validateUpdateKycReq, authComponent.updateKyc);

/**
 * Route to log out the user.
 * @name logOut
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.get('/log-out', authComponent.logOut);

/**
 * Route to become an issuer.
 * @name becomeIssuer
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.patch('/become', authComponent.becomeIssuer);

/**
 * Route to disable OTP.
 * @name disableOTP
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.post('/disable-otp', authComponent.disableOTP);

/**
 * Route to send verification OTP.
 * @name verification
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.post('/send-verification-otp', authComponent.verification);

/**
 * Route to get the user's portfolio.
 * @name getUserPortfolio
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.get('/portfolio', getUserPortfolio, authComponent.getUserPortfolio);

/**
 * Route to get the list of transfer agents.
 * @name getTransferAgent
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.get('/transferagent', transferAgentListValidationReq, authComponent.getTransferAgent);

/**
 * Route to get the details of a transfer agent by their ID.
 * @name getTransferAgentDetails
 * @function
 * @inner
 * @param {Request} req - The incoming request.
 * @param {Response} res - The outgoing response.
 */
router.get('/transferagent/:transferAgentId', authComponent.getTransferAgentDetails);

/**
 * @export {express.Router}
 */

export default router;
