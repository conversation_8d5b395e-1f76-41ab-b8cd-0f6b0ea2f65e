import * as express from 'express';
import * as swaggerUi from 'swagger-ui-express';
import AuthRouter from './auth.router';
import UserRouter from './user.router';
import DocuSignRouter from './docusign.router';
import OfferingRouter from './offering.router';
import transactions from './transactions.router';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import { ResponseHandler } from '../helpers/response.helper';
import { swaggerDefinition } from '../utils/swaggerDef';
import KycRouter from './kyc.router';
import TransferRouter from './transfer.router';
import OrderRouter from './order.router';
import DividentsRouter from './dividends.router';
import logger from '../helpers/logging/logger.helper';
import NotificationRouter from './notification.router';
import nftRouter from './nft.router';
// import { configureRateLimiting } from '../middleware/rate-limiter';

/**
 * @export
 * @param {express.Application} app
 */
export function init(app: express.Application): void {
  // Configure rate limiting
  // configureRateLimiting(app);
  /**
   * Create a base router with the "v1" prefix
   */
  const router: express.Router = express.Router();
  app.use((req, res, next) => {
    logger.info(`Requested URL ====>>>>>>>>>>>>>> ${req.originalUrl}`);
    next();
  });
  /**
   * @description Auth Router
   * @constructs
   * @param {express.Application} app - The express application
   * @returns {void}
   */
  router.use('/', AuthRouter);

  /**
   * @description Kyc Router
   * @constructs
   * @param {express.Application} app - The express application
   * @returns {void}
   */
  router.use('/', KycRouter);

  /**
   * @description Order Router
   * @constructs
   * @param {express.Application} app - The express application
   * @returns {void}
   */
  router.use('/order', OrderRouter);

  /**
   * @description Dividend Router
   * @constructs
   * @param {express.Application} app - The express application
   * @returns {void}
   */
  router.use('/dividend', DividentsRouter);

  /**
   * @description notification Router
   * @constructs
   * @param {express.Application} app - The express application
   * @returns {void}
   */
  router.use('/auth/notification', NotificationRouter);

  /**
   * @description Authenticated user routes
   */
  router.use('/auth', UserRouter);

  /**
   * @description Token transfer Router
   * @constructs
   * @param {express.Application} app - The express application
   * @returns {void}
   */
  router.use('/auth/transfer', TransferRouter);

  /**
   * @description Docusign Router
   * @constructs
   * @param {express.Application} app - The express application
   * @returns {void}
   */
  router.use('/auth/docusign', DocuSignRouter);

  /**
   * @description offering Router
   * @constructs
   * @param {express.Application} app - The express application
   * @returns {void}
   */
  router.use('/auth/offering', OfferingRouter);

  /**
   * @description nft Router
   * @constructs
   * @param {express.Application} app - The express application
   * @returns {void}
   */
  router.use('/auth/nft', nftRouter);

  /**
   * @description Transactions Router
   * @constructs
   * @param {express.Application} app - The express application
   * @returns {void}
   */
  router.use('/auth', transactions);

  /**
   * @description Swagger Routes
   */
  router.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDefinition));

  /**
   * Attach the base "v1" router to the app
   */
  app.use('/marketplace/v1', router);

  /**
   * @description HealthCheck
   * @constructs
   */
  app.use('/marketplace/v1/health-check', (req, res) => {
    try {
      return ResponseHandler.success(res, { message: RES_MSG?.SUCCESS_MSG.HEALTH_CHECK, status: RESPONSES.SUCCESS, error: false, data: { timestamp: new Date() } });
    } catch (error: any) {
      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  });

  /**
   * @description if page not found
   * @constructs
   */
  app.use((req, res) => {
    ResponseHandler.error(res, { error: true, message: RES_MSG.ERROR_MSG.PAGE_NOT_FOUND, status: RESPONSES.NOTFOUND });
  });
}
