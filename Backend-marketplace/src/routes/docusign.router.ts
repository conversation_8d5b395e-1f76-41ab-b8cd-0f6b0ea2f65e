import { Router } from 'express';
import * as jwtAuthenticated from '../config/middleware/jwtAuthenticated';
import { generateEmbeddedSigningUrl, docusignWebhook } from '../component/docuSign';
import { DocuSignValidationReq, DocuSignWebhookValidationReq } from '../middleware/docusign.middleware';

const router: Router = Router();

// Ensure the user is authenticated before accessing the routes
router.use(jwtAuthenticated.isAuthenticated);

// Get all orders by user ID with query validation (pagination)
// router.post('/generate-template-id', DocuSignValidationReq, generateTemplateId);
// Route to generate an embedded signing URL for a given offering
// This route requires the user to be authenticated and the request to be validated
// using the DocuSignValidationReq middleware.
router.post('/generate-embedded-signing-url', DocuSignValidationReq, generateEmbeddedSigningUrl);

// Route to handle DocuSign webhook events
// This route requires the user to be authenticated and the request to be validated
// using the DocuSignWebhookValidationReq middleware.
router.post('/webhook_event', DocuSignWebhookValidationReq, docusignWebhook);

export default router;
