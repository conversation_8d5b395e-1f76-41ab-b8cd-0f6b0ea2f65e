import { Router } from 'express';
import * as jwtAuthenticated from '../config/middleware/jwtAuthenticated';
import { Dividents } from '../component';
import * as file from '../middleware/googleCloud.middleware';
import { investorDividendHistoryValidationReq, DividendCalculateValidationReq, DividendFetchValidationReq, DividendInvestorFetchValidationReq, DividendValidationReq } from '../middleware/dividend.middleware';

const router: Router = Router();

router.use(jwtAuthenticated.isAuthenticated);

/**
 * @route POST /
 * @description Create a new dividend request
 * @access Issuer (completed)
 */
router.post('/', file.validateImageFiles, DividendValidationReq, Dividents.createDividentReq);

/**
 * @route GET /
 * @description Fetch all dividend requests
 * @access Issuer (completed)
 */
router.get('/', DividendFetchValidationReq, Dividents.getDividentReq);

/**
 * @route GET /investors
 * @description Fetch dividends for investors
 * @access Issuer (completed)
 */
router.get('/investors', DividendInvestorFetchValidationReq, Dividents.investors);

/**
 * @route POST /calculate
 * @description Calculate dividends
 * @access Issuer (completed)
 */
router.post('/calculate', DividendCalculateValidationReq, Dividents.calculateDividend);

/**
 * @route GET /investor-history
 * @description Fetch dividend history for an investor
 * @access Investor
 */
router.get('/investor-history', investorDividendHistoryValidationReq, Dividents.dividendHistory);

export default router;
