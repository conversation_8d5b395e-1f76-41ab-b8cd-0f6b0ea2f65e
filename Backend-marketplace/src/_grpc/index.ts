import { loadSync } from '@grpc/proto-loader';
import { loadPackageDefinition, Server, ServerCredentials, ServerUnaryCall, sendUnaryData, status as grpcStatus } from '@grpc/grpc-js';
import * as path from 'path';
import CONFIG from '../config/env';
import { offeringStatusEnum, PromiseResolve } from '../utils/common.interface';
import { RES_MSG } from '../utils/responseUtils';
import logger from '../helpers/logging/logger.helper';
import TransferRequestController from '../component/transfer';
import UserAuthenticationController from '../component/userAuthentications';
import { offeringComponent } from '../component';
import OfferingService from '../component/offerings/service';
import NftCollectionService from '../component/nft/service';
import { DividendReq } from '../component/dividends/models/dividend';
import { Types } from 'mongoose';
import dividendRecords from '../component/dividends/models/dividendRecords';
// Constants for proto loading
const PROTO_PATH = path.join(__dirname, '/proto/user.proto');
const PROTO_OPTIONS = { keepCase: true, longs: String, enums: String, arrays: true };

// Helper to load proto definition
function loadProto() {
  const packageDefinition = loadSync(PROTO_PATH, PROTO_OPTIONS);

  return loadPackageDefinition(packageDefinition);
}

class GrpcServer {
  private server: Server;

  private host: string;

  private port: string;

  constructor(host: string = CONFIG.GRPC.USER_SERVICE_GRPC_CONTAINER_NAME.toString(), port: string = CONFIG.GRPC.USER_SERVICE_GRPC_PORT.toString()) {
    this.host = host;
    this.port = port;
    this.server = new Server();
  }

  /**
   * Initialize and start the gRPC server
   */

  public async start() {
    const proto: any = loadProto();
    const userGrpcService = proto.user?.UserGrpcService?.service;

    if (!userGrpcService) {
      logger.error('UserGrpcService is not defined in the proto file');

      return;
    }

    this.addServices(userGrpcService);
    this.bindServer();
  }

  /**
   * Bind gRPC server to host and port
   */
  private bindServer() {
    this.server.bindAsync(`${this.host}:${this.port}`, ServerCredentials.createInsecure(), (error: Error | null) => {
      logger.info(this.host, this.port, '-----------');
      if (error) {
        logger.error(error.message, 'Failed to bind gRPC server:');

        return;
      }
      logger.info(`Marketplace Service gRPC server started on ${this.host}:${this.port}`);
      this.server.start();
    });
  }

  /**
   * Add services to the gRPC server
   * @param userService - gRPC service definition for user operations
   */
  private addServices(userService: any) {
    this.server.addService(userService, {
      approveKyc: this.approveKycHandler.bind(this),
      approveWhitelist: this.approveWhitelistHandler.bind(this),
      approveIssuer: this.becomeIssuerHandler.bind(this),
      unblockUser: this.unblockHandler.bind(this),
      rejectOffering: this.rejectHandler.bind(this),
      rejectForceTransfer: this.rejectForcedTransferHandler.bind(this),
      sendOffering: this.sendOffering.bind(this),
      scheduleOffering: this.scheduleOffering.bind(this),
      updateNftStatus: this.updateNftStatus.bind(this),
      checkDividendStatus: this.checkDividendStatus.bind(this),
    });
  }

  /**
   * Handler for approve KYC gRPC call
   * @param call - Incoming gRPC call
   * @param callback - gRPC callback function to send the response
   */
  private async checkDividendStatus(call: ServerUnaryCall<any, any>, callback: sendUnaryData<any>) {
    try {
      const req = call.request;
      const { _id } = req;

      const objectId = new Types.ObjectId(_id.toString());

      // Fetch the latest dividend status from DividendReq
      const lastDividend = await DividendReq.findOne({ offeringId: objectId }, {}, { sort: { declarationDate: -1 } });

      if (!lastDividend) {
        return callback(null, {
          success: true,
          message: 'No dividend record found.',
        });
      }

      const { status } = lastDividend;

      // CASE 3: Dividend is COMPLETED → admin can change the fee
      if (status === 'COMPLETED') {
        return callback(null, {
          success: true,
          message: 'Dividend is completed. Admin can update the fee.',
        });
      }

      // CASE 2: Dividend is pending and exists in dividendRecords (not distributed)
      const dividendRecord = await dividendRecords.findOne({ offeringId: objectId }, {}, { sort: { declarationDate: -1 } });

      if (dividendRecord) {
        return callback(null, {
          success: false,
          message: 'Dividend is still pending and distribution has not completed.',
        });
      }

      // CASE 1: Dividend is pending and not even in records → still false
      return callback(null, {
        success: false,
        message: 'Dividend is still pending.',
      });
    } catch (error: any) {
      logger.error(error, 'Error checking dividend status:');
      callback(
        {
          code: grpcStatus.INTERNAL,
          message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        },
        null,
      );
    }
  }

  /**
   * Handler for approve KYC gRPC call
   * @param call - Incoming gRPC call
   * @param callback - gRPC callback function to send the response
   */
  private async approveKycHandler(call: ServerUnaryCall<any, any>, callback: sendUnaryData<any>) {
    try {
      const req = call.request;
      const res: PromiseResolve = await UserAuthenticationController.approveKyc(req);
      callback(null, res);
    } catch (error: any) {
      logger.error(error, 'Error during KYC approval:');
      callback({ code: grpcStatus.INTERNAL, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR }, null);
    }
  }

  private async approveWhitelistHandler(call: ServerUnaryCall<any, any>, callback: sendUnaryData<any>) {
    try {
      const req = call.request;
      const res: PromiseResolve = await offeringComponent.whiteList(req);
      callback(null, res);
    } catch (error: any) {
      logger.error(error, 'Error during KYC approval:');
      callback({ code: grpcStatus.INTERNAL, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR }, null);
    }
  }

  private async rejectHandler(call: ServerUnaryCall<any, any>, callback: sendUnaryData<any>) {
    try {
      const req = call.request;
      const res: PromiseResolve = await offeringComponent.rejectOffering(req);
      callback(null, res);
    } catch (error: any) {
      logger.error(error, 'Error during KYC approval:');
      callback({ code: grpcStatus.INTERNAL, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR }, null);
    }
  }

  private async rejectForcedTransferHandler(call: ServerUnaryCall<any, any>, callback: sendUnaryData<any>) {
    try {
      const req = call.request;
      const res: PromiseResolve = await TransferRequestController.rejectForcedTransferHandler(req);
      callback(null, res);
    } catch (error: any) {
      logger.error(error, 'Error during KYC approval:');
      callback({ code: grpcStatus.INTERNAL, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR }, null);
    }
  }

  /**
   * Handler offering from subadmin
   * @param call - Incoming gRPC call
   * @param callback - gRPC callback function to send the response
   */
  private async sendOffering(call: ServerUnaryCall<any, any>, callback: sendUnaryData<any>) {
    try {
      const req = call.request.data;
      const data = JSON.parse(req);
      const res: PromiseResolve = await OfferingService.createOffering(data);
      await OfferingService.requestOffering({ status: offeringStatusEnum.REVIEW, _id: data._id });

      callback(null, res);
    } catch (error: any) {
      logger.error(error, 'Error sendOffering:');
      callback({ code: grpcStatus.INTERNAL, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR }, null);
    }
  }

  private async scheduleOffering(call: ServerUnaryCall<any, any>, callback: sendUnaryData<any>) {
    try {
      const req = call.request.data;
      const data = JSON.parse(req);
      const res: PromiseResolve = await OfferingService.updateOfferingDetails(data, { _id: data?._id });
      callback(null, res);
    } catch (error: any) {
      logger.error(error, 'Error scheduleOffering:');
      callback({ code: grpcStatus.INTERNAL, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR }, null);
    }
  }

  /**
   * Handler for approve KYC gRPC call
   * @param call - Incoming gRPC call
   * @param callback - gRPC callback function to send the response
   */
  private async unblockHandler(call: ServerUnaryCall<any, any>, callback: sendUnaryData<any>) {
    try {
      const req = call.request;
      const res: PromiseResolve = await UserAuthenticationController.unblockHandler(req);
      callback(null, res);
    } catch (error: any) {
      logger.error(error, 'Error during KYC approval:');
      callback({ code: grpcStatus.INTERNAL, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR }, null);
    }
  }

  /**
   * Handler for Become Issuer gRPC call
   * @param call - Incoming gRPC call
   * @param callback - gRPC callback function to send the response
   */
  private async becomeIssuerHandler(call: ServerUnaryCall<any, any>, callback: sendUnaryData<any>) {
    try {
      const req = call.request;
      const res: PromiseResolve = await UserAuthenticationController.approveBecomeIssuer(req);
      callback(null, res);
    } catch (error: any) {
      logger.error('Error becomeIssuerHandler:', error);
      callback({ code: grpcStatus.INTERNAL, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR }, null);
    }
  }

  private async updateNftStatus(call: ServerUnaryCall<any, any>, callback: sendUnaryData<any>) {
    try {
      const data = call.request.id;
      const res: PromiseResolve = await NftCollectionService.updateNftStatus(data);
      callback(null, res);
    } catch (error: any) {
      logger.error(error, 'Error updateNftStatus:');
      callback({ code: grpcStatus.INTERNAL, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR }, null);
    }
  }
}

export default GrpcServer;
