import CONFIG from '../../config/env';
import logger from '../../helpers/logging/logger.helper';
import * as path from 'path';
import * as grpc from '@grpc/grpc-js';
import * as protoLoader from '@grpc/proto-loader';

const PROTO_PATH = path.join(__dirname, '../proto/user.proto');
const PROTO_OPTIONS = {
  keepCase: true,
  longs: String,
  enums: String,
  arrays: true,
};

const RECONNECT_TIMEOUT_MS = 5000; // 5 seconds reconnection timeout

class AdminGrpcClient {
  public client: any;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;

  constructor() {
    this.connectUserClient();
  }

  public async connectUserClient() {
    try {
      const host = CONFIG.GRPC.ADMIN_SERVICE_GRPC_CONTAINER_NAME;
      const port = CONFIG.GRPC.ADMIN_SERVICE_GRPC_PORT;
      const isSsl = CONFIG.GRPC.GRPC_SSL;

      if (!host || !port) {
        throw new Error('Invalid gRPC host or port configuration');
      }

      // Load the protobuf definition
      const packageDefinition = protoLoader.loadSync(PROTO_PATH, PROTO_OPTIONS);
      if (!packageDefinition) {
        throw new Error(`Failed to load proto file from path: ${PROTO_PATH}`);
      }

      // Load the package definition into gRPC
      const grpcObject = grpc.loadPackageDefinition(packageDefinition) as any;

      // Get the service definition from the loaded gRPC object
      const adminGrpcService = grpcObject?.user?.AdminGrpcService;
      if (!adminGrpcService) {
        throw new Error('AdminGrpcService not found in proto definition');
      }

      // Create credentials based on SSL configuration
      const credentials = isSsl ? grpc.credentials.createSsl() : grpc.credentials.createInsecure();

      // Create the gRPC client instance using the service constructor
      this.client = new adminGrpcService(`${host}:${port}`, credentials);

      // Check if the connection is successful
      this.client.waitForReady(Date.now() + 5000, (error: Error | null) => {
        if (error) {
          console.warn(`Failed to connect to gRPC server: ${error.message}`);
        }

        this.reconnectAttempts = 0; // Reset reconnect attempts on successful connection
        logger.info(`Admin Service gRPC Client connected successfully at ${host}:${port}`);
      });
    } catch (error: any) {
      this.handleConnectionError(error);
    }
  }

  private handleConnectionError(error: any) {
    this.reconnectAttempts++;

    logger.error(`Admin gRPC client connection error: ${error.message}`, {
      error,
      reconnectAttempt: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts,
    });

    if (this.reconnectAttempts <= this.maxReconnectAttempts) {
      logger.info(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) to Admin Service in ${RECONNECT_TIMEOUT_MS}ms`);

      setTimeout(() => {
        this.connectUserClient();
      }, RECONNECT_TIMEOUT_MS);
    } else {
      logger.error(`Failed to connect to Admin Service after ${this.maxReconnectAttempts} attempts. Giving up.`);
      // You might want to implement a more sophisticated error handling strategy here
      // For example, sending alerts, or stopping the application if this connection is critical
    }
  }

  // Method to manually trigger reconnection
  public reconnect() {
    logger.info('Manually triggering reconnection to Admin Service');
    this.reconnectAttempts = 0; // Reset reconnect attempts for manual reconnection
    this.connectUserClient();
  }
}

export default new AdminGrpcClient();
