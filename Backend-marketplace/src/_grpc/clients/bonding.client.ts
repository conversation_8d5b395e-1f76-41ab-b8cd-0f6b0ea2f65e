import CONFIG from '../../config/env';
import logger from '../../helpers/logging/logger.helper';
import * as path from 'path';
import * as grpc from '@grpc/grpc-js';
import * as protoLoader from '@grpc/proto-loader';

const PROTO_PATH = path.join(__dirname, '../proto/bonding.proto');
const PROTO_OPTIONS = {
  keepCase: true,
  longs: String,
  enums: String,
  arrays: true,
};

const RECONNECT_TIMEOUT_MS = 5000; // 5 seconds reconnection timeout

class BondingClient {
  public client: any;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;

  constructor() {
    this.connectBondingClient();
  }

  public async connectBondingClient() {
    try {
      const host = CONFIG.GRPC.BONDING_SERVICE_GRPC_CONTAINER_NAME;
      const port = CONFIG.GRPC.BONDING_SERVICE_GRPC_PORT;
      const isSsl = CONFIG.GRPC.GRPC_SSL;

      if (!host || !port) {
        throw new Error('Invalid gRPC host or port configuration for Bonding service');
      }

      // Load the protobuf definition
      const packageDefinition = protoLoader.loadSync(PROTO_PATH, PROTO_OPTIONS);
      if (!packageDefinition) {
        throw new Error(`Failed to load proto file from path: ${PROTO_PATH}`);
      }

      // Load the package definition into gRPC
      const grpcObject = grpc.loadPackageDefinition(packageDefinition) as any;

      // Get the service definition from the loaded gRPC object
      const bondingService = grpcObject?.bonding?.BondingService;
      if (!bondingService) {
        throw new Error('BondingService not found in proto definition');
      }

      // Create credentials based on SSL configuration
      const credentials = isSsl ? grpc.credentials.createSsl() : grpc.credentials.createInsecure();

      // Create the gRPC client instance using the service constructor
      this.client = new bondingService(`${host}:${port}`, credentials);

      // Check if the connection is successful
      this.client.waitForReady(Date.now() + 5000, (error: Error | null) => {
        if (error) {
          logger.error(`Failed to connect to Bonding gRPC server: ${error.message}`);
        }

        this.reconnectAttempts = 0; // Reset reconnect attempts on successful connection
        logger.info(`Bonding Service gRPC Client connected successfully at ${host}:${port}`);
      });
    } catch (error: any) {
      this.handleConnectionError(error);
    }
  }

  private handleConnectionError(error: any) {
    this.reconnectAttempts++;

    logger.error(`Bonding gRPC client connection error: ${error.message}`, {
      error,
      reconnectAttempt: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts,
    });

    if (this.reconnectAttempts <= this.maxReconnectAttempts) {
      logger.info(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) to Bonding Service in ${RECONNECT_TIMEOUT_MS}ms`);

      setTimeout(() => {
        this.connectBondingClient();
      }, RECONNECT_TIMEOUT_MS);
    } else {
      logger.error(`Failed to connect to Bonding Service after ${this.maxReconnectAttempts} attempts. Giving up.`);
    }
  }

  // Method to manually trigger reconnection
  public reconnect() {
    logger.info('Manually triggering reconnection to Bonding Service');
    this.reconnectAttempts = 0; // Reset reconnect attempts for manual reconnection
    this.connectBondingClient();
  }

  /**
   * Get staking transactions via gRPC call
   * @param token - Token address
   * @param pagination - Pagination parameters
   * @returns Promise with staking transactions data
   */
  public getStakingTransactions(token: string, pagination: any, userAddress?: string, type?: string): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.client) {
        reject(new Error('Bonding gRPC client not initialized'));
        return;
      }

      this.client.GetStakingTransactions({ token, pagination, userAddress, type }, (error: any, response: any) => {
        if (error) {
          logger.error('getStakingTransactions gRPC Error:', error);
          reject(error);
        } else {
          resolve(response);
        }
      });
    });
  }

  /**
   * Get user staking summary via gRPC call
   * @param token - Token address
   * @param pagination - Pagination parameters
   * @returns Promise with user staking summary data
   */
  public getUserStakingSummary(token: string, pagination: any): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.client) {
        reject(new Error('Bonding gRPC client not initialized'));
        return;
      }

      this.client.GetUserStakingSummary({ token, pagination }, (error: any, response: any) => {
        if (error) {
          logger.error('getUserStakingSummary gRPC Error:', error);
          reject(error);
        } else {
          resolve(response);
        }
      });
    });
  }

  /**
   * Get staking dividend list via gRPC call
   * @param token - Token address
   * @param monthString - Month string in format "YYYY-MM-DD" or "YYYY-MM"
   * @param pagination - Pagination parameters
   * @returns Promise with staking dividend list data
   */
  public getStakingDividendList(token: string, monthString: string, pagination: any): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.client) {
        reject(new Error('Bonding gRPC client not initialized'));
        return;
      }

      this.client.GetStakingDividendList({ token, monthString, pagination }, (error: any, response: any) => {
        if (error) {
          logger.error('getStakingDividendList gRPC Error:', error);
          reject(error);
        } else {
          resolve(response);
        }
      });
    });
  }

  /**
   * Update staking reward amount via gRPC call
   * @param token - Token address
   * @param month - Month string in format "YYYY-MM"
   * @param totalRewardAmount - Total reward amount to distribute
   * @returns Promise with update result
   */
  public updateStakingRewardAmount(token: string, month: string, totalRewardAmount: number): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.client) {
        reject(new Error('Bonding gRPC client not initialized'));
        return;
      }

      this.client.UpdateStakingRewardAmount({ token, month, totalRewardAmount }, (error: any, response: any) => {
        if (error) {
          logger.error('updateStakingRewardAmount gRPC Error:', error);
          reject(error);
        } else {
          resolve(response);
        }
      });
    });
  }
}

export default new BondingClient();
