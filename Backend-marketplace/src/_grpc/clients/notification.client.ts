import CONFIG from '../../config/env';
import * as grpc from '@grpc/grpc-js';
import * as protoLoader from '@grpc/proto-loader';
import * as path from 'path';

const PROTO_PATH = path.join(__dirname, '../proto/user.proto');
const PROTO_OPTIONS = {
  keepCase: true,
  longs: String,
  enums: String,
  arrays: true,
};

/**
 * Client class for handling gRPC communication with the notification service.
 * Manages the connection and provides methods for interacting with the notification service.
 */
class NotificationClient {
  /**
   * The gRPC client instance for notification service communication.
   */
  public client: any;

  /**
   * Creates a new instance of NotificationClient and establishes the connection.
   */
  constructor() {
    this.connectNotificationClient();
  }

  /**
   * Establishes a connection to the notification service using gRPC.
   * Configures the connection based on environment settings and loads the proto definition.
   * @returns {Promise<void>} A promise that resolves when the connection is established
   */
  public async connectNotificationClient() {
    const host = CONFIG.GRPC.NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME;
    const port = CONFIG.GRPC.NOTIFICATION_SERVICE_GRPC_PORT;
    const isSsl = process.env.GRPC_SSL;

    const packageDefinition = protoLoader.loadSync(PROTO_PATH, PROTO_OPTIONS);
    const grpcObject: any = grpc.loadPackageDefinition(packageDefinition);

    // Access the UserService from the 'user' package
    const AdminGrpcService = grpcObject.user.AdminGrpcService;

    this.client = new AdminGrpcService(`${host}:${port}`, isSsl === 'True' ? grpc.credentials.createSsl() : grpc.credentials.createInsecure());

    console.log(`Notification Service gRPC Client running at ${host}:${port}`);
  }
}

export default new NotificationClient();
