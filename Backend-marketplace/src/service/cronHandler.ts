import * as cron from 'node-cron';
import DividendsService from '../component/dividends/service';
import redisHelper from '../helpers/redis.helper';

class CronHandler {
  public cronScheduler() {
    cron.schedule('*/13 * * * * *', async () => {
      const dividend_cron = await redisHelper.getString('Dividend-cron-running');
      if (dividend_cron) {
        // console.log('Skipping: Dividend cron already running!!\n');
        return;
      }

      try {
        await redisHelper.setString('Dividend-cron-running', 'true', 180);
        await DividendsService.saveDividendInvestorData();
      } catch (error) {
        console.error('Error in cron job:', error);
      } finally {
        await redisHelper.deleteKey('Dividend-cron-running');
      }
    });
  }
}

export default new CronHandler();
