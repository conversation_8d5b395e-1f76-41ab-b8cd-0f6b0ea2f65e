import { Types } from 'mongoose';
import { KafkaMessage, Consumer } from 'kafkajs';
import { kafkaHelperService } from '../helpers/kafka.helper';
import logger from '../helpers/logging/logger.helper';
import OfferingService from '../component/offerings/service';
import UserService from '../component/userAuthentications/service';
import OrderService from '../component/order/service';
import TransferRequestService from '../component/transfer/service';
import DividendsService from '../component/dividends/service';
import CollectionService from '../component/nft/service';
import { UserDetailsSchema } from '../component/userAuthentications/models/userDetails.model';
/**
 * KafkaService class handles Kafka consumer and producer operations for user-related messages.
 */
class KafkaService {
  private consumer: Consumer | null = null;

  constructor() {
    this.initialize();
  }
  /**
   * Initializes the Kafka consumer.
   * Sets up the consumer listening to the 'cron-to-user' topic.
   */
  private async initialize() {
    try {
      this.consumer = await this.setupConsumer('cron-to-user', this.handleCronMessage.bind(this));
      // this.consumer = await this.setupConsumer('admin-to-user', this.handleCronMessage.bind(this));
    } catch (error) {
      logger.error(error, 'Error initializing Kafka consumer for user:');
      throw error;
    }
  }

  /**
   * Sets up the Kafka consumer for a specific topic.
   * @param topic - The Kafka topic to listen to.
   * @param messageHandler - The function to handle incoming messages.
   * @returns A Promise that resolves to a Kafka consumer instance.
   */
  private async setupConsumer(
    topic: string,
    messageHandler: (
      message: KafkaMessage,
      // eslint-disable-next-line no-shadow
      topic: string,
      partition: number,
      offset: string,
    ) => void,
  ): Promise<Consumer> {
    try {
      const consumer = await kafkaHelperService.createConsumer('cron-user-group', topic, messageHandler);
      logger.info(`User consumer set up and listening on topic: ${topic}`);

      return consumer;
    } catch (error) {
      logger.error(error, `Error setting up consumer for topic ${topic}:`);
      throw error;
    }
  }
  /**
   * Handles incoming Kafka messages for the 'cron-to-user' topic.
   * Processes messages based on their type and updates the corresponding service.
   * @param message - The Kafka message received.
   * @param topic - The topic of the message.
   * @param partition - The partition of the message.
   */
  private async handleCronMessage(message: KafkaMessage, topic: string, partition: number) {
    try {
      let response: any;
      const body = JSON.parse(message.value.toString());
      const value = typeof body.value === 'string' ? JSON.parse(body?.value) : body?.value;
      const filter = { _id: value?._id };
      console.log('\n\n\n\n Type', value?.type, '====== Event', value?.event, 'data =====>>>>\n\n\n', value);
      delete value?._id;
      switch (value?.type) {
        case 'offering':
          response = await OfferingService.updateOfferingDetails(value, filter);
          break;
        case 'fee':
          response = await OfferingService.updateOfferingDetails(value, filter);
          break;
        case 'ForceTransferred':
          response = await TransferRequestService.updateForceTransferDetails(value, filter);
          break;
        case 'whitelist':
          response = await OfferingService.updateWhitelistDetails(value, filter);
          break;
        case 'DividendDistributed':
          response = await DividendsService.createDividendHistory(value);
          // response = await DividendHistory.create(value);
          break;
        case 'SaveTransfer':
          response = await TransferRequestService.updateForceTransferDetails({ ...value, ...filter }, filter);
          break;
        case 'order':
          response = await OrderService.updateOrderDetails(value, filter);
          break;
        case 'issuerAction':
          response = await OrderService.processOrder(value, filter);
          break;
        case 'user':
          if (value?.onchainID) {
            const { data: userData } = await UserService.fetchUserDetails(filter, [], [], true);
            if (userData?.kycDetails?.wallets?.length) {
              // Mark all wallets as verified
              userData.kycDetails.wallets = userData.kycDetails.wallets.map((wallet: any) => ({
                ...wallet,
                isVerify: true,
              }));

              await UserDetailsSchema.findOneAndUpdate(
                filter,
                { $set: { wallets: userData.kycDetails.wallets } }, // Use $set to update arrays properly
                { new: true, runValidators: true },
              );
            }
          }

          response = await UserService.updateUserDetails(value, filter);
          break;
        case 'NftCollection':
          response = await CollectionService.updateCollection(filter, value);
          break;
        case 'offeringNav': {
          const offeringId = filter._id ? new Types.ObjectId(filter._id) : null;
          response = await OfferingService.tokenValuation(offeringId, value);
          break;
        }
        default:
          logger.warn('Unrecognized or missing type');

          return;
      }
      if (!response?.error && this.consumer) {
        await this.consumer.commitOffsets([{ topic, partition, offset: (parseInt(message.offset, 10) + 1).toString() }]);
      } else {
        logger.warn('Consumer is not initialized or operation failed.', value?.type);
      }
    } catch (error) {
      logger.error(error, 'handleUserMessage');
      throw error;
    }
  }
  /**
   * Sends a message to the 'user-to-admin' Kafka topic.
   * @param message - The message to be sent.
   */
  public async sendMessageToAdmin(message: any) {
    try {
      await kafkaHelperService.sendMessage('user-to-admin', [{ value: JSON.stringify(message) }]);
    } catch (error) {
      logger.error(error, 'Error sending message from user to admin:');
      throw error;
    }
  }

  public async sendMessageToNotification(message: any) {
    try {
      await kafkaHelperService.sendMessage('notifications', [{ value: JSON.stringify(message) }]);
    } catch (error) {
      logger.error(error, 'Error sending message from user to admin:');
      throw error;
    }
  }
}

export default new KafkaService();
