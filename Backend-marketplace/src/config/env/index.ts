import * as dotenv from 'dotenv';

dotenv.config();

interface IConfig {
  ENVIRONMENT: string;
  CAP_TABLE_URL: string;
  PROJECT: {
    NAME: string;
    LOG_LEVEL: string;
  };
  DATABASE: {
    MONGODB_HOST_URL?: string;
    MONG<PERSON>B_HOST: string;
    MONGODB_USER: string;
    MONG<PERSON>B_PASSWORD: string;
    MONGODB_PORT: number;
    MONGODB_DATABASE: string;
  };
  REDIS: {
    HOST: string;
    LOGIN_MAX_ATTEMPT: number;
    LOGIN_BLOCK_TIME: number;
    OTP_EXPIRY: number;
  };
  JWT_AUTH: {
    TOKEN: string;
    AUTH_EXPIRE_TIME: string;
    REFRESH_TOKEN: string;
    REFRESH_EXPIRE_TIME: string;
    FORGOT_EXPIRE_TIME: string;
    JWT_2FA_EXPIRE: string;
  };
  SENDGRID: {
    API_KEY: string;
    SENDER: string;
  };
  TWILIO: {
    ACCOUNT_SID: string;
    AUTH_TOKEN: string;
    PHONE_NUMBER: string;
  };
  CLOUD: {
    CONNECTION_STRING: string;
    ACCOUNT_NAME: string;
    BUCKET_NAME: string;
    MAX_SIZE: number;
  };
  KAFKA: {
    BROKERS: string;
  };
  GRPC: {
    USER_SERVICE_GRPC_CONTAINER_NAME: string;
    USER_SERVICE_GRPC_PORT: string;
    ADMIN_SERVICE_GRPC_CONTAINER_NAME: string;
    ADMIN_SERVICE_GRPC_PORT: string;
    NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME: string;
    NOTIFICATION_SERVICE_GRPC_PORT: string;
    BONDING_SERVICE_GRPC_CONTAINER_NAME: string;
    BONDING_SERVICE_GRPC_PORT: string;
    GRPC_SSL: string;
  };
  SUMSUB: {
    SUMSUB_SECRET_KEY: string;
    SUMSUB_TOKEN: string;
  };
  DOCUSIGN: {
    clientId: string;
    impersonatedUserGuid: string;
    accountId: string;
    OAuthServer: string;
    frontendUrl: string;
    privateKey: string;
    webhookAuth: {
      userName: string;
      pwd: string;
    };
  };
  FUND_CONTRACT_ADDRESS: string;
  USDC_ADDRESS: string;
  RPC_URL: string;
  GOOGLE: {
    CLIENT_ID: string;
  };
  API_HOST_URL: string;
}

const NODE_ENV: string = process.env.NODE_ENV || 'development';

const development: IConfig = {
  CAP_TABLE_URL: process.env.CAP_TABLE_URL,
  ENVIRONMENT: NODE_ENV,
  PROJECT: { NAME: process.env.PROJECT_NAME, LOG_LEVEL: process.env.LOG_LEVEL },
  DATABASE: {
    MONGODB_HOST: process.env.MONGODB_HOST || 'localhost',
    MONGODB_USER: process.env.MONGODB_USER,
    MONGODB_PASSWORD: process.env.MONGODB_PASSWORD,
    MONGODB_PORT: Number(process.env.MONGODB_PORT) || 27017,
    MONGODB_DATABASE: process.env.MONGODB_DATABASE || 'mvp_db',
  },

  REDIS: { HOST: process.env.REDIS_HOST, LOGIN_MAX_ATTEMPT: Number(process.env.LOGIN_MAX_ATTEMPT), LOGIN_BLOCK_TIME: Number(process.env.LOGIN_BLOCK_TIME), OTP_EXPIRY: Number(process.env.OTP_EXPIRY) },
  JWT_AUTH: {
    TOKEN: process.env.JWT_AUTH_SECRET,
    AUTH_EXPIRE_TIME: process.env.JWT_AUTH_EXPIRE,
    REFRESH_TOKEN: process.env.JWT_REFRESH_SECRET,
    REFRESH_EXPIRE_TIME: process.env.JWT_REFRESH_EXPIRE,
    FORGOT_EXPIRE_TIME: process.env.JWT_FORGOT_EXPIRE,
    JWT_2FA_EXPIRE: process.env.JWT_2FA_EXPIRE,
  },
  SENDGRID: { API_KEY: process.env.SENDGRID_API_KEY, SENDER: process.env.SENDER },
  TWILIO: { ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID, AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN, PHONE_NUMBER: process.env.TWILIO_PHONE_NUMBER },
  CLOUD: {
    CONNECTION_STRING: process.env.CONNECTION_STRING,
    ACCOUNT_NAME: process.env.ACCOUNT_NAME,
    BUCKET_NAME: process.env.BUCKET_NAME || '',
    MAX_SIZE: Number(process.env.MAX_SIZE || 10),
  },
  KAFKA: { BROKERS: `${process.env.KAFKA_BROKER}:${process.env.KAFKA_BROKER_PORT}` },
  GRPC: {
    USER_SERVICE_GRPC_CONTAINER_NAME: process.env.USER_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0',
    USER_SERVICE_GRPC_PORT: process.env.USER_SERVICE_GRPC_PORT || '40001',
    ADMIN_SERVICE_GRPC_CONTAINER_NAME: process.env.ADMIN_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0',
    ADMIN_SERVICE_GRPC_PORT: process.env.ADMIN_SERVICE_GRPC_PORT || '40002',
    NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME: process.env.NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0',
    NOTIFICATION_SERVICE_GRPC_PORT: process.env.NOTIFICATION_SERVICE_GRPC_PORT || '40004',
    BONDING_SERVICE_GRPC_CONTAINER_NAME: process.env.BONDING_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0',
    BONDING_SERVICE_GRPC_PORT: process.env.BONDING_SERVICE_GRPC_PORT || '40003',
    GRPC_SSL: process.env.GRPC_SSL,
  },
  SUMSUB: { SUMSUB_SECRET_KEY: process.env.SUMSUB_SECRET_KEY, SUMSUB_TOKEN: process.env.SUMSUB_TOKEN },
  RPC_URL: process.env.RPC_URL,
  FUND_CONTRACT_ADDRESS: process.env.FUND_CONTRACT_ADDRESS,
  USDC_ADDRESS: process.env.USDC_ADDRESS,
  DOCUSIGN: {
    clientId: process.env.DOCUSIGN_CLIENT_ID ?? '',
    impersonatedUserGuid: process.env.DOCUSIGN_IMPERSONATED_USER_ID ?? '',
    accountId: process.env.DOCUSIGN_ACCOUNT_ID ?? '',
    OAuthServer: process.env.DOCUSIGN_OAUTH_URL ?? '',
    privateKey: process.env.DOCUSIGN_PRIVATE_KEY ?? '',
    frontendUrl: process.env.FRONTEND_URL ?? '',
    webhookAuth: { userName: process.env.DOCUSIGN_WEBHOOK_USERNAME ?? '', pwd: process.env.DOCUSIGN_WEBHOOK_PWD ?? '' },
  },
  GOOGLE: {
    CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
  },
  API_HOST_URL: process.env.API_HOST_URL,
};
const stage: IConfig = {
  CAP_TABLE_URL: process.env.CAP_TABLE_URL,
  ENVIRONMENT: NODE_ENV,
  PROJECT: { NAME: process.env.PROJECT_NAME, LOG_LEVEL: process.env.LOG_LEVEL },
  DATABASE: {
    MONGODB_HOST: process.env.MONGODB_HOST || 'localhost',
    MONGODB_USER: process.env.MONGODB_USER,
    MONGODB_PASSWORD: process.env.MONGODB_PASSWORD,
    MONGODB_PORT: Number(process.env.MONGODB_PORT) || 27017,
    MONGODB_DATABASE: process.env.MONGODB_DATABASE || 'mvp_db',
  },

  REDIS: { HOST: process.env.REDIS_HOST, LOGIN_MAX_ATTEMPT: Number(process.env.LOGIN_MAX_ATTEMPT), LOGIN_BLOCK_TIME: Number(process.env.LOGIN_BLOCK_TIME), OTP_EXPIRY: Number(process.env.OTP_EXPIRY) },
  JWT_AUTH: {
    TOKEN: process.env.JWT_AUTH_SECRET,
    AUTH_EXPIRE_TIME: process.env.JWT_AUTH_EXPIRE,
    REFRESH_TOKEN: process.env.JWT_REFRESH_SECRET,
    REFRESH_EXPIRE_TIME: process.env.JWT_REFRESH_EXPIRE,
    FORGOT_EXPIRE_TIME: process.env.JWT_FORGOT_EXPIRE,
    JWT_2FA_EXPIRE: process.env.JWT_2FA_EXPIRE,
  },
  SENDGRID: { API_KEY: process.env.SENDGRID_API_KEY, SENDER: process.env.SENDER },
  TWILIO: { ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID, AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN, PHONE_NUMBER: process.env.TWILIO_PHONE_NUMBER },
  CLOUD: {
    CONNECTION_STRING: process.env.CONNECTION_STRING,
    ACCOUNT_NAME: process.env.ACCOUNT_NAME,
    BUCKET_NAME: process.env.BUCKET_NAME || '',
    MAX_SIZE: Number(process.env.MAX_SIZE || 10),
  },
  KAFKA: { BROKERS: `${process.env.KAFKA_BROKER}:${process.env.KAFKA_BROKER_PORT}` },
  GRPC: {
    USER_SERVICE_GRPC_CONTAINER_NAME: process.env.USER_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0',
    USER_SERVICE_GRPC_PORT: process.env.USER_SERVICE_GRPC_PORT || '40001',
    ADMIN_SERVICE_GRPC_CONTAINER_NAME: process.env.ADMIN_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0',
    ADMIN_SERVICE_GRPC_PORT: process.env.ADMIN_SERVICE_GRPC_PORT || '40002',
    NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME: process.env.NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0',
    NOTIFICATION_SERVICE_GRPC_PORT: process.env.NOTIFICATION_SERVICE_GRPC_PORT || '40004',
    BONDING_SERVICE_GRPC_CONTAINER_NAME: process.env.BONDING_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0',
    BONDING_SERVICE_GRPC_PORT: process.env.BONDING_SERVICE_GRPC_PORT || '40003',
    GRPC_SSL: process.env.GRPC_SSL,
  },
  SUMSUB: { SUMSUB_SECRET_KEY: process.env.SUMSUB_SECRET_KEY, SUMSUB_TOKEN: process.env.SUMSUB_TOKEN },
  FUND_CONTRACT_ADDRESS: process.env.FUND_CONTRACT_ADDRESS,
  USDC_ADDRESS: process.env.USDC_ADDRESS,
  RPC_URL: process.env.RPC_URL,
  DOCUSIGN: {
    clientId: process.env.DOCUSIGN_CLIENT_ID ?? '',
    impersonatedUserGuid: process.env.DOCUSIGN_IMPERSONATED_USER_ID ?? '',
    accountId: process.env.DOCUSIGN_ACCOUNT_ID ?? '',
    OAuthServer: process.env.DOCUSIGN_OAUTH_URL ?? '',
    privateKey: process.env.DOCUSIGN_PRIVATE_KEY ?? '',
    frontendUrl: process.env.FRONTEND_URL ?? '',
    webhookAuth: { userName: process.env.DOCUSIGN_WEBHOOK_USERNAME ?? '', pwd: process.env.DOCUSIGN_WEBHOOK_PWD ?? '' },
  },
  GOOGLE: {
    CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
  },
  API_HOST_URL: process.env.API_HOST_URL,
};
const qa: IConfig = {
  CAP_TABLE_URL: process.env.CAP_TABLE_URL,
  ENVIRONMENT: NODE_ENV,
  PROJECT: { NAME: process.env.PROJECT_NAME, LOG_LEVEL: process.env.LOG_LEVEL },
  DATABASE: {
    MONGODB_HOST: process.env.MONGODB_HOST || 'localhost',
    MONGODB_USER: process.env.MONGODB_USER,
    MONGODB_PASSWORD: process.env.MONGODB_PASSWORD,
    MONGODB_PORT: Number(process.env.MONGODB_PORT) || 27017,
    MONGODB_DATABASE: process.env.MONGODB_DATABASE || 'mvp_db',
  },

  REDIS: { HOST: process.env.REDIS_HOST, LOGIN_MAX_ATTEMPT: Number(process.env.LOGIN_MAX_ATTEMPT), LOGIN_BLOCK_TIME: Number(process.env.LOGIN_BLOCK_TIME), OTP_EXPIRY: Number(process.env.OTP_EXPIRY) },
  JWT_AUTH: {
    TOKEN: process.env.JWT_AUTH_SECRET,
    AUTH_EXPIRE_TIME: process.env.JWT_AUTH_EXPIRE,
    REFRESH_TOKEN: process.env.JWT_REFRESH_SECRET,
    REFRESH_EXPIRE_TIME: process.env.JWT_REFRESH_EXPIRE,
    FORGOT_EXPIRE_TIME: process.env.JWT_FORGOT_EXPIRE,
    JWT_2FA_EXPIRE: process.env.JWT_2FA_EXPIRE,
  },
  SENDGRID: { API_KEY: process.env.SENDGRID_API_KEY, SENDER: process.env.SENDER },
  TWILIO: { ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID, AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN, PHONE_NUMBER: process.env.TWILIO_PHONE_NUMBER },
  CLOUD: {
    CONNECTION_STRING: process.env.CONNECTION_STRING,
    ACCOUNT_NAME: process.env.ACCOUNT_NAME,
    BUCKET_NAME: process.env.BUCKET_NAME || '',
    MAX_SIZE: Number(process.env.MAX_SIZE || 10),
  },
  KAFKA: { BROKERS: `${process.env.KAFKA_BROKER}:${process.env.KAFKA_BROKER_PORT}` },
  GRPC: {
    USER_SERVICE_GRPC_CONTAINER_NAME: process.env.USER_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0',
    USER_SERVICE_GRPC_PORT: process.env.USER_SERVICE_GRPC_PORT || '40001',
    ADMIN_SERVICE_GRPC_CONTAINER_NAME: process.env.ADMIN_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0',
    ADMIN_SERVICE_GRPC_PORT: process.env.ADMIN_SERVICE_GRPC_PORT || '40002',
    NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME: process.env.NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0',
    NOTIFICATION_SERVICE_GRPC_PORT: process.env.NOTIFICATION_SERVICE_GRPC_PORT || '40004',
    BONDING_SERVICE_GRPC_CONTAINER_NAME: process.env.BONDING_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0',
    BONDING_SERVICE_GRPC_PORT: process.env.BONDING_SERVICE_GRPC_PORT || '40003',
    GRPC_SSL: process.env.GRPC_SSL,
  },
  SUMSUB: { SUMSUB_SECRET_KEY: process.env.SUMSUB_SECRET_KEY, SUMSUB_TOKEN: process.env.SUMSUB_TOKEN },
  FUND_CONTRACT_ADDRESS: process.env.FUND_CONTRACT_ADDRESS,
  USDC_ADDRESS: process.env.USDC_ADDRESS,
  RPC_URL: process.env.RPC_URL,
  DOCUSIGN: {
    clientId: process.env.DOCUSIGN_CLIENT_ID ?? '',
    impersonatedUserGuid: process.env.DOCUSIGN_IMPERSONATED_USER_ID ?? '',
    accountId: process.env.DOCUSIGN_ACCOUNT_ID ?? '',
    OAuthServer: process.env.DOCUSIGN_OAUTH_URL ?? '',
    privateKey: process.env.DOCUSIGN_PRIVATE_KEY ?? '',
    frontendUrl: process.env.FRONTEND_URL ?? '',
    webhookAuth: { userName: process.env.DOCUSIGN_WEBHOOK_USERNAME ?? '', pwd: process.env.DOCUSIGN_WEBHOOK_PWD ?? '' },
  },
  GOOGLE: {
    CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
  },
  API_HOST_URL: process.env.API_HOST_URL,
};
const uat: IConfig = {
  CAP_TABLE_URL: process.env.CAP_TABLE_URL,

  ENVIRONMENT: NODE_ENV,
  PROJECT: { NAME: process.env.PROJECT_NAME, LOG_LEVEL: process.env.LOG_LEVEL },
  DATABASE: {
    MONGODB_HOST: process.env.MONGODB_HOST || 'localhost',
    MONGODB_USER: process.env.MONGODB_USER,
    MONGODB_PASSWORD: process.env.MONGODB_PASSWORD,
    MONGODB_PORT: Number(process.env.MONGODB_PORT) || 27017,
    MONGODB_DATABASE: process.env.MONGODB_DATABASE || 'mvp_db',
  },

  REDIS: { HOST: process.env.REDIS_HOST, LOGIN_MAX_ATTEMPT: Number(process.env.LOGIN_MAX_ATTEMPT), LOGIN_BLOCK_TIME: Number(process.env.LOGIN_BLOCK_TIME), OTP_EXPIRY: Number(process.env.OTP_EXPIRY) },
  JWT_AUTH: {
    TOKEN: process.env.JWT_AUTH_SECRET,
    AUTH_EXPIRE_TIME: process.env.JWT_AUTH_EXPIRE,
    REFRESH_TOKEN: process.env.JWT_REFRESH_SECRET,
    REFRESH_EXPIRE_TIME: process.env.JWT_REFRESH_EXPIRE,
    FORGOT_EXPIRE_TIME: process.env.JWT_FORGOT_EXPIRE,
    JWT_2FA_EXPIRE: process.env.JWT_2FA_EXPIRE,
  },
  SENDGRID: { API_KEY: process.env.SENDGRID_API_KEY, SENDER: process.env.SENDER },
  TWILIO: { ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID, AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN, PHONE_NUMBER: process.env.TWILIO_PHONE_NUMBER },
  CLOUD: {
    CONNECTION_STRING: process.env.CONNECTION_STRING,
    ACCOUNT_NAME: process.env.ACCOUNT_NAME,
    BUCKET_NAME: process.env.BUCKET_NAME || '',
    MAX_SIZE: Number(process.env.MAX_SIZE || 10),
  },
  KAFKA: { BROKERS: `${process.env.KAFKA_BROKER}:${process.env.KAFKA_BROKER_PORT}` },
  GRPC: {
    USER_SERVICE_GRPC_CONTAINER_NAME: process.env.USER_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0',
    USER_SERVICE_GRPC_PORT: process.env.USER_SERVICE_GRPC_PORT || '40001',
    ADMIN_SERVICE_GRPC_CONTAINER_NAME: process.env.ADMIN_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0',
    ADMIN_SERVICE_GRPC_PORT: process.env.ADMIN_SERVICE_GRPC_PORT || '40002',
    NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME: process.env.NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0',
    NOTIFICATION_SERVICE_GRPC_PORT: process.env.NOTIFICATION_SERVICE_GRPC_PORT || '40004',
    BONDING_SERVICE_GRPC_CONTAINER_NAME: process.env.BONDING_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0',
    BONDING_SERVICE_GRPC_PORT: process.env.BONDING_SERVICE_GRPC_PORT || '40003',
    GRPC_SSL: process.env.GRPC_SSL,
  },
  SUMSUB: { SUMSUB_SECRET_KEY: process.env.SUMSUB_SECRET_KEY, SUMSUB_TOKEN: process.env.SUMSUB_TOKEN },
  FUND_CONTRACT_ADDRESS: process.env.FUND_CONTRACT_ADDRESS,
  USDC_ADDRESS: process.env.USDC_ADDRESS,
  RPC_URL: process.env.RPC_URL,
  DOCUSIGN: {
    clientId: process.env.DOCUSIGN_CLIENT_ID ?? '',
    impersonatedUserGuid: process.env.DOCUSIGN_IMPERSONATED_USER_ID ?? '',
    accountId: process.env.DOCUSIGN_ACCOUNT_ID ?? '',
    OAuthServer: process.env.DOCUSIGN_OAUTH_URL ?? '',
    privateKey: process.env.DOCUSIGN_PRIVATE_KEY ?? '',
    frontendUrl: process.env.FRONTEND_URL ?? '',
    webhookAuth: { userName: process.env.DOCUSIGN_WEBHOOK_USERNAME ?? '', pwd: process.env.DOCUSIGN_WEBHOOK_PWD ?? '' },
  },
  GOOGLE: {
    CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
  },
  API_HOST_URL: process.env.API_HOST_URL,
};

const prod: IConfig = {
  CAP_TABLE_URL: process.env.CAP_TABLE_URL,
  ENVIRONMENT: NODE_ENV,
  PROJECT: { NAME: process.env.PROJECT_NAME, LOG_LEVEL: process.env.LOG_LEVEL },
  DATABASE: {
    MONGODB_HOST_URL: process.env.MONGODB_HOST_URL || '0.0.0.0',
    MONGODB_HOST: process.env.MONGODB_HOST || 'localhost',
    MONGODB_USER: process.env.MONGODB_USER,
    MONGODB_PASSWORD: process.env.MONGODB_PASSWORD,
    MONGODB_PORT: Number(process.env.MONGODB_PORT) || 27017,
    MONGODB_DATABASE: process.env.MONGODB_DATABASE || 'mvp_db',
  },

  REDIS: { HOST: process.env.REDIS_HOST, LOGIN_MAX_ATTEMPT: Number(process.env.LOGIN_MAX_ATTEMPT), LOGIN_BLOCK_TIME: Number(process.env.LOGIN_BLOCK_TIME), OTP_EXPIRY: Number(process.env.OTP_EXPIRY) },
  JWT_AUTH: {
    TOKEN: process.env.JWT_AUTH_SECRET,
    AUTH_EXPIRE_TIME: process.env.JWT_AUTH_EXPIRE,
    REFRESH_TOKEN: process.env.JWT_REFRESH_SECRET,
    REFRESH_EXPIRE_TIME: process.env.JWT_REFRESH_EXPIRE,
    FORGOT_EXPIRE_TIME: process.env.JWT_FORGOT_EXPIRE,
    JWT_2FA_EXPIRE: process.env.JWT_2FA_EXPIRE,
  },
  SENDGRID: { API_KEY: process.env.SENDGRID_API_KEY, SENDER: process.env.SENDER },
  TWILIO: { ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID, AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN, PHONE_NUMBER: process.env.TWILIO_PHONE_NUMBER },
  CLOUD: {
    CONNECTION_STRING: process.env.CONNECTION_STRING,
    ACCOUNT_NAME: process.env.ACCOUNT_NAME,
    BUCKET_NAME: process.env.BUCKET_NAME || '',
    MAX_SIZE: Number(process.env.MAX_SIZE || 10),
  },
  KAFKA: { BROKERS: `${process.env.KAFKA_BROKER}:${process.env.KAFKA_BROKER_PORT}` },
  GRPC: {
    USER_SERVICE_GRPC_CONTAINER_NAME: process.env.USER_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0',
    USER_SERVICE_GRPC_PORT: process.env.USER_SERVICE_GRPC_PORT || '40001',
    ADMIN_SERVICE_GRPC_CONTAINER_NAME: process.env.ADMIN_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0',
    ADMIN_SERVICE_GRPC_PORT: process.env.ADMIN_SERVICE_GRPC_PORT || '40002',
    NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME: process.env.NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0',
    NOTIFICATION_SERVICE_GRPC_PORT: process.env.NOTIFICATION_SERVICE_GRPC_PORT || '40004',
    BONDING_SERVICE_GRPC_CONTAINER_NAME: process.env.BONDING_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0',
    BONDING_SERVICE_GRPC_PORT: process.env.BONDING_SERVICE_GRPC_PORT || '40003',
    GRPC_SSL: process.env.GRPC_SSL,
  },
  SUMSUB: { SUMSUB_SECRET_KEY: process.env.SUMSUB_SECRET_KEY, SUMSUB_TOKEN: process.env.SUMSUB_TOKEN },
  FUND_CONTRACT_ADDRESS: process.env.FUND_CONTRACT_ADDRESS,
  USDC_ADDRESS: process.env.USDC_ADDRESS,
  RPC_URL: process.env.RPC_URL,
  DOCUSIGN: {
    clientId: process.env.DOCUSIGN_CLIENT_ID ?? '',
    impersonatedUserGuid: process.env.DOCUSIGN_IMPERSONATED_USER_ID ?? '',
    accountId: process.env.DOCUSIGN_ACCOUNT_ID ?? '',
    OAuthServer: process.env.DOCUSIGN_OAUTH_URL ?? '',
    privateKey: process.env.DOCUSIGN_PRIVATE_KEY ?? '',
    frontendUrl: process.env.FRONTEND_URL ?? '',
    webhookAuth: { userName: process.env.DOCUSIGN_WEBHOOK_USERNAME ?? '', pwd: process.env.DOCUSIGN_WEBHOOK_PWD ?? '' },
  },

  GOOGLE: {
    CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
  },
  API_HOST_URL: process.env.API_HOST_URL,
};
const sandbox: IConfig = {
  CAP_TABLE_URL: process.env.CAP_TABLE_URL,
  ENVIRONMENT: NODE_ENV,
  PROJECT: { NAME: process.env.PROJECT_NAME, LOG_LEVEL: process.env.LOG_LEVEL },
  DATABASE: {
    MONGODB_HOST: process.env.MONGODB_HOST || 'localhost',
    MONGODB_USER: process.env.MONGODB_USER,
    MONGODB_PASSWORD: process.env.MONGODB_PASSWORD,
    MONGODB_PORT: Number(process.env.MONGODB_PORT) || 27017,
    MONGODB_DATABASE: process.env.MONGODB_DATABASE || 'mvp_db',
  },

  REDIS: { HOST: process.env.REDIS_HOST, LOGIN_MAX_ATTEMPT: Number(process.env.LOGIN_MAX_ATTEMPT), LOGIN_BLOCK_TIME: Number(process.env.LOGIN_BLOCK_TIME), OTP_EXPIRY: Number(process.env.OTP_EXPIRY) },
  JWT_AUTH: {
    TOKEN: process.env.JWT_AUTH_SECRET,
    AUTH_EXPIRE_TIME: process.env.JWT_AUTH_EXPIRE,
    REFRESH_TOKEN: process.env.JWT_REFRESH_SECRET,
    REFRESH_EXPIRE_TIME: process.env.JWT_REFRESH_EXPIRE,
    FORGOT_EXPIRE_TIME: process.env.JWT_FORGOT_EXPIRE,
    JWT_2FA_EXPIRE: process.env.JWT_2FA_EXPIRE,
  },
  SENDGRID: { API_KEY: process.env.SENDGRID_API_KEY, SENDER: process.env.SENDER },
  TWILIO: { ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID, AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN, PHONE_NUMBER: process.env.TWILIO_PHONE_NUMBER },
  CLOUD: {
    CONNECTION_STRING: process.env.CONNECTION_STRING,
    ACCOUNT_NAME: process.env.ACCOUNT_NAME,
    BUCKET_NAME: process.env.BUCKET_NAME || '',
    MAX_SIZE: Number(process.env.MAX_SIZE || 10),
  },
  KAFKA: { BROKERS: `${process.env.KAFKA_BROKER}:${process.env.KAFKA_BROKER_PORT}` },
  GRPC: {
    USER_SERVICE_GRPC_CONTAINER_NAME: process.env.USER_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0',
    USER_SERVICE_GRPC_PORT: process.env.USER_SERVICE_GRPC_PORT || '40001',
    ADMIN_SERVICE_GRPC_CONTAINER_NAME: process.env.ADMIN_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0',
    ADMIN_SERVICE_GRPC_PORT: process.env.ADMIN_SERVICE_GRPC_PORT || '40002',
    NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME: process.env.NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0',
    NOTIFICATION_SERVICE_GRPC_PORT: process.env.NOTIFICATION_SERVICE_GRPC_PORT || '40004',
    BONDING_SERVICE_GRPC_CONTAINER_NAME: process.env.BONDING_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0',
    BONDING_SERVICE_GRPC_PORT: process.env.BONDING_SERVICE_GRPC_PORT || '40003',
    GRPC_SSL: process.env.GRPC_SSL,
  },
  SUMSUB: { SUMSUB_SECRET_KEY: process.env.SUMSUB_SECRET_KEY, SUMSUB_TOKEN: process.env.SUMSUB_TOKEN },
  FUND_CONTRACT_ADDRESS: process.env.FUND_CONTRACT_ADDRESS,
  USDC_ADDRESS: process.env.USDC_ADDRESS,
  RPC_URL: process.env.RPC_URL,
  DOCUSIGN: {
    clientId: process.env.DOCUSIGN_CLIENT_ID ?? '',
    impersonatedUserGuid: process.env.DOCUSIGN_IMPERSONATED_USER_ID ?? '',
    accountId: process.env.DOCUSIGN_ACCOUNT_ID ?? '',
    OAuthServer: process.env.DOCUSIGN_OAUTH_URL ?? '',
    privateKey: process.env.DOCUSIGN_PRIVATE_KEY ?? '',
    frontendUrl: process.env.FRONTEND_URL ?? '',
    webhookAuth: { userName: process.env.DOCUSIGN_WEBHOOK_USERNAME ?? '', pwd: process.env.DOCUSIGN_WEBHOOK_PWD ?? '' },
  },

  GOOGLE: {
    CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
  },
  API_HOST_URL: process.env.API_HOST_URL,
};

const config: { [name: string]: IConfig } = { development, prod, stage, qa, uat, sandbox };

export default config[NODE_ENV];
