import * as express from 'express';
import * as path from 'path';
import * as Middleware from '../middleware/middleware';
import * as Routes from '../../routes';
import RedisHelper from '../../helpers/redis.helper';
import Db from '../connection/connection';
import logger from '../../helpers/logging/logger.helper';
import GrpcServer from '../../_grpc';
import CONFIG from '../env';
import { initSentryMiddleware } from '../middleware/sentry.middleware';
import cronHandler from '../../service/cronHandler';

/**
 * @constant {express.Application}
 */
const app: express.Application = express();

app.use(express.static('public'));

app.use('/marketplace/public', express.static(path.join(__dirname, '../../public')));

/**
 * @constructs express.Application Middleware
 */
Middleware.configure(app);

/**
 * @constructs express.Application Routes
 */
Routes.init(app);

/**
 * @constructs express.Application Error Handler
 */
Middleware.initErrorHandler(app);

/**
 * Function to start the server
 */
const startServer = async () => {
  try {
    // Establish database connection
    await Db.dbConnection();

    // Sets port 3000 to default or unless otherwise specified in the environment
    app.set('port', process.env.PORT || 3000);
    const port = app.get('port');

    // Start gRPC Server
    const grpcServer = new GrpcServer(CONFIG.GRPC.USER_SERVICE_GRPC_CONTAINER_NAME.toString(), CONFIG.GRPC.USER_SERVICE_GRPC_PORT.toString());
    grpcServer.start();

    // Start Express server
    app.listen(port, () => {
      logger.info(`Marketplace Service Server is running on http://localhost:${port}`);
    });
    // Connect to Redis with a slight delay
    setTimeout(() => {
      RedisHelper.connectRedis();
    }, 200); // 200 milliseconds

    // save data at record date
    cronHandler.cronScheduler();
  } catch (error) {
    logger.error(error, 'Failed to start the server');
  }
};

/**
 * Start the server
 */
startServer();

/**
 * Handle graceful shutdown
 */
process.on('SIGINT', async () => {
  await Db.dbDisconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await Db.dbDisconnect();
  process.exit(0);
});

/**
 * @exports {express.Application}
 * Initialize Sentry middleware
 */
initSentryMiddleware(app);
export default app;
