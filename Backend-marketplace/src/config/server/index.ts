import * as http from 'http';
import * as serverHandlers from './serverHandlers';
import server from './server';
import { initSentry } from '../../helpers/sentry.helper';

const Server: http.Server = http.createServer(server);

/**
 * Initialize Sentry
 */
initSentry();

/**
 * Attach event listeners to the server
 *
 * @description
 * This section binds event handlers to the server for handling specific events such as errors and listening state.
 */
Server.on('error', (error: Error) => serverHandlers.onError(error, server.get('port')));
Server.on('listening', serverHandlers.onListening.bind(Server));
