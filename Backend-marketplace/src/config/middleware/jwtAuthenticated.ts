import { NextFunction, Request, Response } from 'express';
import RedisHelper from '../../helpers/redis.helper';
import { PromiseResolve, UserTypeEnum } from '../../utils/common.interface';
import CustomError from '../../helpers/customError.helper';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { ResponseHandler } from '../../helpers/response.helper';
import CommonHelper from '../../helpers/common.helper';
import UserService from '../../component/userAuthentications/service';
import logger from '../../helpers/logging/logger.helper';

/**
 * Checks if the user is authenticated by verifying the JWT token.
 * If the token is invalid, it throws an error.
 * If the token is valid, it fetches the user details from the database
 * and adds the user info to the request.
 * If the user is not active, it throws an error.
 * If the user is blocked by the admin, it throws an error.
 * If the user has 2FA enabled but not verified, it throws an error.
 * @param req - The request object.
 * @param res - The response object.
 * @param next - The next middleware function.
 * @returns A promise that resolves to the next middleware function or throws an error.
 */
export async function isAuthenticated(req: Request | any, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    let token;
    const authorization: string = req.headers.authorization || null;
    if (authorization) {
      token = authorization.toLowerCase().startsWith('bearer') ? authorization.slice('bearer'.length).trim() : authorization;
    }
    if (!token) {
      throw new CustomError(RES_MSG.COMMON.UNAUTHORIZED_ACCESS, RESPONSES.UN_AUTHORIZED);
    }
    const isTokenValid: PromiseResolve = await CommonHelper.isValidToken(token);
    if (isTokenValid.error) {
      throw new CustomError(RES_MSG.COMMON.UNAUTHORIZED_ACCESS, RESPONSES.UN_AUTHORIZED);
    }

    const authenticatedUserToken: string | null = await RedisHelper.getString(`accessToken:${isTokenValid.data.email}`);
    // const FAToken: string | null = await RedisHelper.getString(`twoFA:${isTokenValid.data.email}`);
    // if (!authenticatedUserToken) throw new CustomError(RES_MSG.TWO_FA.TOKEN_EXPIRE, RESPONSES.UN_AUTHORIZED);

    if (authenticatedUserToken !== token) {
      throw new CustomError(RES_MSG.TWO_FA.TOKEN_EXPIRE, RESPONSES.UN_AUTHORIZED);
    }

    const profileGetResponse: PromiseResolve = await UserService.fetchUserDetails({ email: isTokenValid.data.email }, [], ['kycDetails', 'wallets']);
    if (profileGetResponse.error) {
      throw new CustomError(RES_MSG.COMMON.UNAUTHORIZED_ACCESS, RESPONSES.UN_AUTHORIZED);
    }
    if (!profileGetResponse.data.isActive) {
      await RedisHelper.deleteKey(`accessToken:${isTokenValid.data.email}`);
      throw new CustomError(RES_MSG.COMMON.ADMIN_BLOCK_USER, RESPONSES.FORBIDDEN);
    }
    req.userInfo = { userId: isTokenValid.data.userId, email: isTokenValid.data.email, userType: isTokenValid.data.userType, isKyc: profileGetResponse.data.isKyc, isIssuer: profileGetResponse.data.isIssuer, exp: isTokenValid.data.exp };
    const is2FAVerified: boolean | string | null = await RedisHelper.getString(`2FA_${isTokenValid.data.userId}`);
    if (profileGetResponse.data.is2FAActive && !is2FAVerified) {
      return ResponseHandler.error(res, { error: true, status: RESPONSES.FORBIDDEN, message: RES_MSG.TWO_FA.PENDING });
    }

    next();
  } catch (error: any) {
    logger.error(error, 'verifyJWTToken error');

    return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Verify if the user is an investor
 * @param {Request} req - Request object
 * @param {Response} res - Response object
 * @param {NextFunction} next - Next function
 * @returns {Promise<PromiseResolve | void>} - Returns a promise that resolves to void
 */
export async function isInvestor(req: Request | any, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const { userInfo } = req;

    if (userInfo?.userType !== UserTypeEnum.Investor) {
      return ResponseHandler.error(res, { status: RESPONSES.FORBIDDEN, error: true, message: RES_MSG.COMMON.FORBIDDEN_ACCESS });
    }
    next();
  } catch (error) {
    logger.error(error, 'isInvestor error: ');

    return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

export function isInvestorOrInstitution(req: Request | any, res: Response, next: NextFunction) {
  try {
    const { userInfo } = req;

    if (userInfo?.userType !== UserTypeEnum.Investor && userInfo?.userType !== UserTypeEnum.Institution) {
      return ResponseHandler.error(res, {
        status: RESPONSES.FORBIDDEN,
        error: true,
        message: RES_MSG.COMMON.FORBIDDEN_ACCESS,
      });
    }

    next();
  } catch (error) {
    logger.error(error, 'isInvestorOrInstitution error: ');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * Verify if the user is an institution
 * @param {Request} req - Request object
 * @param {Response} res - Response object
 * @param {NextFunction} next - Next function
 * @returns {Promise<PromiseResolve | void>} - Returns a promise that resolves to void
 */
export async function isInstitution(req: Request | any, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const { userInfo } = req;
    if (userInfo?.userType !== UserTypeEnum.Institution) {
      return ResponseHandler.error(res, { status: RESPONSES.FORBIDDEN, error: true, message: RES_MSG.COMMON.FORBIDDEN_ACCESS });
    }
    next();
  } catch (error) {
    logger.error(error, 'isInstitution error: ');

    return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Middleware to verify if the user's KYC process is completed.
 *
 * If the KYC process is not completed, an error response is sent.
 * If the KYC process is completed, the middleware allows the request to proceed.
 *
 * @param req - The request object containing user information.
 * @param res - The response object used to send error responses.
 * @param next - The next middleware function to be called if KYC is completed.
 * @returns A promise that resolves to void or an error response.
 */
export async function isKycCompleted(req: Request | any, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const { userInfo } = req;
    if (!userInfo?.isKyc) {
      return ResponseHandler.error(res, { status: RESPONSES.FORBIDDEN, error: true, message: RES_MSG.ERROR_MSG.KYC_PENDING });
    }
    next();
  } catch (error) {
    logger.error(error, 'isKycCompleted error: ');

    return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}

/**
 * Middleware to verify if the user has completed the issuer process.
 *
 * If the issuer process is not completed, an error response is sent.
 * If the issuer process is completed, the middleware allows the request to proceed.
 *
 * @param req - The request object containing user information.
 * @param res - The response object used to send error responses.
 * @param next - The next middleware function to be called if issuer process is completed.
 * @returns A promise that resolves to void or an error response.
 */
export async function isIssuerCompleted(req: Request | any, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const { userInfo } = req;
    if (!userInfo?.isIssuer) {
      return ResponseHandler.error(res, { status: RESPONSES.FORBIDDEN, error: true, message: RES_MSG.ERROR_MSG.ISSUER_PENDING });
    }
    next();
  } catch (error) {
    logger.error(error, 'isIssuerCompleted error: ');

    return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
