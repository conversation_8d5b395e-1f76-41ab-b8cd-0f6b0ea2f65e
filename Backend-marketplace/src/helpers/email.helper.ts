import * as sgMail from '@sendgrid/mail';
import * as ejs from 'ejs';
import * as path from 'path';
import config from '../config/env';
import { RES_MSG } from '../utils/responseUtils';
import logger from './logging/logger.helper';

/**
 * EmailHelper class to handle sending emails using SendGrid.
 */
class EmailHelper {
  private sgMail: any;

  /**
   * Constructor to initialize the EmailHelper.
   * Sets up SendGrid with the API key from the configuration.
   */
  constructor() {
    // Set the SendGrid API key
    this.sgMail = sgMail.setApiKey(config.SENDGRID.API_KEY);
  }

  /**
   * Sends an email using SendGrid.
   *
   * @param to - Recipient email address
   * @param subject - Subject of the email
   * @param text - Plain text content of the email
   * @param html - HTML content of the email
   * @returns A promise that resolves when the email is sent or rejects with an error
   */
  public async sendMail(to: string, subject: string, text: string, html: string): Promise<boolean> {
    try {
      // Create the email message object
      const msg = {
        to,
        from: {
          email: config.SENDGRID.SENDER,
          name: '<PERSON><PERSON><PERSON>', // Set the display name here
        },
        subject,
        text,
        html,
      };

      // Send the email using SendGrid
      return this.sgMail
        .send(msg)
        .then(() => {
          return true;
        })
        .catch((error: any) => {
          logger.error(error?.message, 'error in SendGrid');

          return false;
        });
    } catch (error) {
      logger.error(error, 'Error of sendMail');

      return false;
    }
  }

  /**
   * Sends an email using SendGrid.
   *
   * @param to - Recipient email address
   * @param templateName - Template name
   * @param Details
   * @returns A promise that resolves when the email is sent or rejects with an error
   */
  public async sendEmailTemplate(to: string, templateName: string, details: any): Promise<boolean> {
    try {
      let templatePath;
      let subject;
      let text;
      if (templateName === 'reset-password') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.FORGOT_PASSWORD_SUBJECT;
        text = RES_MSG.EMAIL.FORGOT_PASSWORD_TEXT;
      } else if (templateName === 'verify-otp') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = 'Your Email OTP for Libertum';
        text = RES_MSG.EMAIL.OTP_TEXT;
      } else if (templateName === 'kycreceived') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.KYC_RECEIVED;
        text = RES_MSG.EMAIL.KYC_RECEIVED;
      } else if (templateName === 'orderMint') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.ORDER_MINTED;
        text = RES_MSG.EMAIL.ORDER_MINTED;
      } else if (templateName === 'walletWhitelistRequestSent') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.WALLET_WHITELIST_SUBMITTED;
        text = RES_MSG.EMAIL.WALLET_WHITELIST_SUBMITTED;
      } else if (templateName === 'walletWhitelistRequestApproved') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.WALLET_WHITELIST;
        text = RES_MSG.EMAIL.WALLET_WHITELIST;
      } else if (templateName === 'WalletWhitelistRequestRejected') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.WHITELIST_REJECT;
        text = RES_MSG.EMAIL.WHITELIST_REJECT;
      } else if (templateName === 'userTokens') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.WHITELIST_REJECT;
        text = RES_MSG.EMAIL.WHITELIST_REJECT;
      } else if (templateName === 'transferrejected') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.TRANSFER_RJECTED;
        text = RES_MSG.EMAIL.TRANSFER_RJECTED;
      } else if (templateName === 'offeringDeployed') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.OFFERING_DEPLOYED;
        text = RES_MSG.EMAIL.OFFERING_DEPLOYED;
      } else if (templateName === 'transferapproval') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.TRANSFER_APPROVED;
        text = RES_MSG.EMAIL.TRANSFER_APPROVED;
      } else if (templateName === 'orderCreate') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.ORDER_CREATE;
        text = RES_MSG.EMAIL.ORDER_CREATE;
      } else if (templateName === 'kybreceived') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.KYB_RECEIVED;
        text = RES_MSG.EMAIL.KYB_RECEIVED;
      } else if (templateName === 'accountBlocked') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.USER_BLOCKED;
        text = RES_MSG.EMAIL.USER_BLOCKED;
      } else if (templateName === 'signup') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.SIGN_UP_SUBJECT;
        text = RES_MSG.EMAIL.OTP_TEXT;
      } else if (templateName === 'login') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.SIGN_IN_SUBJECT;
        text = RES_MSG.EMAIL.SIGN_IN_SUBJECT;
      } else if (templateName === 'multipleLogin') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.MULTIPLE_LOGIN;
        text = RES_MSG.EMAIL.OTP_TEXT;
      } else if (templateName === 'PasswordResetConfirmation') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.FORGOT_PASSWORD_SUCCESS_SUBJECT;
        text = RES_MSG.EMAIL.FORGOT_PASSWORD_SUCCESS_SUBJECT;
      } else if (templateName === 'orderReject') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.ORDER_REJECTED;
        text = RES_MSG.EMAIL.ORDER_REJECTED;
      } else if (templateName === 'orderCancelled') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.ORDER_CANCELLED;
        text = RES_MSG.EMAIL.ORDER_CANCELLED;
      } else if (templateName === 'forceTransfer') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.FORCE_TRANSFER;
        text = RES_MSG.EMAIL.FORCE_TRANSFER;
      } else if (templateName === 'fromforceTransfer') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.FORCE_TRANSFER;
        text = RES_MSG.EMAIL.FORCE_TRANSFER;
      } else {
        logger.error(templateName, 'Error: no matching template found');

        return false;
      }

      const htmlDetails = await ejs.renderFile(templatePath, { detail: { ...details, baseUrl: config.API_HOST_URL } }, { async: true }); // render the template
      return await this.sendMail(
        to,
        subject,
        text,
        // htmldata
        htmlDetails,
      );
    } catch (error: any) {
      logger.error(error, 'Error in sendEmailTemplate');

      return false;
    }
  }
}

// Export an instance of the EmailHelper class
export default new EmailHelper();
