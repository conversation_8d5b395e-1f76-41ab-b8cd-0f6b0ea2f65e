/* eslint-disable @typescript-eslint/no-unused-vars */
import Big from 'big.js';

type Operation = 'add' | 'sub' | 'mul' | 'div';

/**
 * Performs precise arithmetic operations with optional precision without rounding.
 * @param {Operation} operation - The operation to perform ('add', 'sub', 'mul', 'div').
 * @param {number | string} num1 - The first number.
 * @param {number | string} num2 - The second number.
 * @param {number} [precision] - Optional precision for truncation (not rounding).
 * @returns {number} - The result as a number.
 */
export function calculate(operation: Operation, num1: number | string, num2: number | string, precision?: number): string {
  // Use regex to remove 'n' if it exists
  const sanitizedNum1 = num1.toString().replace(/n/g, '');
  const sanitizedNum2 = num2.toString().replace(/n/g, '');

  const bigNum1 = new Big(sanitizedNum1);
  const bigNum2 = new Big(sanitizedNum2);
  let result: Big;

  switch (operation) {
    case 'add':
      result = bigNum1.plus(bigNum2);
      break;
    case 'sub':
      result = bigNum1.minus(bigNum2);
      break;
    case 'mul':
      result = bigNum1.times(bigNum2);
      break;
    case 'div':
      result = bigNum1.div(bigNum2);
      break;
    default:
      throw new Error('Invalid operation. Use "add", "sub", "mul", or "div".');
  }

  let finalResult = result?.toString();

  // Convert scientific notation (e.g., 1.23e+4) to fixed-point
  if (finalResult?.includes('e')) {
    finalResult = new Big(finalResult).toFixed();
  }

  // // Optional: Apply precision truncation (not rounding)
  // if (precision && precision !== undefined) {
  //   finalResult = new Big(finalResult).toFixed(precision);
  // }
  return finalResult;
}
