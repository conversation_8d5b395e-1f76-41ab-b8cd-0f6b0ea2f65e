/**
 * Logger configuration using <PERSON> for application-wide logging.
 * Provides structured logging with timestamp, error stack traces, and JSON formatting.
 * Supports console output with colorization and stack trace inclusion.
 */
import { createLogger, format, transports } from 'winston';
// import * as fs from 'fs';
// import * as path from 'path';
import 'winston-daily-rotate-file';

import CONFIG from '../../config/env/index';

// const logDir = path.resolve(__dirname, '../../../logs');
// if (!fs.existsSync(logDir)) {
//   fs.mkdirSync(logDir, { recursive: true });
// }

// const dailyRotateFileTransport = new transports.DailyRotateFile({ filename: `${logDir}/%DATE%-app.log`, datePattern: 'YYYY-MM-DD', zippedArchive: true, maxSize: '20m', maxFiles: '14d', level: CONFIG.PROJECT.LOG_LEVEL });

// const exceptionsFilePath = path.join(logDir, 'exceptions.log');
// const rejectionsFilePath = path.join(logDir, 'rejections.log');

// const isProduction = CONFIG.ENVIRONMENT === 'prod';

/**
 * Custom format to handle error objects properly
 * This allows the first parameter to be an error object
 */
const errorFormat = format((info: any) => {
  // If the message is an error object and we have a second parameter
  if (info.message instanceof Error && info[Symbol.for('splat')] && info[Symbol.for('splat')].length > 0) {
    // Extract the error object
    const error = info.message;
    // Use the custom message (second parameter) as the main message
    info.message = info[Symbol.for('splat')][0];
    // Add error details to the log entry
    info.error = {
      name: error.name,
      message: error.message,
      stack: error.stack,
    };
  }
  return info;
});

/**
 * Creates and configures a Winston logger instance with the following features:
 * - Log level based on environment configuration
 * - Timestamp formatting
 * - Error stack trace inclusion
 * - JSON formatting for structured logging
 * - Service and environment metadata
 * - Console transport with colorization
 */
const logger = createLogger({
  level: CONFIG.PROJECT.LOG_LEVEL,
  format: format.combine(
    errorFormat(),
    format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    format.errors({ stack: true }), // Ensures stack traces are included
    format.json(),
  ),
  defaultMeta: { service: CONFIG.PROJECT.NAME, environment: CONFIG.ENVIRONMENT || 'development' },
  // transports: [dailyRotateFileTransport, new transports.File({ filename: path.join(logDir, 'app.log'), handleExceptions: true, level: CONFIG.PROJECT.LOG_LEVEL })],
  // exceptionHandlers: [new transports.File({ filename: exceptionsFilePath })],
  // rejectionHandlers: [new transports.File({ filename: rejectionsFilePath })],
});

// Add console transport
logger.add(
  new transports.Console({
    format: format.combine(
      format.colorize(),
      format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
      format.printf(({ timestamp, level, message, error }: any) => {
        let logOutput = `[${timestamp}] ${level}: ${message}`;

        // Add error details if available
        if (error) {
          logOutput += `\n  Error: ${error.message}`;
          if (error.stack) {
            logOutput += `\n  Stack: ${error.stack}`;
          }
        }

        return logOutput;
      }),
    ),
    handleExceptions: true,
    level: 'debug',
  }),
);

export default logger;
