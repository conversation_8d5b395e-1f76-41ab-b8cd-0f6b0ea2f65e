import express from 'express';
import { ResponsePayLoad, PromiseResolve } from '../utils/common.interface';
import { capitalizeString } from './messageHelper';

/**
 * ResponseHandler class provides utility methods for handling HTTP responses.
 * It standardizes the response format and handles both success and error cases.
 */
export class ResponseHandler {
  /**
   * Sends a successful response with standardized format.
   * @template T - The type of data being returned
   * @param {express.Response} response - Express response object
   * @param {ResponsePayLoad<T>} responseData - Response payload containing status, message, and data
   * @returns {PromiseResolve} The standardized response object
   */
  static success<T>(response: express.Response, responseData: ResponsePayLoad<T>): PromiseResolve {
    let { message } = responseData;
    const { status, data = null, error } = responseData;
    message = capitalizeString(message);
    response.status(status || 200).json({ message, status, data, error });

    return { message, status: status || 200, data, error };
  }

  /**
   * Sends an error response with standardized format.
   * @template T - The type of data being returned
   * @param {express.Response} response - Express response object
   * @param {ResponsePayLoad<T>} responseData - Response payload containing status, message, and data
   * @returns {PromiseResolve} The standardized response object
   */
  static error<T>(response: express.Response, responseData: ResponsePayLoad<T>): PromiseResolve {
    let { message } = responseData;
    const { status, data = null, error } = responseData;
    message = capitalizeString(message);
    response.status(status || 500).json({ message, status, data, error });

    return { message, status: status || 500, data, error };
  }
}
