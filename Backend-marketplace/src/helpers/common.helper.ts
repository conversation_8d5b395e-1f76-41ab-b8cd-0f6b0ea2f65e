/* eslint-disable @typescript-eslint/no-explicit-any */
import * as jwt from 'jsonwebtoken';
import * as bcrypt from 'bcrypt';
import axios from 'axios';
import * as moment from 'moment';
import { AuthTokenResponseType, otpMethodsEnum, PromiseResolve } from '../utils/common.interface';
import CONFIG from '../config/env';
import RedisHelper from './redis.helper';
import CustomError from './customError.helper';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import { coolDownTimeInSeconds, otpLength } from '../utils/constant';
import logger from './logging/logger.helper';
import { disposableDomains } from '../utils/disposableDomains';
import emailHelper from './email.helper';
import { IUpdateUserModel } from '../component/userAuthentications/models/user.model';
import smsHelper from './sms.helper';

const { LOGIN_MAX_ATTEMPT, LOGIN_BLOCK_TIME } = CONFIG.REDIS;

const { AUTH_EXPIRE_TIME, TOKEN, REFRESH_EXPIRE_TIME, REFRESH_TOKEN } = CONFIG.JWT_AUTH;

const CommonHelper = {
  /**
   * Creates a JWT authentication token.
   * @param {Object} data - The data to be included in the token
   * @param {boolean} [isRefreshToken=false] - Whether to create a refresh token
   * @param {string} [tokenType] - Optional token type
   * @param {string} [expiresIn=AUTH_EXPIRE_TIME] - Token expiration time
   * @returns {Promise<PromiseResolve>} The authentication response containing tokens
   */
  async createJWTAuth(
    data: object | any,
    // eslint-disable-next-line default-param-last
    isRefreshToken: boolean = false,
    tokenType?: string,
    expiresIn: string = AUTH_EXPIRE_TIME,
  ): Promise<PromiseResolve> {
    try {
      const expiresIN = this.parseExpirationTime(expiresIn);
      const refReshExpiresIN = this.parseExpirationTime(REFRESH_EXPIRE_TIME);
      const payload = data;
      const options = { expiresIn: expiresIN, issuer: CONFIG.PROJECT.NAME, audience: data.email };

      const refreshOptions = { expiresIn: refReshExpiresIN, issuer: CONFIG.PROJECT.NAME, audience: data.email };
      const accessToken: string = jwt.sign(payload, TOKEN, options);
      let refreshToken: string;
      if (isRefreshToken) {
        refreshToken = jwt.sign(payload, REFRESH_TOKEN, refreshOptions);
      }

      const accessTokenKey = tokenType ? `${tokenType}:${data.email}` : `accessToken:${data.email}`;

      await RedisHelper.setString(accessTokenKey, accessToken, Number(this.convertToMilliseconds(expiresIn)));

      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: { refreshToken, accessToken } };
    } catch (error: any) {
      logger.error(error, 'createJWTAuth Error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message };
    }
  },
  /**
   * @param {string} expiration
   * @returns {Promise <PromiseResolve>}
   * @memberof CommonHelper
   */
  parseExpirationTime(expiration: string | number): number | string {
    if (typeof expiration === 'string' && /^\d+$/.test(expiration)) {
      return Number(expiration); // Convert numeric strings to number
    }

    return expiration; // Return the original string if it's like "1h", "30m", etc.
  },
  /**
   * @param {string} accessToken
   * @returns {Promise <PromiseResolve>}
   * @memberof CommonHelper
   */
  async isValidToken(accessToken: string, isAccessToken: boolean = true): Promise<PromiseResolve> {
    try {
      let secretToken: string = TOKEN;
      if (!isAccessToken) {
        secretToken = REFRESH_TOKEN;
      }
      const aud = jwt.verify(accessToken, secretToken) as AuthTokenResponseType;
      if (!aud) {
        throw new CustomError(RES_MSG.COMMON.UNAUTHORIZED_ACCESS, RESPONSES.UN_AUTHORIZED);
      }

      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: aud };
    } catch (error: any) {
      logger.error(error, ' invalidToken Error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  },

  /**
   * @param {number} length
   * @returns {Promise <PromiseResolve>}
   * @memberof CommonHelper
   */
  async generateOTP(length: number = otpLength): Promise<PromiseResolve> {
    try {
      const characters = '0123456789';
      const charactersLength = characters.length;
      let otp: string = '';
      // eslint-disable-next-line no-plusplus
      for (let i = 0; i < length; i++) {
        otp += characters.charAt(Math.floor(Math.random() * charactersLength));
      }

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: otp,
      };
    } catch (error: any) {
      logger.error(error, ' generateOTP Error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  },

  /**
   * Format a given date to DD-MM-YYYY format.
   * @param {Date} date - The date to be formatted.
   * @returns {Promise<PromiseResolve>} The formatted date wrapped in a PromiseResolve object.
   * @memberof CommonHelper
   */
  async formatDates(date: Date | string): Promise<string | boolean> {
    try {
      const formattedDate = moment(date).format('MM/DD/YYYY');

      return formattedDate;
    } catch (error: any) {
      logger.error(error, 'formatDates Error');

      return error.message;
    }
  },

  /**
   * Send otp on user email.
   * @param {IUpdateUserModel} data - user data.
   * @param {string} otpType - otp type.
   * @param {otpMethodsEnum} method -OTP method.
   * @returns {Promise<PromiseResolve>} The formatted date wrapped in a PromiseResolve object.
   * @memberof CommonHelper
   */
  async sendOTP(data: IUpdateUserModel, otpType: string, method: string = otpMethodsEnum.EMAIL): Promise<PromiseResolve> {
    try {
      const coolDownKey = `${otpType}_${method}_otp_cool_down:${data._id}`;
      const otpKey = `otp_${otpType}_${method}_${data._id}`;
      const lastRequestTimestamp = await RedisHelper.getString(coolDownKey);
      if (lastRequestTimestamp) {
        // eslint-disable-next-line radix
        const timeElapsed = Math.floor(Date.now() / 1000) - parseInt(lastRequestTimestamp);
        if (timeElapsed < coolDownTimeInSeconds) {
          const remainingTime = coolDownTimeInSeconds - timeElapsed;
          throw new CustomError(`You can request OTP again in ${remainingTime} seconds`, RESPONSES.TOO_MANY_REQUESTS);
        }
      }

      const otpResult = await CommonHelper.generateOTP(otpLength);
      if (otpResult.error) {
        throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.BAD_REQUEST);
      }
      const otp = otpResult.data;
      const setOtpResult = await RedisHelper.setString(otpKey, otp, CONFIG.REDIS.OTP_EXPIRY);
      if (!setOtpResult) {
        throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.BAD_REQUEST);
      }
      const emailDetails = { otp, name: data?.name, expireTime: Number(CONFIG.REDIS.OTP_EXPIRY) / 60 };

      if (method === otpMethodsEnum.MOBILE) {
        smsHelper.sendSMSTemplates(`${data.countryCode}${data.mobile}`, 'verify-otp', emailDetails);
      } else if (method === otpMethodsEnum.EMAIL) {
        emailHelper.sendEmailTemplate(data.email, 'verify-otp', emailDetails);
      }

      await RedisHelper.setString(coolDownKey, Math.floor(Date.now() / 1000).toString(), coolDownTimeInSeconds);

      // const attemptsKey: string = `otp_attempts:${data.email}`;
      // const totalAttemptsCounts = await RedisHelper.getString(attemptsKey);
      // if (Number(totalAttemptsCounts) <= Number(LOGIN_MAX_ATTEMPT)) {
      //   await RedisHelper.incrementKey(attemptsKey, LOGIN_BLOCK_TIME);
      // }

      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.USER.OTP_SUCCESS, data: otp };
    } catch (error: any) {
      logger.error(error, 'sendOTP');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  },

  /**
   * get otp.
   * @param {string} key - userId for key.
   * @param {string} otpType - otp type.
   * @returns {Promise<PromiseResolve>} PromiseResolve object.
   * @memberof CommonHelper
   */
  async getOTP(key: string, otpType: string, method: string = otpMethodsEnum.EMAIL): Promise<PromiseResolve> {
    try {
      const otpKey = `otp_${otpType}_${method}_${key}`;
      const storedOTP = await RedisHelper.getString(otpKey);
      if (!storedOTP) {
        throw new CustomError(RES_MSG.ERROR_MSG.INVALID_OTP, RESPONSES.BAD_REQUEST);
      }

      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: { otp: storedOTP, key: otpKey } };
    } catch (error: any) {
      logger.error(error, 'getOTP error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  },

  /**
   * @param {string} password
   * @param {string} comparePassword
   * @returns {Promise<PromiseResolve>}
   * @memberof CommonHelper
   */
  async verifyPassword(password: string, comparePassword?: string): Promise<PromiseResolve> {
    try {
      const validPassword: boolean = await bcrypt.compare(password, comparePassword);

      if (!validPassword) {
        throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.NOTFOUND);
      }

      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS };
    } catch (error) {
      logger.error(error, 'verifyPassword error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  },

  /**
   * @param {string} key
   * @returns {Promise<PromiseResolve>}
   * @memberof CommonHelper
   */
  async userLock(key: string): Promise<PromiseResolve> {
    try {
      let lockedTimes: string;
      const lockKey: string = `locked:${key}`;

      const attemptsKey: string = `otp_attempts:${key}`;
      const totalAttemptsCounts = await RedisHelper.getString(attemptsKey);

      if (!totalAttemptsCounts) {
        await RedisHelper.setString(attemptsKey, '1', LOGIN_BLOCK_TIME);
      }
      if (totalAttemptsCounts && Number(totalAttemptsCounts) <= Number(LOGIN_MAX_ATTEMPT)) {
        await RedisHelper.incrementKey(attemptsKey, LOGIN_BLOCK_TIME);
      }
      if (totalAttemptsCounts && Number(totalAttemptsCounts) >= LOGIN_MAX_ATTEMPT) {
        const isLocked = await RedisHelper.getString(lockKey);
        if (!isLocked) {
          await RedisHelper.setString(lockKey, 'locked', LOGIN_BLOCK_TIME);
        }
        const lockDuration = await RedisHelper.getTTL(lockKey);

        if (lockDuration) {
          lockedTimes = await this.convertSecondsToHMS(lockDuration);
        }
        throw new CustomError(RES_MSG.USER.USER_LOCKED_TIME.replace('{minutes}', lockedTimes.toString()), RESPONSES.RESOURCE_LOCKED);
      }

      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: { totalAttemptsCounts, lockedTimes } };
    } catch (error) {
      logger.error(error, 'userLock error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, data: LOGIN_MAX_ATTEMPT, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  },

  /**
   * @param {string} key
   * @returns {Promise<PromiseResolve>}
   * @memberof CommonHelper
   */
  async isUserLocked(key: string): Promise<PromiseResolve> {
    try {
      let lockedTimes: string;
      const lockKey: string = `locked:${key}`;
      const attemptsKey: string = `otp_attempts:${key}`;
      const totalAttemptsCounts = await RedisHelper.getString(attemptsKey);

      const isLocked = await RedisHelper.getString(lockKey);
      if (totalAttemptsCounts && Number(totalAttemptsCounts) >= LOGIN_MAX_ATTEMPT && isLocked) {
        const lockDuration = await RedisHelper.getTTL(lockKey);

        if (lockDuration) {
          lockedTimes = await this.convertSecondsToHMS(lockDuration);
        }
        throw new CustomError(RES_MSG.USER.USER_LOCKED_TIME.replace('{minutes}', lockedTimes.toString()), RESPONSES.RESOURCE_LOCKED);
      }

      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: { totalAttemptsCounts, lockedTimes } };
    } catch (error) {
      logger.error(error, 'isUserLock error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, data: LOGIN_MAX_ATTEMPT, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  },

  /**
   * @param {string} key
   * @returns {Promise<PromiseResolve>}
   * @memberof CommonHelper
   */
  async userUnLock(key: string): Promise<PromiseResolve> {
    try {
      const lockKey: string = `locked:${key}`;
      const attemptsKey: string = `otp_attempts:${key}`;
      await RedisHelper.deleteKey(lockKey);
      await RedisHelper.deleteKey(attemptsKey);

      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS };
    } catch (error) {
      logger.error(error, 'userLock error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  },

  /**
   * @param {string} provider
   * @param {string} token
   * @returns {Promise<PromiseResolve>}
   * @memberof CommonHelper
   */
  async verifySocialToken(provider: string, token: string): Promise<PromiseResolve> {
    try {
      let userData;
      switch (provider) {
        case 'google':
          userData = await this.verifyGoogleToken(token);
          break;
        case 'facebook':
          userData = await this.verifyFacebookToken(token);
          break;
        default:
          throw new CustomError('Unsupported social media provider', RESPONSES.BAD_REQUEST);
      }
      if (userData.error) {
        return { error: true, message: RES_MSG.TWO_FA.TOKEN_INVALID, status: RESPONSES.UN_AUTHORIZED };
      }

      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: userData.data };
    } catch (error) {
      logger.error(error, 'verifySocialToken error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  },

  /**
   * @param {string} token
   * @returns {Promise<PromiseResolve>}
   * @memberof CommonHelper
   */
  async verifyGoogleToken(token: string): Promise<PromiseResolve> {
    try {
      const url = `https://www.googleapis.com/oauth2/v3/tokeninfo?access_token=${token}`;
      const response = await axios.get(url);
      if (response.data && response.data.aud === CONFIG.GOOGLE.CLIENT_ID) {
        const userInfoUrl = 'https://www.googleapis.com/oauth2/v3/userinfo';
        const userInfoResponse = await axios.get(userInfoUrl, { headers: { Authorization: `Bearer ${token}` } });

        return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: { isEmailVerify: response.data.email_verified, email: response.data.email, name: userInfoResponse.data.name } };
      }
      throw new CustomError('Invalid Google token', RESPONSES.BAD_REQUEST);
    } catch (error: any) {
      logger.error(error, 'verifyGoogleToken error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  },

  /**
   * @param {string} token
   * @returns {Promise<PromiseResolve>}
   * @memberof CommonHelper
   */
  async verifyFacebookToken(token: string): Promise<PromiseResolve> {
    const url = `https://graph.facebook.com/me?access_token=${token}&fields=id,name,email`;
    const response = await axios.get(url);
    if (response.data && response.data.email) {
      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: { id: response.data.sub, email: response.data.email, name: response.data.name } };
    }
    throw new CustomError('Invalid Facebook token', RESPONSES.BAD_REQUEST);
  },

  /**
   * @param {string} email
   * @returns {Promise<PromiseResolve>}
   * @memberof CommonHelper
   */

  async isValidEmail(email: string): Promise<PromiseResolve> {
    try {
      const domain = email.split('@')[1];
      const isInvalid = disposableDomains.includes(domain);
      if (isInvalid) {
        return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.ERROR_MSG.INVALID_EMAIL };
      }

      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: { isInvalid } };
    } catch (error) {
      logger.error(error, 'isValidEmail error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  },

  /**
   * Merges arrays without duplicates based on specified keys.
   * @param {Array} newItems - Array of new items to merge
   * @param {Array} [existingItems=[]] - Array of existing items
   * @param {Array} [keys=[]] - Array of keys to check for duplicates
   * @returns {Array} The merged array without duplicates
   */
  mergeWithoutDuplicates(newItems: any[], existingItems: any[] = [], keys: any[] = []): any[] {
    if (keys.length === 0) {
      const uniqueItems = [...new Set([...existingItems, ...newItems])];

      return uniqueItems;
    }

    // If keys are provided, process the merging logic based on keys
    newItems.forEach((newItem) => {
      const existingIndex = existingItems.findIndex((existingItem) =>
        keys.every((key) => {
          const existingValue = key.split('.').reduce((o: any, k: string) => (o || {})[k], existingItem);
          const newValue = key.split('.').reduce((o: any, k: string) => (o || {})[k], newItem);

          return existingValue === newValue;
        }),
      );

      if (existingIndex > -1) {
        // Merge the items if a match is found based on keys
        existingItems[existingIndex] = { ...existingItems[existingIndex], ...newItem };
      } else {
        // Push newItem to existingItems if no match is found
        existingItems.push(newItem);
      }
    });

    // Return the updated existingItems array
    return existingItems;
  },

  /**
   * Converts a time string to milliseconds.
   * @param {string|number} timeString - The time string to convert (e.g., '1hr', '30m', '60s')
   * @returns {number} The time in milliseconds
   * @throws Error if the time format is invalid
   */
  convertToMilliseconds(timeString: string | number): number {
    if (typeof timeString === 'number') {
      return timeString;
    }

    const regex = /^(\d+)(ms|s|m|hr)$/;
    const match = timeString.match(regex);

    if (!match) {
      throw new Error('Invalid time format');
    }

    const value = parseInt(match[1], 10);
    const unit = match[2];

    switch (unit) {
      case 's':
        return value;
      case 'm':
        return value * 60;
      case 'hr':
        return value * 60 * 60;
      default:
        throw new Error('Unknown time unit');
    }
  },

  /**
   * Converts seconds to a human-readable time string.
   * @param {number} seconds - The number of seconds to convert
   * @returns {Promise<string>} The formatted time string (e.g., '2 hours 30 minutes 15 seconds')
   */
  async convertSecondsToHMS(seconds: number): Promise<string> {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    let timeString = '';

    if (hours > 0) {
      timeString += `${hours} hour${hours !== 1 ? 's' : ''} `;
    }
    if (minutes > 0) {
      timeString += `${minutes} minute${minutes !== 1 ? 's' : ''} `;
    }
    if (remainingSeconds > 0 || (!hours && !minutes && remainingSeconds === 0)) {
      timeString += `${remainingSeconds} second${remainingSeconds !== 1 ? 's' : ''}`;
    }

    return timeString.trim();
  },

  /**
   * @param {string} period
   * @returns {startDate,endDate}
   * @memberof CommonHelper
   */
  getDateRange(period: string) {
    let startDate;
    const endDate = moment();

    switch (period) {
      case '1Y':
        startDate = moment().subtract(1, 'year');
        break;
      case '2Y':
        startDate = moment().subtract(2, 'years');
        break;
      case '3Y':
        startDate = moment().subtract(2, 'years');
        break;
      case '1M':
        startDate = moment().subtract(1, 'month');
        break;
      case '15D':
        startDate = moment().subtract(15, 'days');
        break;
      case '7D':
        startDate = moment().subtract(7, 'days');
        break;
      case '1D':
        startDate = moment().subtract(1, 'day');
        break;
      default:
        startDate = moment().subtract(1, 'year'); // default to last 1 year if no valid period
    }

    return {
      startDate: startDate.unix(), // Convert startDate to timestamp
      endDate: endDate.unix(), // Convert endDate to timestamp
    };
  },

  /**
   * Utility function to replace all occurrences of oldId with newId in an object,
   * including within string values (e.g., URLs, paths).
   */
  replaceIdsInObject(obj: any, oldId: string, newId: string) {
    // eslint-disable-next-line no-restricted-syntax
    for (const key in obj) {
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        // Recursively process nested objects or arrays
        this.replaceIdsInObject(obj[key], oldId, newId);
      } else if (typeof obj[key] === 'string') {
        // Replace oldId with newId inside string values
        obj[key] = obj[key].replace(new RegExp(oldId, 'g'), newId);
      }
    }
  },

  // async validateEthereumAddress(address: string) {
  //   return utils.isAddress(address);
  // },

  /**
   * Generates a random key for various purposes.
   * @returns {Promise<string>} A random key string
   */
  async generateRandomKey() {
    return Math.random().toString().slice(2, 6);
  },

  /**
   * Masks sensitive information in an email or mobile number.
   * @param {string} value - The value to mask
   * @param {'email' | 'mobile'} type - The type of masking to apply
   * @returns {string} The masked value
   */
  maskSensitiveInfo(value: string, type: 'email' | 'mobile'): string {
    if (!value) return '';

    if (type === 'email') {
      return this.maskEmail(value);
    } else if (type === 'mobile') {
      return this.maskMobile(value);
    }

    return value; // Default return in case of an unknown type
  },

  /**
   * Masks an email address by replacing characters with asterisks.
   * @param {string} email - The email address to mask
   * @returns {string} The masked email address
   */
  maskEmail(email: string): string {
    const emailRegex = /^(.{2})(.*)(@.*)$/; // Capture first 2 letters, middle part, and domain
    return email.replace(emailRegex, (match, start, middle, end) => {
      return `${start}${'*'.repeat(middle.length)}${end}`;
    });
  },

  /**
   * Masks a mobile number by replacing digits with 'x' except for the last 4 digits.
   * @param {string} mobile - The mobile number to mask
   * @returns {string} The masked mobile number
   */
  maskMobile(mobile: string): string {
    return mobile.replace(/\d(?=\d{4})/g, 'x'); // Replace all but the last 4 digits
  },

  /**
   * Escapes special characters in a string for use in regular expressions.
   * @param {string} text - The text to escape
   * @returns {string} The escaped text
   */
  escapeRegex(text: string): string {
    return text.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
  },
};

export default CommonHelper;
