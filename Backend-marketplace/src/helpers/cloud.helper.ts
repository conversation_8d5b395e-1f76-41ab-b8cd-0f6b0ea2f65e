import { BlobServiceClient, StorageSharedKeyCredential } from '@azure/storage-blob';
import CONFIG from '../config/env';
import logger from './logging/logger.helper';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import { DocumentTypesEnum, offeringDocumentTypesEnum, PromiseResolve } from '../utils/common.interface';
import * as path from 'path';
import axios from 'axios';
import CustomError from './customError.helper';

/**
 * CloudHelper class handles file operations with Azure Blob Storage.
 * It manages file uploads, downloads, and deletions in the cloud storage.
 */
export class CloudHelper {
  private blobServiceClient: BlobServiceClient;
  private containerClient: any;

  /**
   * Initializes the CloudHelper with Azure Blob Storage credentials.
   * @constructor
   */
  constructor() {
    this.initClient();
  }

  /**
   * Initializes the Azure Blob Storage client with credentials.
   * @private
   */
  private async initClient() {
    try {
      // Check if Azure connection string and container name are properly set
      if (!CONFIG.CLOUD.CONNECTION_STRING || !CONFIG.CLOUD.BUCKET_NAME || !CONFIG.CLOUD.ACCOUNT_NAME) {
        throw new CustomError('Azure Cloud keys are not set correctly', RESPONSES.EXPECTATION_FAILED);
      }
      // // Initialize BlobServiceClient and ContainerClient using the provided connection string
      // logger.info(CONFIG.CLOUD.BUCKET_NAME, 'CONFIG.CLOUD.CONNECTION_STRING', CONFIG.CLOUD.CONNECTION_STRING);
      // this.blobServiceClient = BlobServiceClient.fromConnectionString(CONFIG.CLOUD.CONNECTION_STRING);
      // this.containerClient = this.blobServiceClient.getContainerClient(CONFIG.CLOUD.BUCKET_NAME);

      const credentials = new StorageSharedKeyCredential(CONFIG.CLOUD.ACCOUNT_NAME, CONFIG.CLOUD.CONNECTION_STRING);

      this.blobServiceClient = new BlobServiceClient(`https://${CONFIG.CLOUD.ACCOUNT_NAME}.blob.core.windows.net`, credentials);

      this.containerClient = this.blobServiceClient.getContainerClient(CONFIG.CLOUD.BUCKET_NAME);
    } catch (error: any) {
      // Log any error that occurs during initialization
      logger.error(error, 'File Upload Init error');
    }
  }

  /**
   * Uploads files to Azure Blob Storage.
   * @param {string} userId - ID of the user uploading the file
   * @param {Express.Multer.File} file - The file being uploaded
   * @param {string} [documentType='othersDocs'] - Type of document
   * @param {string} folderName - Folder name for organizing files
   * @param {string} [offeringId] - Optional ID related to the offering
   * @returns {Promise<PromiseResolve|any>} The upload response
   */
  public async uploadFiles(userId: string, file: Express.Multer.File, documentType: string = 'othersDocs', folderName: string, offeringId?: string): Promise<PromiseResolve | any> {
    try {
      // Construct folder and file names
      const envFolder = CONFIG.ENVIRONMENT;
      const fileExtension = path.extname(file.originalname);
      const currentTimestamp = Date.now();
      const newFileName = `${userId}-${currentTimestamp}-${documentType}${fileExtension}`;
      const keyName = offeringId ? `${envFolder}/${folderName}/${userId}/${offeringId}/${documentType}/${newFileName}` : `${envFolder}/${folderName}/${userId}/${documentType}/${newFileName}`;

      // Exclude certain document types for deletion of old files
      const excludedDocumentTypes: Array<offeringDocumentTypesEnum | DocumentTypesEnum> = [
        offeringDocumentTypesEnum.url,
        DocumentTypesEnum.FRONT_ID_CARD,
        DocumentTypesEnum.BACK_ID_CARD,
        offeringDocumentTypesEnum.customImage,
        offeringDocumentTypesEnum.propertyImages,
      ];

      // If the document type is not excluded, delete old images in the folder
      if (!excludedDocumentTypes.includes(documentType as offeringDocumentTypesEnum | DocumentTypesEnum)) {
        await this.deleteOldImages(`${envFolder}/${folderName}/${userId}/${offeringId || ''}/${documentType}/`);
      }

      // Upload the file to Azure Blob Storage
      const blockBlobClient = this.containerClient.getBlockBlobClient(keyName);
      await blockBlobClient.uploadData(file.buffer, {
        blobHTTPHeaders: {
          blobContentType: file.mimetype,
        },
      });

      // Construct the file endpoint
      const endPoint = blockBlobClient.url;

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.CREATE_SUCCESS,
        data: { url: endPoint, keyName },
      };
    } catch (error: any) {
      // Log any error that occurs during file upload
      logger.error(error, 'Failed to uploadFile');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * Delete a file from Azure Blob Storage
   * @param keyName - The name (key) of the file to delete
   */
  public async deleteFile(keyName: string): Promise<boolean> {
    try {
      // Get the BlobClient for the specified file and delete it
      const blockBlobClient = this.containerClient.getBlockBlobClient(keyName);
      await blockBlobClient.delete();
      return true;
    } catch (error: any) {
      // Log any error that occurs during file deletion
      logger.error(error, 'Failed to deleteFile');
      return false;
    }
  }

  /**
   * Delete old images in a folder on Azure Blob Storage
   * @param oldFolderName - The folder name containing the images to delete
   */
  private async deleteOldImages(oldFolderName: string): Promise<void> {
    try {
      // List all blobs in the specified folder and delete them
      const blobs = this.containerClient.listBlobsFlat({ prefix: oldFolderName });
      for await (const blob of blobs) {
        const blockBlobClient = this.containerClient.getBlockBlobClient(blob.name);
        await blockBlobClient.delete();
      }
    } catch (error) {
      // Log any error that occurs during the deletion of old images
      logger.error(`Failed to delete images in folder ${oldFolderName}:`, error);
    }
  }

  /**
   * Copy all files from one folder to another
   * @param oldOfferingId - The ID of the source offering
   * @param newOfferingId - The ID of the destination offering
   * @param userId - ID of the user
   * @param folderName - Folder name for organizing files
   */
  public async copyFolder(oldOfferingId: string, newOfferingId: string, userId: string, folderName: string): Promise<any> {
    try {
      const envFolder = CONFIG.ENVIRONMENT;
      const sourceFolderName = `${envFolder}/${folderName}/${userId}/${oldOfferingId}`;
      const destinationFolderName = `${envFolder}/${folderName}/${userId}/${newOfferingId}`;

      // List all files in the source folder and copy them to the destination folder
      const blobs = this.containerClient.listBlobsFlat({ prefix: sourceFolderName });
      for await (const blob of blobs) {
        const sourceBlobClient = this.containerClient.getBlobClient(blob.name);
        const destinationBlobClient = this.containerClient.getBlobClient(blob.name.replace(sourceFolderName, destinationFolderName));
        await destinationBlobClient.beginCopyFromURL(sourceBlobClient.url);
      }

      logger.info(`Successfully copied folder from ${sourceFolderName} to ${destinationFolderName}`);
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.CREATE_SUCCESS,
      };
    } catch (error: any) {
      // Log any error that occurs during the folder copy operation
      logger.error(error, 'Failed to copy folder');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * Fetches data from a URL and converts it to base64 format
   * @param url - The URL of the file to fetch
   */
  public async getFileAsBase64(url: string): Promise<PromiseResolve | any> {
    try {
      // Fetch the file data from the URL using axios
      const response = await axios.get(url, { responseType: 'arraybuffer' });

      // Convert the file data to base64 format
      return Buffer.from(response.data).toString('base64');
    } catch (error: any) {
      // Log any error that occurs while fetching or converting the file
      logger.error(error, 'Failed to fetch and convert file to base64');
      throw new Error(error);
    }
  }
}

export default new CloudHelper();
