import { createClient } from 'redis';
import CONFIG from '../config/env';
import logger from './logging/logger.helper';
import { RedisSetOptions } from '../utils/common.interface';

const { REDIS } = CONFIG;

/**
 * RedisHelper class provides methods for interacting with Redis database.
 * It handles connection management, string operations, and key management.
 */
export class RedisHelper {
  public client: any;

  public clientInternal: any;

  private host: string = `${REDIS.HOST}`;

  public isConnected: boolean = false;

  /**
   * Establishes a connection to Redis.
   * @returns {Promise<boolean|null>} Returns false if already connected, null if connection fails
   */
  public async connectRedis() {
    try {
      if (this.isConnected) {
        return false;
      }
      const tlsOptions = { url: this.host };
      this.clientInternal = createClient(tlsOptions);
      this.clientInternal.on('error', (err: any) => {
        logger.error(err, 'Redis Client Error');
        throw new Error(err);
      });
      this.clientInternal.on('ready', () => {
        logger.info('Redis Client Ready for use');
      });

      await this.clientInternal.connect();
      this.isConnected = true;
    } catch (err: any) {
      logger.error(err, 'Failed to create Redis client');
      this.isConnected = false;
      this.connectRedis();

      return null;
    }
  }

  /**
   * Sets a string value in Redis for a given key.
   * @param {string} key - The key to set
   * @param {string|boolean|object} value - The value to store
   * @param {number} [expires=0] - Expiration time in seconds
   * @param {number} [database=0] - Redis database number
   * @returns {Promise<string|null>} The Redis response or null if operation fails
   */
  public async setString(key: string, value: string | boolean | object, expires: number = 0, database: number = 0): Promise<string | null> {
    try {
      await this.clientInternal.select(database);
      const options: RedisSetOptions = { NX: false };
      if (expires) {
        options.EX = expires;
      }
      const response: string | null = await this.clientInternal.set(key, value, options);

      return response;
    } catch (err: any) {
      logger.error(err, 'Redis Error during Set String');

      return null;
    }
  }

  /**
   * Retrieves a string value from Redis for a given key.
   * @param {string} key - The key to retrieve
   * @param {number} [database=0] - Redis database number
   * @returns {Promise<string|null>} The stored value or null if not found
   */
  public async getString(key: string, database: number = 0): Promise<string | null> {
    try {
      await this.clientInternal.select(database);

      return await this.clientInternal.get(key);
    } catch (err: any) {
      logger.error(err, 'Redis Error during Get String');

      return null;
    }
  }

  /**
   * Deletes a key from Redis.
   * @param {string} key - The key to delete
   * @param {number} [database=0] - Redis database number
   * @returns {Promise<number>} Number of keys deleted
   */
  public async deleteKey(key: string, database: number = 0): Promise<number | null> {
    try {
      await this.clientInternal.select(database);

      return await this.clientInternal.del(key);
    } catch (err: any) {
      logger.error(err, 'Redis Error during Del String');

      return null;
    }
  }

  /**
   * Increments the value of a key in Redis.
   * @param {string} key - The key to increment
   * @param {number} [database=0] - Redis database number
   * @returns {Promise<number>} The new value after increment
   */
  public async incrementKey(key: string, expireTimeInSeconds: string | number, database: number = 0): Promise<number | null> {
    try {
      await this.clientInternal.select(database);
      const incrementedValue = await this.clientInternal.incr(key);
      if (incrementedValue) {
        await this.clientInternal.expire(key, expireTimeInSeconds);
      }

      return incrementedValue;
    } catch (err: any) {
      logger.error(err, 'Redis Error during Increment Key');

      return null;
    }
  }

  /**
   * Gets the time to live (TTL) of a key in Redis.
   * @param {string} key - The key to check
   * @param {number} [database=0] - Redis database number
   * @returns {Promise<number>} The TTL in seconds, -1 if key exists but has no TTL, -2 if key doesn't exist
   */
  public async getTTL(key: string): Promise<number> {
    try {
      const ttl = await this.clientInternal.ttl(key); // TTL in seconds

      return ttl;
    } catch (err) {
      logger.error(err, 'Error fetching TTL:');

      return CONFIG.REDIS.LOGIN_BLOCK_TIME;
    }
  }
}
export default new RedisHelper();
