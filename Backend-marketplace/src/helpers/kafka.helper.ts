/* eslint-disable no-shadow */
import { <PERSON><PERSON><PERSON>, Producer, Consumer, KafkaMessage, logLevel } from 'kafkajs';
import logger from './logging/logger.helper';
import CONFIG from '../config/env';

class KafkaHelper {
  private kafka: Kafka;

  private producer: Producer;

  private consumers: Map<string, Consumer> = new Map();

  constructor(clientId: string, brokers: string[] = [CONFIG.KAFKA.BROKERS]) {
    this.kafka = new Kafka({ clientId, brokers, logLevel: logLevel.NOTHING, retry: { retries: 5, initialRetryTime: 300, factor: 0.2 }, connectionTimeout: 10000 });
    this.producer = this.kafka.producer();
    this.connectProducer();
  }

  /**
   * Connect to Kafka broker.
   */
  async connectProducer(): Promise<void> {
    try {
      await this.producer.connect();
      logger.info('Kafka producer connected successfully.');
    } catch (error) {
      logger.error(error, 'Error connecting Kafka producer:');
    }
  }

  /**
   * Disconnect from Kafka broker.
   */
  async disconnectProducer(): Promise<void> {
    try {
      await this.producer.disconnect();
      logger.info('Kafka producer disconnected successfully.');
    } catch (error) {
      logger.error(error, 'Error disconnecting Kafka producer:');
    }
  }

  /**
   * Send a message to a Kafka topic.
   * @param topic - Kafka topic
   * @param messages - Array of messages to send
   */
  async sendMessage(topic: string, messages: KafkaMessage[] | any): Promise<void> {
    try {
      await this.producer.send({ topic, messages });
      logger.info(`Message sent successfully to topic ${topic}.`);
    } catch (error) {
      logger.error(error, `Error sending message to topic ${topic}:`);
    }
  }

  /**
   * Create a consumer and subscribe to a Kafka topic.
   * @param groupId - Kafka consumer group ID
   * @param topic - Kafka topic to subscribe to
   * @param eachMessageHandler - Callback to handle each received message
   */
  async createConsumer(groupId: string, topic: string, eachMessageHandler: (message: KafkaMessage, topic: string, partition: number, offset: string) => void): Promise<Consumer | null> {
    try {
      const consumer = this.kafka.consumer({ groupId });
      await consumer.connect();
      await consumer.subscribe({ topic, fromBeginning: true });

      await consumer.run({
        eachMessage: async ({ topic, partition, message }) => {
          eachMessageHandler(message, topic, partition, message.offset);
        },
      });

      this.consumers.set(groupId, consumer);
      logger.info(`Consumer created and subscribed to topic ${topic} with group ID ${groupId}.`);

      return consumer;
    } catch (error) {
      logger.error(error, `Error creating consumer for topic ${topic} with group ID ${groupId}:`);

      return null;
    }
  }

  /**
   * Disconnect a consumer from Kafka broker.
   * @param groupId - Kafka consumer group ID
   */
  async disconnectConsumer(groupId: string): Promise<void> {
    const consumer = this.consumers.get(groupId);
    if (consumer) {
      try {
        await consumer.disconnect();
        this.consumers.delete(groupId);
        logger.info(`Consumer with group ID ${groupId} disconnected successfully.`);
      } catch (error) {
        logger.error(error, `Error disconnecting consumer with group ID ${groupId}:`);
      }
    } else {
      logger.warn(`Consumer with group ID ${groupId} not found.`);
    }
  }
}

export const kafkaHelperService = new KafkaHelper('service-client', [CONFIG.KAFKA.BROKERS]);
