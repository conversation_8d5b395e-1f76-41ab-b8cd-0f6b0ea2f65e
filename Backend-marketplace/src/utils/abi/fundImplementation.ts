export default [
  {
    anonymous: false,
    inputs: [
      { indexed: false, internalType: 'address', name: 'investor', type: 'address' },
      { indexed: false, internalType: 'uint256', name: 'amount', type: 'uint256' },
      { indexed: false, internalType: 'uint256', name: 'taxAmount', type: 'uint256' },
      { indexed: false, internalType: 'string', name: '_userID', type: 'string' },
      { indexed: false, internalType: 'string', name: '_dividendID', type: 'string' },
    ],
    name: 'DividendDistributed',
    type: 'event',
  },
  { anonymous: false, inputs: [{ indexed: false, internalType: 'uint8', name: 'version', type: 'uint8' }], name: 'Initialized', type: 'event' },
  {
    anonymous: false,
    inputs: [
      { indexed: false, internalType: 'uint256', name: 'newMaximumInvestment', type: 'uint256' },
      { indexed: false, internalType: 'string', name: 'actionID', type: 'string' },
    ],
    name: 'MaximumInvestmentUpdated',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      { indexed: false, internalType: 'uint256', name: 'newMinimumInvestment', type: 'uint256' },
      { indexed: false, internalType: 'string', name: 'actionID', type: 'string' },
    ],
    name: 'MinimumInvestmentUpdated',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      { indexed: false, internalType: 'uint256', name: 'latestNAV', type: 'uint256' },
      { indexed: false, internalType: 'string', name: 'actionID', type: 'string' },
    ],
    name: 'NAVUpdated',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      { indexed: false, internalType: 'uint256', name: 'newProjectedYield', type: 'uint256' },
      { indexed: false, internalType: 'string', name: 'actionID', type: 'string' },
    ],
    name: 'ProjectedYieldUpdated',
    type: 'event',
  },
  { inputs: [], name: 'FEE_DENOMINATOR', outputs: [{ internalType: 'uint16', name: '', type: 'uint16' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'NAVLatestPrice', outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'NAVLaunchPrice', outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'cusip', outputs: [{ internalType: 'string', name: '', type: 'string' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'factory', outputs: [{ internalType: 'address', name: '', type: 'address' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'fundName', outputs: [{ internalType: 'string', name: '', type: 'string' }], stateMutability: 'view', type: 'function' },
  { inputs: [{ internalType: 'string', name: '_id', type: 'string' }], name: 'getDividendStatus', outputs: [{ internalType: 'bool', name: '', type: 'bool' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'getNAV', outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'getOffChainPrice', outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'getOffChainPriceStatus', outputs: [{ internalType: 'bool', name: '', type: 'bool' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'getToken', outputs: [{ internalType: 'address', name: '', type: 'address' }], stateMutability: 'view', type: 'function' },
  {
    inputs: [
      { internalType: 'address', name: '_token', type: 'address' },
      { internalType: 'bytes', name: '_data', type: 'bytes' },
    ],
    name: 'init',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  { inputs: [], name: 'maxInvestment', outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'minInvestment', outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'offChainPrice', outputs: [{ internalType: 'bool', name: '', type: 'bool' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'projectedYield', outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'propertyType', outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }], stateMutability: 'view', type: 'function' },
  { inputs: [{ internalType: 'uint256', name: '_newPrice', type: 'uint256' }], name: 'setAssetPriceOffChain', outputs: [], stateMutability: 'nonpayable', type: 'function' },
  {
    inputs: [
      { internalType: 'uint256', name: '_newMaxInvestment', type: 'uint256' },
      { internalType: 'string', name: 'actionID', type: 'string' },
    ],
    name: 'setMaxInvestment',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      { internalType: 'uint256', name: '_newMinInvestment', type: 'uint256' },
      { internalType: 'string', name: 'actionID', type: 'string' },
    ],
    name: 'setMinInvestment',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      { internalType: 'uint256', name: '_latestNAV', type: 'uint256' },
      { internalType: 'string', name: 'actionID', type: 'string' },
    ],
    name: 'setNAV',
    outputs: [{ internalType: 'bool', name: '', type: 'bool' }],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  { inputs: [{ internalType: 'bool', name: '_status', type: 'bool' }], name: 'setOffChainPrice', outputs: [], stateMutability: 'nonpayable', type: 'function' },
  {
    inputs: [
      { internalType: 'uint256', name: '_newProjectedYield', type: 'uint256' },
      { internalType: 'string', name: 'actionID', type: 'string' },
    ],
    name: 'setProjectedYield',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      { internalType: 'address', name: '_address', type: 'address' },
      { internalType: 'uint256', name: '_dividend', type: 'uint256' },
      { internalType: 'string', name: '_userIds', type: 'string' },
      { internalType: 'string', name: '_dividendIds', type: 'string' },
      { internalType: 'address', name: 'stableCoin_', type: 'address' },
      { internalType: 'address', name: '_agent', type: 'address' },
    ],
    name: 'shareDividend',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  { inputs: [], name: 'token', outputs: [{ internalType: 'address', name: '', type: 'address' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'tokenPrice', outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }], stateMutability: 'view', type: 'function' },
];
