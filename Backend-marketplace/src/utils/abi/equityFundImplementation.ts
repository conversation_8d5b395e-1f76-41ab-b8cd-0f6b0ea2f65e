export default [
  {
    anonymous: false,
    inputs: [
      { indexed: false, internalType: 'string', name: 'newDERatio', type: 'string' },
      { indexed: false, internalType: 'string', name: 'actionID', type: 'string' },
    ],
    name: 'DERatioUpdated',
    type: 'event',
  },
  { anonymous: false, inputs: [{ indexed: false, internalType: 'uint8', name: 'version', type: 'uint8' }], name: 'Initialized', type: 'event' },
  {
    anonymous: false,
    inputs: [
      { indexed: false, internalType: 'uint256', name: 'newMaximumInvestment', type: 'uint256' },
      { indexed: false, internalType: 'string', name: 'actionID', type: 'string' },
    ],
    name: 'MaximumInvestmentUpdated',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      { indexed: false, internalType: 'uint256', name: 'newMinimumInvestment', type: 'uint256' },
      { indexed: false, internalType: 'string', name: 'actionID', type: 'string' },
    ],
    name: 'MinimumInvestmentUpdated',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      { indexed: false, internalType: 'uint256', name: 'new<PERSON>ro<PERSON><PERSON><PERSON>', type: 'uint256' },
      { indexed: false, internalType: 'string', name: 'actionID', type: 'string' },
    ],
    name: 'ProjectedYieldUpdated',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      { indexed: false, internalType: 'uint256', name: 'newValuation', type: 'uint256' },
      { indexed: false, internalType: 'string', name: 'actionID', type: 'string' },
    ],
    name: 'ValuationUpdated',
    type: 'event',
  },
  { inputs: [], name: 'factory', outputs: [{ internalType: 'address', name: '', type: 'address' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'fundName', outputs: [{ internalType: 'string', name: '', type: 'string' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'getCurrentValuation', outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'getDERatio', outputs: [{ internalType: 'string', name: '', type: 'string' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'getLaunchValuation', outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'getMaxInvestment', outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'getMinInvestment', outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'getOffChainPrice', outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'getOffChainPriceStatus', outputs: [{ internalType: 'bool', name: '', type: 'bool' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'getPreviousValutaion', outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'getProjectedYield', outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }], stateMutability: 'view', type: 'function' },
  {
    inputs: [
      { internalType: 'address', name: '_token', type: 'address' },
      { internalType: 'bytes', name: '_data', type: 'bytes' },
    ],
    name: 'init',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  { inputs: [], name: 'offChainPrice', outputs: [{ internalType: 'bool', name: '', type: 'bool' }], stateMutability: 'view', type: 'function' },
  { inputs: [{ internalType: 'uint256', name: '_newPrice', type: 'uint256' }], name: 'setAssetPriceOffChain', outputs: [], stateMutability: 'nonpayable', type: 'function' },
  {
    inputs: [
      { internalType: 'string', name: '_newDERatio', type: 'string' },
      { internalType: 'string', name: 'actionID', type: 'string' },
    ],
    name: 'setDERatio',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      { internalType: 'uint256', name: '_newMaxInvestment', type: 'uint256' },
      { internalType: 'string', name: 'actionID', type: 'string' },
    ],
    name: 'setMaxInvesrment',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      { internalType: 'uint256', name: '_newMinInvestment', type: 'uint256' },
      { internalType: 'string', name: 'actionID', type: 'string' },
    ],
    name: 'setMinInvestment',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  { inputs: [{ internalType: 'bool', name: '_status', type: 'bool' }], name: 'setOffChainPrice', outputs: [], stateMutability: 'nonpayable', type: 'function' },
  {
    inputs: [
      { internalType: 'uint256', name: '_newProjectedYield', type: 'uint256' },
      { internalType: 'string', name: 'actionID', type: 'string' },
    ],
    name: 'setProjectedYeild',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      { internalType: 'uint256', name: '_latestValuation', type: 'uint256' },
      { internalType: 'string', name: 'actionID', type: 'string' },
    ],
    name: 'setValuation',
    outputs: [{ internalType: 'bool', name: '', type: 'bool' }],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  { inputs: [], name: 'token', outputs: [{ internalType: 'address', name: '', type: 'address' }], stateMutability: 'view', type: 'function' },
  { inputs: [], name: 'tokenPrice', outputs: [{ internalType: 'uint256', name: '', type: 'uint256' }], stateMutability: 'view', type: 'function' },
];
