export const swaggerDefinition: any = {
  openapi: '3.0.0',
  info: {
    title: 'Libertum Tokenization Platform API Documentation',
    description: `

## Overview
The Libertum Tokenization Platform is a comprehensive solution for tokenizing real-world assets on the blockchain. This platform enables:
- Asset tokenization and management
- Investor onboarding and KYC/AML compliance
- Token trading and transfers
- Dividend distribution
- NFT creation and management
- Document signing and verification

## Key Features
- **Asset Tokenization**: Convert real-world assets into digital tokens
- **Investor Management**: Complete KYC/AML process for investors
- **Trading**: Buy, sell, and transfer tokens
- **Dividends**: Distribute and manage dividends for token holders
- **NFTs**: Create and manage NFT collections
- **Security**: Two-factor authentication and secure document signing

## Authentication
All authenticated endpoints require a JWT token in the Authorization header:
\`Authorization: Bearer <token>\`

## Rate Limiting
API requests are rate-limited to ensure system stability. Please contact support for specific limits.

## Support
For technical support or questions, please contact:
- Email: <EMAIL>
- Documentation: https://docs.libertum.com
    `,
    version: '1.0.0',
    contact: {
      name: 'Libertum Support',
      email: '<EMAIL>',
      url: 'https://libertum.com',
    },
    license: {
      name: 'Proprietary',
      url: 'https://libertum.com/license',
    },
  },
  servers: [
    {
      url: 'https://api.libertum.com/marketplace/v1',
      description: 'Production server',
    },
    {
      url: 'https://stage-api.libertum.com/marketplace/v1',
      description: 'Staging server',
    },
    {
      url: 'https://qa-api.libertum.com/marketplace/v1',
      description: 'qa server',
    },
    {
      url: 'https://sandbox-api.libertum.com/marketplace/v1',
      description: 'sandbox server',
    },
  ],
  paths: {
    '/health-check': {
      get: {
        summary: 'Check API Health Status',
        tags: ['Health'],
        description: `
Checks the health and operational status of the API service.

### Purpose
- Verify if the API service is running and responding
- Check database connectivity
- Monitor system status

### Response Details
- Status message indicating service health
- Timestamp of the health check
- Service status code
`,
        responses: {
          200: {
            description: 'API is healthy',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string', example: 'User service is up and running' },
                    status: { type: 'integer', example: 200 },
                    data: { type: 'object', properties: { timestamp: { type: 'string', example: '2024-09-11T10:26:22.270Z' } }, required: ['timestamp'] },
                    error: { type: 'boolean', example: false },
                  },
                },
              },
            },
          },
          500: { description: 'Internal Server Error - Service is not healthy' },
        },
      },
    },
    '/marketplace/v1/signup': {
      post: {
        summary: 'User Registration',
        tags: ['Auth'],
        description: `
Register a new user in the Libertum platform.

### User Types
- Investor: Individual or institutional investors
- Institution: Companies or organizations offering assets

### Features
- Email verification
- Mobile number validation
- Password security requirements
- Two-factor authentication setup
- KYC/AML integration

### Required Fields
- Name
- Email
- Password (must meet security requirements)
- Country Code
- Mobile Number
- User Type
- OTP Method Selection

### Process Flow
1. Submit registration details
2. Receive verification email/OTP
3. Verify email/phone
4. Complete KYC/AML process
5. Access platform features
`,
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/SignupRequest' },
              examples: {
                'Investor Example ': {
                  summary: 'Investor Registration Example',
                  value: {
                    name: 'Amit Saroj',
                    email: '<EMAIL>',
                    password: 'Admin@123',
                    countryCode: '+91',
                    mobile: '8707831230',
                    userType: 'investor',
                    otpMethods: 'Email',
                  },
                },
                'institution Example ': {
                  summary: 'Institution Registration Example',
                  value: {
                    name: 'Amit Saroj',
                    email: '<EMAIL>',
                    password: 'Admin@123',
                    countryCode: '+91',
                    mobile: '8707831230',
                    userType: 'institution',
                    legalFullName: 'libertum',
                    otpMethods: 'Email',
                  },
                },
              },
            },
          },
        },
        responses: { 201: { $ref: '#/components/responses/CreatedResponse' }, 404: { $ref: '#/components/responses/NotFoundResponse' } },
      },
    },
    '/marketplace/v1/login': {
      post: {
        summary: 'User Authentication',
        tags: ['Auth'],
        description: `
Authenticate users and provide access tokens for the platform.

### Authentication Methods
- Email & Password
- Two-Factor Authentication (if enabled)
- Social Login Integration

### Security Features
- Rate limiting for failed attempts
- Account locking after multiple failures
- Session management
- IP-based security checks

### Process Flow
1. Submit credentials
2. Validate user status
3. Check 2FA requirement
4. Generate access token
5. Return session details

### Response Includes
- Authentication status
- Access token (if successful)
- 2FA requirement status
- User profile summary
`,
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/LoginRequest' },
            },
          },
        },
        responses: {
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
          200: { $ref: '#/components/responses/LoginSuccessResponse' },
          '200-without-login-otp': { $ref: '#/components/responses/LoginSuccessWithoutOtpResponse' },
        },
      },
    },
    '/marketplace/v1/verify': {
      post: {
        summary: 'Verify User Account',
        tags: ['Verification'],
        description: `
Verify user account through OTP validation for various processes.

### Verification Types
- Sign-up verification
- Login verification (2FA)
- Password reset verification
- Email change verification

### OTP Methods
- Email OTP
- SMS OTP
- Authenticator app

### Features
- Multiple OTP attempts tracking
- OTP expiration management
- Rate limiting for security
- Verification status tracking

### Process Flow
1. Receive OTP
2. Validate OTP format
3. Check expiration
4. Verify against stored OTP
5. Update verification status
`,
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/VerifyRequest' },
              examples: {
                'sing-up': {
                  summary: 'Sign Up Verification',
                  value: {
                    otp: '310712',
                    email: '<EMAIL>',
                    type: 'sign_up',
                    otpMethods: 'Email',
                  },
                },
                login: {
                  summary: 'Login Verification',
                  value: {
                    otp: '310712',
                    email: '<EMAIL>',
                    type: 'login',
                    otpMethods: 'Email',
                  },
                },
                forgot: {
                  summary: 'Password Reset Verification',
                  value: {
                    otp: '310712',
                    email: '<EMAIL>',
                    type: 'forgot',
                    otpMethods: 'Email',
                  },
                },
              },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/VerifySuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
        },
      },
    },
    '/marketplace/v1/social-login': {
      post: {
        summary: 'Social Media Authentication',
        tags: ['Auth'],
        description: `
Authenticate users through various social media platforms.

### Supported Platforms
- Google
- Facebook
- LinkedIn
- Twitter

### Features
- Single Sign-On (SSO)
- Profile data import
- Account linking
- OAuth2 integration

### Process Flow
1. Receive social platform token
2. Validate token authenticity
3. Fetch user profile
4. Create/Update user account
5. Generate platform access token

### Security Measures
- Token validation
- Platform verification
- Scope management
- Data privacy compliance
`,
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/SocialLoginRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/LoginSuccessWithoutOtpResponse' },
        },
      },
    },
    '/marketplace/v1/resend-otp': {
      post: {
        summary: 'Resend Verification OTP',
        tags: ['Auth'],
        description: `
Resend verification OTP for various authentication processes.

### Use Cases
- Failed OTP delivery
- Expired OTP
- User request for new OTP

### Features
- Cooldown period management
- Rate limiting
- Multiple delivery methods
- OTP expiration handling

### Process Flow
1. Validate resend eligibility
2. Generate new OTP
3. Send via selected method
4. Update OTP records
5. Return status

### Security Measures
- Maximum resend attempts
- Time-based restrictions
- Fraud prevention
`,
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/ResendOtpRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
        },
      },
    },
    '/marketplace/v1/forgot-password': {
      post: {
        summary: 'Initiate Password Reset',
        tags: ['Auth'],
        description: `
Initiate the password reset process for users who forgot their password.

### Features
- Multiple verification methods
- Secure reset link generation
- Rate limiting protection
- Account verification

### Process Flow
1. Submit email/phone
2. Verify account existence
3. Generate reset token
4. Send reset instructions
5. Track reset request

### Security Measures
- Token expiration
- One-time use tokens
- IP tracking
- Account protection
`,
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/ForgotPasswordRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
        },
      },
    },
    '/marketplace/v1/reset-password': {
      post: {
        summary: 'Complete Password Reset',
        tags: ['Auth'],
        description: `
Complete the password reset process with a new password.

### Features
- Password strength validation
- Token verification
- Password history check
- Account security update

### Process Flow
1. Validate reset token
2. Check password requirements
3. Update password
4. Invalidate token
5. Notify user

### Security Requirements
- Minimum password length
- Special characters
- Number requirements
- Case sensitivity
`,
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/ResetPasswordRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
        },
      },
    },
    '/marketplace/v1/auth/log-out': {
      get: {
        summary: 'User Logout',
        tags: ['Auth'],
        description: `
Securely log out user from the platform.

### Features
- Session termination
- Token invalidation
- Activity logging
- Multi-device handling

### Process Flow
1. Validate session
2. Revoke access token
3. Clear session data
4. Log logout event
5. Return confirmation

### Security Measures
- Token blacklisting
- Session cleanup
- Device tracking
- Audit logging
`,
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
        },
        security: [{ bearerAuth: [] }],
      },
    },
    '/marketplace/v1/auth/enable-2fa': {
      get: {
        summary: 'Enable Two-Factor Authentication',
        tags: ['2FA'],
        description: `
Enable two-factor authentication for enhanced account security.

### Features
- QR code generation
- Backup codes provision
- Multiple 2FA methods
- Device management

### Supported Methods
- Authenticator apps (Google, Microsoft)
- SMS verification
- Email verification
- Hardware tokens

### Process Flow
1. Generate secret key
2. Create QR code
3. Verify setup
4. Enable 2FA
5. Provide backup codes

### Security Measures
- Secure key generation
- Time-based tokens
- Backup recovery options
- Device verification
`,
        responses: {
          200: { $ref: '#/components/responses/Enable2FAResponse' },
        },
        security: [{ bearerAuth: [] }],
      },
    },
    '/marketplace/v1/auth/verify-2fa': {
      post: {
        summary: 'Verify Two-Factor Authentication',
        tags: ['2FA'],
        description: `
Verify the two-factor authentication token during setup or login.

### Features
- Token validation
- Time-window checking
- Attempt tracking
- Backup code support

### Verification Types
- Initial setup verification
- Login verification
- Device authorization
- Recovery verification

### Process Flow
1. Receive 2FA token
2. Validate format
3. Check time window
4. Verify against secret
5. Grant access

### Security Measures
- Time-based validation
- Rate limiting
- Attempt logging
- Device tracking
`,
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/Verify2FARequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
        },
        security: [{ bearerAuth: [] }],
      },
    },
    '/marketplace/v1/verify-2fa': {
      post: {
        summary: 'Verify Login Two-Factor Authentication',
        tags: ['2FA'],
        description: `
Verify two-factor authentication during the login process.

### Features
- Login-specific 2FA validation
- Session management
- Remember device option
- Backup code support

### Process Flow
1. Submit login credentials
2. Request 2FA token
3. Validate token
4. Create session
5. Return access token

### Security Features
- Time-based tokens
- Device fingerprinting
- Attempt limiting
- Session tracking
`,
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/VerifyLogin2FARequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
        },
        security: [{ bearerAuth: [] }],
      },
    },
    '/marketplace/v1/auth/change-password': {
      patch: {
        summary: 'Change User Password',
        tags: ['Auth'],
        description: `
Change the user's password with current password verification.

### Features
- Current password verification
- Password strength validation
- History checking
- Security notifications

### Requirements
- Current password
- New password
- Password confirmation
- Security verification

### Process Flow
1. Verify current password
2. Validate new password
3. Check password history
4. Update credentials
5. Notify user

### Security Measures
- Password strength rules
- History prevention
- Activity logging
- Session management
`,
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/ChangePasswordRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
        security: [{ bearerAuth: [] }],
      },
    },
    '/marketplace/v1/auth/become': {
      patch: {
        summary: 'Upgrade to Issuer Role',
        description: `
Upgrade a regular user account to an issuer role with additional privileges.

### Features
- Role upgrade verification
- KYB requirements
- Document validation
- Compliance checks

### Requirements
- Business verification
- KYB documentation
- Compliance approval
- Legal agreements

### Process Flow
1. Submit upgrade request
2. Verify eligibility
3. Process documentation
4. Approve upgrade
5. Grant permissions

### Compliance
- KYB verification
- AML checks
- Legal requirements
- Regulatory compliance
`,
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/emptyRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
        security: [{ bearerAuth: [] }],
      },
    },
    '/marketplace/v1/auth/disable-otp': {
      post: {
        summary: 'Disable OTP Authentication',
        description: `
Disable One-Time Password (OTP) authentication for the user account.

### Features
- OTP method management
- Security verification
- Fallback authentication
- Account protection

### Process Flow
1. Verify user identity
2. Confirm disable request
3. Remove OTP settings
4. Update security status
5. Notify user

### Security Measures
- Identity verification
- Security questions
- Activity logging
- Risk assessment
`,
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/emptyRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
        security: [{ bearerAuth: [] }],
      },
    },
    '/marketplace/v1/auth/send-verification-otp': {
      post: {
        summary: 'Send Verification OTP',
        description: `
Send a verification OTP for various authentication processes.

### Features
- Multiple delivery methods
- Rate limiting
- Template customization
- Delivery tracking

### Delivery Methods
- Email
- SMS
- Voice call
- Authenticator app

### Process Flow
1. Validate request
2. Generate OTP
3. Select delivery method
4. Send OTP
5. Track delivery

### Security Features
- Rate limiting
- Expiration time
- Attempt tracking
- IP validation
`,
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/emptyRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
        security: [{ bearerAuth: [] }],
      },
    },
    '/marketplace/v1/forgot-2fa': {
      patch: {
        summary: 'Reset Two-Factor Authentication',
        tags: ['2FA'],
        description: `
Reset two-factor authentication settings when user loses access.

### Features
- Identity verification
- New 2FA setup
- Backup code validation
- Security notifications

### Process Flow
1. Verify user identity
2. Validate backup codes
3. Reset 2FA settings
4. Generate new setup
5. Update security status

### Security Measures
- Identity confirmation
- Backup verification
- Activity logging
- Risk assessment
`,
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/Forgot2FARequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/Reset2FAResponse' },
        },
        security: [{ bearerAuth: [] }],
      },
    },
    '/marketplace/v1/auth/disable-2fa': {
      patch: {
        summary: 'Disable Two-Factor Authentication',
        tags: ['2FA'],
        description: `
Disable two-factor authentication for the user account.

### Features
- Security verification
- Backup code removal
- Session management
- Security notifications

### Process Flow
1. Verify current 2FA
2. Confirm disable request
3. Remove 2FA settings
4. Update security status
5. Notify user

### Security Measures
- Identity verification
- Session validation
- Activity logging
- Risk assessment
`,
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/Disable2FARequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
        },
        security: [{ bearerAuth: [] }],
      },
    },
    '/marketplace/v1/auth/profile': {
      put: {
        summary: 'Update User Profile',
        description: `
Update user profile information and settings.

### Features
- Profile information update
- Settings management
- Preferences configuration
- Profile verification

### Updatable Fields
- Personal information
- Contact details
- Preferences
- Notification settings

### Process Flow
1. Validate updates
2. Verify changes
3. Apply updates
4. Update timestamp
5. Return status

### Validation
- Data format check
- Required fields
- Field constraints
- Security validation
`,
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/UpdateProfileRequest' },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
        security: [{ bearerAuth: [] }],
      },
      get: {
        summary: 'Get User Profile',
        description: `
Retrieve user profile information and settings.

### Retrieved Data
- Personal information
- Account settings
- Security status
- Verification levels

### Features
- Complete profile view
- Security settings
- Account status
- KYC/AML status

### Response Details
- User information
- Account settings
- Security status
- Verification status

### Access Control
- Authentication required
- Role-based access
- Data privacy rules
- Security checks
`,
        responses: {
          200: { $ref: '#/components/responses/GetProfileResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
        security: [{ bearerAuth: [] }],
      },
    },
    '/marketplace/v1/auth/portfolio': {
      get: {
        summary: 'Get User Portfolio',
        description: `
Retrieve user's investment portfolio details.

### Portfolio Details
- Asset holdings
- Investment history
- Performance metrics
- Transaction records

### Features
- Portfolio valuation
- Asset breakdown
- Performance analytics
- Transaction history

### Response Includes
- Total value
- Asset distribution
- Historical performance
- Recent transactions

### Data Organization
- Paginated results
- Sortable data
- Filterable records
- Detailed metrics
`,
        parameters: [
          {
            name: 'page',
            in: 'query',
            required: false,
            description: 'Page number for pagination',
            schema: { type: 'integer', example: 1 },
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            description: 'Number of offerings to retrieve per page',
            schema: { type: 'integer', example: 10 },
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
        security: [{ bearerAuth: [] }],
      },
    },
    '/marketplace/v1/auth/kyc': {
      post: {
        summary: 'Submit KYC Data',
        tags: ['KYC'],
        description: 'This is a POST request, submitting data to an API via the request body.',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                oneOf: [
                  { $ref: '#/components/schemas/institutionKYBStep1Request' },
                  { $ref: '#/components/schemas/institutionKYBStep2Request' },
                  { $ref: '#/components/schemas/institutionKYBStep3Request' },
                  { $ref: '#/components/schemas/institutionKYBStep4Request' },
                  { $ref: '#/components/schemas/institutionKYBStep5Request' },
                  { $ref: '#/components/schemas/institutionKYBStep6Request' },
                  { $ref: '#/components/schemas/institutionKYBStep7Request' },
                  { $ref: '#/components/schemas/institutionKYBStep8Request' },
                  { $ref: '#/components/schemas/investerKycStep1Request' },
                  { $ref: '#/components/schemas/investerKycStep2Request' },
                  { $ref: '#/components/schemas/investerKycStep3Request' },
                  { $ref: '#/components/schemas/investerKycStep4Request' },
                  { $ref: '#/components/schemas/investerKycStep5Request' },
                ],
              },
              examples: {
                institutionStep1: {
                  summary: 'Institution KYB Step 1 Data',
                  value: {
                    institutions: {
                      companyInformation: { name: 'Tech Corp', entityType: 'LLC', webSite: 'https://www.techcorp.com', business: 'Technology', sourceOfFunds: 'Sales Revenue' },
                      address: { address: '123 Tech Street', address2: 'Suite 456', country: 'USA', state: 'California', city: 'San Francisco', zipCode: '94107' },
                    },
                    kycSteps: 1,
                  },
                },
                institutionStep2: {
                  summary: 'Institution KYB Step 2 Data',
                  value: {
                    primaryContactInfo: {
                      personalInformation: { name: 'John Doe', jobTitle: 'CEO', dob: '1985-05-15', socialSecurityNumber: '*********', citizenship: 'USA', countryCode: '+91', mobile: '9876543210', email: '<EMAIL>' },
                      address: { address: '123 Tech Street', address2: 'Suite 456', country: 'USA', state: 'California', city: 'San Francisco', zipCode: '94107' },
                    },
                    kycSteps: 2,
                  },
                },
                institutionStep3: { summary: 'Institution KYB Step 3 Data', value: { wallets: [{ type: 'WalletConnect', address: '******************************************' }], kycSteps: 3 } },
                institutionStep4: {
                  summary: 'Institution KYB Step 4 Data',
                  value: { documents: { frontId: 'https://example.com/front-id.jpg', backId: 'https://example.com/back-id.jpg', otherIdentification: 'https://example.com/company-id.jpg' }, kycSteps: 4 },
                },
                institutionStep5: { summary: 'Institution KYB Step 5 Data', value: { isIdentityVerification: { status: false }, kycSteps: 5 } },
                institutionStep6: {
                  summary: 'Institution KYB Step 6 Data',
                  value: {
                    beneficialOwners: [
                      {
                        personalInformation: { name: 'Jane Smith', dob: '1980-02-20', socialSecurityNumber: 'S987654321', citizenship: 'USA' },
                        address: { address: '456 Main Street', address2: 'ccccccccccc', country: 'USA', state: 'New York', city: 'New York', zipCode: '10001' },
                        identityProof: {
                          passport: { front: 'https://example.com/passport-front.jpg', back: 'https://example.com/passport-back.jpg' },
                          driversLicense: { front: 'https://example.com/drivers-license-front.jpg', back: 'https://example.com/drivers-license-back.jpg' },
                          idCard: { front: 'https://example.com/id-card-front.jpg', back: 'https://example.com/id-card-back.jpg' },
                        },
                      },
                    ],
                    kycSteps: 6,
                  },
                },
                institutionStep7: {
                  summary: 'Institution KYB Step 7 Data',
                  value: {
                    managementInfo: [
                      {
                        personalInformation: { name: 'Alice Johnson', jobTitle: 'CFO', dob: '1975-12-05', socialSecurityNumber: '*********', citizenship: 'USA', countryCode: '+91', mobile: '**********', email: '<EMAIL>' },
                        address: { address: '789 Corporate Blvd', address2: 'Floor 10', country: 'USA', state: 'California', city: 'San Francisco', zipCode: '94105' },
                      },
                    ],
                    kycSteps: 7,
                  },
                },
                institutionStep8: {
                  summary: 'Institution KYB Final Review Data',
                  value: {
                    institutions: {
                      companyInformation: { name: 'Tech Corp', entityType: 'LLC', webSite: 'https://www.techcorp.com', business: 'Technology', sourceOfFunds: 'Sales Revenue' },
                      address: { address: '123 Tech Street', address2: 'Suite 456', country: 'USA', state: 'California', city: 'San Francisco', zipCode: '94107' },
                    },
                    primaryContactInfo: {
                      personalInformation: { name: 'John Doe', jobTitle: 'CEO', dob: '1985-05-15', socialSecurityNumber: 'SA*********', citizenship: 'USA', countryCode: '+91', mobile: '**********', email: '<EMAIL>' },
                      address: { address: '123 Tech Street', address2: 'Suite 456', country: 'USA', state: 'California', city: 'San Francisco', zipCode: '94107' },
                    },
                    wallets: [{ type: 'MetamaskWallet', address: '******************************************' }],
                    isIdentityVerification: { status: false },
                    documents: { frontId: 'https://example.com/front-id.jpg', backId: 'https://example.com/back-id.jpg', otherIdentification: 'https://example.com/company-id.jpg' },
                    beneficialOwners: [
                      {
                        personalInformation: { name: 'Jane Smith', dob: '1980-02-20', socialSecurityNumber: 'AS987654321', citizenship: 'USA' },
                        address: { address: '456 Main Street', address2: 'USA', country: 'USA', state: 'New York', city: 'New York', zipCode: '10001' },
                        identityProof: {
                          passport: { front: 'https://example.com/passport-front.jpg', back: 'https://example.com/passport-back.jpg' },
                          driversLicense: { front: 'https://example.com/drivers-license-front.jpg', back: 'https://example.com/drivers-license-back.jpg' },
                          idCard: { front: 'https://example.com/id-card-front.jpg', back: 'https://example.com/id-card-back.jpg' },
                        },
                      },
                    ],
                    managementInfo: [
                      {
                        personalInformation: { name: 'Alice Johnson', jobTitle: 'CFO', dob: '1975-12-05', socialSecurityNumber: 'AS*********', citizenship: 'USA', countryCode: '+91', mobile: '**********', email: '<EMAIL>' },
                        address: { address: '789 Corporate Blvd', address2: 'Floor 10', country: 'USA', state: 'California', city: 'San Francisco', zipCode: '94105' },
                      },
                    ],
                    kycSteps: 8,
                    isFinalSubmission: true,
                  },
                },
                investerStep1: {
                  summary: 'Investor KYC Step 1 Data',
                  value: {
                    mainInformation: { birthPlace: 'New York', nationality: 'American', nationalIdNumber: '*********', identificationDocument: 'Passport', occupation: 'Software Developer', documentExpiration: '05/12/2041', dob: '05/12/2011' },
                    kycSteps: 1,
                  },
                },
                investerStep2: { summary: 'Investor KYC Step 2 Data', value: { wallets: [{ type: 'WalletConnect', address: '0x*********0abcdef*********0abcdef12345678' }], kycSteps: 2 } },
                investerStep3: {
                  summary: 'Investor KYC Step 3 Data',
                  value: {
                    documents: {
                      frontId: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR-hNzpwUDWsAISt0-eMKK1NLBpNu3evdNWcg&s',
                      backId: 'https://upload.wikimedia.org/wikipedia/commons/c/c5/Italian_electronic_ID_card_%28back%29.png',
                      otherIdentification: 'https://upload.wikimedia.org/wikipedia/commons/e/eb/Italian_electronic_ID_card_%28front-back%29.png',
                    },
                    kycSteps: 3,
                  },
                },
                investerStep4: { summary: 'Investor KYC Step 4 Data', value: { isIdentityVerification: { status: false }, kycSteps: 4 } },
                investerStep5: {
                  summary: 'Investor KYC Step 5 Data',
                  value: {
                    mainInformation: { birthPlace: 'New York', nationality: 'American', nationalIdNumber: '*********', identificationDocument: 'Passport', occupation: 'Software Developer', documentExpiration: '05/12/2040', dob: '05/12/2011' },
                    wallets: [{ type: 'MetamaskWallet', address: '0x*********0abcdef*********0abcdef12345678' }],
                    documents: {
                      frontId: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR-hNzpwUDWsAISt0-eMKK1NLBpNu3evdNWcg&s',
                      backId: 'https://upload.wikimedia.org/wikipedia/commons/c/c5/Italian_electronic_ID_card_%28back%29.png',
                      otherIdentification: 'https://upload.wikimedia.org/wikipedia/commons/e/eb/Italian_electronic_ID_card_%28front-back%29.png',
                    },
                    isIdentityVerification: { status: false },
                    kycSteps: 5,
                    isFinalSubmission: true,
                  },
                },
              },
            },
          },
        },
        responses: { 200: { $ref: '#/components/responses/kycResponse' } },
        security: [{ bearerAuth: [] }],
      },
    },
    '/marketplace/v1/auth/upload-docs': {
      post: {
        tags: ['DOCS UPLOAD'],
        summary: 'Upload Documents',
        description: 'This is a POST request, to upload user documents.',
        requestBody: { required: true, content: { 'multipart/form-data': { schema: { $ref: '#/components/schemas/UploadDocsRequest' } } } },
        responses: { 200: { $ref: '#/components/responses/UploadDocsResponse' } },
        security: [{ bearerAuth: [] }],
      },
    },
    '/marketplace/v1/auth/offering/order/{offeringId}': {
      get: {
        summary: 'Get orders by offering ID',
        description: 'Retrieve a list of orders for a specific offering ID with optional pagination.',
        tags: ['Offering'],
        parameters: [
          { in: 'path', name: 'offeringId', required: true, schema: { type: 'string' }, description: 'The unique ID of the offering.' },
          { in: 'query', name: 'page', required: false, schema: { type: 'integer', default: 1 }, description: 'Page number for pagination.' },
          { in: 'query', name: 'status', required: false, schema: { type: 'string', default: 'PENDING' }, description: 'Status' },
          { in: 'query', name: 'search', required: false, schema: { type: 'string', default: 'sss' }, description: 'search' },
          { in: 'query', name: 'limit', required: false, schema: { type: 'integer', default: 10 }, description: 'Number of records per page.' },
        ],
        responses: {
          200: {
            description: 'A list of orders.',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    orders: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          name: { type: 'string', description: 'Name of the user who placed the order.' },
                          email: { type: 'string', description: 'Email of the user who placed the order.' },
                          amount: { type: 'number', description: 'Amount of the order.' },
                          createdAt: { type: 'string', format: 'date-time', description: 'Order creation date.' },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          400: { description: 'Invalid input or validation error.', content: { 'application/json': { schema: { type: 'object', properties: { error: { type: 'boolean' }, message: { type: 'string', description: 'Error message.' } } } } } },
          500: { description: 'Internal server error.', content: { 'application/json': { schema: { type: 'object', properties: { error: { type: 'boolean' }, message: { type: 'string', description: 'Error message.' } } } } } },
        },
      },
    },
    '/marketplace/v1/auth/offering/whitelist_status': {
      put: {
        summary: 'Update Whitelist Status',
        description: `
Update the whitelist status for an offering participant.

### Features
- Status management
- Whitelist tracking
- Participant verification
- Access control

### Status Options
- Pending
- Approved
- Rejected
- Waitlisted

### Process Flow
1. Verify request
2. Check eligibility
3. Update status
4. Notify participant
5. Log changes

### Security Measures
- Role verification
- Status validation
- Audit logging
- Access control
`,
        tags: ['Offering'],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  whitelistId: {
                    type: 'string',
                    example: '60b8d6a9b8478e001c5d4435',
                    description: 'A valid MongoDB ObjectId for the offering',
                    minLength: 24,
                    maxLength: 24,
                  },
                  status: {
                    type: 'string',
                    example: 'REJECTED',
                    description: 'New whitelist status',
                  },
                },
                required: ['whitelistId', 'status'],
              },
            },
          },
        },
        responses: {
          200: {
            description: 'Successfully updated whitelist status',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    status: { type: 'string', example: 'SUCCESS' },
                    error: { type: 'boolean', example: false },
                    message: { type: 'string', example: 'Status successfully updated.' },
                  },
                },
              },
            },
          },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
        security: [{ bearerAuth: [] }],
      },
    },
    '/marketplace/v1/auth/docusign/generate-template-id': {
      post: {
        tags: ['Docusign'],
        summary: 'Generate Document Template',
        description: `
Generate a DocuSign template ID for document signing.

### Features
- Template generation
- Document preparation
- Field mapping
- Version control

### Template Types
- Investment agreements
- KYC documents
- Legal contracts
- Disclosure forms

### Process Flow
1. Validate offering
2. Select template
3. Generate ID
4. Map fields
5. Return details

### Security Features
- Template validation
- Access control
- Version tracking
- Audit logging
`,
        operationId: 'generate-template-id',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  offeringId: {
                    type: 'string',
                    example: '60b8d6a9b8478e001c5d4435',
                    description: 'A valid MongoDB ObjectId for the offering',
                    minLength: 24,
                    maxLength: 24,
                  },
                },
                required: ['offeringId'],
              },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
        security: [{ bearerAuth: [] }],
      },
    },
    '/marketplace/v1/auth/docusign/generate-embedded-signing-url': {
      post: {
        tags: ['Docusign'],
        summary: 'Generate Embedded Signing URL',
        description: `
Generate a DocuSign embedded signing URL for document signing.

### Features
- Embedded signing experience
- URL generation
- Session management
- Signing workflow

### Process Flow
1. Validate offering
2. Prepare documents
3. Generate URL
4. Configure signing
5. Return URL

### Security Features
- URL expiration
- Session validation
- Access control
- Audit logging

### Response Details
- Signing URL
- Session details
- Expiration time
- Redirect URLs
`,
        operationId: 'generate-embedded-signing-url',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  offeringId: {
                    type: 'string',
                    example: '60b8d6a9b8478e001c5d4435',
                    description: 'A valid MongoDB ObjectId for the offering',
                    minLength: 24,
                    maxLength: 24,
                  },
                },
                required: ['offeringId'],
              },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
        security: [{ bearerAuth: [] }],
      },
    },

    '/marketplace/v1/auth/docusign/webhook_event': {
      post: {
        tags: ['Docusign'],
        summary: 'Handle DocuSign Webhook Events',
        description: `
Process DocuSign webhook events for document signing status updates.

### Event Types
- Envelope sent
- Envelope signed
- Envelope completed
- Signing declined

### Features
- Event processing
- Status updates
- Notification handling
- Error management

### Process Flow
1. Receive webhook
2. Validate event
3. Process update
4. Update status
5. Send notifications

### Security Measures
- Event validation
- IP whitelisting
- Signature verification
- Rate limiting
`,
        operationId: 'handle-docusign-webhook',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  event: { type: 'string' },
                  data: { type: 'object' },
                },
                required: ['event', 'data'],
              },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          500: { $ref: '#/components/responses/ServerErrorResponse' },
        },
      },
    },
    '/marketplace/v1/auth/transfer': {
      post: {
        summary: 'Create Transfer Request',
        tags: ['Transfer'],
        description: 'Create a new token transfer request',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                required: ['offeringId', 'amount', 'toAddress'],
                properties: {
                  offeringId: {
                    type: 'string',
                    description: 'ID of the offering to transfer tokens from',
                    example: '60b8d6a9b8478e001c5d4435',
                  },
                  amount: {
                    type: 'number',
                    description: 'Amount of tokens to transfer',
                    minimum: 0,
                    example: 100.5,
                  },
                  toAddress: {
                    type: 'string',
                    description: 'Recipient wallet address',
                    pattern: '^0x[a-fA-F0-9]{40}$',
                    example: '******************************************',
                  },
                  memo: {
                    type: 'string',
                    description: 'Optional memo for the transfer',
                    example: 'Payment for services',
                  },
                },
              },
              examples: {
                'Basic Transfer': {
                  value: {
                    offeringId: '60b8d6a9b8478e001c5d4435',
                    amount: 100.5,
                    toAddress: '******************************************',
                  },
                },
                'Transfer with Memo': {
                  value: {
                    offeringId: '60b8d6a9b8478e001c5d4435',
                    amount: 100.5,
                    toAddress: '******************************************',
                    memo: 'Payment for services',
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: 'Transfer request created successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: {
                      type: 'string',
                      example: 'Transfer request created successfully',
                    },
                    status: {
                      type: 'integer',
                      example: 200,
                    },
                    data: {
                      type: 'object',
                      properties: {
                        transferId: {
                          type: 'string',
                          example: '60b8d6a9b8478e001c5d4436',
                        },
                        transactionHash: {
                          type: 'string',
                          example: '0x123...abc',
                        },
                        status: {
                          totalAmount: {
                            type: 'number',
                            example: 10000.0,
                          },
                          perTokenAmount: {
                            type: 'number',
                            example: 0.5,
                          },
                          totalTokenHolders: {
                            type: 'integer',
                            example: 50,
                          },
                          distributionBreakdown: {
                            type: 'array',
                            items: {
                              type: 'object',
                              properties: {
                                holderAddress: {
                                  type: 'string',
                                  example: '******************************************',
                                },
                                tokenBalance: {
                                  type: 'number',
                                  example: 1000,
                                },
                                dividendAmount: {
                                  type: 'number',
                                  example: 500.0,
                                },
                              },
                            },
                          },
                        },
                      },
                      error: {
                        type: 'boolean',
                        example: false,
                      },
                    },
                  },
                },
              },
            },
            400: { $ref: '#/components/responses/BadRequestResponse' },
            401: { $ref: '#/components/responses/UnauthorizedResponse' },
          },
        },
      },
    },
    '/marketplace/v1/auth/notification/seen': {
      post: {
        summary: 'Mark Notification as Seen',
        tags: ['Notification'],
        description: 'Mark a notification as seen by the user',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                required: ['notificationId'],
                properties: {
                  notificationId: {
                    type: 'string',
                    description: 'ID of the notification',
                    example: '60b8d6a9b8478e001c5d4435',
                  },
                },
              },
              examples: {
                'Single Notification': {
                  value: {
                    notificationId: '60b8d6a9b8478e001c5d4435',
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: 'Notification marked as seen successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: {
                      type: 'string',
                      example: 'Notification marked as seen successfully',
                    },
                    status: {
                      type: 'integer',
                      example: 200,
                    },
                    data: {
                      type: 'object',
                      properties: {
                        notificationId: {
                          type: 'string',
                          example: '60b8d6a9b8478e001c5d4435',
                        },
                        isSeen: {
                          type: 'boolean',
                          example: true,
                        },
                      },
                    },
                    error: {
                      type: 'boolean',
                      example: false,
                    },
                  },
                },
              },
            },
          },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
        },
      },
    },
    '/marketplace/v1/auth/offering/request-for-Offering': {
      post: {
        summary: 'Request for Offering',
        tags: ['Offering'],
        description: 'Request access to an offering',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                required: ['offeringId'],
                properties: {
                  offeringId: {
                    type: 'string',
                    description: 'ID of the offering',
                    example: '60b8d6a9b8478e001c5d4435',
                  },
                  message: {
                    type: 'string',
                    description: 'Optional message with the request',
                    example: 'Interested in participating in this offering',
                  },
                },
              },
              examples: {
                'Basic Request': {
                  value: {
                    offeringId: '60b8d6a9b8478e001c5d4435',
                  },
                },
                'Request with Message': {
                  value: {
                    offeringId: '60b8d6a9b8478e001c5d4435',
                    message: 'Interested in participating in this offering',
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: 'Request submitted successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: {
                      type: 'string',
                      example: 'Request submitted successfully',
                    },
                    status: {
                      type: 'integer',
                      example: 200,
                    },
                    data: {
                      type: 'object',
                      properties: {
                        requestId: {
                          type: 'string',
                          example: '60b8d6a9b8478e001c5d4436',
                        },
                        status: {
                          type: 'string',
                          enum: ['PENDING', 'APPROVED', 'REJECTED'],
                          example: 'PENDING',
                        },
                        createdAt: {
                          type: 'string',
                          format: 'date-time',
                          example: '2024-03-15T10:30:00Z',
                        },
                      },
                    },
                    error: {
                      type: 'boolean',
                      example: false,
                    },
                  },
                },
              },
            },
          },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
        },
      },
    },
    '/marketplace/v1/auth/offering/duplicate-offering/{offeringId}': {
      post: {
        summary: 'Duplicate Offering',
        tags: ['Offering'],
        description: 'Create a duplicate of an existing offering',
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'offeringId',
            in: 'path',
            required: true,
            schema: { type: 'string' },
            description: 'ID of the offering to duplicate',
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
        },
      },
    },
    '/marketplace/v1/auth/offering/subscribe': {
      post: {
        summary: 'Subscribe to Offering',
        tags: ['Offering'],
        description: 'Subscribe to an offering',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                required: ['offeringId'],
                properties: {
                  offeringId: { type: 'string', description: 'ID of the offering' },
                },
              },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/GenericSuccessResponse' },
          400: { $ref: '#/components/responses/BadRequestResponse' },
          401: { $ref: '#/components/responses/UnauthorizedResponse' },
        },
      },
    },
    '/marketplace/v1/auth/notification': {
      get: {
        summary: 'Get Notifications',
        tags: ['Notification'],
        description: `
Retrieve user notifications. This endpoint provides a paginated list of notifications for the authenticated user.

### Features:
- Real-time updates
- Pagination support
- Unread/Read status
- Notification types:
  - Transfer updates
  - Dividend announcements
  - KYC status changes
  - System alerts

### Response Format:
- Notification ID
- Type
- Message
- Timestamp
- Status (read/unread)
        `,
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'page',
            in: 'query',
            required: false,
            schema: {
              type: 'integer',
              default: 1,
            },
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            schema: {
              type: 'integer',
              default: 10,
            },
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/SuccessResponse' },
        },
      },
      put: {
        summary: 'Mark notification as seen',
        tags: ['Notification'],
        description: 'Mark a notification as seen',
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                required: ['notificationId'],
                properties: {
                  notificationId: {
                    type: 'string',
                    description: 'ID of the notification to mark as seen',
                  },
                },
              },
            },
          },
        },
        responses: {
          200: { $ref: '#/components/responses/SuccessResponse' },
          404: { $ref: '#/components/responses/NotFoundResponse' },
        },
      },
    },
    '/marketplace/v1/transactions': {
      get: {
        summary: 'Get Transactions',
        tags: ['Transactions'],
        description: `
Retrieve transaction history. This endpoint provides a comprehensive view of all user transactions.

### Features:
- Transaction type filtering (BUY, SELL, TRANSFER)
- Pagination support
- Detailed transaction information
- Historical tracking

### Response Includes:
- Transaction ID
- Type
- Amount
- Status
- Timestamps
- Related offering details
        `,
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'type',
            in: 'query',
            required: false,
            schema: {
              type: 'string',
              enum: ['BUY', 'SELL', 'TRANSFER'],
            },
          },
          {
            name: 'page',
            in: 'query',
            required: false,
            schema: {
              type: 'integer',
              default: 1,
            },
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            schema: {
              type: 'integer',
              default: 10,
            },
          },
        ],
        responses: {
          200: { $ref: '#/components/responses/SuccessResponse' },
        },
      },
    },
    '/marketplace/v1/auth/offering': {
      put: {
        summary: 'Submit offering Data',
        tags: ['Offering'],
        description: 'This is a POST request, submitting data to an API via the request body.',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  offeringId: {
                    type: 'string',
                    description: 'ID of the offering',
                  },
                  currentStep: {
                    type: 'number',
                    description: 'Current step number',
                  },
                  team: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        name: {
                          type: 'string',
                          description: 'Team member name',
                        },
                        title: {
                          type: 'string',
                          description: 'Team member title',
                        },
                        summary: {
                          type: 'string',
                          description: 'Team member summary',
                        },
                        url: {
                          type: 'string',
                          description: 'Team member URL',
                        },
                        linkedInUrl: {
                          type: 'string',
                          description: 'Team member LinkedIn URL',
                        },
                        twitterUrl: {
                          type: 'string',
                          description: 'Team member Twitter URL',
                        },
                      },
                      required: ['name', 'title'],
                    },
                  },
                },
                required: ['offeringId', 'currentStep'],
              },
            },
          },
        },
        responses: {
          200: {
            $ref: '#/components/responses/SuccessResponse',
          },
          400: {
            $ref: '#/components/responses/BadRequestResponse',
          },
        },
      },
    },
    '/marketplace/v1/auth/offering/{id}': {
      get: {
        tags: ['Offering'],
        summary: 'Get Offering Details by ID',
        description: 'Fetch the details of a specific offering by its ID.',
        operationId: 'getOfferingDetails',
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            description: 'Unique identifier of the offering',
            schema: {
              type: 'string',
              example: '66ed28308d71a26a1fe4e322',
            },
          },
        ],
        responses: {
          200: {
            description: 'Successful response with offering details',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    status: { type: 'integer' },
                    data: {
                      type: 'object',
                      properties: {
                        _id: { type: 'string' },
                        overview: {
                          type: 'object',
                          properties: {
                            title: { type: 'string' },
                            subTitle: { type: 'string' },
                            description: { type: 'string' },
                            entityName: { type: 'string' },
                            entityType: { type: 'string' },
                            webUrl: { type: 'string' },
                            lineOfBusiness: { type: 'string' },
                            sourceOfFunds: { type: 'string' },
                            location: { type: 'string' },
                            companyDescription: { type: 'string' },
                            icon: { type: 'string' },
                            cover: { type: 'string' },
                            logo: { type: 'string' },
                          },
                        },
                        team: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              name: { type: 'string' },
                              title: { type: 'string' },
                              summary: { type: 'string' },
                              url: { type: 'string' },
                              linkedInUrl: { type: 'string' },
                              twitterUrl: { type: 'string' },
                            },
                          },
                        },
                        offeringSteps: {
                          type: 'object',
                          properties: {
                            offeringStep5: {
                              type: 'object',
                              properties: {
                                summary: { type: 'string' },
                                value: {
                                  type: 'object',
                                  properties: {
                                    offeringId: { type: 'string' },
                                    currentStep: { type: 'integer' },
                                    isFinalSubmission: { type: 'boolean' },
                                  },
                                },
                              },
                            },
                          },
                        },
                      },
                      error: { type: 'boolean' },
                    },
                  },
                },
              },
            },
          },
        },
      },
      '/marketplace/v1/order/create': {
        post: {
          tags: ['Order'],
          summary: 'Create a new Order',
          description: 'This is a POST request, to create a new order.',
          requestBody: { required: true, content: { 'application/json': { schema: { $ref: '#/components/schemas/CreateOrderRequest' } } } },
          responses: { 200: { $ref: '#/components/responses/GenericSuccessResponse' } },
          security: [{ bearerAuth: [] }],
        },
      },
      '/marketplace/v1/order': {
        get: {
          tags: ['Order'],
          operationId: 'getAllOrders',
          parameters: [
            { name: 'limit', in: 'query', required: false, description: 'Number of orders to retrieve per page', schema: { type: 'integer', example: 4 } },
            { name: 'page', in: 'query', required: false, description: 'Page number to retrieve', schema: { type: 'integer', example: 1 } },
            { in: 'query', name: 'search', required: false, schema: { type: 'string', default: 'sss' }, description: 'search' },
            { in: 'query', name: 'status', required: false, schema: { type: 'string', default: 'PENDING' }, description: 'status' },
          ],
          summary: 'Fetch all Orders',
          description: 'This is a GET request to fetch all orders with pagination.',
          responses: { 200: { $ref: '#/components/responses/GenericSuccessResponse' } },
          security: [{ bearerAuth: [] }],
        },
      },
      '/marketplace/v1/order/{id}': {
        get: {
          tags: ['Order'],
          operationId: 'getOrderById',
          parameters: [{ name: 'id', in: 'path', required: true, description: 'Unique identifier of the order', schema: { type: 'string', example: '671350ad0cd14c9b618be82e' } }],
          summary: 'Fetch single Order',
          description: 'This is a GET request to fetch a single order by its unique identifier.',
          responses: { 200: { $ref: '#/components/responses/GenericSuccessResponse' } },
          security: [{ bearerAuth: [] }],
        },
      },
      '/marketplace/v1/dividends': {
        post: {
          summary: 'Create a new dividend request',
          tags: ['Dividends'],
          description: 'Create a new dividend request (Issuer only)',
          security: [{ bearerAuth: [] }],
          requestBody: {
            required: true,
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/DividendRequest' },
              },
            },
          },
          responses: {
            201: { $ref: '#/components/responses/CreatedResponse' },
            401: { $ref: '#/components/responses/UnauthorizedResponse' },
          },
        },
        get: {
          summary: 'Fetch all dividend requests',
          tags: ['Dividends'],
          description: 'Get all dividend requests (Issuer only)',
          security: [{ bearerAuth: [] }],
          responses: {
            200: { $ref: '#/components/responses/SuccessResponse' },
            401: { $ref: '#/components/responses/UnauthorizedResponse' },
          },
        },
      },
      '/marketplace/v1/dividends/investors': {
        get: {
          summary: 'Fetch dividends for investors',
          tags: ['Dividends'],
          description: 'Get dividends for investors (Issuer only)',
          security: [{ bearerAuth: [] }],
          responses: {
            200: { $ref: '#/components/responses/SuccessResponse' },
            401: { $ref: '#/components/responses/UnauthorizedResponse' },
          },
        },
      },
      '/marketplace/v1/dividends/calculate': {
        post: {
          summary: 'Calculate dividends',
          tags: ['Dividends'],
          description: 'Calculate dividends (Issuer only)',
          security: [{ bearerAuth: [] }],
          requestBody: {
            required: true,
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/DividendCalculateRequest' },
              },
            },
          },
          responses: {
            200: { $ref: '#/components/responses/SuccessResponse' },
            401: { $ref: '#/components/responses/UnauthorizedResponse' },
          },
        },
      },
      '/marketplace/v1/dividends/investor-history': {
        get: {
          summary: 'Fetch dividend history for an investor',
          tags: ['Dividends'],
          description: 'Get dividend history for an investor',
          security: [{ bearerAuth: [] }],
          responses: {
            200: { $ref: '#/components/responses/SuccessResponse' },
            401: { $ref: '#/components/responses/UnauthorizedResponse' },
          },
        },
      },
      '/marketplace/v1/nft': {
        get: {
          summary: 'Get NFT collections',
          tags: ['NFT'],
          description: 'Get list of all NFT collections (Issuer only)',
          security: [{ bearerAuth: [] }],
          responses: {
            200: { $ref: '#/components/responses/SuccessResponse' },
            401: { $ref: '#/components/responses/UnauthorizedResponse' },
          },
        },
        post: {
          summary: 'Create NFT',
          tags: ['NFT'],
          description: 'Create a new NFT (Issuer only)',
          security: [{ bearerAuth: [] }],
          requestBody: {
            required: true,
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/NFTCreateRequest' },
              },
            },
          },
          responses: {
            201: { $ref: '#/components/responses/CreatedResponse' },
            401: { $ref: '#/components/responses/UnauthorizedResponse' },
          },
        },
      },
      '/marketplace/v1/nft/create-collection': {
        post: {
          summary: 'Create NFT collection',
          tags: ['NFT'],
          description: 'Create a new NFT collection (Issuer only)',
          security: [{ bearerAuth: [] }],
          requestBody: {
            required: true,
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/NFTCollectionCreateRequest' },
              },
            },
          },
          responses: {
            201: { $ref: '#/components/responses/CreatedResponse' },
            401: { $ref: '#/components/responses/UnauthorizedResponse' },
          },
        },
      },
      '/marketplace/v1/nft/{id}': {
        get: {
          summary: 'Get NFT details',
          tags: ['NFT'],
          description: 'Get details of a specific NFT',
          security: [{ bearerAuth: [] }],
          parameters: [
            {
              name: 'id',
              in: 'path',
              required: true,
              schema: {
                type: 'string',
              },
              description: 'NFT ID',
            },
          ],
          responses: {
            200: { $ref: '#/components/responses/SuccessResponse' },
            404: { $ref: '#/components/responses/NotFoundResponse' },
          },
        },
      },
    },
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
      },
    },
    responses: {
      CreatedResponse: {
        description: 'Created',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string', example: 'Congratulations! You Have Successfully Registered. Please Verify Your Email To Continue Enjoying Our Services.' },
                status: { type: 'integer', example: 201 },
                data: { type: 'object', properties: { isOtpActive: { type: 'boolean', example: true } }, required: ['isOtpActive'] },
                error: { type: 'boolean', example: false },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
        },
      },
      NotFoundResponse: {
        description: 'Not Found',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: { message: { type: 'string', example: 'Page Not Found' }, status: { type: 'integer', example: 404 }, data: { type: 'object', example: {} }, error: { type: 'boolean', example: true } },
              required: ['message', 'status', 'data', 'error'],
            },
          },
        },
      },
      UnauthorizedResponse: {
        description: 'Unauthorized',
        content: { 'application/json': { schema: { type: 'object', properties: { message: { type: 'string' }, status: { type: 'integer' }, data: { type: 'object' }, error: { type: 'boolean' } }, required: ['message', 'status', 'data', 'error'] } } },
      },
      LoginSuccessResponse: {
        description: 'Login Successful',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: { message: { type: 'string' }, status: { type: 'integer' }, data: { type: 'object', properties: { isOtpActive: { type: 'boolean' } }, required: ['isOtpActive'] }, error: { type: 'boolean' } },
              required: ['message', 'status', 'data', 'error'],
            },
          },
        },
      },
      LoginSuccessWithoutOtpResponse: {
        description: 'Login Successful without OTP',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: {
                  type: 'integer',
                  data: { type: 'object', properties: { accessToken: { type: 'string' }, is2FAActive: { type: 'boolean' } }, required: ['accessToken', 'is2FAActive'] },
                  error: { type: 'boolean' },
                },
                required: ['message', 'status', 'data', 'error'],
              },
            },
          },
        },
        VerifySuccessResponse: {
          description: 'Verification Successful',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: { message: { type: 'string' }, status: { type: 'integer' }, data: { type: 'object', properties: { accessToken: { type: 'string' } }, required: ['accessToken'] }, error: { type: 'boolean' } },
                required: ['message', 'status', 'data', 'error'],
              },
            },
          },
        },
        GenericSuccessResponse: {
          description: 'Successful Operation',
          content: {
            'application/json': { schema: { type: 'object', properties: { message: { type: 'string' }, status: { type: 'integer' }, data: { type: 'object' }, error: { type: 'boolean' } }, required: ['message', 'status', 'data', 'error'] } },
          },
        },
        ServerErrorResponse: {
          description: 'Internal Server Error',
          content: {
            'application/json': { schema: { type: 'object', properties: { message: { type: 'string' }, status: { type: 'integer' }, data: { type: 'object' }, error: { type: 'boolean' } }, required: ['message', 'status', 'data', 'error'] } },
          },
        },
        Enable2FAResponse: {
          description: '2FA Enabled',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: { message: { type: 'string' }, status: { type: 'integer' }, data: { type: 'object', properties: { qrCode: { type: 'string' } }, required: ['qrCode'] }, error: { type: 'boolean' } },
                required: ['message', 'status', 'data', 'error'],
              },
            },
          },
        },
        Reset2FAResponse: {
          description: '2FA Reset',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: { message: { type: 'string' }, status: { type: 'integer' }, data: { type: 'object', properties: { qrCode: { type: 'string' }, secret: { type: 'string' } }, required: ['qrCode', 'secret'] }, error: { type: 'boolean' } },
                required: ['message', 'status', 'data', 'error'],
              },
            },
          },
        },
        UploadDocsResponse: {
          description: 'Documents Uploaded',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: { message: { type: 'string' }, status: { type: 'integer' }, data: { type: 'object', properties: { url: { type: 'string' } }, required: ['url'] }, error: { type: 'boolean' } },
                required: ['message', 'status', 'data', 'error'],
              },
            },
          },
        },
        GetProfileResponse: {
          description: 'Profile Data',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: { type: 'string' },
                  status: {
                    type: 'integer',
                    data: {
                      type: 'object',
                      properties: { _id: { type: 'string' }, name: { type: 'string' }, countryCode: { type: 'string' }, mobile: { type: 'string' }, dob: { type: 'string' }, isActive: { type: 'boolean' }, createdAt: { type: 'string' } },
                      required: ['_id', 'name', 'countryCode', 'mobile', 'dob', 'isActive', 'createdAt'],
                    },
                    error: { type: 'boolean', example: false },
                  },
                  required: ['message', 'status', 'data', 'error'],
                },
              },
            },
          },
          kycResponse: {
            description: 'Created',
            content: {
              'application/json': {
                schema: { type: 'object', properties: { message: { type: 'string' }, status: { type: 'integer', example: 200 }, data: { type: 'object' }, error: { type: 'boolean' } }, required: ['message', 'status', 'data', 'error'] },
              },
            },
          },
          offeringResponse: {
            description: 'Created',
            content: {
              'application/json': {
                schema: { type: 'object', properties: { message: { type: 'string' }, status: { type: 'integer', example: 200 }, data: { type: 'object' }, error: { type: 'boolean' } }, required: ['message', 'status', 'data', 'error'] },
              },
            },
          },
          SuccessResponse: {
            description: 'Success',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    data: { type: 'object' },
                  },
                },
              },
            },
          },
          BadRequestResponse: {
            description: 'Bad Request',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/BadRequestResponse',
                },
              },
            },
          },
        },
      },
      security: [{ bearerAuth: [] }],
    },
  },
  components: {
    schemas: {
      SignupRequest: {
        type: 'object',
        required: ['name', 'email', 'password', 'countryCode', 'mobile', 'userType', 'otpMethods'],
        properties: {
          name: {
            type: 'string',
            example: 'John Doe',
          },
          email: {
            type: 'string',
            format: 'email',
            example: '<EMAIL>',
          },
          password: {
            type: 'string',
            format: 'password',
            example: 'Password123!',
          },
          countryCode: {
            type: 'string',
            example: '+1',
          },
          mobile: {
            type: 'string',
            example: '*********0',
          },
          userType: {
            type: 'string',
            enum: ['investor', 'institution'],
            example: 'investor',
          },
          otpMethods: {
            type: 'string',
            enum: ['Email', 'SMS'],
            example: 'Email',
          },
        },
      },
      LoginRequest: {
        type: 'object',
        required: ['email', 'password'],
        properties: {
          email: {
            type: 'string',
            format: 'email',
            example: '<EMAIL>',
          },
          password: {
            type: 'string',
            format: 'password',
            example: 'Password123!',
          },
        },
      },
      SocialLoginRequest: {
        type: 'object',
        required: ['provider', 'token'],
        properties: {
          provider: {
            type: 'string',
            enum: ['google', 'facebook', 'linkedin'],
            example: 'google',
          },
          token: {
            type: 'string',
            example: 'oauth2token',
          },
        },
      },
      ResendOtpRequest: {
        type: 'object',
        required: ['email', 'type', 'otpMethods'],
        properties: {
          email: {
            type: 'string',
            format: 'email',
            example: '<EMAIL>',
          },
          type: {
            type: 'string',
            enum: ['signup', 'login', 'forgot'],
            example: 'signup',
          },
          otpMethods: {
            type: 'string',
            enum: ['Email', 'SMS'],
            example: 'Email',
          },
        },
      },
      ForgotPasswordRequest: {
        type: 'object',
        required: ['email'],
        properties: {
          email: {
            type: 'string',
            format: 'email',
            example: '<EMAIL>',
          },
        },
      },
      ResetPasswordRequest: {
        type: 'object',
        required: ['email', 'otp', 'newPassword'],
        properties: {
          email: {
            type: 'string',
            format: 'email',
            example: '<EMAIL>',
          },
          otp: {
            type: 'string',
            example: '123456',
          },
          newPassword: {
            type: 'string',
            format: 'password',
            example: 'NewPassword123!',
          },
        },
      },
      BadRequestResponse: {
        type: 'object',
        properties: {
          message: {
            type: 'string',
            example: 'Invalid input',
          },
          status: {
            type: 'integer',
            example: 400,
          },
          data: {
            type: 'object',
          },
          error: {
            type: 'boolean',
            example: true,
          },
        },
      },
      institutionKYBStep1Request: {
        type: 'object',
        required: ['institutions', 'kycSteps'],
        properties: {
          institutions: {
            type: 'object',
            properties: {
              companyInformation: {
                type: 'object',
                properties: {
                  name: { type: 'string' },
                  entityType: { type: 'string' },
                  webSite: { type: 'string' },
                  business: { type: 'string' },
                  sourceOfFunds: { type: 'string' },
                },
              },
              address: {
                type: 'object',
                properties: {
                  address: { type: 'string' },
                  address2: { type: 'string' },
                  country: { type: 'string' },
                  state: { type: 'string' },
                  city: { type: 'string' },
                  zipCode: { type: 'string' },
                },
              },
            },
          },
          kycSteps: { type: 'number', enum: [1] },
        },
      },

      institutionKYBStep2Request: {
        type: 'object',
        required: ['primaryContactInfo', 'kycSteps'],
        properties: {
          primaryContactInfo: {
            type: 'object',
            properties: {
              personalInformation: {
                type: 'object',
                properties: {
                  name: { type: 'string' },
                  jobTitle: { type: 'string' },
                  dob: { type: 'string' },
                  socialSecurityNumber: { type: 'string' },
                  citizenship: { type: 'string' },
                  countryCode: { type: 'string' },
                  mobile: { type: 'string' },
                  email: { type: 'string' },
                },
              },
              address: {
                type: 'object',
                properties: {
                  address: { type: 'string' },
                  address2: { type: 'string' },
                  country: { type: 'string' },
                  state: { type: 'string' },
                  city: { type: 'string' },
                  zipCode: { type: 'string' },
                },
              },
            },
          },
          kycSteps: { type: 'number', enum: [2] },
        },
      },

      institutionKYBStep3Request: {
        type: 'object',
        required: ['wallets', 'kycSteps'],
        properties: {
          wallets: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                type: { type: 'string' },
                address: { type: 'string' },
              },
            },
          },
          kycSteps: { type: 'number', enum: [3] },
        },
      },

      institutionKYBStep4Request: {
        type: 'object',
        required: ['documents', 'kycSteps'],
        properties: {
          documents: {
            type: 'object',
            properties: {
              frontId: { type: 'string' },
              backId: { type: 'string' },
              otherIdentification: { type: 'string' },
            },
          },
          kycSteps: { type: 'number', enum: [4] },
        },
      },

      institutionKYBStep5Request: {
        type: 'object',
        required: ['isIdentityVerification', 'kycSteps'],
        properties: {
          isIdentityVerification: {
            type: 'object',
            properties: {
              status: { type: 'boolean' },
            },
          },
          kycSteps: { type: 'number', enum: [5] },
        },
      },

      institutionKYBStep6Request: {
        type: 'object',
        required: ['beneficialOwners', 'kycSteps'],
        properties: {
          beneficialOwners: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                personalInformation: {
                  type: 'object',
                  properties: {
                    name: { type: 'string' },
                    dob: { type: 'string' },
                    socialSecurityNumber: { type: 'string' },
                    citizenship: { type: 'string' },
                  },
                },
                address: {
                  type: 'object',
                  properties: {
                    address: { type: 'string' },
                    address2: { type: 'string' },
                    country: { type: 'string' },
                    state: { type: 'string' },
                    city: { type: 'string' },
                    zipCode: { type: 'string' },
                  },
                },
                identityProof: {
                  type: 'object',
                  properties: {
                    passport: {
                      type: 'object',
                      properties: {
                        front: { type: 'string' },
                        back: { type: 'string' },
                      },
                    },
                    driversLicense: {
                      type: 'object',
                      properties: {
                        front: { type: 'string' },
                        back: { type: 'string' },
                      },
                    },
                    idCard: {
                      type: 'object',
                      properties: {
                        front: { type: 'string' },
                        back: { type: 'string' },
                      },
                    },
                  },
                },
              },
            },
          },
          kycSteps: { type: 'number', enum: [6] },
        },
      },

      institutionKYBStep7Request: {
        type: 'object',
        required: ['managementInfo', 'kycSteps'],
        properties: {
          managementInfo: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                personalInformation: {
                  type: 'object',
                  properties: {
                    name: { type: 'string' },
                    jobTitle: { type: 'string' },
                    dob: { type: 'string' },
                    socialSecurityNumber: { type: 'string' },
                    citizenship: { type: 'string' },
                    countryCode: { type: 'string' },
                    mobile: { type: 'string' },
                    email: { type: 'string' },
                  },
                },
                address: {
                  type: 'object',
                  properties: {
                    address: { type: 'string' },
                    address2: { type: 'string' },
                    country: { type: 'string' },
                    state: { type: 'string' },
                    city: { type: 'string' },
                    zipCode: { type: 'string' },
                  },
                },
              },
            },
          },
          kycSteps: { type: 'number', enum: [7] },
        },
      },

      institutionKYBStep8Request: {
        type: 'object',
        required: ['institutions', 'primaryContactInfo', 'wallets', 'documents', 'isIdentityVerification', 'beneficialOwners', 'managementInfo', 'kycSteps', 'isFinalSubmission'],
        properties: {
          institutions: { $ref: '#/components/schemas/institutionKYBStep1Request/properties/institutions' },
          primaryContactInfo: { $ref: '#/components/schemas/institutionKYBStep2Request/properties/primaryContactInfo' },
          wallets: { $ref: '#/components/schemas/institutionKYBStep3Request/properties/wallets' },
          documents: { $ref: '#/components/schemas/institutionKYBStep4Request/properties/documents' },
          isIdentityVerification: { $ref: '#/components/schemas/institutionKYBStep5Request/properties/isIdentityVerification' },
          beneficialOwners: { $ref: '#/components/schemas/institutionKYBStep6Request/properties/beneficialOwners' },
          managementInfo: { $ref: '#/components/schemas/institutionKYBStep7Request/properties/managementInfo' },
          kycSteps: { type: 'number', enum: [8] },
          isFinalSubmission: { type: 'boolean' },
        },
      },

      investerKycStep1Request: {
        type: 'object',
        required: ['mainInformation', 'kycSteps'],
        properties: {
          mainInformation: {
            type: 'object',
            properties: {
              birthPlace: { type: 'string' },
              nationality: { type: 'string' },
              nationalIdNumber: { type: 'string' },
              identificationDocument: { type: 'string' },
              occupation: { type: 'string' },
              documentExpiration: { type: 'string' },
              dob: { type: 'string' },
            },
          },
          kycSteps: { type: 'number', enum: [1] },
        },
      },

      investerKycStep2Request: {
        type: 'object',
        required: ['wallets', 'kycSteps'],
        properties: {
          wallets: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                type: { type: 'string' },
                address: { type: 'string' },
              },
            },
          },
          kycSteps: { type: 'number', enum: [2] },
        },
      },

      investerKycStep3Request: {
        type: 'object',
        required: ['documents', 'kycSteps'],
        properties: {
          documents: {
            type: 'object',
            properties: {
              frontId: { type: 'string' },
              backId: { type: 'string' },
              otherIdentification: { type: 'string' },
            },
          },
          kycSteps: { type: 'number', enum: [3] },
        },
      },

      investerKycStep4Request: {
        type: 'object',
        required: ['isIdentityVerification', 'kycSteps'],
        properties: {
          isIdentityVerification: {
            type: 'object',
            properties: {
              status: { type: 'boolean' },
            },
          },
          kycSteps: { type: 'number', enum: [4] },
        },
      },

      investerKycStep5Request: {
        type: 'object',
        required: ['mainInformation', 'wallets', 'documents', 'isIdentityVerification', 'kycSteps', 'isFinalSubmission'],
        properties: {
          mainInformation: { $ref: '#/components/schemas/investerKycStep1Request/properties/mainInformation' },
          wallets: { $ref: '#/components/schemas/investerKycStep2Request/properties/wallets' },
          documents: { $ref: '#/components/schemas/investerKycStep3Request/properties/documents' },
          isIdentityVerification: { $ref: '#/components/schemas/investerKycStep4Request/properties/isIdentityVerification' },
          kycSteps: { type: 'number', enum: [5] },
          isFinalSubmission: { type: 'boolean' },
        },
      },

      ChangePasswordRequest: {
        type: 'object',
        required: ['currentPassword', 'newPassword'],
        properties: {
          currentPassword: {
            type: 'string',
            format: 'password',
          },
          newPassword: {
            type: 'string',
            format: 'password',
          },
        },
      },

      VerifyRequest: {
        type: 'object',
        required: ['otp', 'email', 'type', 'otpMethods'],
        properties: {
          otp: {
            type: 'string',
            example: '123456',
          },
          email: {
            type: 'string',
            format: 'email',
          },
          type: {
            type: 'string',
            enum: ['sign_up', 'login', 'forgot'],
          },
          otpMethods: {
            type: 'string',
            enum: ['Email', 'SMS'],
          },
        },
      },

      Verify2FARequest: {
        type: 'object',
        required: ['token'],
        properties: {
          token: {
            type: 'string',
            example: '123456',
          },
        },
      },

      VerifyLogin2FARequest: {
        type: 'object',
        required: ['token'],
        properties: {
          token: {
            type: 'string',
            example: '123456',
          },
        },
      },

      Forgot2FARequest: {
        type: 'object',
        required: ['email'],
        properties: {
          email: {
            type: 'string',
            format: 'email',
          },
        },
      },

      Disable2FARequest: {
        type: 'object',
        required: ['token'],
        properties: {
          token: {
            type: 'string',
            example: '123456',
          },
        },
      },

      emptyRequest: {
        type: 'object',
        properties: {},
      },

      UpdateProfileRequest: {
        type: 'object',
        properties: {
          name: { type: 'string' },
          countryCode: { type: 'string' },
          mobile: { type: 'string' },
          dob: { type: 'string' },
        },
      },

      UploadDocsRequest: {
        type: 'object',
        required: ['file'],
        properties: {
          file: {
            type: 'string',
            format: 'binary',
          },
        },
      },
    },
    responses: {
      CreatedResponse: {
        description: 'Created',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  example: 'Resource created successfully',
                },
                status: {
                  type: 'integer',
                  example: 201,
                },
                data: {
                  type: 'object',
                },
                error: {
                  type: 'boolean',
                  example: false,
                },
              },
            },
          },
        },
      },
      NotFoundResponse: {
        description: 'Not Found',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  example: 'Resource not found',
                },
                status: {
                  type: 'integer',
                  example: 404,
                },
                data: {
                  type: 'object',
                },
                error: {
                  type: 'boolean',
                  example: true,
                },
              },
            },
          },
        },
      },
      UnauthorizedResponse: {
        description: 'Unauthorized',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  example: 'Unauthorized access',
                },
                status: {
                  type: 'integer',
                  example: 401,
                },
                data: {
                  type: 'object',
                },
                error: {
                  type: 'boolean',
                  example: true,
                },
              },
            },
          },
        },
      },
      LoginSuccessResponse: {
        description: 'Login Successful',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  example: 'Login successful',
                },
                status: {
                  type: 'integer',
                  example: 200,
                },
                data: {
                  type: 'object',
                  properties: {
                    isOtpActive: {
                      type: 'boolean',
                      example: true,
                    },
                  },
                },
                error: {
                  type: 'boolean',
                  example: false,
                },
              },
            },
          },
        },
      },
      LoginSuccessWithoutOtpResponse: {
        description: 'Login Successful without OTP',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  example: 'Login successful',
                },
                status: {
                  type: 'integer',
                  example: 200,
                },
                data: {
                  type: 'object',
                  properties: {
                    accessToken: {
                      type: 'string',
                      example: 'jwt.token.here',
                    },
                    is2FAActive: {
                      type: 'boolean',
                      example: false,
                    },
                  },
                },
                error: {
                  type: 'boolean',
                  example: false,
                },
              },
            },
          },
        },
      },
      GenericSuccessResponse: {
        description: 'Success',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  example: 'Operation successful',
                },
                status: {
                  type: 'integer',
                  example: 200,
                },
                data: {
                  type: 'object',
                },
                error: {
                  type: 'boolean',
                  example: false,
                },
              },
            },
          },
        },
      },
      kycResponse: {
        description: 'KYC submission response',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer', example: 200 },
                data: { type: 'object' },
                error: { type: 'boolean' },
              },
            },
          },
        },
      },
      ServerErrorResponse: {
        description: 'Internal Server Error',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer', example: 500 },
                data: { type: 'object' },
                error: { type: 'boolean', example: true },
              },
            },
          },
        },
      },
      VerifySuccessResponse: {
        description: 'Verification Successful',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    accessToken: { type: 'string' },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      Enable2FAResponse: {
        description: '2FA Enabled Successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    qrCode: { type: 'string' },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      Reset2FAResponse: {
        description: '2FA Reset Successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    qrCode: { type: 'string' },
                    secret: { type: 'string' },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      UploadDocsResponse: {
        description: 'Document Upload Successful',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    url: { type: 'string' },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      GetProfileResponse: {
        description: 'Profile Retrieved Successfully',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer', example: 200 },
                data: {
                  type: 'object',
                  properties: {
                    _id: { type: 'string' },
                    name: { type: 'string' },
                    countryCode: { type: 'string' },
                    mobile: { type: 'string' },
                    dob: { type: 'string' },
                    isActive: { type: 'boolean' },
                    createdAt: { type: 'string' },
                  },
                },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      SuccessResponse: {
        description: 'Generic Success Response',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: { type: 'string' },
                status: { type: 'integer', example: 200 },
                data: { type: 'object' },
                error: { type: 'boolean', example: false },
              },
            },
          },
        },
      },
      BadRequestResponse: {
        description: 'Bad Request',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                message: {
                  type: 'string',
                  example: 'Invalid input parameters',
                },
                status: {
                  type: 'integer',
                  example: 400,
                },
                data: {
                  type: 'object',
                  example: {},
                },
                error: {
                  type: 'boolean',
                  example: true,
                },
              },
              required: ['message', 'status', 'data', 'error'],
            },
          },
        },
      },
    },
  },
};
