/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * Standard response payload interface for API responses.
 * @template T - The type of data in the response
 */
export interface ResponsePayLoad<T = void> {
  message: string;
  status: number;
  data?: T;
  error: boolean;
}

/**
 * Interface for Joi validation results.
 */
export interface JoiValidationResult {
  error: boolean;
  value: any;
  message?: string;
  status?: number;
}

export interface UserInfo {
  userId: string;
  email: string;
  userType: string;
  isKyc: boolean;
  isIssuer: boolean;
  exp: number;
}

export enum UserTypeEnum {
  Investor = 'investor',
  Institution = 'institution',
}

export enum EntityTypeEnum {
  IndividualPerson = 'Individual (Person)',
  SoleProprietorship = 'Sole Proprietorship',
  Partnership = 'Partnership',
  Corporation = 'Corporation (or Company)',
  LLC = 'Limited Liability Company (LLC)',
  NonProfitOrganization = 'Non-Profit Organization',
  GovernmentEntity = 'Government Entity',
  Trust = 'Trust',
  JointVenture = 'Joint Venture',
  Association = 'Association',
  Cooperative = 'Cooperative',
  PubliclyTradedCompany = 'Publicly Traded Company',
  PrivateCompany = 'Private Company',
  EducationalInstitution = 'Educational Institution',
  Foundation = 'Foundation',
}

export enum IncomeSourceEnum {
  EmploymentIncome = 'Employment Income',
  BusinessIncome = 'Business Income',
  InvestmentIncome = 'Investment Income',
  Savings = 'Savings',
  Inheritance = 'Inheritance',
  Gift = 'Gift',
  SaleOfPropertyOrAssets = 'Sale of Property or Assets',
  LoanOrCredit = 'Loan or Credit',
  PensionRetirementFund = 'Pension/Retirement Fund',
  Dividends = 'Dividends',
  GovernmentAssistanceBenefits = 'Government Assistance/Benefits',
}

export enum AssetTypeEnum {
  RealEstate = 'Real Estate',
  Equity = 'Equity',
  // Debt = 'Debt',
  // BondingToken = 'Bonding Token',
  // Others = 'Others',
}

export enum BlockchainEnum {
  Base = 'Base',
}
export enum OfferingTypeEnum {
  PublicOffering = 'Public Offering',
  PrivatePlacement = 'Private Placement',
  ShareCapitalManagement = 'Share Capital Management',
  BondingToken = 'Bonding Token',
}

export enum StandardTokenEnum {
  ERC3643 = 'ERC-3643',
}

export enum PropertyTypeEnum {
  Commercial = 'Commercial',
  Residential = 'Residential',
  Office = 'Office',
  Multifamily = 'Multifamily',
  Retail = 'Retail',
  Hotel = 'Hotel',
  Industrial = 'Industrial',
  Others = 'Others',
}

export enum sumSubKycStatusEnum {
  NOT_STARTED = 'NOT_STARTED',
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  DECLINED = 'DECLINED',
  HOLD = 'HOLD',
  PRECHECKRED = 'PRECHECKRED',
}

export enum KycStatusEnum {
  REQUESTED = 'REQUESTED',
  STARTED = 'STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  NOT_STARTED = 'NOT_STARTED',
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  RESUBMIT = 'RESUBMIT',
  MINTED = 'MINTED',
  DELETED = 'DELETED',
  REVIEW = 'REVIEW',
}

export enum IssuerStatusEnum {
  NOT_APPLIED = 'NOT_APPLIED',
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  BLOCKED = 'BLOCKED',
}

export enum nftStatusEnum {
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  IN_PROGRESS = 'IN_PROGRESS',
}

export enum offeringStatusEnum {
  STARTED = 'STARTED',
  // MINTED = 'MINTED',
  IN_PROGRESS = 'IN_PROGRESS',
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  RESUBMIT = 'RESUBMIT',
  DELETED = 'DELETED',
  REVIEW = 'REVIEW',
}
export enum orderStatusEnum {
  STARTED = 'STARTED',
  MINTED = 'MINTED',
  BURN = 'BURN',
  FREEZE = 'FREEZE',
  UNFREEZE = 'UNFREEZE',
  TRANSFER = 'TRANSFER',
  PENDING = 'PENDING',
  REDEEM = 'REDEEM',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  RESUBMIT = 'RESUBMIT',
  DELETED = 'DELETED',
  TRANSFER_FROM = 'TRANSFER_FROM',
  TRANSFER_TO = 'TRANSFER_TO',
  CONVERT_FROM = 'CONVERT_FROM',
  CONVERT_TO = 'CONVERT_TO',
  CONVERT = 'CONVERT',
  PRICE_CHANGE = 'PRICE_CHANGE',
  INITIAL_ORDER = 'INITIAL_ORDER',
  NFT = 'NFT',
}
export enum transferStatusEnum {
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
}

export enum whiteListEnum {
  STARTED = 'STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  RESUBMIT = 'RESUBMIT',
}

export enum WalletTypeEnum {
  METAMASK_WALLET = 'MetamaskWallet',
  TRUST_WALLET = 'TrustWallet',
  WALLET_CONNECT = 'WalletConnect',
  FIREBLOCKS_WALLET = 'FireblocksWallet',
}

export const otpTypeEnum = { SIGN_UP: 'sign_up', LOGIN: 'login', FORGOT: 'forgot', DISABLE_2FA: 'tfa', DISABLE_OTP: 'disable', VERIFICATION_OTP: 'verification' };

export enum otpMethodsEnum {
  EMAIL = 'Email',
  MOBILE = 'Mobile',
}

export enum DocumentTypesEnum {
  FRONT_ID_CARD = 'frontId',
  BACK_ID_CARD = 'backId',
  NATIONAL_ID = 'proofOfResidence',
  PROFILE = 'profile',
  DIVIDEND = 'DIVIDEND',
  PROPOSAL = 'proposal',
  nft = 'nft',
}
export enum DocumentFolderTypesEnum {
  USER = 'users',
  OFFERING = 'offering',
  DIVIDEND = 'DIVIDEND',
  PROPOSAL = 'proposal',
}
/**
 * Enum for offering document types.
 * Defines all possible types of documents that can be associated with an offering.
 */
export enum offeringDocumentTypesEnum {
  eSign = 'eSign',
  pitchDeck = 'pitchDeck',
  confidentialInformationMemorendum = 'confidentialInformationMemorendum',
  landRegistration = 'landRegistration',
  titleDocs = 'titleDocs',
  bankApproval = 'bankApproval',
  encumbranceCertificate = 'encumbranceCertificate',
  propertyTaxReceipt = 'propertyTaxReceipt',
  articlesOfAssociation = 'articlesOfAssociation',
  operatingAgreement = 'operatingAgreement',
  taxAssignmentLetter = 'taxAssignmentLetter',
  certificateOfRegistration = 'certificateOfRegistration',
  registerOfManagers = 'registerOfManagers',
  icon = 'icon',
  companyLogo = 'companyLogo',
  offeringCoverImage = 'offeringCoverImage',
  url = 'url',
  propertyImages = 'propertyImages',
  customImage = 'customImage',
  poweredByLogo = 'poweredByLogo',
}
/**
 * Standard promise resolution interface for async operations.
 */
export interface PromiseResolve {
  status: number;
  error: boolean;
  message: string;
  data?: any;
}

/**
 * Interface for authentication token response data.
 */
export interface AuthTokenResponseType {
  iat: number;
  exp: number;
  aud: string;
  iss: string;
}

/**
 * Interface for PUT command responses.
 */
export interface PutCommandResponse {
  endPoint: string;
  keyName: string;
}
export const enum queueMessageTypeEnum {
  USER = 'user',
  OFFERING = 'offering',
  REQ_OFFERING = 'reqOffering',
  REQ_WHITELIST = 'reqWhitelist',
  ORDER = 'order',
  WHITELIST = 'whitelist',
  TRANSFER = 'ForceTransferred',
  NFT_COLLECTION = 'NftCollection',
  NFT = 'Nft',
  PRIMARY_MARKET = 'primaryMarketData',
}

/**
 * Interface for Redis SET operation options.
 */
export interface RedisSetOptions {
  EX?: number; // Optional expiration time in seconds
  NX: boolean; // Optional flag to set the key only if it does not already exist
}

/**
 * Interface for pagination parameters.
 */
export interface IPagination {
  page?: number;
  limit?: number;
  sort?: any;
  search?: any;
}

export enum WebhookTypeEnum {
  APPLICANT_CREATED = 'applicantCreated',
  APPLICANT_PENDING = 'applicantPending',
  APPLICANT_REVIEWED = 'applicantReviewed',
  APPLICANT_ON_HOLD = 'applicantActionOnHold',
  APPLICANT_PRECHECKED = 'applicantPrechecked',
}

export enum paymentTypeEnum {
  USDC = 'USDC',
  USDT = 'USDT',
  ERC20 = 'ERC20',
  ERC3643 = 'ERC3643',
  // BANK_TRANSFER = 'Bank Transfer',
}

export enum agentFunctionsEnum {
  TENANT = 'Tenant Management',
  RENT = 'Rent Collection',
  STABLECOIN = 'Stablecoin Conversion',
  YIELD = 'Yield Distribution',
  BUYBACK = 'Buyback & Burn Mechanics',
}
