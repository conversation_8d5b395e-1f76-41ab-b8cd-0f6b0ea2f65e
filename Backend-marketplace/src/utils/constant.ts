/* eslint-disable no-useless-escape */
export const otpLength = 6;
export const coolDownTimeInSeconds = 60;
export const nameMaxLength = 70;
export const nameMinLength = 3;
export const emailMaxLength = 200;
export const passwordMaxLength = 50;
export const passwordMinLength = 2;
// export const passwordPattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*\W)[A-Za-z\d\W]{8,}$/;
export const passwordPattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).+$/;
export const namePattern = /^[A-Za-z\s]+$/;
export const alphanumericPattern = /^[A-Za-z0-9À-ÖØ-öø-ÿ\s'’\-]+$/;
export const otpPattern = /^[0-9]{6}$/;
export const maxPasswordHistory = 5;
export const maxKycAttempt = 3;
export const urlPattern = /^(https?:\/\/)?([a-zA-Z0-9-]+\.)?[a-zA-Z0-9-]{1,}\.[a-zA-Z]{2,}(\/.*)?$/;
