export const RESPONSES = {
  CONTINUE: 100,
  SWITCHING_PROTOCOLS: 101,
  PROCESSING: 102,
  EARLY_HINTS: 103,
  SUCCESS: 200,
  CREATED: 201,
  ACCEPTED: 202,
  NON_AUTHORITATIVE_INFORMATION: 203,
  NO_CONTENT: 204,
  RESET_CONTENT: 205,
  PARTIAL_CONTENT: 206,
  AMBIGUOUS: 300,
  MOVED_PERMANENTLY: 301,
  FOUND: 302,
  SEE_OTHER: 303,
  NOT_MODIFIED: 304,
  TEMPORARY_REDIRECT: 307,
  PERMANENT_REDIRECT: 308,
  BAD_REQUEST: 400,
  UN_AUTHORIZED: 401,
  PAYMENT_REQUIRED: 402,
  FORBIDDEN: 403,
  NOTFOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  NOT_ACCEPTABLE: 406,
  PROXY_AUTHENTICATION_REQUIRED: 407,
  REQUEST_TIMEOUT: 408,
  CONFLICT: 409,
  GONE: 410,
  LENGTH_REQUIRED: 411,
  PRECONDITION_FAILED: 412,
  PAYLOAD_TOO_LARGE: 413,
  URI_TOO_LONG: 414,
  UNSUPPORTED_MEDIA_TYPE: 415,
  REQUESTED_RANGE_NOT_SATISFIABLE: 416,
  EXPECTATION_FAILED: 417,
  I_AM_A_TEAPOT: 418,
  MISDIRECTED: 421,
  UNPROCESSABLE_ENTITY: 422,
  RESOURCE_LOCKED: 423,
  FAILED_DEPENDENCY: 424,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  NOT_IMPLEMENTED: 501,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
  HTTP_VERSION_NOT_SUPPORTED: 505,
};

export const RES_MSG = {
  COMMON: {
    BAD_REQUEST: 'Not a valid request',
    SOMETHING_WRONG: 'Something went wrong',
    DUPLICATE_DATA: 'Data already available',
    OFFERING_NOT_FOUND: 'Offering Details not available',
    UNAUTHORIZED_ACCESS: 'Unauthorized access',
    NO_USER: 'No record found for such credentials',
    NO_RECORD: 'No record found.',
    NO_OFFERING: 'No offering found.',
    ADMIN_BLOCK_USER: 'Your account has been blocked by the administrator. please contact support for further assistance.',
    FORBIDDEN_ACCESS: 'Access forbidden',
    REQ_SENT: 'Request sent successfully',
    ALREADY_EXIST: 'already exist',
    EMAIL_ALREADY_EXIST: 'Email Already Exist',
    MOBILE_ALREADY_EXIST: 'Mobile Number Already Exist',
    RECORD_FETCH: 'Fetch Record Successfully',
  },

  USER: {
    USER_NOT_FOUND: 'User not exist',
    TOKEN_TRANSFER_USER_NOT_FOUND: 'The email you provided for the token transfer is not registered on our platform.',
    USER_FETCH: 'User fetched successfully.',
    NOT_ISSUER: 'You are not an issuer !',
    OTP_SUCCESS: 'OTP verified successfully.',
    CREATED_SUCCESS: 'Created Successfully.',
    OFFERING_SUBSCRIBE_SUCCESS: 'Offering Subscribed Successfully.',
    OFFERING_SUBSCRIBE_REJECT: 'Wallet Whitelist Request Rejected.',
    VERIFICATION_SUCCESS: 'Verification successful.',
    OTP_DISABLE_SUCCESS: 'OTP disabled successfully.',
    TFA_DISABLE_SUCCESS: 'two factor authentication disabled successfully',
    LOGIN_SUCCESS: 'User logged in successfully',
    USERS_FETCH: 'Record fetched successfully.',
    USER_LOCKED: 'Account locked, try again later.',
    USER_LOCKED_TIME: 'Account locked, try again after {minutes}',
    USER_DATA_SAVED: 'User data saved',
    USER_CREATE_ERROR: 'Error in creating user',
    USER_DELETED: 'User deleted successfully',
    USER_FETCH_ERROR: 'Error in fetching user',
    EVENT_RECIEVED: 'Event received from sumsub',
    USER_UPDATION_SUCCESS: 'User updated successfully',
    ORDER_UPDATION_SUCCESS: 'order updated successfully',
    USER_ALREADY_EXIST: 'User account already exists',
    MOBILE_ALREADY_EXIST: 'Mobile number is already registered',
    USER_ALREADY_VERIFIED: 'User already verified',
    USER_NOT_VERIFIED: 'An OTP has been sent. Please verify your {methods} before continuing.',
    USER_KYC_UPDATE_SUCCESS: 'KYC submitted!',
    USER_KYC_PENDING: 'KYC verification is pending before KYC verification not allow to become issuer',
    ALREADY_APPROVED_OFFERING: 'Updating an offering that has already been approved is not allowed.',
    DELETE_ALREADY_APPROVED_OFFERING: 'Deleting an offering that has already been approved is not allowed.',
    ALREADY_PENDING_OFFERING: 'Updating an offering that has already been pending is not allowed.',
    OFFERING_UNAUTHORIZED_COUNTRY: 'This offering is not available for subscription in your country.',
  },

  TWO_FA: {
    CREATED: 'Two-factor authentication has been successfully set up.',
    VERIFIED_SUCCESS: 'Two-factor authentication verified successfully.',
    DISABLE_SUCCESS: 'Two-factor authentication has been disabled successfully.',
    PENDING: 'Two-factor authentication setup is pending. Please complete the verification.',
    TOKEN_EXPIRE: 'The request token has expired. Please try again.',
    TOKEN_INVALID: 'The request token is invalid. Please try again.',
    TFA_CODE_INVALID: 'Invalid 2FA code, Please try again.',
  },

  ERROR_MSG: {
    TA_EXIST: 'TA already exists. Please assign different TA',
    ORDER_NOT_FOUND: 'Order not found',
    PASSWORD_CHANGE_ERROR: 'An error occurred while changing the password. Please try again.',
    LOGIN_ERROR: 'An error occurred during login. Please try again.',
    INTERNAL_SERVER_ERROR: 'A server error occurred. Please try again later.',
    INVALID_CREDENTIALS: 'The provided credentials are incorrect. Please review and try again',
    INVALID_OTP: 'The OTP entered is invalid. Please check and try again.',
    EXPIRE_OTP: 'Your OTP has expired. Please request a new one.',
    FORGOT_CRED: 'Please verify the email address entered. If it is registered, an OTP will be sent to you.',
    INVALID_USER: 'The user does not exist. Please check your details.',
    INVALID_EMAIL: ' Please enter a valid email.',
    INVALID_FILE: 'Invalid file type. Only JPG, PNG, and PDF formats are accepted.',
    ALREADY_EXIST_ERROR: 'This user already exists. Please log in or recover your account.',
    REGISTRATION_COMMON_ERROR: 'An error occurred during registration. Please try again.',
    INVALID_OTP_TYPE: 'The OTP type is invalid. Please check and try again.',
    USER_NOT_FOUND: 'The user was not found. Please verify the details entered.',
    PAGE_NOT_FOUND: 'The page you are looking for does not exist.',
    LOGIN_COMMON_ERROR: 'An error occurred while logging in. Please try again.',
    INCORRECT_CURRENT_PASSWORD: 'The old password entered is incorrect. Please try again.',
    NEW_PASSWORD_SAME_AS_CURRENT_PASSWORD: 'The new password must be different from your current password.',
    PASSWORD_RECENTLY_USED: 'You have recently used this password. Please choose a different one.',
    OTP_REQUIRED: 'An OTP is required to proceed.',
    OTP_AND_SECRET_REQUIRED: 'Both OTP and secret are required to continue.',
    USER_ID_REQUIRED: 'Please provide your user ID.',
    VALIDATION_FAILED: 'Validation unsuccessful..',
    DATA_FETCH_ERROR: 'There was an error retrieving the data. Please try again later.',
    USER_UPDATION_ERROR: 'An error occurred while updating your profile. Please try again.',
    MOBILE_NO_EXIST: 'This mobile number is already linked to an existing account.',
    ALREADY_KYC_SUBMITTED: 'Your KYC has already been submitted. Please wait for admin verification.',
    ALREADY_EMAIL_VERIFIED: 'OTP-based sign-up is not permitted. Please use an alternative method to register.',
    ALREADY_KYC_APPROVED: 'Already Approved',
    USER_BLOCKED: 'Your account has been blocked by the admin.',
    UPDATE_FAILED: 'Failed to update the offering. Please try again.',
    KYC_PENDING: 'KYC verification is still pending.',
    ISSUER_PENDING: 'Your request to become an issuer approval is pending from the Admin.',
    WRONG_STEP: 'Data entry is only allowed for step 1 .',
    WALLET_NOT_CONNECTED: 'The requested wallet address is not connected.',
    START_DATE: "Start Date must be greater than or equal to today's date",
    CUSIP_ERR: 'The provided CUSIP is already in use. Please enter a unique CUSIP.',
    ID_ERR: 'The provided Id is already in use.',
    TOKEN_TICKER_ERR: 'The provided Token Ticker is already in use. Please enter a unique Token Ticker.',
    OFFERING_OWN_EMAIL: 'You cannot use your own email address',
    OFFERING_OWN_SUBS: 'You cannot Subscribed your own Offering',
  },

  SUCCESS_MSG: {
    REGISTER_SUCCESS: 'Congratulations! You have successfully registered. Please verify your Email/Mobile to continue enjoying our services.',
    DATA_SUCCESS: 'Data retrieved successfully.',
    DELETE_SUCCESS: 'Offering Deleted successfully.',
    EMAIL_VERIFIED_SUCCESS: 'Your email has been verified successfully.',
    CREATE_SUCCESS: 'Created successfully.',
    TRANSFER_REQ_CREATE_SUCCESS: 'Transfer request Created successfully !',
    TRANSFER_REQ_REJECTED_SUCCESS: 'Transfer request rejected successfully !',
    OFFERING_CREATE_SUCCESS: 'Offering created successfully',
    OFFERING_UPDATION_SUCCESS: 'Offering updated successfully',
    VOTING_SUCCESS: 'Your Voting request received Successfully',

    ORDER_CREATE_SUCCESS: 'Order Created successfully.',
    OTP_SENT_SUCCESS: 'OTP has been sent successfully.',
    LOGOUT: 'You have been logged out successfully.',
    PASSWORD_RESET_SUCCESS: 'Your password has been changed successfully.',
    DOCS_UPLOADED_SUCCESS: 'Documents uploaded successfully.',
    HEALTH_CHECK: 'Service is up and running smoothly.',
    KYC_APPROVED: 'Your KYC has been approved successfully.',
    KYC_REJECTED: 'Your KYC was rejected. Please review and resubmit your information.',
    FETCHED: 'Data has been fetched successfully.',
  },

  EMAIL: {
    FORGOT_PASSWORD_SUBJECT: 'Password Reset Request',
    FORGOT_PASSWORD_SUCCESS_SUBJECT: 'Password Reset Successfully.',
    FORGOT_PASSWORD_TEXT: 'We received a request to reset your password. Please follow the instructions to set a new password.',
    OTP_TEXT: 'Your Libertum OTP is:',
    OTP_SUBJECT: 'Your Libertum OTP Code',
    SIGN_UP_SUBJECT: 'Welcome to Libertum! Your Account is Ready',
    SIGN_IN_SUBJECT: 'Login Successful: Welcome Back to Libertum',
    MULTIPLE_LOGIN: 'Alert: Multiple Failed Login Attempts Detected',
    KYC_RECEIVED: 'Confirmation: Your KYC Submission Has Been Received',
    ORDER_MINTED: 'Your Token Request Has Been Approved and Minted',
    WHITELIST_REJECT: 'Update on Your Wallet Whitelist Request',
    TRANSFER_APPROVED: ' Good News! Your Token Transfer Request has Been Approved',
    TRANSFER_RJECTED: 'Your Token Transfer request was Rejected',
    KYB_RECEIVED: 'Confirmation: Your KYB Submission Has Been Received',
    KYC_APPROVED: 'Congratulations! Your KYC Has Been Approved',
    WALLET_WHITELIST: 'Your Wallet Has Been Successfully Whitelisted!',
    WALLET_WHITELIST_SUBMITTED: 'Your Wallet Whitelist Request Has Been Successfully Submitted!',
    ORDER_CREATE: 'Investment Confirmation',
    KYC_REJECTED: 'KYC Rejected: Please Review and Resubmit',
    USER_BLOCKED: 'Account Blocked due to KYC Rejection',
    OFFERING_DEPLOYED: 'Your Offering has Been Successfully Deployed',
    ORDER_REJECTED: 'Your Mint Order Request Has Been Rejected',
    ORDER_CANCELLED: 'Your Mint Order Request Has Been Cancelled',
    FORCE_TRANSFER: 'Notification of Force Transfer Initiated by Issuer',
  },
};
