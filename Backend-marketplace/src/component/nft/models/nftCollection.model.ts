import mongoose, { Schema, Types } from 'mongoose';
import { nftStatusEnum } from '../../../utils/common.interface';

export interface INftCollection {
  _id?: Types.ObjectId;
  collectionSymbol?: string;
  paymentMethod?: 'usdc' | 'usdt';
  description?: string;
  txHash?: string;
  collection?: string;
  owner?: string;
  stableCoin_?: string;
  userId?: Types.ObjectId;
  isDeploy: boolean;
  status?: nftStatusEnum;
  isActive?: boolean;
  isDelete?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

const NftCollection = new Schema<INftCollection>(
  {
    collectionSymbol: { type: String, required: true, unique: true },
    paymentMethod: { type: String, enum: ['usdc', 'usdt'], required: true },
    description: { type: String, required: true },
    txHash: { type: String, required: false, unique: true, sparse: true },
    collection: { type: String, required: false },
    owner: { type: String, required: false },
    stableCoin_: { type: String, required: false },
    userId: { type: Schema.Types.ObjectId, ref: 'users', required: true },
    status: { type: String, enum: Object.values(nftStatusEnum), default: nftStatusEnum.PENDING },
    isActive: { type: Boolean, default: false },
    isDeploy: { type: Boolean, default: false },
    isDelete: { type: Boolean, default: false },
  },
  { timestamps: true, versionKey: false },
);

const nftCollectionSchema = mongoose.model<INftCollection>('nftcollections', NftCollection);
export { nftCollectionSchema };
