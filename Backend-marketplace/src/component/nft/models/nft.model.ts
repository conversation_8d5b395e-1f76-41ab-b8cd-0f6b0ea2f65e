import { Types } from 'mongoose';
import { nftStatusEnum } from '../../../utils/common.interface';

export interface INft {
  _id?: Types.ObjectId;
  image: string;
  step: number;
  name: string;
  nftDescription?: string;
  externalLink: string;
  creatorId: Types.ObjectId;
  royalty: number;
  currency: 'usdc' | 'usdt';
  traits: string[];
  floorPrice: number;
  launchDate: Date;
  launchTime: string;
  category: string;
  type: string;
  team?: {
    name: string;
    title: string;
    summary?: string;
    url?: string;
    linkedInUrl?: string;
    twitterUrl?: string;
  }[];
  userId: Types.ObjectId;
  collectionId: Types.ObjectId;
  txHash: string;
  isActive: boolean;
  status?: nftStatusEnum;
  isDelete: boolean;
  isDeploy: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}
