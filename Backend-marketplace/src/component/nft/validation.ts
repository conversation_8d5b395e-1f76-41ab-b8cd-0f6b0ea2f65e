import { JoiValidationResult } from '../../utils/common.interface';
import * as joiOptions from '../../helpers/joi.helper';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import logger from '../../helpers/logging/logger.helper';
import * as Jo<PERSON> from 'joi';

class NftCollectionValidation {
  /**
   * Collection list Validate .
   * @returns {Promise<JoiValidationResult>}
   */
  async getNftCollectionValidation(params: Record<string, any>): Promise<JoiValidationResult> {
    try {
      const schema = joiOptions.paginationSchema; // Disallow any keys not explicitly defined in the schema

      const { error, value } = schema.validate(params, joiOptions.options);

      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message, // Provide specific error message
          status: RESPONSES.BAD_REQUEST,
        };
      }

      return {
        error: false,
        value, // Return the validated value
      };
    } catch (error) {
      logger.error(error, 'getNftCollectionValidation Error');

      return {
        error: true,
        value: '',
        message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, // General error message
        status: RESPONSES.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * Create Collection list Validate .
   * @returns {Promise<JoiValidationResult>}
   */
  async createNftCollectionValidation(params: Record<string, any>): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        collectionSymbol: Joi.string().min(3).max(500).required().messages({
          'string.base': 'Collection Symbol must be a string',
          'string.min': 'Collection Symbol must be at least 3 characters long',
          'string.max': 'Collection Symbol must be at most 100 characters long',
          'any.required': 'Collection Symbol is required',
        }),
        paymentMethod: Joi.string().valid('usdc', 'usdt').required(),
        description: Joi.string().min(3).max(500).required().messages({
          'string.base': 'description must be a string',
          'string.min': 'description must be at least 3 characters long',
          'string.max': 'description must be at most 100 characters long',
          'any.required': 'description is required',
        }),
      }).unknown(false); // Disallow extra keys

      const { error, value } = schema.validate(params, { abortEarly: false });

      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }

      return {
        error: false,
        value, // Return the validated value
      };
    } catch (error) {
      logger.error(error, 'getNftCollectionValidation Error');

      return {
        error: true,
        value: '',
        message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, // General error message
        status: RESPONSES.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * Create nft Validate .
   * @returns {Promise<JoiValidationResult>}
   */
  async createNftValidation(params: Record<string, any>): Promise<JoiValidationResult> {
    try {
      let schema = Joi.object();
      if (params?.step === 1) {
        schema = Joi.object({
          _id: Joi.string().hex().length(24).optional(),
          image: Joi.string().uri().required().messages({
            'string.base': 'Image URL must be a string',
            'string.uri': 'Invalid image URL format',
            'any.required': 'Image URL is required',
          }),
          name: Joi.string().min(3).max(500).required().messages({
            'string.base': 'Name must be a string',
            'string.min': 'Name must be at least 3 characters long',
            'string.max': 'Name must be at most 100 characters long',
            'any.required': 'Name is required',
          }),
          nftDescription: Joi.string().min(3).max(1000).required().messages({
            'string.base': 'Description must be a string',
            'string.min': 'Description must be at least 3 characters long',
            'string.max': 'Description must be at most 1000 characters long',
            'any.required': 'Description is required',
          }),
          externalLink: Joi.string().uri().allow(null, '').messages({
            'string.uri': 'Invalid external link URL format',
          }),
          creatorId: Joi.string().length(24).required().messages({
            'string.base': 'Creator ID must be a string',
            'string.hex': 'Invalid Creator ID format',
            'string.length': 'Creator ID must be a 24-character hex string',
            'any.required': 'Creator ID is required',
          }),
          royalty: Joi.number().min(0).max(100).required().messages({
            'number.base': 'Royalty must be a number',
            'number.min': 'Royalty cannot be negative',
            'number.max': 'Royalty cannot exceed 100%',
            'any.required': 'Royalty is required',
          }),
          currency: Joi.string().valid('usdc', 'usdt').required().messages({
            'any.only': 'Currency must be either usdc or usdt',
            'any.required': 'Currency is required',
          }),
          traits: Joi.array().items(Joi.string()).messages({
            'array.base': 'Traits must be an array of strings',
            'string.base': 'Each trait must be a string',
          }),
          floorPrice: Joi.number().min(0).required().messages({
            'number.base': 'Floor price must be a number',
            'number.min': 'Floor price cannot be negative',
            'any.required': 'Floor price is required',
          }),
          launchDate: Joi.date().iso().required().messages({
            'date.base': 'Launch date must be a valid date',
            'date.format': 'Launch date must be in ISO format',
            'any.required': 'Launch date is required',
          }),
          launchTime: Joi.string()
            .pattern(/^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$/)
            .required()
            .messages({
              'string.pattern.base': 'Launch time must be in HH:mm:ss format',
              'any.required': 'Launch time is required',
            }),
          category: Joi.string().required().messages({
            'string.base': 'Category must be a string',
            'any.required': 'Category is required',
          }),
          type: Joi.string().required().messages({
            'string.base': 'Type must be a string',
            'any.required': 'Type is required',
          }),
          collectionId: Joi.string().hex().length(24).required().messages({
            'string.base': 'Creator ID must be a string',
            'string.hex': 'Invalid Creator ID format',
            'string.length': 'Creator ID must be a 24-character hex string',
            'any.required': 'Creator ID is required',
          }),
          // team: Joi.array()
          //   .items(
          //     Joi.object({
          //       name: Joi.string().required().messages({ 'any.required': 'Team member name is required' }),
          //       title: Joi.string().required().messages({ 'any.required': 'Team member title is required' }),
          //       summary: Joi.string().allow(null, ''),
          //       url: Joi.string().uri().allow(null, ''),
          //       linkedInUrl: Joi.string().uri().allow(null, ''),
          //       twitterUrl: Joi.string().uri().allow(null, ''),
          //     }),
          //   )
          //   .optional(), // OPTIONAL in step 1
          step: Joi.number().min(1).max(2).required(),
        }).unknown(false);
      } else if (params?.step === 2) {
        schema = Joi.object({
          _id: Joi.string().hex().length(24).required(),
          team: Joi.array()
            .items(
              Joi.object({
                name: Joi.string().required().messages({ 'any.required': 'Team member name is required' }),
                title: Joi.string().required().messages({ 'any.required': 'Team member title is required' }),
                summary: Joi.string().allow(null, ''),
                url: Joi.string().uri().allow(null, ''),
                linkedInUrl: Joi.string().uri().allow(null, ''),
                twitterUrl: Joi.string().uri().allow(null, ''),
              }),
            )
            .required(), // REQUIRED in step 2
          step: Joi.number().min(1).max(2).required(),
        }).unknown(false);
      }

      const { error, value } = schema.validate(params, { abortEarly: false });
      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message,
          status: RESPONSES.BAD_REQUEST,
        };
      }

      return {
        error: false,
        value, // Return the validated value
      };
    } catch (error) {
      logger.error(error, 'createNftValidation Error');
      return {
        error: true,
        value: '',
        message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, // General error message
        status: RESPONSES.INTERNAL_SERVER_ERROR,
      };
    }
  }
}

export default new NftCollectionValidation();
