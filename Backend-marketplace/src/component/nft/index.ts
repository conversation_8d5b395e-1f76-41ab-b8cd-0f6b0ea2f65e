import { Request, Response } from 'express';
import { Types } from 'mongoose';
import { PromiseResolve } from '../../utils/common.interface';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { ResponseHandler } from '../../helpers/response.helper';
import logger from '../../helpers/logging/logger.helper';
import NftService from './service';
import CustomError from '../../helpers/customError.helper';

class NftCollectionController {
  /**
   * Handles fetching collection list with filters and pagination.
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise<void>}
   */
  public getCollection = async (req: Request, res: Response): Promise<PromiseResolve | void> => {
    try {
      const { page = 1, limit = 10, sort = '', search = '' } = req.query;
      const userId = new Types.ObjectId(req.userInfo.userId);

      const filters = { isActive: true, userId };

      const result: PromiseResolve = await NftService.fetchCollection(filters, { page: Number(page), limit: Number(limit), ...(search && { search }), ...(sort && { sort }) });

      return ResponseHandler.success(res, { status: result.status || RESPONSES.SUCCESS, error: false, message: result.message || RES_MSG.USER.USERS_FETCH, data: result.data || [] });
    } catch (error) {
      logger.error(error, 'getCollection Error');

      return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
    }
  };

  /**
   * Handles nft collection Creation.
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise<void>}
   */
  public createNftCollection = async (req: Request, res: Response): Promise<PromiseResolve | void> => {
    try {
      const userId = new Types.ObjectId(req.userInfo.userId);
      const result: PromiseResolve = await NftService.createCollection({ ...req.body, userId });

      return ResponseHandler.success(res, { status: result.status || RESPONSES.SUCCESS, error: false, message: result.message || RES_MSG.USER.USERS_FETCH, data: result.data || [] });
    } catch (error) {
      logger.error(error, 'getCollection Error');

      return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
    }
  };

  /**
   * Handles nft Creation.
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise<void>}
   */
  public createNft = async (req: Request, res: Response): Promise<PromiseResolve | void> => {
    try {
      const userId = new Types.ObjectId(req.userInfo.userId);
      const result: PromiseResolve = await NftService.createNft({
        ...req.body,
        userId,
        createdBy: userId,
        projectDetails: { assetType: 'NFT' },
        isNft: true,
      });

      return ResponseHandler.success(res, { status: result.status || RESPONSES.SUCCESS, error: false, message: result.message || RES_MSG.USER.USERS_FETCH, data: result.data || [] });
    } catch (error) {
      logger.error(error, 'getCollection Error');

      return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
    }
  };
  /**
   * @export
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise < PromiseResolve >}
   */

  public getNftDetails = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { id } = req.params;
      if (id && !Types.ObjectId.isValid(id)) {
        return ResponseHandler.error(res, { message: RES_MSG.COMMON.BAD_REQUEST, status: RESPONSES.BAD_REQUEST, error: true });
      }

      const nftId = new Types.ObjectId(id);
      const projection = [
        '_id',
        'userId',
        'projectDetails',
        'deployedDate',
        'isActive',
        'isDelete',
        'status',
        'createdBy',
        'image',
        'name',
        'nftDescription',
        'creatorId',
        'royalty',
        'traits',
        'floorPrice',
        'category',
        'type',
        'team',
        'isNft',
        'isSold',
        'purchaser',
        'createdAt',
        'updatedAt',
        'collectionDetails',
        'collectionId',
        'currency',
        'externalLink',

        'launchDate',
        'launchTime',
        'name',
      ];
      const { error, message, status, data } = await NftService.fetchNftDetails({ _id: nftId, isNft: true }, projection);

      if (error) throw new CustomError(message, status);

      return ResponseHandler.success(res, {
        status: status || RESPONSES.SUCCESS,
        error: false,
        message: message || RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data,
      });
    } catch (error: any) {
      logger.error(error, 'getNftDetails Error');

      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };
}

export default new NftCollectionController();
