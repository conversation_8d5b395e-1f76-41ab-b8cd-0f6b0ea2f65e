import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { PromiseResolve, IPagination, queueMessageTypeEnum, nftStatusEnum } from '../../utils/common.interface';
import { INftCollectionService } from './interface';
import logger from '../../helpers/logging/logger.helper';
import mongoose, { FilterQuery, Types } from 'mongoose';
import { nftCollectionSchema, INftCollection } from './models/nftCollection.model';
import { INft } from './models/nft.model';
import kafkaService from '../../service/kafkaService';
import CustomError from '../../helpers/customError.helper';
import { offeringSchema } from '../offerings/models/offerings.model';

class NftService implements INftCollectionService {
  /**
   * Fetch NFT collection with filtering and pagination
   * @param {FilterQuery<INftCollection>} filters
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   */
  fetchCollection = async (filters: FilterQuery<INftCollection>, pagination: IPagination): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, search } = pagination;
      const skip = (page - 1) * limit;
      const sort = { updatedAt: -1 };

      const match: FilterQuery<INftCollection> = {};

      if (search) {
        match.$or = [{ collectionSymbol: { $regex: search, $options: 'i' } }, { description: { $regex: search, $options: 'i' } }];
      }

      if (filters.paymentMethod) {
        match.paymentMethod = filters.paymentMethod;
      }
      if (filters.userId) {
        match.userId = filters.userId;
      }

      if (filters.isActive !== undefined) {
        match.isActive = filters.isActive;
      }

      if (filters.isDelete !== undefined) {
        match.isDelete = filters.isDelete;
      }

      const pipeline = [
        { $match: match },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'userDetails',
          },
        },
        { $unwind: { path: '$userDetails', preserveNullAndEmptyArrays: true } },
        {
          $addFields: {
            userName: '$userDetails.name',
            userEmail: '$userDetails.email',
            userImage: '$userDetails.userImage',
          },
        },
        { $project: { userDetails: 0 } },
        {
          $facet: {
            metadata: [{ $count: 'totalCount' }],
            collections: [{ $skip: skip }, { $limit: limit }, { $sort: sort }],
          },
        },
      ];

      const results = await nftCollectionSchema.aggregate(pipeline as any);
      const metadata = results[0]?.metadata[0] || { totalCount: 0 };
      const collections = results[0]?.collections || [];
      const { totalCount } = metadata;
      const totalPages = Math.ceil(totalCount / limit);
      const nextPage = page < totalPages ? page + 1 : null;
      const previousPage = page > 1 ? page - 1 : null;

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.COMMON.RECORD_FETCH,
        data: {
          collections,
          currentPage: page,
          totalPages,
          totalCount,
          nextPage,
          previousPage,
        },
      };
    } catch (error) {
      logger.error(error, 'fetchCollection error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  /**
   * @param {I} data
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  createCollection = async (data: INftCollection): Promise<PromiseResolve> => {
    try {
      const query = await nftCollectionSchema.create(data);
      if (query) {
        const finalData = JSON.parse(JSON.stringify(query));
        await kafkaService.sendMessageToAdmin({ value: { ...finalData, type: queueMessageTypeEnum.NFT_COLLECTION } });
        return { status: RESPONSES.CREATED, error: false, message: RES_MSG.USER.CREATED_SUCCESS, data: query };
      }

      return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.COMMON.SOMETHING_WRONG };
    } catch (error) {
      logger.error(error, 'createCollection error');
      if (error.code === 11000) {
        return {
          status: RESPONSES.CONFLICT, // 409 Conflict
          error: true,
          message: `Collection with symbol '${data.collectionSymbol}' already exists. Please choose a different symbol.`,
        };
      }
      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * @param {I} data
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  createNft = async (data: INft): Promise<PromiseResolve> => {
    try {
      if (data?._id) {
        const existing: any = await offeringSchema.findOne({ _id: data._id, isNft: true });
        if (!existing) {
          throw new CustomError('NFT not found', RESPONSES.NOTFOUND);
        } else if (existing?.status === nftStatusEnum.PENDING) {
          throw new CustomError('NFT is currently pending approval and cannot be updated.', RESPONSES.FORBIDDEN);
        } else if (existing?.status === nftStatusEnum.APPROVED) {
          throw new CustomError('Approved NFTs cannot be modified.', RESPONSES.FORBIDDEN);
        }

        const updated: any = await offeringSchema.findOneAndUpdate({ _id: data._id }, { $set: { ...data, status: data.step === 1 ? nftStatusEnum.IN_PROGRESS : nftStatusEnum.PENDING } }, { new: true });
        if (updated?.status === nftStatusEnum.PENDING) {
          const finalData = JSON.parse(JSON.stringify(updated));
          await kafkaService.sendMessageToAdmin({ value: { ...finalData, nftType: finalData.type, type: queueMessageTypeEnum.NFT } });
        }
        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.USER.CREATED_SUCCESS,
          data: updated,
        };
      } else if (data.step === 1) {
        const isCollectionExist = await nftCollectionSchema.findById(data.collectionId);
        if (!isCollectionExist) {
          throw new CustomError('Invalid collection Id', RESPONSES.NOTFOUND);
        }

        const created = await offeringSchema.create({
          ...data,
          status: nftStatusEnum.IN_PROGRESS,
          isNft: true,
        });

        return {
          status: RESPONSES.CREATED,
          error: false,
          message: RES_MSG.USER.CREATED_SUCCESS,
          data: created,
        };
      }

      throw new CustomError('Invalid step', RESPONSES.BAD_REQUEST);
    } catch (error) {
      logger.error(error, 'createNft error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  /**
   * Update NFT Collection by _id
   * @param {string} collectionId
   * @param {Partial<INftCollection>} updateData
   * @returns {Promise<PromiseResolve>}
   */
  updateCollection = async (collectionId: any, updateData: Partial<INftCollection>): Promise<PromiseResolve> => {
    try {
      const _id = new mongoose.Types.ObjectId(collectionId?._id);
      if (!Types.ObjectId.isValid(_id)) {
        return {
          status: RESPONSES.BAD_REQUEST,
          error: true,
          message: 'Invalid Collection ID',
        };
      }
      const updatedCollection = await nftCollectionSchema.findByIdAndUpdate(_id, { $set: updateData }, { new: true });
      if (!updatedCollection) {
        return {
          status: RESPONSES.NOTFOUND,
          error: true,
          message: 'NFT Collection not found',
        };
      }
      const finalData = JSON.parse(JSON.stringify(updatedCollection));
      await kafkaService.sendMessageToAdmin({ value: { ...finalData, type: queueMessageTypeEnum.NFT_COLLECTION } });
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: 'NFT Collection updated successfully',
        data: updatedCollection,
      };
    } catch (error) {
      logger.error(error, 'updateCollection error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  /**
   * Update NFT Collection by _id
   * @param {string} collectionId
   * @param {Partial<INftCollection>} updateData
   * @returns {Promise<PromiseResolve>}
   */
  updateNft = async (nftId: string, updateData: Partial<INftCollection>): Promise<PromiseResolve> => {
    try {
      if (!Types.ObjectId.isValid(nftId)) {
        return {
          status: RESPONSES.BAD_REQUEST,
          error: true,
          message: 'Invalid NFT ID',
        };
      }

      const updatedCollection = await offeringSchema.findByIdAndUpdate(nftId, { $set: updateData }, { new: true });

      if (!updatedCollection) {
        return {
          status: RESPONSES.NOTFOUND,
          error: true,
          message: 'NFT not found',
        };
      }
      const finalData = JSON.parse(JSON.stringify(updatedCollection));
      await kafkaService.sendMessageToAdmin({ value: { ...finalData, type: queueMessageTypeEnum.NFT } });
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: 'NFT  updated successfully',
        data: updatedCollection,
      };
    } catch (error) {
      logger.error(error, 'updateNft error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  updateNftStatus = async (data: any): Promise<PromiseResolve> => {
    try {
      // update nft status
      const deployedDate = new Date().toISOString();

      const updatedNft = await offeringSchema.findByIdAndUpdate({ _id: data }, { $set: { status: 'APPROVED', projectDetails: { assetType: 'NFT' }, deployedDate: deployedDate } }, { new: true });
      if (!updatedNft) {
        return {
          status: RESPONSES.NOTFOUND,
          error: true,
          message: 'NFT not found',
        };
      }

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: 'NFT status updated successfully',
      };
    } catch (error) {
      logger.error(error, 'createOffering error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * @param {IUpdateOffering} searchDetails
   * @param {IPagination} fields
   * @param {IPagination} excludeFields
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  fetchNftDetails = async (searchDetails: FilterQuery<INft>, fields?: string[], excludeFields?: string[]): Promise<PromiseResolve> => {
    try {
      const projection: Record<string, any> = {};

      // Include specific fields if provided
      if (fields && fields.length > 0) {
        fields.forEach((field) => (projection[field] = 1));
      }

      // Exclude specific fields if provided
      if (excludeFields && excludeFields.length > 0) {
        excludeFields.forEach((field) => (projection[field] = 0));
      }
      const pipeline: any = [
        { $match: searchDetails },
        {
          $lookup: {
            from: 'nftcollections',
            localField: 'collectionId',
            foreignField: '_id',
            as: 'collection',
          },
        },
        { $unwind: { path: '$collection', preserveNullAndEmptyArrays: true } },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'user',
          },
        },
        { $unwind: { path: '$user', preserveNullAndEmptyArrays: true } },
        {
          $project: {
            ...projection,
            collection: 1,
            userDetails: {
              name: '$user.name',
              email: '$user.email',
              userImage: '$user.userImage',
            },
          },
        },
      ];

      const nftDetails = await offeringSchema.aggregate(pipeline).exec();
      if (nftDetails.length > 0) {
        return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: nftDetails[0] };
      }

      return { status: RESPONSES.NOTFOUND, error: true, message: RES_MSG.COMMON.NO_RECORD };
    } catch (error: any) {
      logger.error(error, 'fetchNftDetails error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };
}

export default new NftService();
