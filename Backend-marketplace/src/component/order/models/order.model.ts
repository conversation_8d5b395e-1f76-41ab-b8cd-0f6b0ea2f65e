import mongoose, { Schema, Document, Types } from 'mongoose';
import { orderStatusEnum, paymentTypeEnum } from '../../../utils/common.interface';

export interface IOrder extends Document {
  userId?: Types.ObjectId | string;
  offeringId?: Types.ObjectId | string;
  walletAddress?: string;
  amount?: string;
  amountBeforeFee?: string;
  status?: orderStatusEnum;
  isMinted?: boolean;
  feesInPercentage?: string;
  isSettled?: boolean;
  isFreezed?: boolean;
  price?: string;
  currentPrice?: string;
  adminFeeAmount?: string;
  orderReceived?: Date;
  orderMinted?: Date;
  quantity?: string;
  txHash?: string;
  collectionAddress?: string;
  mintTxHash?: string;
  emailSent?: boolean;
  mintEmailSent?: boolean;
  paymentMethod?: paymentTypeEnum;
  orderType?: string;
  principleAmount?: string;
  wap?: string;
  profit?: string;
  reason?: string;
}

export interface IUpdateOrder {
  _id?: string;
  userId?: Types.ObjectId | string;
  offeringId?: Types.ObjectId | string;
  walletAddress?: string;
  amount?: string;
  amountBeforeFee?: string;
  status?: orderStatusEnum;
  isMinted?: boolean;
  feesInPercentage?: string;
  isSettled?: boolean;
  isFreezed?: boolean;
  price?: string;
  currentPrice?: string;
  adminFeeAmount?: string;
  orderReceived?: Date;
  orderMinted?: Date;
  quantity?: string;
  txHash?: string;
  collectionAddress?: string;
  mintTxHash?: string;
  emailSent?: boolean;
  mintEmailSent?: boolean;
  paymentMethod?: paymentTypeEnum;
  orderType?: string;
  principleAmount?: string;
  wap?: string;
  profit?: string;
  reason?: string;
}

const order: Schema<IOrder> = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, ref: 'users', required: true },
    offeringId: { type: Schema.Types.ObjectId, ref: 'offerings', required: true },
    walletAddress: { type: String, required: false },
    amount: { type: String, required: false },
    amountBeforeFee: { type: String, required: false },
    isMinted: { type: Boolean, required: false, default: false },
    isSettled: { type: Boolean, required: false, default: false },
    isFreezed: { type: Boolean, required: false, default: false },
    feesInPercentage: { type: String, required: false },
    quantity: { type: String, required: false },
    price: { type: String, required: false },
    currentPrice: { type: String, required: false },
    adminFeeAmount: { type: String, required: false },
    status: { type: String, enum: Object.values(orderStatusEnum), default: orderStatusEnum.PENDING },
    emailSent: { type: Boolean, default: false },
    mintEmailSent: { type: Boolean, default: false },
    orderReceived: { type: Date, required: false },
    orderMinted: { type: Date, required: false },
    paymentMethod: { type: String, enum: Object.values(paymentTypeEnum), default: paymentTypeEnum.USDT },
    txHash: { type: String, required: false, unique: true, sparse: true },
    collectionAddress: { type: String, required: false, unique: false, sparse: true },
    mintTxHash: { type: String, required: false, unique: false, sparse: true, default: null },
    orderType: { type: String, required: false, default: orderStatusEnum.MINTED },
    principleAmount: { type: String, required: false },
    wap: { type: String, required: false },
    profit: { type: String, required: false },
    reason: { type: String, required: false },
  },

  { timestamps: true, versionKey: false },
);

order.pre<IOrder>('save', async function (next) {
  // If txHash is null, skip the uniqueness check
  if (!this.txHash) {
    return next();
  }

  // Check if a document with the same txHash exists (excluding current document)
  const existingOrder = await mongoose.models.order.findOne({ txHash: this.txHash });
  if (existingOrder) {
    // Prevent insertion if a duplicate txHash is found
    return next(new Error('Duplicate txHash: This transaction hash already exists.'));
  }

  // Proceed with saving the document
  next();
});

order.pre<IOrder>('insertMany', async function (next, docs) {
  for (const doc of docs) {
    if (!doc.txHash) continue;

    const existingOrder = await mongoose.models.order.findOne({ txHash: doc.txHash });
    if (existingOrder) {
      return next(new Error(`Duplicate txHash: ${doc.txHash} already exists.`));
    }
  }
  next();
});

const OrderSchema = mongoose.model<IOrder>('order', order);
export { OrderSchema };
