import { Request, Response } from 'express';
import orderService from './service';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import { ResponseHandler } from '../../helpers/response.helper';
import logger from '../../helpers/logging/logger.helper';
import CustomError from '../../helpers/customError.helper';
import { orderStatusEnum, PromiseResolve } from '../../utils/common.interface';

export default class Order {
  /**
   * Creates a new order for a user
   * @param req - request object
   * @param res - response object
   * @returns {Promise<void | PromiseResolve>}
   */

  static createOrder = async (req: Request, res: Response): Promise<void | PromiseResolve> => {
    try {
      const orderData = req.body;
      const newOrder = await orderService.createOrder({ ...orderData, orderType: 'MINTED' }, req.userInfo.userId);
      if (!newOrder) {
        throw new Error('Failed to create Order');
      }

      return ResponseHandler.success(res, { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.ORDER_CREATE_SUCCESS, data: newOrder });
    } catch (error: any) {
      logger.error(error, 'Error while creating order');

      return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
    }
  };

  /**
   * Gets orders by user with pagination
   * @param req - request object
   * @param res - response object
   * @returns {Promise<void>}
   */
  static getOrdersByUser = async (req: Request, res: Response) => {
    try {
      // Page and limit are validated in the middleware, so no need for further conversion here
      const { page, limit, status, search, isCsv } = req.query;
      const orders = await orderService.getOrdersByUser(req.userInfo.userId, Number(page), Number(limit), status as string, search as string, isCsv);

      return ResponseHandler.success(res, { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: orders });
    } catch (error: any) {
      logger.error(error, 'Error while fetching orders by user');

      return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
    }
  };

  /**
   * Generates a CSV file with orders by user with pagination
   * @param req - request object
   * @param res - response object
   * @returns {Promise<void>}
   */
  static getOrdersInCsv = async (req: Request, res: Response) => {
    try {
      // Page and limit are validated in the middleware, so no need for further conversion here
      const { data, error, message, status } = await orderService.getOrdersInCsv(req.userInfo.userId, req.query as any);

      return ResponseHandler.success(res, { status, error, message, data });
    } catch (error: any) {
      logger.error(error, 'Error while fetching orders by user');

      return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
    }
  };

  // Get orders by user with pagination
  static getTopHoldings = async (req: Request, res: Response) => {
    try {
      // Page and limit are validated in the middleware, so no need for further conversion here
      const { page, limit } = req.query;
      const orders = await orderService.getTopHolding(req.userInfo.userId, Number(page), Number(limit));

      return ResponseHandler.success(res, { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: orders });
    } catch (error: any) {
      logger.error(error, 'Error while fetching orders by user');

      return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
    }
  };

  /**
   * Fetches portfolio performance by user
   * @param req - request object
   * @param res - response object
   * @returns {Promise<void>}
   */
  static portfolioPerformance = async (req: Request, res: Response) => {
    try {
      // Page and limit are validated in the middleware, so no need for further conversion here
      const { period } = req.query;
      const orders = await orderService.portfolioPerformance(req.userInfo.userId, period as string);

      return ResponseHandler.success(res, { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: orders });
    } catch (error: any) {
      logger.error(error, 'Error while fetching orders by user');

      return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
    }
  };

  /**
   * Fetches an order by its ID
   * @param req - request object
   * @param res - response object
   * @returns {Promise<void>}
   */
  static getOrderById = async (req: Request, res: Response) => {
    try {
      const { orderId } = req.params;
      const order = await orderService.getOrderById(orderId, req.userInfo.userId);

      if (!order) {
        // Order not found
        return ResponseHandler.error(res, { status: RESPONSES.NOTFOUND, error: true, message: RES_MSG.ERROR_MSG.ORDER_NOT_FOUND });
      }

      return ResponseHandler.success(res, { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: order });
    } catch (error: any) {
      logger.error(error, 'Error while fetching order by ID');

      return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
    }
  };

  /**
   * Updates orders
   * @param req - request object
   * @param res - response object
   * @returns {Promise<void>}
   */
  static rejectOrder = async (req: Request, res: Response) => {
    try {
      const { reason, orderId } = req.body;

      const orderIds = Array.isArray(orderId) ? orderId : [orderId];

      const updateResponse = await orderService.updateOrderDetails({ reason, status: orderStatusEnum.REJECTED }, { _id: orderIds });

      if (updateResponse.error) {
        throw new CustomError(updateResponse.message, updateResponse.status);
      }

      return ResponseHandler.success(res, { status: updateResponse.status || RESPONSES.SUCCESS, error: false, message: updateResponse.message || RES_MSG.SUCCESS_MSG.DATA_SUCCESS });
    } catch (error: any) {
      logger.error(error, 'Error while updating orders');

      return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
    }
  };
}
