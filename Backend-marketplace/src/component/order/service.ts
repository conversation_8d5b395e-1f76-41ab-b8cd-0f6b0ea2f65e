import { Types } from 'mongoose';
// import * as moment from 'moment';
import { IOrder, IUpdateOrder, OrderSchema } from './models/order.model';
import OfferingService from '../offerings/service';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import logger from '../../helpers/logging/logger.helper';
import { AssetTypeEnum, orderStatusEnum, PromiseResolve, queueMessageTypeEnum } from '../../utils/common.interface';
import kafkaService from '../../service/kafkaService';
import { userSchema } from '../userAuthentications/models/user.model';
import { offeringSchema } from '../offerings/models/offerings.model';
import CommonHelper from '../../helpers/common.helper';
import CustomError from '../../helpers/customError.helper';
import emailHelper from '../../helpers/email.helper';
import { UserDetailsSchema } from '../userAuthentications/models/userDetails.model';
import UserService from '../userAuthentications/service';
import { calculate } from '../../helpers/bigMath';

class OrderService {
  /**
   * Creates a new order for the given offering and user.
   * @param {IOrder} order - The order details.
   * @param {string} _userId - The ID of the user creating the order.
   * @returns {Promise<any>} - The created order.
   */
  createOrder = async (order: IUpdateOrder | any, _userId: any): Promise<any> => {
    try {
      const offeringId = order?.offeringId && typeof order?.offeringId === 'string' ? new Types.ObjectId(order?.offeringId) : order?.offeringId;
      const userId = new Types.ObjectId(_userId);
      const data = await UserService.fetchUserDetails({ _id: userId });
      const { email, name } = data.data;
      const subscribeDetails = await OfferingService.fetchSubscriberDetails({ offeringId, userId });
      const { data: offeringDetails, error, message } = await OfferingService.fetchOfferingDetails({ _id: offeringId });

      const isOrderApproved = await OrderSchema.findOne({
        userId,
        offeringId,
        orderType: orderStatusEnum.MINTED,
        status: {
          $in: [
            orderStatusEnum.APPROVED,
            // orderStatusEnum.PENDING
          ],
        },
      });
      if (!order?.status && isOrderApproved) {
        throw new CustomError('Previous order is already pending!', RESPONSES.CONFLICT);
      }
      const tokenDecimals = offeringDetails?.projectDetails?.tokenDecimals;
      const totalSupply = offeringDetails?.projectDetails?.tokenSupply;
      let latestNav;
      if (offeringDetails?.projectDetails?.assetType === AssetTypeEnum.Equity) {
        latestNav = offeringDetails?.projectDetails?.previousValuation;
      } else {
        latestNav = offeringDetails?.projectDetails?.latestNav;
      }
      const price = calculate('div', latestNav, totalSupply, tokenDecimals);
      const feesInPercentage = offeringDetails?.fee?.escrowFee || 0;
      const amountBeforeFee = order?.amount || calculate('mul', order?.quantity, price, tokenDecimals);

      let feeAmount;
      let amountAfterFee = amountBeforeFee;

      if (order?.orderType === 'MINTED') {
        const fee = calculate('div', feesInPercentage, 100, tokenDecimals);
        feeAmount = calculate('mul', amountBeforeFee, calculate('div', fee, 100, tokenDecimals), tokenDecimals);
        amountAfterFee = calculate('sub', amountBeforeFee, feeAmount, tokenDecimals);
      }
      const quantity = order?.quantity || calculate('div', amountAfterFee, price, tokenDecimals); // don't move this line

      if (order?.orderType == 'FREEZE') {
        const emailDetail = {
          event: 'Frozen',
          quantity,
          name,
          offeringName: offeringDetails?.offeringName,
          tokenTicker: offeringDetails?.tokenTicker,
          date: new Date().toLocaleString('en-US', {
            timeZone: 'UTC',
          }),
        };
        emailHelper.sendEmailTemplate(email, 'userTokens', emailDetail);
      } else if (order?.orderType == 'UNFREEZE') {
        const emailDetail = {
          event: 'Unfrozen',
          quantity,
          name,
          offeringName: offeringDetails?.offeringName,
          tokenTicker: offeringDetails?.tokenTicker,
          date: new Date().toLocaleString('en-US', {
            timeZone: 'UTC',
          }),
        };
        emailHelper.sendEmailTemplate(email, 'userTokens', emailDetail);
      } else if (order?.orderType == 'CONVERT') {
        const emailDetail = {
          event: 'Converted',
          quantity,
          name,
          offeringName: offeringDetails?.offeringName,
          tokenTicker: offeringDetails?.tokenTicker,
          date: new Date().toLocaleString('en-US', {
            timeZone: 'UTC',
          }),
        };
        emailHelper.sendEmailTemplate(email, 'userTokens', emailDetail);
      }

      const orderData = {
        ...order,
        userId,
        offeringId,
        price,
        currentPrice: price,
        quantity,
        walletAddress: subscribeDetails?.data?.address?.toLowerCase(),
        feesInPercentage,
        amountBeforeFee,
        feeAmount,
        amount: amountAfterFee,
        adminFeeAmount: feeAmount,
      };

      if (error) {
        throw new Error(message);
      } else if (subscribeDetails.error) {
        throw new Error('Error while fetching SubscriberDetails!');
      } else if (subscribeDetails?.data?.status !== 'APPROVED') {
        throw new Error('Wallet not whitelisted for the requested offering!');
      } else if (_userId.toString() === offeringDetails?.data?.userId.toString()) {
        throw new Error('Issuer can not place order for his own created offering!');
      } else {
        let res;
        if (order?._id) {
          // Check if order is pending before updating
          const existingOrder = await OrderSchema.findById(order._id);
          if (!existingOrder) {
            throw new Error('Order not found!');
          }
          if (existingOrder.status !== orderStatusEnum.PENDING) {
            throw new Error('Only pending orders can be edited!');
          }
          res = await OrderSchema.findByIdAndUpdate(order._id, orderData, { new: true });
        } else {
          const newOrder = new OrderSchema(orderData);
          res = await newOrder.save();
        }
        const finalData = JSON.parse(JSON.stringify(res));

        await kafkaService.sendMessageToAdmin({ value: { ...finalData, type: queueMessageTypeEnum.ORDER } });

        //send message to notification

        // await kafkaService.sendMessageToNotification({
        //   value: {
        //     type: notificationEnum.SUBSCRIBE,
        //     details: { ...finalData },
        //   },
        // });

        return finalData;
      }
    } catch (error) {
      logger.error(error, 'createOrder error');
      throw new Error(error.message);
    }
  };

  /**
   * Get orders by user ID and other query parameters.
   * @param {string} userId - The ID of the user.
   * @param {number} page - The page number for pagination.
   * @param {number} limit - The number of records per page.
   * @param {string} status - The status of the order to filter.
   * @param {string} search - The search query to filter by offering name.
   * @returns {Promise<any>} - The orders and pagination details.
   */
  getOrdersByUser = async (userId: string, page: number, limit: number, status: string, search: string, isCsv: any) => {
    try {
      const skip = (page - 1) * limit;
      const ordersPipeline = [
        { $match: { userId: new Types.ObjectId(userId), orderType: status ? status?.toUpperCase() : orderStatusEnum.MINTED } },
        { $lookup: { from: 'users', localField: 'userId', foreignField: '_id', as: 'userDetails' } },
        { $unwind: { path: '$userDetails', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: 'offerings', localField: 'offeringId', foreignField: '_id', as: 'offeringDetails' } },
        { $unwind: { path: '$offeringDetails', preserveNullAndEmptyArrays: true } },
        {
          $project: {
            userId: 1,
            offeringId: 1,
            amount: 1,
            status: 1,
            isMinted: 1,
            isSettled: 1,
            feesInPercentage: 1,
            quantity: 1,
            txHash: 1,
            mintTxHash: 1,
            walletAddress: 1,
            paymentMethod: 1,
            amountBeforeFee: 1,
            adminFeeAmount: 1,
            createdAt: 1,
            tokenIcon: '$offeringDetails.overview.icon',
            assetType: '$offeringDetails.projectDetails.assetType',
            offeringName: '$offeringDetails.projectDetails.offeringName',
            tokenTicker: '$offeringDetails.projectDetails.tokenTicker',
            tokenAddress: '$offeringDetails.tokenAddress',
            fundAddress: '$offeringDetails.fundAddress',
            tokenDecimals: '$offeringDetails.projectDetails.tokenDecimals',
          },
        },
        // { $match: { ...(status ? { status: status.toUpperCase() } : {}) } },
        // Search by offering name if search is provided
        ...(search ? [{ $match: { offeringName: { $regex: search, $options: 'i' } } }] : []),
        { $sort: { createdAt: -1 } },
        {
          $facet: {
            orders: isCsv ? [] : [{ $skip: skip }, { $limit: limit }], // Pagination for orders
            totalCount: [{ $count: 'count' }], // Get total count of documents
          },
        },
      ];

      const result = await OrderSchema.aggregate(ordersPipeline as []).exec();

      const totalCount = result[0]?.totalCount[0]?.count || 0;
      const orders = result[0]?.orders || [];

      // Calculate total pages
      const totalPages = Math.ceil(totalCount / limit);

      // Calculate nextPage and previousPage
      const nextPage = page < totalPages ? page + 1 : null;
      const previousPage = page > 1 ? page - 1 : null;

      return { orders, currentPage: page, totalPages, totalCount, nextPage, previousPage };
    } catch (error) {
      logger.error(error, 'getOrdersByUser');
      throw new Error(error.message);
    }
  };

  /**
   * Retrieves orders in CSV format.
   * @param {string} userId - The ID of the user.
   * @param {string} startDate - The start date of the date range (inclusive).
   * @param {string} endDate - The end date of the date range (inclusive).
   * @returns {Promise<Object>} - The orders in CSV format or an error message.
   */
  getOrdersInCsv = async (userId: string, query: { endDate: string; startDate: string }) => {
    try {
      const { startDate, endDate } = query;
      const ordersPipeline = [
        {
          $match: { userId: new Types.ObjectId(userId), orderType: orderStatusEnum.MINTED }, // Match by userId initially
        },
        { $lookup: { from: 'users', localField: 'userId', foreignField: '_id', as: 'userDetails' } },
        { $unwind: { path: '$userDetails', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: 'offerings', localField: 'offeringId', foreignField: '_id', as: 'offeringDetails' } },
        { $unwind: { path: '$offeringDetails', preserveNullAndEmptyArrays: true } },
        {
          $project: {
            userId: 1,
            offeringId: 1,
            amount: 1,
            status: 1,
            isMinted: 1,
            isSettled: 1,
            feesInPercentage: 1,
            quantity: 1,
            txHash: 1,
            mintTxHash: 1,
            walletAddress: 1,
            paymentMethod: 1,
            createdAt: 1,
            offeringIcon: '$offeringDetails.overview.icon',
            assetType: '$offeringDetails.projectDetails.assetType',
            offeringName: '$offeringDetails.projectDetails.offeringName',
            tokenTicker: '$offeringDetails.projectDetails.tokenTicker',
          },
        },
        { $sort: { createdAt: -1 } },
      ];

      // Conditionally add date filters (startDate and endDate) if they are present in the query
      if (startDate || endDate) {
        const dateMatch: any = {}; // Initialize an object to hold date filter conditions

        // Add startDate filter if it's provided
        if (startDate) {
          dateMatch.createdAt = { $gte: new Date(startDate) };
        }

        // Add endDate filter if it's provided
        if (endDate) {
          dateMatch.createdAt = { ...dateMatch.createdAt, $lte: new Date(endDate) };
        }

        // If date filters exist, prepend the $match stage with the date conditions
        ordersPipeline.unshift({
          $match: {
            ...ordersPipeline[0].$match, // Keep the original userId match
            ...dateMatch, // Add the date range filters
          },
        });
      }

      const orders = await OrderSchema.aggregate(ordersPipeline as []).exec();

      if (orders) {
        return { status: RESPONSES.SUCCESS, error: false, message: 'Order details found successfully', data: { orders } };
      }
    } catch (error) {
      logger.error(error, 'getOrdersInCsv');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * Retrieves top holdings of a user.
   * @param {string} userId - The ID of the user.
   * @param {number} page - The page number.
   * @param {number} limit - The number of items per page.
   * @returns {Promise<Object>} - The top holdings of the user.
   */
  getTopHolding = async (userId: string, page: number, limit: number) => {
    try {
      const skip = (page - 1) * limit;

      const ordersPipeline: any = [
        {
          $match: {
            userId: new Types.ObjectId(userId),
            orderType: { $ne: 'NFT' },
            status: {
              $in: ['MINTED', 'BURN', 'REDEEM', 'TRANSFER_FROM', 'TRANSFER_TO', 'CONVERT_TO', 'CONVERT_FROM'],
              // $in: ['MINTED', 'BURN', 'REDEEM', 'TRANSFER_FROM', 'TRANSFER_TO', 'CONVERT_TO', 'CONVERT_FROM', 'PRICE_CHANGE', 'INITIAL_ORDER'],
            },
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'userDetails',
          },
        },
        {
          $unwind: {
            path: '$userDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'offerings',
            localField: 'offeringId',
            foreignField: '_id',
            as: 'offeringDetails',
          },
        },
        {
          $unwind: {
            path: '$offeringDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $group: {
            _id: '$offeringId',
            totalAmount: {
              $sum: {
                $cond: {
                  if: { $in: ['$status', ['MINTED', 'TRANSFER_TO', 'CONVERT_TO']] },
                  then: { $multiply: [{ $toDecimal: '$quantity' }, { $toDecimal: '$currentPrice' }] },
                  else: {
                    $multiply: [{ $multiply: [{ $toDecimal: '$quantity' }, { $toDecimal: '$currentPrice' }] }, -1],
                  },
                },
              },
            }, // Convert amount to Decimal128
            // totalQuantity: { $sum: { $toDecimal: '$quantity' } }, // Convert quantity to Decimal128
            userId: { $first: '$userId' },
            createdAt: { $first: '$createdAt' },
            offeringDetails: { $first: '$offeringDetails' },
          },
        },
        {
          $project: {
            userId: 1,
            offeringId: '$_id',
            valuation: { $toString: { $toDecimal: '$totalAmount' } }, // Convert to string
            // quantity: { $toString: { $toDecimal: '$totalQuantity' } }, // Convert to string
            offeringIcon: '$offeringDetails.overview.icon',
            assetType: '$offeringDetails.projectDetails.assetType',
            offeringName: '$offeringDetails.projectDetails.offeringName',
            tokenTicker: '$offeringDetails.projectDetails.tokenTicker',
          },
        },
        {
          $group: {
            _id: null,
            totalValuation: { $sum: { $toDecimal: '$valuation' } }, // Convert total valuation to Decimal128
            items: { $push: '$$ROOT' },
          },
        },
        { $unwind: '$items' },
        {
          $addFields: {
            'items.allocationPercentage': {
              $toString: {
                $round: [
                  {
                    $cond: {
                      if: { $eq: [{ $toDecimal: '$totalValuation' }, 0] }, // Check if totalValuation is 0
                      then: 0, // Avoid division by zero
                      else: {
                        $divide: [{ $toDecimal: '$items.valuation' }, { $toDecimal: '$totalValuation' }],
                      },
                    },
                  },
                  4, // Round to 4 decimal places
                ],
              },
            },
            'items.totalValuation': { $toString: { $toDecimal: '$totalValuation' } },
          },
        },
        { $replaceRoot: { newRoot: '$items' } },
        { $sort: { valuation: -1 } },
        {
          $facet: {
            orders: [{ $skip: skip }, { $limit: limit }], // Pagination for orders
            totalCount: [{ $count: 'count' }], // Get total count of documents
          },
        },
      ];

      const result = await OrderSchema.aggregate(ordersPipeline as []).exec();
      const totalCount = result[0]?.totalCount[0]?.count || 0;
      const orders = result[0]?.orders || [];

      // Calculate total pages
      const totalPages = Math.ceil(totalCount / limit);

      // Calculate nextPage and previousPage
      const nextPage = page < totalPages ? page + 1 : null;
      const previousPage = page > 1 ? page - 1 : null;

      return { orders, currentPage: page, totalPages, totalCount, nextPage, previousPage };
    } catch (error) {
      logger.error(error, 'getTopHolding');

      throw new Error(error.message);
    }
  };

  /**
   * Retrieves the portfolio performance data for a given user and period
   * @param userId User ID
   * @param period Time period for the data (1Y, 2Y, 3Y, 1M, 15D, 7D, or 1D)
   * @returns Portfolio performance data
   */

  portfolioPerformance = async (userId: string, period: string) => {
    try {
      const { startDate, endDate } = CommonHelper.getDateRange(period);

      const pipeline: any = [
        {
          $facet: {
            // ✅ Get transactions within the date range
            mainData: [
              {
                $match: {
                  userId: new Types.ObjectId(userId),
                  createdAt: { $gte: new Date(startDate * 1000), $lte: new Date(endDate * 1000) },
                  status: {
                    $in: ['MINTED', 'BURN', 'REDEEM', 'TRANSFER_FROM', 'TRANSFER_TO', 'CONVERT_TO', 'CONVERT_FROM', 'PRICE_CHANGE', 'INITIAL_ORDER'],
                  },
                },
              },
              {
                $project: {
                  createdAt: 1,
                  status: 1,
                  currentPrice: 1,
                  amount: {
                    $switch: {
                      branches: [
                        {
                          case: { $eq: ['$status', 'PRICE_CHANGE'] },
                          then: { $multiply: [{ $toDecimal: '$amount' }, { $toDecimal: '$price' }] },
                        },
                        {
                          case: { $eq: ['$status', 'REDEEM'] },
                          then: { $multiply: [{ $toDecimal: '$wap' }, -1] },
                        },
                        {
                          case: { $in: ['$status', ['MINTED', 'TRANSFER_TO', 'CONVERT_TO', 'INITIAL_ORDER']] },
                          then: { $multiply: [{ $toDecimal: '$quantity' }, { $toDecimal: '$price' }] },
                        },
                        {
                          case: { $in: ['$status', ['BURN', 'TRANSFER_FROM', 'CONVERT_FROM']] },
                          then: { $multiply: [{ $toDecimal: '$quantity' }, { $toDecimal: '$price' }, -1] },
                        },
                      ],
                      default: 0,
                    },
                  },
                },
              },
              { $sort: { createdAt: 1 } },
            ],

            // ✅ Get cumulative balance before the start date
            totalBeforeStartDate: [
              {
                $match: {
                  userId: new Types.ObjectId(userId),
                  createdAt: { $lt: new Date(startDate * 1000) },
                  status: {
                    $in: ['MINTED', 'BURN', 'REDEEM', 'TRANSFER_FROM', 'TRANSFER_TO', 'CONVERT_TO', 'CONVERT_FROM', 'PRICE_CHANGE', 'INITIAL_ORDER'],
                  },
                },
              },
              {
                $project: {
                  createdAt: 1,
                  status: 1,
                  currentPrice: 1,
                  amount: {
                    $switch: {
                      branches: [
                        {
                          case: { $eq: ['$status', 'PRICE_CHANGE'] },
                          then: { $multiply: [{ $toDecimal: '$amount' }, { $toDecimal: '$price' }] },
                        },
                        {
                          case: { $eq: ['$status', 'REDEEM'] },
                          then: { $multiply: [{ $toDecimal: '$wap' }, -1] },
                        },
                        {
                          case: { $in: ['$status', ['MINTED', 'TRANSFER_TO', 'CONVERT_TO', 'INITIAL_ORDER']] },
                          then: { $multiply: [{ $toDecimal: '$quantity' }, { $toDecimal: '$price' }] },
                        },
                        {
                          case: { $in: ['$status', ['BURN', 'TRANSFER_FROM', 'CONVERT_FROM']] },
                          then: { $multiply: [{ $toDecimal: '$quantity' }, { $toDecimal: '$price' }, -1] },
                        },
                      ],
                      default: 0,
                    },
                  },
                },
              },
              { $sort: { createdAt: 1 } },
            ],
          },
        },
        // { $unwind: { path: '$totalBeforeStartDate', preserveNullAndEmptyArrays: true } },
        { $project: { mainData: 1, totalBeforeStartDate: 1 } },
      ];

      const results = await OrderSchema.aggregate(pipeline);
      const { mainData, totalBeforeStartDate } = results[0];

      // Initialize cumulative balance - use amount from cumulativeBalanceBeforeStartDate if it exists
      let cumulativeBalance = '0';
      let result;
      if (mainData.length === 0 && totalBeforeStartDate.length === 0) {
        // If no data in the date range, return current balance as a single data point
        result = [
          {
            groupStartTime: new Date(Date.now()).toISOString(),
            amount: '0',
            cumulativeBalance: cumulativeBalance,
          },
        ];
      } else if (totalBeforeStartDate.length !== 0) {
        const cumulativeBalanceBeforeStartDate = totalBeforeStartDate.map((txn: any) => {
          const amount = txn.amount.toString();

          // Handle each transaction type appropriately
          if (txn.status === 'PRICE_CHANGE') {
            // For price changes, update the balance but don't add the amount
            // (This depends on your business logic - you might need to adjust this)
          } else {
            // For other transactions, add to the cumulative balance
            cumulativeBalance = calculate('add', cumulativeBalance, amount);
          }

          return {
            groupStartTime: txn.createdAt,
            amount: amount,
            cumulativeBalance: cumulativeBalance,
          };
        });
        mainData.unshift(cumulativeBalanceBeforeStartDate[cumulativeBalanceBeforeStartDate.length - 1]);
      }
      result = mainData.map((txn: any) => {
        const amount = txn.amount.toString();

        // Handle each transaction type appropriately
        if (txn.status === 'PRICE_CHANGE') {
          // For price changes, update the balance but don't add the amount
          // (This depends on your business logic - you might need to adjust this)
        } else {
          // For other transactions, add to the cumulative balance
          cumulativeBalance = calculate('add', cumulativeBalance, amount);
        }

        return {
          groupStartTime: txn.createdAt,
          amount: amount,
          cumulativeBalance: cumulativeBalance,
        };
      });

      return result;
    } catch (error) {
      logger.error(error, 'portfolioPerformance');
      throw new Error(error.message);
    }
  };

  processOrder = async (data: any, filter: any) => {
    try {
      const { event, fromAddress, amount, txHash, tokenAddress, paymentMethod, feesInPercentage } = data;
      const userDetails = await UserDetailsSchema.findOne({ 'wallets.address': fromAddress.toLowerCase() }).select('_id');
      let createOrder;

      const fetchOfferingDetails = async (query: any) => {
        const { data: offeringDetails, error, message, status } = await OfferingService.fetchOfferingDetails(query);
        if (error) throw new CustomError(message, status);

        return offeringDetails;
      };
      const calculateOrder = (offeringDetails: any, status: any, orderType: any) => {
        const { projectDetails, _id: offeringId } = offeringDetails;
        const { tokenSupply, assetType, latestNav, previousValuation } = projectDetails;
        const navValuation = assetType === AssetTypeEnum.Equity ? previousValuation : latestNav;
        const price = calculate('div', navValuation, tokenSupply) || 0;
        let transactionsHash;
        if (event === 'TokenUnlocked') {
          transactionsHash = `${txHash}?type=${orderStatusEnum.CONVERT_TO}`;
        } else if (event === 'TokenLocked') {
          transactionsHash = `${txHash}?type=${orderStatusEnum.CONVERT_FROM}`;
        } else {
          transactionsHash = txHash;
        }
        return {
          offeringId,
          walletAddress: fromAddress,
          quantity: amount.toString(),
          status,
          orderType,
          price: price,
          amount: calculate('mul', amount, price),
          txHash: transactionsHash,
          paymentMethod,
          feesInPercentage,
        };
      };
      if (event === 'UserTokensFrozen' || event === 'UserTokensUnFrozen') {
        const offeringDetails = await fetchOfferingDetails({ _id: new Types.ObjectId(filter._id) });
        createOrder = calculateOrder(offeringDetails, event === 'UserTokensFrozen' ? orderStatusEnum.FREEZE : orderStatusEnum.UNFREEZE, event === 'UserTokensFrozen' ? orderStatusEnum.FREEZE : orderStatusEnum.UNFREEZE);
      } else if (event === 'TokenUnlocked') {
        const offeringDetails = await fetchOfferingDetails({ tokenAddress });
        createOrder = calculateOrder(offeringDetails, orderStatusEnum.CONVERT_TO, orderStatusEnum.CONVERT);
      } else if (event === 'TokenLocked') {
        const offeringDetails = await fetchOfferingDetails({ tokenAddress });
        createOrder = calculateOrder(offeringDetails, orderStatusEnum.CONVERT_FROM, orderStatusEnum.CONVERT);
      }
      const res: PromiseResolve = await this.createOrder(createOrder, userDetails._id);
      if (res.error) throw new CustomError(res.message, res.status);

      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.USER.ORDER_UPDATION_SUCCESS, data: createOrder };
    } catch (error) {
      logger.error(error, 'processOrder error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * Updates the order details and sends an email notification
   * @param {object} data - Order details to update
   * @param {object} filter - Filter to find the order
   * @returns {object} - Response object with status, error and message
   * @throws {Error} - If there is an error updating the order
   */
  updateOrderDetails = async (data: any, filter: any) => {
    try {
      if (data?.event === 'OrderCreated') {
        data.status = orderStatusEnum.APPROVED;
      }
      if (data?.event === 'OrderSettled') {
        data.status = orderStatusEnum.MINTED;
        data.isSettled = true;
        data.isMinted = true;
      } else if (data?.event === 'RedemptionAndBurn') {
        data.status = orderStatusEnum.REDEEM;
      }

      const search = { _id: filter?._id };

      const updateUserResp = await OrderSchema.findOneAndUpdate(search, data, {
        new: true,
        runValidators: true,
        // upsert: true,
      });
      if (updateUserResp) {
        const finalData = JSON.parse(JSON.stringify(updateUserResp));

        await kafkaService.sendMessageToAdmin({ value: { ...finalData, type: queueMessageTypeEnum.ORDER } });
        if (finalData?.status === orderStatusEnum.APPROVED) {
          const Ids = await OrderSchema.findOne({ _id: finalData?._id }).select('userId offeringId emailSent');
          // if (!Ids?.emailSent) {
          const userId = Ids?.userId;
          const offeringId = Ids?.offeringId;
          const user = await userSchema.findOne({ _id: userId }).select('email name');
          const offering = await offeringSchema.findOne({ _id: offeringId }).select('projectDetails.offeringName');

          const emailDetails = {
            name: user?.name,
            offeringName: offering?.projectDetails?.offeringName,
            amount: finalData?.amount,
            quantity: finalData?.quantity,
            transactionsId: finalData?.txHash || finalData.mintTxHash,
            orderReceived: finalData?.orderReceived.toLocaleString('en-US', {
              timeZone: 'UTC',
            }),
          };
          emailHelper.sendEmailTemplate(user?.email, 'orderCreate', emailDetails);
          await OrderSchema.updateOne({ _id: finalData?._id }, { $set: { emailSent: true } });
          // }
        } else if (finalData?.status === orderStatusEnum.REJECTED) {
          const Ids = await OrderSchema.findOne({ _id: finalData?._id }).select('userId offeringId emailSent');
          // if (!Ids?.emailSent) {
          const userId = Ids?.userId;
          const offeringId = Ids?.offeringId;
          const user = await userSchema.findOne({ _id: userId }).select('email name');
          const offering = await offeringSchema.findOne({ _id: offeringId }).select('projectDetails.offeringName');

          const emailDetails = {
            name: user?.name,
            offeringName: offering?.projectDetails?.offeringName,
            amount: finalData?.amount,
            quantity: finalData?.quantity,
            transactionsId: finalData?.txHash || finalData.mintTxHash,
            orderReceived: new Date().toLocaleString('en-US', { timeZone: 'UTC' }),
          };

          emailHelper.sendEmailTemplate(user?.email, 'orderReject', emailDetails);
          await OrderSchema.updateOne({ _id: finalData?._id }, { $set: { emailSent: true } });
        } else if (finalData?.status === orderStatusEnum.MINTED) {
          const Ids = await OrderSchema.findOne({ _id: finalData?._id }).select('userId offeringId emailSent');
          // if (!Ids?.mintEmailSent) {
          const userId = Ids?.userId;
          const offeringId = Ids?.offeringId;
          const user = await userSchema.findOne({ _id: userId }).select('email name');
          const offering = await offeringSchema.findOne({ _id: offeringId }).select('projectDetails.offeringName');

          const emailDetails = {
            name: user?.name,
            offeringName: offering?.projectDetails?.offeringName,
            amount: finalData?.amount,
            quantity: finalData?.quantity,
            transactionsId: finalData?.txHash || finalData.mintTxHash,
            // orderReceived: finalData?.orderReceived.toLocaleString('en-US', {
            //   timeZone: 'UTC',
            // }),
            orderReceived: new Date().toLocaleString('en-US', { timeZone: 'UTC' }),
          };
          emailHelper.sendEmailTemplate(user?.email, 'orderMint', emailDetails);
          await OrderSchema.updateOne({ _id: finalData?._id }, { $set: { mintEmailSent: true } });
          // }
        } else if (finalData?.status == 'CANCELLED') {
          const Ids = await OrderSchema.findOne({ _id: finalData?._id }).select('userId offeringId emailSent');
          const userId = Ids?.userId;
          const offeringId = Ids?.offeringId;
          const user = await userSchema.findOne({ _id: userId }).select('email name');
          const offering = await offeringSchema.findOne({ _id: offeringId }).select('projectDetails.offeringName');

          const emailDetails = {
            name: user?.name,
            offeringName: offering?.projectDetails?.offeringName,
            amount: finalData?.amount,
            quantity: finalData?.quantity,
            transactionsId: finalData?.txHash || finalData.mintTxHash,
            orderReceived: new Date().toLocaleString('en-US', { timeZone: 'UTC' }),
          };
          emailHelper.sendEmailTemplate(user?.email, 'orderCancelled', emailDetails);
        }

        return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.USER.ORDER_UPDATION_SUCCESS, data: updateUserResp };
      }
      await kafkaService.sendMessageToAdmin({ value: JSON.stringify({ ...updateUserResp, type: queueMessageTypeEnum.ORDER }) });

      return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.ERROR_MSG.USER_UPDATION_ERROR };
    } catch (error) {
      logger.error(error, 'updateorderDetails error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  getOrderById = async (orderId: string, userId: string): Promise<IOrder> => {
    try {
      const orderPipeline = [
        {
          $match: {
            _id: new Types.ObjectId(orderId), // Match by orderId
            userId: new Types.ObjectId(userId), // Filter by userId
            orderType: orderStatusEnum.MINTED,
          },
        },
        { $lookup: { from: 'whitelists', localField: 'userId', foreignField: 'userId', as: 'whitelistDetails' } },
        { $unwind: { path: '$whitelistDetails', preserveNullAndEmptyArrays: true } },
        { $match: { $expr: { $eq: ['$offeringId', '$whitelistDetails.offeringId'] } } },
        {
          $project: {
            _id: 1,
            userId: 1,
            totalAmount: 1,
            items: 1,
            amount: 1,
            isMinted: 1,
            isSettled: 1,
            quantity: 1,
            feesInPercentage: 1,
            createdAt: 1,
            orderReceivedAt: '$orderReceived',
            orderMintedAt: '$orderMinted',
            subscribedAt: '$whitelistDetails.createdAt',
            // Rename createdAt from whitelistDetails
            walletWhitelistedAt: '$whitelistDetails.updatedAt', // Rename updatedAt from whitelistDetails
          },
        },
      ];

      const result = await OrderSchema.aggregate(orderPipeline).exec();

      return result[0]; // Return the single order
    } catch (error) {
      logger.error(error, 'getOrderById');

      throw new Error(error.message);
    }
  };

  createNftOrder = async (data: any): Promise<PromiseResolve> => {
    try {
      const userDetails: any = await UserDetailsSchema.findOne({ 'wallets.address': data?.purchaser?.toLowerCase() }).select('_id');

      const nftOrderData = {
        userId: userDetails?._id,
        offeringId: data._id,
        walletAddress: data?.purchaser.toLowerCase(),
        amount: data?.price,
        status: orderStatusEnum.MINTED,
        isMinted: true,
        isSettled: true,
        isFreezed: false,
        price: data?.price,
        currentPrice: data?.price,
        quantity: 1,
        orderType: orderStatusEnum.NFT,
        txHash: data?.txHash,
        collectionAddress: data?.collection,
        orderMinted: new Date().toISOString(),
      };
      const updateUserResp = await OrderSchema.findByIdAndUpdate(data._id, nftOrderData, { new: true, upsert: true });

      if (updateUserResp) {
        const finalData = JSON.parse(JSON.stringify(updateUserResp));
        await kafkaService.sendMessageToAdmin({ value: { ...finalData, type: queueMessageTypeEnum.ORDER } });
        return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.USER.USER_UPDATION_SUCCESS, data: updateUserResp };
      }

      return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.ERROR_MSG.DATA_FETCH_ERROR };
    } catch (error) {
      logger.error(error, 'nftOrder error');
      return { status: RESPONSES.BAD_REQUEST, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };
}

export default new OrderService();
