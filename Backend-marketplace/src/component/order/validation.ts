import * as Joi from 'joi';
import * as joiOptions from '../../helpers/joi.helper';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import logger from '../../helpers/logging/logger.helper';
import { JoiValidationResult } from '../../utils/common.interface';
import { IOrder } from './models/order.model';

class OrderValidation {
  /**
   * Validate the order creation request
   * @returns {Promise<JoiValidationResult>}
   */

  async createOrderValidation(orderData: IOrder): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        _id: Joi.string().length(24).hex().optional().messages({ 'string.length': 'Invalid Order Id', 'string.hex': 'Order Id must be a valid MongoDB ObjectId' }),
        offeringId: Joi.string().length(24).hex().required().messages({ 'string.length': 'Invalid Offering Id', 'string.hex': '"offeringId" must be a valid MongoDB ObjectId' }),
        amount: Joi.number().unsafe().positive().required().messages({ 'number.base': 'Amount must be a number', 'number.positive': 'Amount must be a positive value', 'any.required': 'Amount is required' }),
        // quantity: Joi.number().unsafe().positive().required().messages({ 'number.base': 'Quantity must be a number', 'number.positive': 'Quantity must be a positive value', 'any.required': 'Quantity is required' }),
        paymentMethod: joiOptions.paymentTypeSchema,
      });

      const { error, value } = schema.validate(orderData, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'createOrderValidation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  async exportUserOrdersValidation(query: { startDate?: Date; endDate?: Date }): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        startDate: Joi.date().iso().optional(),
        endDate: Joi.date()
          .iso()
          .greater(Joi.ref('startDate')) // endDate should be after startDate
          .less('now') // endDate should not be in the future
          .optional(),
      }).with('startDate', 'endDate'); // Ensures both dates are present if one is provided

      const { error, value } = schema.validate(query, { abortEarly: false });
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'getOrdersByUserValidation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  async getOrdersByUserValidation(query: { page?: number; limit?: number; status?: string; search?: string; isCsv: boolean }): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        page: Joi.number().integer().min(1).default(1).optional(),
        limit: Joi.number().integer().min(1).default(10).optional(),
        status: Joi.string().default('').optional(),
        search: Joi.string().default('').optional(),
        isCsv: Joi.boolean().default(false).optional(),
      });

      const { error, value } = schema.validate(query, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'getOrdersByUserValidation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }
  /**
   * Validate the fetch orders request by user
   * @returns {Promise<JoiValidationResult>}
   */

  async getPorfolioPerformanceValidation(query: { period?: string }): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        period: Joi.string()
          .valid('1Y', '2Y', '3Y', '1M', '15D', '7D', '1D')
          .required()
          .default('1year')
          .messages({ 'string.base': 'period must be a string', 'string.empty': 'period cannot be empty', 'any.only': 'period must be one of [1Y, 2Y, 3Y, 1M, 15D, 7D, 1D]' }),
      });

      const { error, value } = schema.validate(query, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'getOrdersByUserValidation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  /**
   * Validate the fetch order by ID request
   * @returns {Promise<JoiValidationResult>}
   */
  async getOrderByIdValidation(orderId: string): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({ orderId: Joi.string().length(24).hex().required().messages({ 'string.length': 'Invalid Order Id', 'string.hex': '"orderId" must be a valid MongoDB ObjectId' }) });

      const { error, value } = schema.validate({ orderId }, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'getOrderByIdValidation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  /**
   * Validate the order rejection request
   * @returns {Promise<JoiValidationResult>}
   */
  async rejectOrderValidation(orderData: IOrder): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        orderId: Joi.string().length(24).hex().required().messages({ 'string.length': 'Invalid Offering Id', 'string.hex': '"offeringId" must be a valid MongoDB ObjectId' }),
        reason: Joi.string().allow('').default('').optional(),
      });

      const { error, value } = schema.validate(orderData, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'createOrderValidation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }
}

export default new OrderValidation();
