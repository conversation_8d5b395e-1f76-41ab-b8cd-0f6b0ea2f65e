import * as Jo<PERSON> from 'joi';
import { ITransferRequest } from './models/transfer';
import { JoiValidationResult } from '../../utils/common.interface';
import { RESPONSES } from '../../utils/responseUtils';
import logger from '../../helpers/logging/logger.helper';

export class TransferRequestValidation {
  /**
   * Validate the transfer request creation request
   * @returns {Promise<JoiValidationResult>}
   */

  static async createTransferRequestValidation(transferData: ITransferRequest): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        securityName: Joi.string().min(3).max(50).required().messages({
          'string.min': 'securityName must be at least 3 characters long',
          'string.max': 'securityName must not exceed 50 characters',
        }),
        registeredName: Joi.string().min(3).max(100).required().messages({
          'string.min': 'registeredName must be at least 3 characters long',
          'string.max': '"registeredName must not exceed 100 characters',
        }),
        registeredEmailId: Joi.string().email().required().lowercase().messages({
          'string.email': 'registeredEmailId must be a valid email',
        }),
        walletAddress: Joi.string()
          .pattern(/^0x[a-fA-F0-9]{40}$/)
          .required()
          .lowercase()
          .messages({
            'string.pattern.base': 'Wallet Address must be a valid address',
            'string.empty': 'Wallet Address cannot be empty',
          }),
        newRegisteredName: Joi.string().min(3).max(100).required().messages({
          'string.min': 'newRegisteredName must be at least 3 characters long',
          'string.max': 'newRegisteredName must not exceed 100 characters',
        }),
        newRegisteredEmailId: Joi.string().email().required().messages({ 'string.email': '"newRegisteredEmailId" must be a valid email' }),
        newWalletAddress: Joi.string()
          .pattern(/^0x[a-fA-F0-9]{40}$/)
          .required()
          .lowercase()
          .messages({
            'string.pattern.base': 'New Wallet Address must be a valid address',
            'string.empty': 'New Wallet Address cannot be empty',
          }),
        tokenQuantity: Joi.number().min(0).unsafe().required().messages({
          'number.base': 'tokenQuantity must be a valid number',
          'number.positive': 'tokenQuantity must be a positive value',
        }),
        remark: Joi.string().min(3).max(200).allow('').optional().messages({
          'string.empty': 'remark cannot be empty',
          'string.min': 'remark must be at least 3 characters long',
          'string.max': 'remark must not exceed 200 characters',
        }),
        offeringId: Joi.string().min(3).max(200).required().messages({
          'string.min': 'offering_id must be at least 3 characters long',
          'string.max': 'offering_id must not exceed 200 characters',
        }),
      }).custom((value: any, helpers: any) => {
        // Ensure registeredEmailId and newRegisteredEmailId are not the same
        if (value.registeredEmailId === value.newRegisteredEmailId) {
          return helpers.message('registered Email and new registered Email cannot be the same');
        }

        // Ensure walletAddress and newWalletAddress are not the same
        if (value.walletAddress === value.newWalletAddress) {
          return helpers.message('Wallet Address and new Wallet Address cannot be the same');
        }

        return value;
      });

      const { error, value } = schema.validate(transferData);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: 400 };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'createTransferRequestValidation error');

      return {
        error: true,
        value: '',
        message: 'Internal Server Error',
        status: 500, // Internal Server Error
      };
    }
  }

  /**
   * Validate the request for getting transfer requests
   * @returns {Promise<JoiValidationResult>}
   */

  static async getTransferRequestsValidation(query: { page?: number; limit?: number; search?: string }): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        page: Joi.number().integer().min(1).default(1).optional(),
        limit: Joi.number().integer().min(1).default(10).optional(),
        search: Joi.string().allow('', null).optional(),
      });

      const { error, value } = schema.validate(query);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'getTransferRequestsValidation error');

      return { error: true, value: '', message: 'Internal server error', status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  static async rejectTransferRequestsValidation(body: { reason?: string; transferReqId: string }): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        transferReqId: Joi.string().length(24).hex().required().messages({ 'string.length': 'Invalid transferReq Id', 'string.hex': '"transferReqId" must be a valid MongoDB ObjectId' }),
        reason: Joi.string().allow('', null).optional(),
      });

      const { error, value } = schema.validate(body);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'rejectTransferRequestsValidation error');

      return { error: true, value: '', message: 'Internal server error', status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  static async getTransferRequestsFromOfferingIdValidation(query: { page?: number; limit?: number; search?: string; offeringId: string; isCsv: boolean }): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        page: Joi.number().integer().min(1).default(1).optional(),
        limit: Joi.number().integer().min(1).default(10).optional(),
        search: Joi.string().allow('', null).optional(),
        offeringId: Joi.string().required(),
        isCsv: Joi.boolean().default(false).optional(),
      });

      const { error, value } = schema.validate(query);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'getTransferRequestsFromOfferingIdValidation error');

      return { error: true, value: '', message: 'Internal server error', status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }
}
