/* eslint-disable no-useless-escape */
import mongoose, { Schema, Document, Types } from 'mongoose';
import { transferStatusEnum } from '../../../utils/common.interface';

export interface ITransferRequest extends Document {
  securityName: string;
  registeredName: string;
  registeredEmailId: string;
  walletAddress: string;
  newRegisteredName: string;
  newRegisteredEmailId: string;
  status: transferStatusEnum;
  newWalletAddress: string;
  reason: string;
  tokenQuantity: number;
  remark: string;
  txHash?: string;
  userId: Types.ObjectId;
  offeringId: Types.ObjectId;
}

export interface IUpdateTransferRequest extends Document {
  securityName: string;
  registeredName: string;
  registeredEmailId: string;
  walletAddress: string;
  newRegisteredName: string;
  newRegisteredEmailId: string;
  status: transferStatusEnum;
  newWalletAddress: string;
  reason: string;
  tokenQuantity: number;
  remark: string;
  txHash?: string;
  userId: Types.ObjectId;
  offeringId: Types.ObjectId;
}

const TransferRequestSchema: Schema = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, ref: 'users', required: true },
    offeringId: { type: Schema.Types.ObjectId, ref: 'offerings', required: true },
    securityName: { type: String, required: true },
    registeredName: { type: String, required: true },
    reason: { type: String },
    status: { type: String, enum: Object.values(transferStatusEnum), default: transferStatusEnum.PENDING },
    isForceTransfer: { type: Boolean, default: false },
    registeredEmailId: { type: String, required: true, match: [/.+\@.+\..+/, 'Please enter a valid email address'] },
    walletAddress: { type: String, required: true },
    newRegisteredName: { type: String, required: true },
    newRegisteredEmailId: {
      type: String,
      required: true,
      // eslint-disable-next-line no-useless-escape
      match: [/.+\@.+\..+/, 'Please enter a valid email address'],
    },
    newWalletAddress: { type: String, required: true },
    tokenQuantity: { type: Number, required: true, min: [0, 'Token quantity must be a positive number'] },
    remark: { type: String, required: false },
    txHash: { type: String, required: false },
  },
  { timestamps: true },
);

const TransferRequest = mongoose.model<ITransferRequest>('TransferRequest', TransferRequestSchema);

export default TransferRequest;
