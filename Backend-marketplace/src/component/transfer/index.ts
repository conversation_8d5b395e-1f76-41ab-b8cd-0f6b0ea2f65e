import { Request, Response } from 'express';
import { Types } from 'mongoose';
import { HttpStatusCode } from 'axios';
import TransferRequestService from './service';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import { ResponseHandler } from '../../helpers/response.helper';
import logger from '../../helpers/logging/logger.helper';
import CustomError from '../../helpers/customError.helper';
import { offeringSchema } from '../offerings/models/offerings.model';
import OfferingService from '../offerings/service';
import { PromiseResolve, transferStatusEnum } from '../../utils/common.interface';
import TransferRequest from './models/transfer';
import UserService from '../userAuthentications/service';

export default class TransferRequestController {
  /**
   * Creates a new transfer request.
   * @param {Request} req Express request object.
   * @param {Response} res Express response object.
   * @returns {Promise<PromiseResolve>} A promise with the response object containing the transfer request details and a status code.
   */
  static createTransferRequest = async (req: Request, res: Response) => {
    try {
      const { body, userInfo } = req;
      const { userId: userIdHex } = userInfo;

      // Validate input parameters early
      if (!body.offeringId) {
        return ResponseHandler.error(res, {
          status: RESPONSES.BAD_REQUEST,
          error: true,
          message: 'Offering ID is required',
        });
      }

      // Convert IDs to ObjectId type
      const offeringId = Types.ObjectId.createFromHexString(body.offeringId);
      const userId = Types.ObjectId.createFromHexString(userIdHex);

      // Fetch offering details
      const offeringDetails = await offeringSchema.findById(offeringId);
      if (!offeringDetails) {
        throw new CustomError(RES_MSG.COMMON.OFFERING_NOT_FOUND, RESPONSES.NOTFOUND);
      }

      // Validate users
      await this.validateUserDetails(body);

      // Process transfer request
      const { error, message, status, data } = await TransferRequestService.checkTransfer(userId, offeringId, body);

      if (error) {
        throw new CustomError(message || 'Failed to create transfer request', status || RESPONSES.BAD_REQUEST);
      }

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.TRANSFER_REQ_CREATE_SUCCESS,
        data,
      });
    } catch (error: any) {
      logger.error(error, 'Error while creating transfer request');

      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  // Helper method for user validation
  private static async validateUserDetails(body: any) {
    // Check new user
    if (body.newRegisteredEmailId) {
      const { error: userNotFound, data } = await UserService.fetchUserDetails({ email: body.newRegisteredEmailId.toLowerCase() }, [], [], true);
      if (userNotFound) {
        throw new CustomError(RES_MSG.USER.TOKEN_TRANSFER_USER_NOT_FOUND, RESPONSES.NOTFOUND);
      }
      if (data.kycDetails.wallets[0].address.toLowerCase() !== body.newWalletAddress.toLowerCase()) {
        throw new CustomError('Invalid wallet address', RESPONSES.BAD_REQUEST);
      }
    }

    // Check registered user
    if (body.registeredEmailId) {
      const { error: userNotFound, data } = await UserService.fetchUserDetails({ email: body.registeredEmailId.toLowerCase() }, [], [], true);

      if (userNotFound) {
        throw new CustomError(RES_MSG.USER.TOKEN_TRANSFER_USER_NOT_FOUND, RESPONSES.NOTFOUND);
      }
      if (data.kycDetails.wallets[0].address.toLowerCase() !== body.walletAddress.toLowerCase()) {
        throw new CustomError('Invalid wallet address', RESPONSES.BAD_REQUEST);
      }
    }
  }

  /**
   * Rejects a transfer request.
   * @param {Request} req Express request object containing transfer request ID and reason.
   * @param {Response} res Express response object.
   * @returns {Promise<void>} A promise with the response indicating the success or failure of the operation.
   */
  static rejectTransferRequest = async (req: Request, res: Response) => {
    try {
      const { transferReqId, reason } = req.body;
      const transferReqDetails = await TransferRequest.findById(transferReqId);

      if (!transferReqDetails) {
        throw new CustomError('Transfer req not found for provided id ', HttpStatusCode.NotFound);
      }

      if (transferReqDetails.status === transferStatusEnum.REJECTED) {
        throw new CustomError('Transfer req is already rejected !', HttpStatusCode.NotFound);
      }
      const updateData = {
        status: transferStatusEnum.REJECTED,
        reason: reason || '',
      };

      const updateOfferingResp: any = await TransferRequestService.updateForceTransferDetails(updateData, { _id: transferReqId });
      if (updateOfferingResp.error) {
        throw new CustomError(updateOfferingResp.message, updateOfferingResp.status);
      }

      return ResponseHandler.success(res, { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.TRANSFER_REQ_REJECTED_SUCCESS, data: null });
    } catch (error: any) {
      logger.error(error, 'Error while creating transfer request');

      return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
    }
  };

  /**
   * Fetches all transfer requests with pagination and search.
   * @param {Request} req Express request object containing page, limit and search query.
   * @param {Response} res Express response object.
   * @returns {Promise<PromiseResolve>} A promise with the response object containing the transfer requests and a status code.
   */
  static getAllTransferRequests = async (req: Request, res: Response) => {
    try {
      const { page, limit, search } = req.query;
      const { userId } = req.userInfo;

      const transferRequests = await TransferRequestService.getAllTransferRequests(Number(page), Number(limit), String(search), userId);

      return ResponseHandler.success(res, { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: transferRequests });
    } catch (error: any) {
      logger.error(error, 'Error while fetching transfer requests');

      return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
    }
  };

  /**
   * Rejects a forced transfer request.
   * @param {Request} req Express request object containing transfer request ID and reason.
   * @returns {Promise<PromiseResolve>} A promise with the response indicating the success or failure of the operation.
   */
  static rejectForcedTransferHandler = async (req: any) => {
    try {
      const { _id, status, reason } = req;
      const updateData: any = { status, reason };
      // const updateOfferingResp: any = await TransferRequest.findOneAndUpdate(updateData, { _id: _id });
      const updateOfferingResp: any = await TransferRequestService.updateForceTransferDetails(updateData, { _id });
      if (updateOfferingResp.error) {
        throw new CustomError(updateOfferingResp.message, updateOfferingResp.status);
      }

      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.USER.USER_UPDATION_SUCCESS };
    } catch (error) {
      logger.error(error, 'rejectOfferingController Error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * Retrieves all transfer requests related to a specific offering with pagination and search.
   * @param {Request} req Express request object containing offering ID, page, limit and search query.
   * @param {Response} res Express response object.
   * @returns {Promise<PromiseResolve>} A promise with the response indicating the success or failure of the operation.
   */
  static getAllTransferRequestsByOffering = async (req: Request, res: Response) => {
    try {
      const { page, limit, search, isCsv } = req.query;
      const offeringId = new Types.ObjectId(req.params.offeringId);
      const userId = new Types.ObjectId(req.userInfo.userId);

      const offeringDetails: PromiseResolve = await OfferingService.fetchOfferingDetails({ _id: offeringId, userId });
      if (offeringDetails.error) {
        throw new CustomError(offeringDetails.message, offeringDetails.status);
      }
      const transferRequests = await TransferRequestService.getAllTransferRequestsByOfferingId(Number(page), Number(limit), String(search), offeringId, isCsv);

      return ResponseHandler.success(res, { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: transferRequests });
    } catch (error: any) {
      logger.error(error, 'Error while fetching transfer requests');

      return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
    }
  };
}
