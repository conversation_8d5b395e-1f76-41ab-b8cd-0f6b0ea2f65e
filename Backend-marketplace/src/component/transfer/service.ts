import { FilterQuery, Types } from 'mongoose';
import { ObjectId } from 'bson';
import TransferRequest, { ITransferRequest } from './models/transfer';
import logger from '../../helpers/logging/logger.helper';
import CustomError from '../../helpers/customError.helper';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import { whitelistSchema } from '../offerings/models/whitelist.model';
import kafkaService from '../../service/kafkaService';
import { AssetTypeEnum, orderStatusEnum, PromiseResolve, queueMessageTypeEnum, transferStatusEnum } from '../../utils/common.interface';
import OfferingService from '../offerings/service';
import { UserDetailsSchema } from '../userAuthentications/models/userDetails.model';
import OrderService from '../order/service';
import { calculate } from '../../helpers/bigMath';
import UserService from '../userAuthentications/service';
import emailHelper from '../../helpers/email.helper';
import CommonHelper from '../../helpers/common.helper';

export default class TransferRequestService {
  /**
   * Checks if the transfer request is valid and creates a new transfer request in the database.
   * @param offeringIdDetails - The details of the offering.
   * @param userId - The user ID of the user making the request.
   * @param isForceTransfer - Whether the transfer is forced or not.
   * @param offeringId - The ID of the offering.
   * @param body - The request body.
   * @param transferRequestData - The data to be used to create the transfer request.
   * @returns {Promise<PromiseResolve>} - A promise with the response object containing the transfer request details and a status code.
   */
  static checkTransfer = async (userId: ObjectId, offeringId: ObjectId, transferRequestData: any) => {
    try {
      let isForceTransfer;
      const isNewUserWhitelisted = await whitelistSchema.findOne({ offeringId, address: transferRequestData.newWalletAddress });
      const isTokenOwnerWhitelisted = await whitelistSchema.findOne({ offeringId, userId });
      if (!isNewUserWhitelisted) {
        throw new CustomError('User address has not whitelisted yet !', RESPONSES.BAD_REQUEST);
      } else if (isTokenOwnerWhitelisted?.userId?.toString() === userId?.toString()) {
        isForceTransfer = false;
      } else if (!isTokenOwnerWhitelisted) {
        isForceTransfer = true;
      }
      const newTransferRequest = await this.createTransferRequest({ ...transferRequestData, isForceTransfer, userId });
      if (newTransferRequest) {
        const finalData = JSON.parse(JSON.stringify(newTransferRequest));
        await kafkaService.sendMessageToAdmin({ value: { ...finalData, type: queueMessageTypeEnum.TRANSFER } });
      }

      if (!newTransferRequest) {
        throw new CustomError('Failed to create Transfer Request', RESPONSES.BAD_REQUEST);
      }

      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.CREATE_SUCCESS, data: newTransferRequest };
    } catch (error) {
      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  static createTransferRequest = async (transferRequestData: ITransferRequest) => {
    try {
      const transferRequest = new TransferRequest(transferRequestData);

      return await transferRequest.save();
    } catch (error) {
      logger.error(error, 'Error while creating transfer request');
      throw new Error('Failed to create transfer request');
    }
  };

  /**
   * This method fetches all transfer requests made by the user.
   * The search query is optional and is used to search the transfer requests by wallet address, email, name, and security name.
   * The page and limit parameters are used for pagination.
   * The method returns an object containing the transfer requests, total count, current page, total pages, next page and previous page.
   * @param page The page number to fetch.
   * @param limit The number of records to fetch on each page.
   * @param search The search query to use.
   * @param userId The id of the user making the request.
   * @returns An object containing the transfer requests, total count, current page, total pages, next page and previous page.
   */
  static getAllTransferRequests = async (page: number, limit: number, search: string, userId: string) => {
    try {
      const searchConditions: any = { userId };

      if (search !== undefined && search !== '' && search !== 'undefined') {
        const escapedSearch = CommonHelper.escapeRegex(search as string);

        const searchRegex = new RegExp(escapedSearch, 'i'); // Case insensitive regex search
        searchConditions.$or = [{ walletAddress: { $regex: searchRegex } }, { registeredEmailId: { $regex: searchRegex } }, { registeredName: { $regex: searchRegex } }, { securityName: { $regex: searchRegex } }];
      }
      const transferRequests = await TransferRequest.find(searchConditions)
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .exec();

      const totalCount = await TransferRequest.countDocuments(searchConditions);
      const totalPages = Math.ceil(totalCount / limit);
      const nextPage = page < totalPages ? page + 1 : null;
      const previousPage = page > 1 ? page - 1 : null;

      // Return an empty response if no data found
      if (!transferRequests.length) {
        return { transferRequests: transferRequests || [], totalCount: 0, currentPage: page, totalPages: 0, message: 'No transfer requests found' };
      }

      return { transferRequests, totalCount, currentPage: page, totalPages, nextPage, previousPage };
    } catch (error) {
      logger.error(error, 'Error while fetching transfer requests');
      throw new Error('Failed to fetch transfer requests');
    }
  };

  /**
   * This method updates the transfer request with the new wallet address and status.
   * @param data The data to be updated.
   * @param filter The filter to use to find the transfer request.
   * @returns A promise with the response object containing the transfer request details and a status code.
   */
  static updateForceTransferDetails = async (data: any, filter: FilterQuery<ITransferRequest>): Promise<PromiseResolve> => {
    try {
      if (data.type === 'ForceTransferred') {
        data.status = transferStatusEnum.APPROVED;
        await this.processForceTransfer(data);
      }

      const search = { _id: filter?._id };
      const updateUserResp: any = await TransferRequest.findOneAndUpdate(search, data, { new: true, runValidators: true, upsert: true });
      const registeredName = updateUserResp?.registeredName;
      const registeredEmailId = updateUserResp?.registeredEmailId;
      const newRegisteredEmailId = updateUserResp?.newRegisteredEmailId;
      const newRegisteredName = updateUserResp?.newRegisteredName;
      const WalletAddress = updateUserResp?.walletAddress ?? 'NA';
      const newWalletAddress = updateUserResp?.newWalletAddress ?? 'NA';
      const tokenQuantity = updateUserResp?.tokenQuantity ?? 'NA';
      if (updateUserResp) {
        const user = await UserService.fetchUserDetails({ _id: updateUserResp?.userId });
        const offering = await OfferingService.fetchOfferingDetails({ _id: updateUserResp?.offeringId });
        const { email, name } = user?.data ?? 'user';
        // if ((data.status == transferStatusEnum.REJECTED)) {
        const { offeringName } = offering?.data?.projectDetails ?? 'Libertum';
        const emailDetail: any = {
          email,
          name,
          amount: updateUserResp?.tokenQuantity ?? '0',
          reason: data?.reason || updateUserResp?.tokenQuantity?.remark || 'NA',
          offeringName,
          date: new Date().toLocaleString('en-US', { timeZone: 'UTC' }),
          txHash: data?.txHash,
          registeredName,
          registeredEmailId,
          WalletAddress,
          newRegisteredEmailId,
          newRegisteredName,
          newWalletAddress,
          tokenQuantity,
        };
        if (!updateUserResp?.isForceTransfer) {
          if (data.status == transferStatusEnum.APPROVED) {
            emailHelper.sendEmailTemplate(email, 'transferapproval', emailDetail);
          }
          if (data.status == transferStatusEnum.REJECTED) {
            emailHelper.sendEmailTemplate(email, 'transferrejected', emailDetail);
          }
        } else {
          if (data.status == transferStatusEnum.APPROVED) {
            emailHelper.sendEmailTemplate(registeredEmailId, 'forceTransfer', {
              name: registeredName,
              amount: updateUserResp?.tokenQuantity ?? '0',
              reason: data?.reason ?? 'NA',
              offeringName,
              date: new Date().toLocaleString('en-US', { timeZone: 'UTC' }),
              txHash: data?.txHash,
              registeredName,
              registeredEmailId,
              WalletAddress,
              newRegisteredEmailId,
              newRegisteredName,
              newWalletAddress,
              tokenQuantity,
            });
            emailHelper.sendEmailTemplate(newRegisteredEmailId, 'fromforceTransfer', {
              name: newRegisteredName,
              amount: updateUserResp?.tokenQuantity ?? '0',
              reason: data?.reason ?? 'NA',
              offeringName,
              date: new Date().toLocaleString('en-US', { timeZone: 'UTC' }),
              txHash: data?.txHash,
              registeredName,
              registeredEmailId,
              WalletAddress,
              newRegisteredEmailId,
              newRegisteredName,
              newWalletAddress,
              tokenQuantity,
            });
          }
          if (data.status == transferStatusEnum.REJECTED) {
            emailHelper.sendEmailTemplate(email, 'transferrejected', emailDetail);
          }
        }

        const finalData = JSON.parse(JSON.stringify(updateUserResp));

        await kafkaService.sendMessageToAdmin({ value: { ...finalData, type: queueMessageTypeEnum.TRANSFER } });

        return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.USER.ORDER_UPDATION_SUCCESS, data: updateUserResp };
      }
      await kafkaService.sendMessageToAdmin({ value: JSON.stringify({ ...updateUserResp, type: queueMessageTypeEnum.ORDER }) });

      return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.ERROR_MSG.USER_UPDATION_ERROR };
    } catch (error) {
      // Enhanced error handling
      logger.error(error, 'updateForceTransferDetails error');
      // General error handling
      return { status: RESPONSES.BAD_REQUEST, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * Get all transfer requests associated with an offering Id
   * @param {Number} page - The page number
   * @param {Number} limit - The number of records per page
   * @param {String} search - The search query, can be empty or undefined
   * @param {ObjectId} offeringId - The offering Id
   * @returns {Promise<Object>} - An object containing the transfer requests, total count, current page, total pages, next page and previous page
   */
  static getAllTransferRequestsByOfferingId = async (page: number, limit: number, search: string, offeringId: Types.ObjectId, isCsv: any) => {
    try {
      const matchStage: any = { offeringId };
      if (search !== undefined && search !== '' && search !== 'undefined') {
        const escapedSearch = search ? CommonHelper.escapeRegex(search as string) : '';

        const searchRegex = new RegExp(escapedSearch, 'i'); // Case-insensitive search
        matchStage.$or = [{ walletAddress: { $regex: searchRegex } }, { registeredEmailId: { $regex: searchRegex } }, { registeredName: { $regex: searchRegex } }, { securityName: { $regex: searchRegex } }];
      }

      const pipeline: any = [
        { $match: matchStage },
        {
          $addFields: {
            isOlderThan24Hours: {
              $lt: ['$createdAt', new Date(Date.now() - 24 * 60 * 60 * 1000)],
            },
          },
        },
        {
          $addFields: {
            status: {
              $cond: {
                if: { $and: ['$isForceTransfer', '$isOlderThan24Hours'] },
                then: 'REJECTED',
                else: '$status',
              },
            },
          },
        },
        { $sort: { createdAt: -1 } },
        {
          $facet: {
            metadata: [{ $count: 'totalCount' }],
            // Modify pagination based on `isCsv`
            transferRequests: isCsv
              ? [] // If isCsv is true, no pagination, return all items
              : [{ $skip: (page - 1) * limit }, { $limit: limit }], // Otherwise, apply pagination
          },
        },
      ];
      const [result] = await TransferRequest.aggregate(pipeline);
      const totalCount = result.metadata[0]?.totalCount || 0;
      const totalPages = Math.ceil(totalCount / limit);
      const nextPage = page < totalPages ? page + 1 : null;
      const previousPage = page > 1 ? page - 1 : null;

      return {
        transferRequests: result.transferRequests || [],
        totalCount,
        currentPage: page,
        totalPages,
        nextPage,
        previousPage,
      };
    } catch (error) {
      logger.error(error, 'Error while fetching transfer requests');
      throw new CustomError(error.message || 'Failed to fetch transfer request', error.status || RESPONSES.BAD_REQUEST);
    }
  };

  static processForceTransfer = async (data: any): Promise<PromiseResolve> => {
    try {
      const { fromAddress, toAddress, amount, txHash, tokenAddress } = data;

      const { error, data: offeringData, status, message } = await OfferingService.fetchOfferingDetails({ tokenAddress });

      if (error || !offeringData) throw new CustomError(message, status);

      const { _id: offeringId, projectDetails } = offeringData;

      const [fromUserResp, toUserResp] = await Promise.all([UserDetailsSchema.findOne({ 'wallets.address': fromAddress }), UserDetailsSchema.findOne({ 'wallets.address': toAddress })]);

      if (!fromUserResp || !toUserResp) {
        throw new Error(!fromUserResp ? 'User not found for fromAddress' : 'User not found for toAddress');
      }

      const { _id: fromUserId } = fromUserResp;
      const { _id: toUserId } = toUserResp;

      let currentPrice;
      const { assetType, latestNav, previousValuation, tokenSupply } = projectDetails;

      if (assetType === AssetTypeEnum.RealEstate && latestNav) {
        currentPrice = calculate('div', latestNav, tokenSupply) || 0;
      } else if (assetType === AssetTypeEnum.Equity && previousValuation) {
        currentPrice = calculate('div', previousValuation, tokenSupply) || 0;
      }

      const baseData = { offeringId, quantity: amount, feesInPercentage: 0, currentPrice, orderMinted: new Date().toISOString() };
      const { data: portFolio } = await UserService.fetchUserPortfolio(fromUserId?.toString(), false, {}, offeringId?._id.toString());
      const avgPrice = portFolio?.portfolio?.[0]?.averagePricePerToken || 0;
      //token transfer From one user to another user
      const fromData = {
        ...baseData,
        userId: fromUserId,
        walletAddress: fromAddress,
        orderType: orderStatusEnum.TRANSFER,
        status: orderStatusEnum.TRANSFER_FROM,
        amount: calculate('mul', baseData.quantity, avgPrice),
        txHash: `${txHash}?from`,
        price: avgPrice,
      };

      //token received from another user

      const toData = {
        ...baseData,
        userId: toUserId,
        walletAddress: toAddress,
        orderType: orderStatusEnum.TRANSFER,
        status: orderStatusEnum.TRANSFER_TO,
        amount: calculate('mul', baseData.quantity, currentPrice),
        txHash: `${txHash}?to`,
        price: currentPrice,
      };

      await OrderService.createOrder(toData, toData.userId);
      await OrderService.createOrder(fromData, fromData.userId);

      return { status: RESPONSES.SUCCESS, error: false, message: 'Force transfer processed successfully', data: { fromData, toData } };
    } catch (error) {
      logger.error(error, 'Error while processing force transfer');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };
}
