import { FilterQuery } from 'mongoose';
import { IPagination, PromiseResolve } from '../../utils/common.interface';
import { IUserModel, IUpdateUserModel } from './models/user.model';
import { IUserDetails } from './models/userDetails.model';

export interface IUserService {
  /**
   * @param {IUserModel} body
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  signUp(body: IUserModel): Promise<PromiseResolve>;

  /**
   * @param {IUserModel} body
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  createUser(body: any): Promise<PromiseResolve>;

  /**
   * @param {IUserModel} searchDetails
   * @param {IUserModel} fields
   * @param {IUserModel} excludeFields
   * @param {Boolean} isKyc
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  fetchUserDetails(searchDetails: FilterQuery<IUserModel>, fields?: string[], excludeFields?: string[], isKyc?: boolean): Promise<PromiseResolve>;

  /**
   * @param {IUpdateUserModel} body
   * @param {IUserModel} filter
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  updateUserDetails(body: IUpdateUserModel, filter: FilterQuery<IUserModel>): Promise<PromiseResolve>;

  /**
   * @param {string} _id
   * @param {string} password
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  addPassword(_id: string, password: string): Promise<PromiseResolve>;

  /**
   * @param {PasswordModel} _id
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  fetchRecentPasswords(_id: string): Promise<PromiseResolve>;

  /**
   * @param {string} userId
   * @param {IPagination} pagination
   * @param {string} offeringId
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  fetchUserPortfolio(userId: string, isCsv: any, pagination: IPagination, offeringId?: string): Promise<PromiseResolve>;

  /**
   * @param {IUserDetails} body
   * @param {IUserDetails} filter
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  updateUserKycDetails(body: IUserDetails, filter: FilterQuery<IUserDetails>): Promise<PromiseResolve>;
}
