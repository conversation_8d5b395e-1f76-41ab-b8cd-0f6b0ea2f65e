import * as <PERSON><PERSON> from 'joi';
import { getCountries, getCountryCallingCode } from 'libphonenumber-js';
import { JoiValidationResult } from '../../utils/common.interface';
import * as joiOptions from '../../helpers/joi.helper';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import { passwordMaxLength, passwordMinLength, passwordPattern } from '../../utils/constant';
import logger from '../../helpers/logging/logger.helper';
import { IUserDetails } from './models/userDetails.model';
import { mainInformationSchema } from '../../helpers/joi.helper';

class UserValidation {
  /**
   * @description
   * This method will validate the user registration data
   * @param {object} params - The user registration data
   * @returns {Promise<JoiValidationResult>} - The validation result
   * @memberof AuthValidation
   */
  async singUpValidation(params: object): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({
        name: joiOptions.nameSchema,
        email: joiOptions.emailSchema,
        password: joiOptions.passwordJioSchema,
        countryCode: joiOptions.countryCodeSchema,
        mobile: joiOptions.mobileSchema,
        userType: joiOptions.userTypeSchema,
        legalFullName: joiOptions.nameSchema.optional(),
        otpMethods: joiOptions.otpMethodsSchema,
      });

      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error: any) {
      logger.error(error, 'singUpValidation Error');

      return { error: true, value: '', message: RES_MSG.COMMON.SOMETHING_WRONG };
    }
  }

  /**
   * Validates the login credentials.
   *
   * @param {object} params - The login credentials, including userName and password.
   * @returns {Promise<JoiValidationResult>} - The result of the validation. If validation fails, an error message is included.
   * @memberof AuthValidation
   */
  async loginValidation(params: object): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({
        userName: Joi.string().trim().lowercase().required().label('userName').messages({ 'string.empty': '{#label} is required', 'any.required': '{#label} is required' }),
        password: Joi.string().trim().required().label('Password').messages({ 'string.empty': '{#label} is required', 'any.required': '{#label} is required' }),
        // otpMethods: joiOptions.otpMethodsSchema,
      });

      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error: any) {
      logger.error(error, 'loginValidation Error');

      return { error: true, value: '', message: RES_MSG.COMMON.SOMETHING_WRONG };
    }
  }
  /**
   * Validates the social login credentials.
   *
   * @param {object} params - The social login credentials, including token and userType.
   * @returns {Promise<JoiValidationResult>} - The result of the validation. If validation fails, an error message is included.
   * @memberof AuthValidation
   */
  async socialLoginValidation(params: any): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({ token: Joi.string().required().trim(), userType: joiOptions.userTypeSchema });

      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error: any) {
      logger.error(error, 'verifyTokenValidation Validation');

      return { error: true, value: '', message: RES_MSG.COMMON.SOMETHING_WRONG };
    }
  }

  /**
   * Validate the verification request parameters.
   * @param {object} params - Parameters containing userName, otp, type, and otpMethods.
   * @returns {Promise<JoiValidationResult>} - The result of the validation. If validation fails, an error message is included.
   * @memberof AuthValidation
   */
  async verificationValidation(params: object): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({
        // email: joiOptions.emailSchema,
        userName: Joi.string().trim().required().label('userName').messages({ 'string.empty': '{#label} is required', 'any.required': '{#label} is required' }),
        otp: joiOptions.otpSchema,
        type: joiOptions.otpTypeSchema,
        otpMethods: joiOptions.otpMethodsSchema,
      });

      const { error, value } = schema.validate(params, joiOptions.options);

      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error: any) {
      logger.error(error, 'verificationValidation Error');

      return { error: true, value: '', message: RES_MSG.COMMON.SOMETHING_WRONG };
    }
  }

  /**
   * Validates the update profile parameters.
   * Ensures that if either mobile or countryCode is provided, the other must also be present.
   *
   * @param {object} params - The profile update parameters including name, email, dob, countryCode, and mobile.
   * @returns {Promise<JoiValidationResult>} - The result of the validation. If validation fails, an error message is included.
   * @memberof UserValidation
   */
  async updateProfileValidation(params: object): Promise<JoiValidationResult> {
    try {
      const updateProfileSchema = Joi.object({
        name: joiOptions.nameSchema.optional(),
        email: joiOptions.emailSchema.optional(),
        dob: Joi.date().optional().label('Date of birth').messages({ 'date.base': '{#label} must be a valid date' }),
        countryCode: joiOptions.countryCodeSchema.optional(),
        mobile: joiOptions.mobileSchema.optional(),
      }).custom((value, helpers) => {
        const { mobile, countryCode } = value;

        // Check if either mobile or countryCode is provided
        const isMobileProvided = mobile !== undefined && mobile !== null;
        const isCountryCodeProvided = countryCode !== undefined && countryCode !== null;

        // If one is provided, the other must be present
        if (isMobileProvided && !isCountryCodeProvided) {
          return helpers.error('any.required', { message: 'countryCode is required when mobile is provided.' });
        }

        if (isCountryCodeProvided && !isMobileProvided) {
          return helpers.error('any.required', { message: 'mobile is required when countryCode is provided.' });
        }

        return value; // Return the validated value
      });

      const { error, value } = updateProfileSchema.validate(params, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'userUpdateValidation Error');

      return { error: true, value: '', message: RES_MSG.COMMON.SOMETHING_WRONG };
    }
  }

  /**
   *    * @description Validates the KYB update parameters.
   * @param {IUserDetails} params - The KYB update parameters including kycSteps and relevant information.
   * @returns {Promise<JoiValidationResult>} - The result of the validation. If validation fails, an error message is included.
   
   * @memberof UserValidation
   */
  async updateKybValidation(params: IUserDetails): Promise<JoiValidationResult> {
    try {
      let schemaToValidate;
      const countryCodeMap: { [key: string]: string } = {};

      getCountries().forEach((country) => {
        countryCodeMap[`+${getCountryCallingCode(country)}`] = country;
      });

      // step 1
      const institutionsSchema = Joi.object({
        companyInformation: Joi.object({
          name: Joi.string().required().label('Company Name'),
          entityType: Joi.string().required().label('Entity Type'),
          webSite: joiOptions.webSite,
          business: Joi.string().required().label('Business Type'),
          sourceOfFunds: Joi.string().required().label('Source of Funds'),
        }),
        address: joiOptions.addressSchema,
      });

      const isIdentityVerificationSchema = Joi.object({ status: Joi.boolean().optional().label('Identity Verification Status').messages({ 'any.required': '{#label} is required' }) });

      const personalInformationSchema = Joi.object({
        name: joiOptions.nameSchema,
        jobTitle: Joi.string().optional().label('Job Title').messages({ 'string.empty': '{#label} is required', 'any.required': '{#label} is required' }),
        dob: Joi.date().required().label('Date of Birth').messages({ 'date.base': '{#label} must be a valid date' }),
        socialSecurityNumber: Joi.string().required().label('Social Security Number').messages({ 'number.base': '{#label} must be a valid number', 'any.required': '{#label} is required' }),
        citizenship: Joi.string().required().label('Citizenship').messages({ 'string.empty': '{#label} is required', 'any.required': '{#label} is required' }),
        countryCode: joiOptions.countryCodeSchema,
        mobile: joiOptions.mobileSchema,
        email: joiOptions.emailSchema,
      });
      const beneficialOwnersPersonalInformationSchema = Joi.object({
        name: joiOptions.nameSchema,
        dob: Joi.date().required().label('Date of Birth').messages({ 'date.base': '{#label} must be a valid date' }),
        socialSecurityNumber: Joi.string().required().label('Social Security Number').messages({ 'string.empty': '{#label} is required', 'any.required': '{#label} is required' }),
        citizenship: Joi.string().required().label('Citizenship').messages({ 'string.empty': '{#label} is required', 'any.required': '{#label} is required' }),
      });

      const beneficialOwnersSchema = Joi.array().items(
        Joi.object({
          personalInformation: beneficialOwnersPersonalInformationSchema,
          address: joiOptions.addressSchema,
          identityProof: Joi.object({
            passport: Joi.object({
              front: Joi.string().uri().optional().label('Passport Front').messages({ 'string.uri': '{#label} must be a valid URL' }),
              back: Joi.string().uri().optional().label('Passport Back').messages({ 'string.uri': '{#label} must be a valid URL' }),
            }).optional(),
            driversLicense: Joi.object({
              front: Joi.string().uri().optional().label('Driver’s License Front').messages({ 'string.uri': '{#label} must be a valid URL' }),
              back: Joi.string().uri().optional().label('Driver’s License Back').messages({ 'string.uri': '{#label} must be a valid URL' }),
            }).optional(),
            idCard: Joi.object({
              front: Joi.string().uri().optional().label('ID Card Front').messages({ 'string.uri': '{#label} must be a valid URL' }),
              back: Joi.string().uri().optional().label('ID Card Back').messages({ 'string.uri': '{#label} must be a valid URL' }),
            }).optional(),
          }).optional(),
        }),
      );

      const managementInfoSchema = Joi.array().items(Joi.object({ personalInformation: personalInformationSchema, address: joiOptions.addressSchema }));

      switch (params.kycSteps) {
        case 1:
          schemaToValidate = joiOptions.kycStepSchema.append({ institutions: institutionsSchema.required() });
          break;
        case 2:
          schemaToValidate = joiOptions.kycStepSchema.append({ primaryContactInfo: Joi.object({ personalInformation: personalInformationSchema, address: joiOptions.addressSchema }).required() });
          break;
        case 3:
          schemaToValidate = joiOptions.kycStepSchema.append({ wallets: Joi.array().items(joiOptions.walletSchema).required().min(1).required().messages({ 'array.min': 'At least one wallet is required.', 'any.required': 'Wallets are required.' }) });
          break;
        case 4:
          schemaToValidate = joiOptions.kycStepSchema.append({ documents: joiOptions.documentsSchema.required() });
          break;
        case 5:
          schemaToValidate = joiOptions.kycStepSchema.append({ isIdentityVerification: isIdentityVerificationSchema.required() });
          break;
        case 6:
          schemaToValidate = joiOptions.kycStepSchema.append({ beneficialOwners: beneficialOwnersSchema.required() });
          break;
        case 7:
          schemaToValidate = joiOptions.kycStepSchema.append({ managementInfo: managementInfoSchema.required() });
          break;
        case 8:
          schemaToValidate = joiOptions.kycStepSchema.append({
            institutions: institutionsSchema.required(),
            primaryContactInfo: Joi.object({ personalInformation: personalInformationSchema, address: joiOptions.addressSchema }).required(),
            wallets: Joi.array().items(joiOptions.walletSchema).min(1).required().messages({ 'array.min': 'At least one wallet is required.', 'any.required': 'Wallets are required.' }),
            isIdentityVerification: isIdentityVerificationSchema.required(),
            documents: joiOptions.documentsSchema.required(),
            beneficialOwners: beneficialOwnersSchema.required(),
            managementInfo: managementInfoSchema.required(),
            isFinalSubmission: Joi.boolean().required().default(true).label('Final Submission'),
          });
          break;
        default:
          return { error: true, value: '', message: 'Invalid kycSteps value', status: RESPONSES.BAD_REQUEST };
      }

      // Validate the relevant schema
      const { error, value } = schemaToValidate.validate(params, joiOptions.options);

      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'userUpdateValidation Error');

      return { error: true, value: '', message: RES_MSG.COMMON.SOMETHING_WRONG };
    }
  }

  /**
   * Validates the user's input for updating their KYC information.
   * @param {IUserDetails} params - The user's input for updating their KYC information.
   * @returns {Promise<JoiValidationResult>} - The result of the validation.
   * @memberof UserValidation
   */
  async updateKycValidation(params: IUserDetails): Promise<JoiValidationResult> {
    try {
      let schemaToValidate;
      switch (params.kycSteps) {
        case 1:
          schemaToValidate = joiOptions.kycStepSchema.append({ mainInformation: joiOptions.mainInformationSchema.required() });
          break;
        case 2:
          schemaToValidate = joiOptions.kycStepSchema.append({ wallets: Joi.array().items(joiOptions.walletSchema).min(1).required().messages({ 'array.min': 'At least one wallet is required.', 'any.required': 'Wallets are required.' }) });
          break;
        case 3:
          schemaToValidate = joiOptions.kycStepSchema.append({ documents: joiOptions.documentsSchema.required() });
          break;
        case 4:
          schemaToValidate = joiOptions.kycStepSchema.append({
            isIdentityVerification: joiOptions.isIdentityVerificationSchema.required().label('Identification Document').messages({ 'string.empty': '{#label} is required', 'any.required': '{#label} is required' }),
          });
          break;
        case 5:
          schemaToValidate = joiOptions.kycStepSchema.append({
            mainInformation: mainInformationSchema.required(),
            wallets: Joi.array().items(joiOptions.walletSchema).min(1).required().messages({ 'array.min': 'At least one wallet is required.', 'any.required': 'Wallets are required.' }),
            documents: joiOptions.documentsSchema.required(),
            isIdentityVerification: joiOptions.isIdentityVerificationSchema.required(),
            isFinalSubmission: Joi.boolean().required().default(true).label('Final Submission'),
          });
          break;
        default:
          return { error: true, value: '', message: 'Invalid kycSteps value', status: RESPONSES.BAD_REQUEST };
      }

      const { error, value } = schemaToValidate.validate(params, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'updateKycValidation Error');

      return { error: true, value: '', message: RES_MSG.COMMON.SOMETHING_WRONG };
    }
  }

  /**
   * @description
   * Validates the change password request.
   * Ensures the new password is different from the old one and matches the required pattern.
   * @param {object} params - The change password request.
   * @returns {Promise<JoiValidationResult>} - The result of the validation.
   * @memberof UserValidation
   */
  async changePasswordValidation(params: object): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({
        password: Joi.string()
          .trim()
          .min(passwordMinLength)
          .max(passwordMaxLength)
          .pattern(/^\S*$/, { name: 'no spaces' })
          .pattern(passwordPattern)
          .required()
          .label('Password')
          .invalid(Joi.ref('oldPassword'))
          .messages({
            'string.empty': '{#label} is required',
            'string.min': `{#label} must contain at least ${passwordMinLength} characters, one uppercase, one lowercase, one number, and one special character. `,
            'string.max': `{#label} must contain at most ${passwordMaxLength} characters, one uppercase, one lowercase, one number, and one special character.`,
            'string.pattern.base': '{#label} must contain at least 8 characters, one uppercase, one lowercase, one number, and one special character.',
            'string.pattern.name': '{#label} must not contain spaces.',
            'any.required': '{#label} is required',
            'any.invalid': '{#label} must not be the same as Old Password',
          }),
        oldPassword: Joi.string()
          .trim()
          .min(passwordMinLength)
          .max(passwordMaxLength)
          .pattern(/^\S*$/, { name: 'no spaces' })
          .pattern(passwordPattern)
          .required()
          .label('Old Password')
          .messages({
            'string.empty': '{#label} is required',
            'string.min': `{#label} must contain at least ${passwordMinLength} characters, one uppercase, one lowercase, one number, and one special character. `,
            'string.max': `{#label} must contain at most ${passwordMaxLength} characters, one uppercase, one lowercase, one number, and one special character.`,
            'string.pattern.base': '{#label} must contain at least 8 characters, one uppercase, one lowercase, one number, and one special character.',
            'string.pattern.name': '{#label} must not contain spaces.',
            'any.required': '{#label} is required',
          }),
      }).custom((value) => {
        if (value.password === value.oldPassword) {
          return 'Password must not be the same as Old Password';
        }

        return value;
      });

      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'userPasswordValidation Error');

      return { error: true, value: '', message: RES_MSG.COMMON.SOMETHING_WRONG };
    }
  }

  /**
   * Validates the email address provided.
   *
   * @param {object} params - The request payload containing the email address.
   *
   * @returns {Promise<JoiValidationResult>} - The result of the validation.
   *
   * @memberof AuthValidation
   */
  async emailValidation(params: any): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({ email: joiOptions.emailSchema });
      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error: any) {
      logger.error(error, 'email Validation failed');

      return { error: true, value: '', message: RES_MSG.COMMON.SOMETHING_WRONG };
    }
  }

  /**
   * Validates the reset password data.
   *
   * @param {object} params - The request payload containing the token and new password.
   *
   * @returns {Promise<JoiValidationResult>} - The result of the validation.
   *
   * @memberof AuthValidation
   */
  async resetPasswordValidation(params: any): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({
        token: Joi.string()
          .required()
          .trim()
          .pattern(/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/)
          .messages({ 'string.pattern.base': 'Invalid token format. Must be a valid JWT.', 'any.required': 'Token is required.' }),
        newPassword: joiOptions.passwordJioSchema,
      });

      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error: any) {
      logger.error(error, 'reset PasswordValidation');

      return { error: true, value: '', message: RES_MSG.COMMON.SOMETHING_WRONG };
    }
  }

  /**
   * Validates the 2FA code.
   * @param {object} params - The request payload containing the 2FA code.
   * @returns {Promise<JoiValidationResult>} - The result of the validation.
   * @memberof AuthValidation
   */
  async verifyTokenValidation(params: any): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({ token: Joi.string().required().trim().label('2FA code').messages({ 'string.empty': '{#label} is required', 'any.required': '{#label} is required' }) });

      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error: any) {
      logger.error(error, 'verifyTokenValidation Validation');

      return { error: true, value: '', message: RES_MSG.COMMON.SOMETHING_WRONG };
    }
  }

  /**
   * Validates the parameters for the /api/v1/user/docs endpoint.
   * @param {object} params - The parameters for the /api/v1/user/docs endpoint.
   * @returns {Promise<JoiValidationResult>} - The result of the validation.
   * @memberof AuthValidation
   */
  async docsValidation(params: any): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({ offeringId: Joi.string().optional(), documentType: Joi.when('offeringId', { is: Joi.exist(), then: joiOptions.offeringDocsTypeSchema, otherwise: joiOptions.docsTypeSchema }) });

      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error: any) {
      logger.error(error, 'verifyTokenValidation Validation');

      return { error: true, value: '', message: RES_MSG.COMMON.SOMETHING_WRONG };
    }
  }

  /**
   * Validates the parameters for the /api/v1/user/resend-otp endpoint.
   * @param {object} params - The parameters for the /api/v1/user/resend-otp endpoint.
   * @returns {Promise<JoiValidationResult>} - The result of the validation.
   * @memberof AuthValidation
   */
  async resendOtpValidation(params: any): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({
        userName: Joi.string().trim().required().label('userName').messages({ 'string.empty': '{#label} is required', 'any.required': '{#label} is required' }),
        type: joiOptions.otpTypeSchema,
        otpMethods: joiOptions.otpMethodsSchema,
      });
      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error: any) {
      logger.error(error, 'forgotPassword Validation failed');

      return { error: true, value: '', message: RES_MSG.COMMON.SOMETHING_WRONG };
    }
  }

  /**
   * Validates the 2FA token and OTP.
   *
   * @param {object} params - The request payload containing the token and OTP.
   * @returns {Promise<JoiValidationResult>} - The result of the validation.
   * @memberof AuthValidation
   */
  async verify2FAValidation(params: any): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({
        token: Joi.string()
          .required()
          .trim()
          .label('Token')
          .pattern(/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/)
          .messages({ 'string.pattern.base': 'Invalid token', 'string.empty': '{#label} is required', 'any.required': '{#label} is required' }),
        otp: joiOptions.otpSchema,
      });

      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error: any) {
      logger.error(error, 'verifyTokenValidation Validation');

      return { error: true, value: '', message: RES_MSG.COMMON.SOMETHING_WRONG };
    }
  }

  /**
   * Validate the fetch orders request by user
   *
   * This function validates the query parameters passed to fetch user orders.
   * It checks if the page and limit parameters are valid numbers and returns
   * the validated value.
   *
   * @param {Object} query - The query parameters containing the page and limit.
   * @returns {Promise<JoiValidationResult>} - The result of the validation.
   * @memberof UserValidation
   */

  async getUserPortfolio(query: { page?: number; limit?: number; search?: string; isCsv: boolean }): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        page: Joi.number().integer().min(1).default(1).optional(),
        limit: Joi.number().integer().min(1).default(10).optional(),
        search: Joi.string().default('').optional(),
        isCsv: Joi.boolean().default(false).optional(),
      });
      const { error, value } = schema.validate(query, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'getUserPortfolio Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  /**
   * Validates the get user profile request.
   *
   * This function validates the query parameters passed to get the user profile.
   * It checks if the kyc parameter is a valid boolean and returns the validated
   * value.
   *
   * @param {Object} params - The query parameters containing the kyc parameter.
   * @returns {Promise<JoiValidationResult>} - The result of the validation.
   * @memberof UserValidation
   */
  async getProfileValidation(params: object): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({
        kyc: Joi.boolean().optional().default(true).messages({
          'boolean.base': 'kyc must be true or false', // Custom error message
        }),
      });

      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'getProfileValidation Error');

      return { error: true, value: '', message: RES_MSG.COMMON.SOMETHING_WRONG };
    }
  }

  /**
   * Validates the parameters for the get user profile endpoint.
   * @param {object} params - The parameters to validate. It should contain the user id and kyc boolean.
   * @returns {Promise<JoiValidationResult>} - The result of the validation.
   * @memberof UserValidation
   */
  async getUserProfileValidation(params: object): Promise<JoiValidationResult> {
    try {
      const schema: Joi.Schema = Joi.object({
        kyc: Joi.boolean().required().default(true).messages({
          'boolean.base': 'kyc must be true or false', // Custom error message
        }),
        id: Joi.string()
          .regex(/^[a-fA-F0-9]{24}$/)
          .required()
          .messages({ 'string.pattern.base': 'Invalid user id', 'any.required': 'The ID is required.' }),
      });

      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'getProfileValidation Error');

      return { error: true, value: '', message: RES_MSG.COMMON.SOMETHING_WRONG };
    }
  }

  /**
   * Validates the parameters for the transfer agent list.
   * @param {Record<string, any>} params - The parameters to validate.
   * @returns {Promise<JoiValidationResult>} - A promise that resolves to the validated result.
   */
  async transferAgentListValidation(params: Record<string, any>): Promise<JoiValidationResult> {
    try {
      const schema = joiOptions.paginationSchema;
      const { error, value } = schema.validate(params, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'transferAgentListValidation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }
}

export default new UserValidation();
