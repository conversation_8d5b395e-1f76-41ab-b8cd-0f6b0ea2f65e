import mongoose, { Schema, Document } from 'mongoose';
import * as bcrypt from 'bcrypt';
import { KycStatusEnum, UserTypeEnum, IssuerStatusEnum, sumSubKycStatusEnum, otpMethodsEnum } from '../../../utils/common.interface';
import { RES_MSG } from '../../../utils/responseUtils';

export interface IUserModel extends Document {
  _id: string;
  countryCode?: string;
  createdAt?: Date;
  dob?: string;
  email?: string;
  is2FAActive?: boolean;
  isActive: boolean;
  isDeleted?: boolean;
  isEmailVerify: boolean;
  isIssuer?: boolean;
  isKyc?: boolean;
  onchainID?: string;
  isMobileVerify?: boolean;
  isOtpActive?: boolean;
  isSocialMedia?: boolean;
  issuerReason?: string;
  issuerStatus?: IssuerStatusEnum;
  kycApplicationId: string;
  kycCount?: number;
  kycReason?: string;
  kycStatus?: KycStatusEnum;
  levelName?: string;
  mobile?: string;
  name?: string;
  password?: string;
  sumSubKycStatus?: sumSubKycStatusEnum;
  twoFASecret?: string;
  updatedAt?: Date;
  userImage?: string;
  userType?: UserTypeEnum;
  webhookReceived?: string;
  legalFullName?: string;
  otpMethods?: otpMethodsEnum;
}

export interface IUpdateUserModel {
  _id?: string;
  countryCode?: string;
  dob?: string;
  is2FAActive?: boolean;
  isActive?: boolean;
  isEmailVerify?: boolean;
  isIssuer?: boolean;
  isKyc?: boolean;
  isMobileVerify?: boolean;
  isOtpActive?: boolean;
  isSocialMedia?: boolean;
  issuerReason?: string;
  issuerStatus?: IssuerStatusEnum;
  kycApplicationId?: string;
  kycCount?: number;
  onchainID?: string;
  kycReason?: string;
  kycStatus?: KycStatusEnum;
  levelName?: string;
  mobile?: string;
  email?: string;
  name?: string;
  password?: string;
  sumSubKycStatus?: string;
  twoFASecret?: string;
  userImage?: string;
  userType?: UserTypeEnum;
  webhookReceived?: string;
  legalFullName?: string;
  otpMethods?: otpMethodsEnum;
}

const UserSchema: Schema<IUserModel> = new Schema(
  {
    name: { type: String, required: true },
    legalFullName: { type: String, required: false },
    email: { type: String, required: true, unique: true, index: true },
    onchainID: { type: String, default: null },
    password: {
      type: String,
      required() {
        return !this.isSocialMedia;
      },
    },
    countryCode: {
      type: String,
      required() {
        return !this.isSocialMedia;
      },
    },
    mobile: {
      type: String,
      required() {
        return !this.isSocialMedia;
      },
      unique: true,
      index: true,
    },
    dob: { type: String, required: false, default: '' },
    isActive: { type: Boolean, default: true },
    isEmailVerify: { type: Boolean, default: false },
    kycApplicationId: { type: String, required: false },
    sumSubKycStatus: { type: String, enum: sumSubKycStatusEnum, required: false, default: sumSubKycStatusEnum.NOT_STARTED },
    webhookReceived: { type: String, required: false },
    levelName: { type: String, required: false },

    isMobileVerify: { type: Boolean, default: false },
    is2FAActive: { type: Boolean, default: false },
    twoFASecret: { type: String, required: false },
    isOtpActive: { type: Boolean, default: true },
    isDeleted: { type: Boolean, default: false },
    isSocialMedia: { type: Boolean, default: false },
    userType: { type: String, enum: UserTypeEnum, required: true },
    isKyc: { type: Boolean, default: false },
    kycStatus: { type: String, enum: KycStatusEnum, default: KycStatusEnum.NOT_STARTED },
    userImage: { type: String, default: null },
    kycCount: { type: Number, default: null },
    kycReason: { type: String, required: false },
    issuerReason: { type: String, required: false },
    isIssuer: { type: Boolean, default: false },
    issuerStatus: { type: String, enum: IssuerStatusEnum, default: IssuerStatusEnum.NOT_APPLIED },
  },
  { timestamps: true, versionKey: false },
);

UserSchema.post('save', (error: any, doc: any, next: any) => {
  if (error.code === 11000) {
    if (error.keyPattern && error.keyPattern.mobile) {
      next(new Error(RES_MSG.ERROR_MSG.MOBILE_NO_EXIST));
    } else if (error.keyPattern && error.keyPattern.email) {
      next(new Error(RES_MSG.USER.USER_ALREADY_EXIST));
    } else if (error.keyPattern && error.keyPattern.mainInformation.nationalIdNumber) {
      next(new Error(RES_MSG.ERROR_MSG.ID_ERR));
    } else {
      next(new Error('Duplicate key error.'));
    }
  } else {
    next(error);
  }
});
UserSchema.pre('save', async function (next) {
  const user = this as IUserModel;
  if (!user.isModified('password')) {
    return next();
  }

  try {
    const salt = await bcrypt.genSalt(10);
    user.password = await bcrypt.hash(user.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

async function hashPassword(next: any) {
  const update = this.getUpdate();

  if (update.password) {
    try {
      const salt = await bcrypt.genSalt(10);
      update.password = await bcrypt.hash(update.password, salt);
    } catch (error) {
      next(error);
    }
  }
  next();
}

UserSchema.pre('updateOne', hashPassword);
UserSchema.pre('findOneAndUpdate', hashPassword);

const userSchema = mongoose.model<IUserModel>('User', UserSchema);
export { userSchema };
