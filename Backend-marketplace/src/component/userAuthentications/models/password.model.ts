import mongoose, { Schema, Document } from 'mongoose';

export interface PasswordModel extends Document {
  _id: string;
  passwords: string[];
}

const PasswordSchema: Schema<PasswordModel> = new Schema({ passwords: { type: [String], required: true } }, { timestamps: false, versionKey: false });

const passwordSchema = mongoose.model<PasswordModel>('Password', PasswordSchema);
export { passwordSchema };
