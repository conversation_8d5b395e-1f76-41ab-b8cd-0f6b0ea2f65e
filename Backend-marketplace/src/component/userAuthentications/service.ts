import { FilterQuery, Types } from 'mongoose';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { PromiseResolve, queueMessageTypeEnum, IPagination } from '../../utils/common.interface';
import { IUserModel, IUpdateUserModel, userSchema } from './models/user.model';
import { maxPasswordHistory } from '../../utils/constant';
import logger from '../../helpers/logging/logger.helper';
import { IUserDetails, UserDetailsSchema } from './models/userDetails.model';
import kafkaService from '../../service/kafkaService';
import { PasswordModel, passwordSchema } from './models/password.model';
import { IUserService } from './interface';
import { OrderSchema } from '../order/models/order.model';
import CommonHelper from '../../helpers/common.helper';

class UserService implements IUserService {
  /**
   * Registers a new user. If the user is created successfully, it returns a
   * successful response with the newly created user object. If the user already
   * exists, it throws a conflict error. If the user creation fails, it throws an
   * internal server error.
   *
   * @param {IUserModel} body The user details to be registered. It should contain
   * the email, password, mobile number, country code, and the OTP method.
   * @returns {Promise<PromiseResolve>} A promise that resolves to a successful
   * response if the user is created successfully, or an error response if the
   * user creation fails.
   * @memberof UserService
   */
  signUp = async (body: IUserModel): Promise<PromiseResolve> => {
    try {
      body.email = body.email.toLowerCase();
      const createQuery: IUserModel = await userSchema.create(body);
      if (createQuery) {
        const userObject = createQuery.toObject();
        await this.addPassword(createQuery._id, createQuery.password);

        return { status: RESPONSES.CREATED, error: false, message: RES_MSG.SUCCESS_MSG.REGISTER_SUCCESS.replace('{methods}', body.otpMethods), data: userObject };
      }

      return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.COMMON.SOMETHING_WRONG };
    } catch (error) {
      logger.error(error, 'signUp error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * @description Fetches the user details from the database based on the given
   * search criteria. The search criteria can be an email, mobile number, or
   * username. If the user is found, it returns a successful response with the
   * user object. If the user is not found, it throws a not found error. If the
   * user fetching fails, it throws an internal server error.
   *
   * @param {IUserModel} searchDetails The search criteria to search the user in
   * the database.
   * @param {IUserModel} fields The fields to include in the response. If no
   * fields are specified, it will return all the fields.
   * @param {IUserModel} excludeFields The fields to exclude from the response.
   * If no fields are specified, it will return all the fields.
   * @param {boolean} isKyc Whether to include the KYC details in the response.
   * @returns {Promise<PromiseResolve>} A promise that resolves to a successful
   * response if the user is found, or an error response if the user is not
   * found or if the user fetching fails.
   * @memberof UserService
   */

  fetchUserDetails = async (searchDetails: FilterQuery<IUserModel>, fields?: string[], excludeFields?: string[], isKyc: boolean = false): Promise<PromiseResolve> => {
    try {
      let kycDetails: IUserDetails;
      let query: FilterQuery<IUserModel>;
      if (searchDetails?.userName) {
        query = { $or: [{ email: searchDetails.userName.toLowerCase() }, { mobile: searchDetails.userName.toLowerCase() }] };
      } else {
        query = searchDetails;
      }
      const userDetailsQuery = userSchema.findOne(query);
      // const userDetailsQuery = userSchema.findOne(searchDetails);
      if (fields && fields.length > 0) {
        const fieldsString = fields.join(' ');
        userDetailsQuery.select(fieldsString);
      }

      if (excludeFields && excludeFields.length > 0) {
        const excludeFieldsString = excludeFields.map((field) => `-${field}`).join(' ');
        userDetailsQuery.select(excludeFieldsString);
      }

      const userDetails: IUserModel = await userDetailsQuery.exec();

      if (isKyc) {
        kycDetails = await UserDetailsSchema.findOne({ _id: userDetails._id });
      }

      if (userDetails && userDetails.email) {
        return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: { ...userDetails?.toObject(), kycDetails: kycDetails ? kycDetails.toObject() : {} } };
      }

      return { status: RESPONSES.NOTFOUND, error: true, message: RES_MSG.COMMON.NO_USER };
    } catch (error) {
      logger.error(error, 'fetchUserDetails error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * Fetch user portfolio data
   * @param {string} userId - User's ID
   * @param {number} page - Page number
   * @param {number} limit - Number of items to return per page
   * @param {string} search - Search query
   * @returns {Promise<PromiseResolve>} - Array of user's portfolio items with pagination details
   * @memberof UserService
   */
  fetchUserPortfolio = async (userId: string, isCsv: any, pagination: IPagination, offeringId?: string): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, search: escapedSearch } = pagination;

      const skip = (page - 1) * limit; // Calculate the number of documents to skip
      const search = escapedSearch ? CommonHelper.escapeRegex(escapedSearch) : null;

      const matchQuery: any = {
        userId: new Types.ObjectId(userId),
        status: { $in: ['MINTED', 'BURN', 'REDEEM', 'TRANSFER_FROM', 'TRANSFER_TO', 'CONVERT_TO', 'CONVERT_FROM'] },
      };

      if (offeringId) {
        matchQuery.offeringId = new Types.ObjectId(offeringId);
      }

      const portfolioPipeline = [
        { $match: matchQuery },

        // Convert string fields to numbers
        {
          $addFields: {
            quantity: { $toDecimal: '$quantity' },
            amount: { $toDecimal: '$amount' },
            amountBeforeFee: { $toDecimal: '$amountBeforeFee' },
            feesInPercentage: { $toDecimal: '$feesInPercentage' },
            currentPrice: { $toDecimal: '$currentPrice' },
            wap: { $toDecimal: '$wap' },
          },
        },

        // Lookup details from offerings collection
        {
          $lookup: {
            from: 'offerings',
            localField: 'offeringId',
            foreignField: '_id',
            as: 'offeringDetails',
          },
        },
        { $unwind: '$offeringDetails' },

        // Group data
        {
          $group: {
            _id: '$offeringId',
            offeringDetails: { $first: '$offeringDetails' },
            userId: { $first: '$userId' },
            lastUpdated: { $first: '$updatedAt' },
            orderMinted: { $first: '$orderMinted' },
            orderId: { $first: '$_id' },
            collectionAddress: { $first: '$collectionAddress' },
            currentPrice: { $first: '$currentPrice' },

            // Calculate current quantity
            currentQuantity: {
              $sum: {
                $switch: {
                  branches: [
                    { case: { $eq: ['$status', 'MINTED'] }, then: '$quantity' },
                    { case: { $eq: ['$status', 'TRANSFER_TO'] }, then: '$quantity' },
                    { case: { $eq: ['$status', 'CONVERT_TO'] }, then: '$quantity' },
                    { case: { $in: ['$status', ['BURN', 'REDEEM', 'TRANSFER_FROM', 'CONVERT_FROM']] }, then: { $multiply: ['$quantity', -1] } },
                  ],
                  default: 0,
                },
              },
            },

            // Calculate total invested amount
            totalInvestedAmount: {
              $sum: {
                $switch: {
                  branches: [
                    { case: { $eq: ['$status', 'MINTED'] }, then: '$amount' },
                    { case: { $eq: ['$status', 'TRANSFER_TO'] }, then: '$amount' },
                    { case: { $eq: ['$status', 'CONVERT_TO'] }, then: '$amountBeforeFee' },
                    { case: { $eq: ['$status', 'REDEEM'] }, then: { $multiply: ['$wap', -1] } },
                    { case: { $eq: ['$status', 'CONVERT_FROM'] }, then: { $multiply: ['$amountBeforeFee', -1] } },
                    { case: { $in: ['$status', ['BURN', 'TRANSFER_FROM']] }, then: { $multiply: ['$amount', -1] } },
                  ],
                  default: 0,
                },
              },
            },
          },
        },

        // Add computed fields for price per token
        {
          $addFields: {
            averagePricePerToken: {
              $cond: {
                if: { $eq: ['$currentQuantity', 0] },
                then: 0,
                else: { $divide: [{ $toDecimal: '$totalInvestedAmount' }, { $toDecimal: '$currentQuantity' }] },
              },
            },
          },
        },

        // Final projection
        {
          $project: {
            _id: 0,
            userId: 1,
            tokenDecimal: '$offeringDetails.projectDetails.tokenDecimal',
            tokenAddress: '$offeringDetails.tokenAddress',
            offeringName: '$offeringDetails.projectDetails.offeringName',
            isNft: '$offeringDetails.isNft',
            icon: '$offeringDetails.overview.icon',
            cover: '$offeringDetails.overview.cover',
            logo: '$offeringDetails.overview.logo',
            offeringId: '$_id',
            totalSupply: 1,
            totalInvestedAmount: 1,
            latestNav: 1,
            averagePricePerToken: 1,
            currentPrice: '$currentPrice',
            orderMinted: 1,
            currentQuantity: 1,
            orderId: 1,
            nftDetails: {
              $cond: {
                if: '$offeringDetails.isNft',
                then: {
                  type: '$offeringDetails.type',
                  floorPrice: '$offeringDetails.floorPrice',
                  traits: '$offeringDetails.traits',
                  launchDate: '$offeringDetails.launchDate',
                  launchTime: '$offeringDetails.launchTime',
                  category: '$offeringDetails.category',
                  currency: '$offeringDetails.currency',
                  royalty: '$offeringDetails.royalty',
                  externalLink: '$offeringDetails.externalLink',
                  nftDescription: '$offeringDetails.nftDescription',
                  name: '$offeringDetails.name',
                  image: '$offeringDetails.image',
                  purchaser: '$offeringDetails.purchaser',
                  collectionAddress: '$collectionAddress',
                },
                else: '$$REMOVE',
              },
            },
            currentValue: { $multiply: ['$currentQuantity', '$currentPrice'] },
            investedAmount: { $multiply: ['$currentQuantity', '$averagePricePerToken'] },
          },
        },
        // Add profit and total gain/loss percentage
        {
          $addFields: {
            averagePricePerToken: {
              $cond: {
                if: { $eq: ['$currentQuantity', 0] },
                then: 0,
                else: {
                  $divide: ['$totalInvestedAmount', '$currentQuantity'],
                },
              },
            },
            currentPrice: '$currentPrice',
            currentQuantity: '$currentQuantity',
            totalInvestedAmount: '$totalInvestedAmount',
            investedAmount: '$investedAmount',
            currentValue: '$currentValue',
            profit: {
              $let: {
                vars: {
                  computedProfit: { $trunc: [{ $subtract: ['$currentValue', '$investedAmount'] }, 18] },
                },
                in: {
                  $cond: {
                    if: { $eq: [{ $abs: '$$computedProfit' }, 0] },
                    then: 0,
                    else: '$$computedProfit',
                  },
                },
              },
            },
            totalGainLossPercentage: {
              $cond: {
                if: { $or: [{ $eq: ['$investedAmount', 0] }, { $eq: ['$investedAmount', null] }] },
                then: 0,
                else: {
                  $let: {
                    vars: {
                      computedPercentage: {
                        $trunc: [
                          {
                            $multiply: [
                              {
                                $divide: [{ $subtract: ['$currentValue', '$investedAmount'] }, '$investedAmount'],
                              },
                              100,
                            ],
                          },
                          18,
                        ],
                      },
                    },
                    in: {
                      $cond: {
                        if: { $eq: [{ $abs: '$$computedPercentage' }, 0] },
                        then: 0,
                        else: '$$computedPercentage',
                      },
                    },
                  },
                },
              },
            },
          },
        },

        // Convert Decimal128 fields to strings to preserve precision
        {
          $addFields: {
            currentQuantity: { $toString: '$currentQuantity' },
            totalInvestedAmount: { $toString: '$totalInvestedAmount' },
            averagePricePerToken: { $toString: '$averagePricePerToken' },
            currentPrice: { $toString: '$currentPrice' },
            currentValue: { $toString: '$currentValue' },
            investedAmount: { $toString: '$investedAmount' },
            profit: { $toString: '$profit' },
            totalGainLossPercentage: { $toString: '$totalGainLossPercentage' },
          },
        },

        // Sorting
        { $sort: { orderMinted: -1 } },

        // Optional search query
        ...(search ? [{ $match: { $or: [{ offeringName: { $regex: search, $options: 'i' } }, { tokenAddress: { $regex: search, $options: 'i' } }] } }] : []),

        // Pagination and count
        {
          $facet: {
            portfolio: isCsv
              ? [] // If isCsv is true, no pagination, return all items
              : [{ $skip: skip }, { $limit: limit }], // Otherwise, apply pagination
            totalCount: [{ $count: 'count' }],
          },
        },
      ];

      const result = await OrderSchema.aggregate(portfolioPipeline as any).exec();

      const totalCount = result[0]?.totalCount[0]?.count || 0;
      const portfolio = result[0]?.portfolio || [];

      // Calculate total pages
      const totalPages = Math.ceil(totalCount / limit);

      // Calculate nextPage and previousPage
      const nextPage = page < totalPages ? page + 1 : null;
      const previousPage = page > 1 ? page - 1 : null;

      return { status: RESPONSES.SUCCESS, error: false, message: 'Portfolio found successfully!', data: { portfolio, currentPage: page, totalPages, totalCount, nextPage, previousPage } };
    } catch (error) {
      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * @param {IUpdateUserModel} data
   * @param {IUserModel} filter
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  updateUserDetails = async (data: IUpdateUserModel, filter: FilterQuery<IUserModel>): Promise<PromiseResolve> => {
    try {
      const updateUserResp = await userSchema.findOneAndUpdate(filter, data, { new: true, runValidators: true });
      if (updateUserResp) {
        const finalData = JSON.parse(JSON.stringify(data));
        await kafkaService.sendMessageToAdmin({ value: { ...finalData, type: queueMessageTypeEnum.USER, ...filter, _id: updateUserResp._id } });

        return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.USER.USER_UPDATION_SUCCESS, data: updateUserResp };
      }

      return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.ERROR_MSG.USER_UPDATION_ERROR };
    } catch (error) {
      logger.error(error, 'updateUserDetails error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * @description Updates the user KYC details in the DB.
   * @param {IUserDetails} data The KYC details to update.
   * @param {FilterQuery<IUserDetails>} filter The filter to find the user document.
   * @returns {Promise<PromiseResolve>} A promise that resolves to a successful response if the document is updated, or an error response if the document is not found or if the update fails.
   * @memberof UserService
   */
  updateUserKycDetails = async (data: IUserDetails, filter: FilterQuery<IUserDetails>): Promise<PromiseResolve> => {
    try {
      const updateUserResp = await UserDetailsSchema.findOneAndUpdate(filter, data, { new: true, runValidators: true, upsert: true });
      if (updateUserResp) {
        return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.USER.USER_UPDATION_SUCCESS, data: updateUserResp };
      }

      return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.ERROR_MSG.USER_UPDATION_ERROR };
    } catch (error) {
      logger.error(error, 'updateUserDetails error');
      if (error.keyPattern?.['mainInformation.nationalIdNumber'] || JSON.stringify(error)?.includes('nationalIdNumber')) {
        return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.ERROR_MSG.ID_ERR };
      }
      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * @description Creates a new user in the DB.
   * @param {IUserModel} body The user details to create.
   * @returns {Promise<PromiseResolve>} A promise that resolves to a successful response if the user is created successfully, or an error response if the user creation fails.
   * @memberof UserService
   */
  createUser = async (body: IUpdateUserModel): Promise<PromiseResolve> => {
    try {
      const createQuery: IUserModel = await userSchema.create(body);
      if (createQuery) {
        const finalData = JSON.parse(JSON.stringify(createQuery));
        await kafkaService.sendMessageToAdmin({ value: { ...finalData, type: queueMessageTypeEnum.USER } });

        return { status: RESPONSES.CREATED, error: false, message: RES_MSG.SUCCESS_MSG.REGISTER_SUCCESS, data: createQuery };
      }

      return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.COMMON.SOMETHING_WRONG };
    } catch (error) {
      logger.error(error, 'signUp error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * Adds a new password to the user's password history.
   * @param {string} _id The user's ID in the database.
   * @param {string} password The new password to add to the user's history.
   * @returns {Promise<PromiseResolve>} A promise that resolves to a successful response if the password is successfully added, or an error response if it fails.
   * @memberof UserService
   */
  addPassword = async (_id: string, password: string): Promise<PromiseResolve> => {
    try {
      const userPasswords = await passwordSchema.findOne({ _id });
      if (userPasswords) {
        // Check if the new password already exists in the array
        if (userPasswords.passwords.includes(password)) {
          return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.ERROR_MSG.PASSWORD_RECENTLY_USED };
        }

        userPasswords.passwords.push(password);
        if (userPasswords.passwords.length > maxPasswordHistory) {
          userPasswords.passwords.shift(); // Remove the oldest password (index 0)
        }

        const updatedQuery = await userPasswords.save();

        return { status: RESPONSES.CREATED, error: false, message: RES_MSG.SUCCESS_MSG.REGISTER_SUCCESS, data: updatedQuery };
      }
      // eslint-disable-next-line new-cap
      const newPasswordEntry = new passwordSchema({ _id, passwords: [password] });

      const createQuery = await newPasswordEntry.save();

      return { status: RESPONSES.CREATED, error: false, message: RES_MSG.SUCCESS_MSG.REGISTER_SUCCESS, data: createQuery };
    } catch (error: any) {
      logger.error(error, 'addPassword error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * Retrieves the user's recent passwords from the database.
   * @param {string} _id The user's ID in the database.
   * @returns {Promise<PromiseResolve>} A promise that resolves to a successful response with the user's recent passwords if they are found, or an error response if they are not found or if the retrieval fails.
   * @memberof UserService
   */
  fetchRecentPasswords = async (_id: string): Promise<PromiseResolve> => {
    try {
      const recentPasswords: PasswordModel[] = await passwordSchema.findOne({ _id });

      if (recentPasswords) {
        return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS, data: recentPasswords };
      }

      return { status: RESPONSES.NOTFOUND, error: true, message: RES_MSG.COMMON.NO_USER };
    } catch (error) {
      logger.error(error, 'fetchRecentPasswords error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };
}

export default new UserService();
