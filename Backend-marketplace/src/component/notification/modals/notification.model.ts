import mongoose, { Schema, Document } from 'mongoose';

// Enum for notification types (extend as needed)
enum NotificationType {
  OFFERING_UPDATE = 'create-offering',
  TRANSACTION = 'transaction',
  SYSTEM_ALERT = 'system_alert',
  GENERAL = 'general',
}

// Interface for Notification Document
interface INotification extends Document {
  userId: mongoose.Types.ObjectId;
  type: NotificationType;
  title: string;
  message?: string;
  data?: any; // Flexible field for additional details
  icon?: string;
  isRead: boolean;
  createdAt: Date;
}

// Notification Schema
const NotificationSchema = new Schema<INotification>(
  {
    userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    type: { type: String, enum: Object.values(NotificationType), required: true },
    title: { type: String, required: true },
    message: { type: String },
    data: { type: Schema.Types.Mixed }, // Store flexible extra details
    icon: { type: String, required: false }, // URL or path to an icon
    isRead: { type: Boolean, default: false }, // Flag for read/unread status
  },
  { timestamps: true },
);

// Export Model
const Notification = mongoose.model<INotification>('Notification', NotificationSchema);
export default Notification;
