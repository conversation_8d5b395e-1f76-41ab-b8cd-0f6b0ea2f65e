import { Request, Response } from 'express';
import { PromiseResolve } from '../../utils/common.interface';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { ResponseHandler } from '../../helpers/response.helper';
import logger from '../../helpers/logging/logger.helper';
import notificationClient from '../../_grpc/clients/notification.client';

/**
 * Controller for handling notification-related operations.
 * This controller manages notifications for offerings and other system events.
 */
class NotificationController {
  /**
   * Retrieves offering notifications for a user with pagination.
   * @param {Request} req - The Express request object containing user info and query parameters
   * @param {Response} res - The Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves with the notification data or error response
   */
  public getOfferingNotification = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { userInfo } = req;
      const { userId } = userInfo;
      const payload = {
        page: req.query.page,
        limit: req.query.limit,
        userId: userId,
      };
      await notificationClient.client.getIssuerNotification(payload, async (error: any, response: any) => {
        if (error || response?.error) {
          const errorMessage = error?.message || response?.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR;
          const errorStatus = error?.status || RESPONSES.INTERNAL_SERVER_ERROR;

          console.error('gRPC Error:', error || response);
          return ResponseHandler.error(res, {
            status: errorStatus,
            error: true,
            message: errorMessage,
          });
        }

        return ResponseHandler.success(res, {
          status: response.status || RESPONSES.SUCCESS,
          error: false,
          message: response.message || RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
          data: JSON.parse(response.data),
        });
      });
    } catch (error: any) {
      logger.error(error, 'getOffering Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Marks an offering notification as seen for a specific user.
   * @param {Request} req - The Express request object containing notification ID and user info
   * @param {Response} res - The Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves with success message or error response
   */
  public seenOfferingNotification = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { userInfo } = req;
      const { userId } = userInfo;
      const payload = {
        _id: req.query._id,
        userId: userId,
      };
      await notificationClient.client.seenIssuerNotification(payload, async (error: any, response: any) => {
        if (error || response?.error) {
          const errorMessage = error?.message || response?.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR;
          const errorStatus = error?.status || RESPONSES.INTERNAL_SERVER_ERROR;

          console.error('gRPC Error:', error || response);
          return ResponseHandler.error(res, {
            status: errorStatus,
            error: true,
            message: errorMessage,
          });
        }

        return ResponseHandler.success(res, {
          status: response.status || RESPONSES.SUCCESS,
          error: false,
          message: response.message || RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
          data: JSON.parse(response.data),
        });
      });
    } catch (error: any) {
      logger.error(error, 'getOffering Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };
}

export default new NotificationController();
