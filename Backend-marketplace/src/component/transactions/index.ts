import { Request, Response } from 'express';
import { Types } from 'mongoose';
import { PromiseResolve } from '../../utils/common.interface';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { <PERSON><PERSON>and<PERSON> } from '../../helpers/response.helper';
import logger from '../../helpers/logging/logger.helper';
import TransactionsService from './service';
import BondingClient from '../../_grpc/clients/bonding.client';

/**
 * Controller for handling transaction-related operations.
 * This controller manages the retrieval and processing of financial transactions.
 */
class TransactionController {
  /**
   * Retrieves transactions with optional filtering, pagination, and sorting.
   * @param {Request} req - The Express request object containing query parameters and user info
   * @param {Response} res - The Express response object
   * @returns {Promise<PromiseResolve|void>} A promise that resolves with the transaction data or error response
   */
  public getTransactions = async (req: Request, res: Response): Promise<PromiseResolve | void> => {
    try {
      const { page = 1, limit = 10, sort = '', search = '', type = '', isCsv } = req.query;
      const userId = new Types.ObjectId(req.userInfo.userId);
      const offeringId = new Types.ObjectId(req.params.offeringId);

      const filters = { ...(type && { status: type?.toString().toUpperCase() }), userId, offeringId };

      const result: PromiseResolve = await TransactionsService.fetchTransactions(filters, { page: Number(page), limit: Number(limit), ...(search && { search }), ...(sort && { sort }) }, isCsv);

      return ResponseHandler.success(res, { status: result.status || RESPONSES.SUCCESS, error: false, message: result.message || RES_MSG.USER.USERS_FETCH, data: result.data || [] });
    } catch (error) {
      logger.error(error, 'getTransactions Error');

      return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
    }
  };

  /**
   * Retrieves staking transactions for a specific token with pagination.
   * Calls bonding service via gRPC to fetch staking transaction data.
   * @param {Request} req - The Express request object containing token and pagination parameters
   * @param {Response} res - The Express response object
   * @returns {Promise<PromiseResolve|void>} A promise that resolves with the staking transaction data or error response
   */
  public getStakingTransactions = async (req: Request, res: Response): Promise<PromiseResolve | void> => {
    try {
      const { page, limit, search, userAddress, type } = req.query;
      const { token } = req.params;

      const pagination = {
        page: Number(page),
        limit: Number(limit),
        search: String(search || ''),
      };

      // Call bonding service via gRPC using class-based client
      const result: any = await BondingClient.getStakingTransactions(token as string, pagination, userAddress as string, type as string);

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: 'Staking transactions fetched successfully',
        data: result,
      });
    } catch (error) {
      logger.error(error, 'getStakingTransactions Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Retrieves user staking summary for a specific token with pagination.
   * Calls bonding service via gRPC to fetch aggregated user staking data.
   * @param {Request} req - The Express request object containing token and pagination parameters
   * @param {Response} res - The Express response object
   * @returns {Promise<PromiseResolve|void>} A promise that resolves with the user staking summary data or error response
   */
  public getUserStakingSummary = async (req: Request, res: Response): Promise<PromiseResolve | void> => {
    try {
      const { page, limit, search } = req.query;
      const { token } = req.params;

      const pagination = {
        page: Number(page),
        limit: Number(limit),
        search: String(search || ''),
      };

      // Call bonding service via gRPC using class-based client
      const result: any = await BondingClient.getUserStakingSummary(token as string, pagination);

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: 'User staking summary fetched successfully',
        data: result,
      });
    } catch (error) {
      logger.error(error, 'getUserStakingSummary Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Retrieves staking dividend list for a specific token and month with pagination.
   * Calls bonding service via gRPC to fetch monthly dividend/reward data.
   * @param {Request} req - The Express request object containing token, month and pagination parameters
   * @param {Response} res - The Express response object
   * @returns {Promise<PromiseResolve|void>} A promise that resolves with the staking dividend list data or error response
   */
  public getStakingDividendList = async (req: Request, res: Response): Promise<PromiseResolve | void> => {
    try {
      const { page, limit, search } = req.query;
      const { token, month } = req.params;

      const pagination = {
        page: Number(page),
        limit: Number(limit),
        search: String(search || ''),
      };

      // Call bonding service via gRPC using class-based client
      const result: any = await BondingClient.getStakingDividendList(token as string, month as string, pagination);

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: 'Staking dividend list fetched successfully',
        data: result,
      });
    } catch (error) {
      logger.error(error, 'getStakingDividendList Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Updates staking reward amount for a specific token and month.
   * Calls bonding service via gRPC to update totalRewardAmount for all records matching token and month.
   * @param {Request} req - The Express request object containing token, month in path params and totalRewardAmount in body
   * @param {Response} res - The Express response object
   * @returns {Promise<PromiseResolve|void>} A promise that resolves with the update result or error response
   */
  public updateStakingRewardAmount = async (req: Request, res: Response): Promise<PromiseResolve | void> => {
    try {
      const { token, month } = req.params;
      const { totalRewardAmount } = req.body;

      // Validate input
      if (!token || !month) {
        return ResponseHandler.error(res, {
          status: RESPONSES.BAD_REQUEST,
          error: true,
          message: 'Token and month are required in path parameters',
        });
      }

      if (totalRewardAmount === undefined || totalRewardAmount === null) {
        return ResponseHandler.error(res, {
          status: RESPONSES.BAD_REQUEST,
          error: true,
          message: 'totalRewardAmount is required in request body',
        });
      }

      if (totalRewardAmount < 0) {
        return ResponseHandler.error(res, {
          status: RESPONSES.BAD_REQUEST,
          error: true,
          message: 'Total reward amount must be non-negative',
        });
      }

      // Call bonding service via gRPC using class-based client
      const result: any = await BondingClient.updateStakingRewardAmount(token as string, month as string, Number(totalRewardAmount));

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: 'Staking reward amount updated successfully',
        data: result,
      });
    } catch (error) {
      logger.error(error, 'updateStakingRewardAmount Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };
}

export default new TransactionController();
