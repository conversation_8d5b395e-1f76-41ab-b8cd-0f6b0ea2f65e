import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { PromiseResolve, IPagination } from '../../utils/common.interface';
import { ITransactionsService } from './interface';
import logger from '../../helpers/logging/logger.helper';
import { OrderSchema } from '../order/models/order.model';
import { FilterQuery } from 'mongoose';

class TransactionsService implements ITransactionsService {
  /**
   * Fetch transactions with filtering and pagination
   * @param {EventLog} filters
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  fetchTransactions = async (filters: FilterQuery<any>, pagination: IPagination, isCsv: any): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, search } = pagination;
      const skip = (page - 1) * limit;
      const match: any = {
        offeringId: filters.offeringId,
        status: { $nin: ['PENDING', 'APPROVED'] },
      };
      const sort = filters.status === 'MINTED' ? { orderMinted: -1 } : { updatedAt: -1 };
      // if (search) {
      //   match.$or = [{ 'name': { $regex: search, $options: 'i' } }, { 'email': { $regex: search, $options: 'i' } }];
      // }
      if (filters.status) {
        match.orderType = filters.status.toUpperCase();
      } else {
        match.orderType = { $in: ['MINTED', 'FREEZE', 'UNFREEZE', 'CONVERT'] };
      }

      const pipeline: any = [
        { $match: match },
        {
          $lookup: { from: 'users', localField: 'userId', foreignField: '_id', as: 'userDetails' },
        },
        {
          $unwind: { path: '$userDetails', preserveNullAndEmptyArrays: true },
        },
        {
          $addFields: { name: '$userDetails.name', email: '$userDetails.email', userImage: '$userDetails.userImage' },
        },
        {
          $match: search
            ? {
                $or: [{ name: { $regex: search, $options: 'i' } }, { email: { $regex: search, $options: 'i' } }],
              }
            : {},
        },
        {
          $project: { userDetails: 0 },
        },
        {
          $facet: {
            metadata: [
              {
                $count: 'totalCount',
              },
            ],
            orders: isCsv
              ? []
              : [
                  { $skip: skip },
                  { $limit: limit },
                  {
                    $sort: sort,
                  },
                ],
          },
        },
      ];
      const results = await OrderSchema.aggregate(pipeline);
      const metadata = results[0]?.metadata[0] || { totalCount: 0 };
      const orders = results[0]?.orders || [];
      const { totalCount } = metadata;
      const totalPages = Math.ceil(totalCount / limit);
      const nextPage = page < totalPages ? page + 1 : null;
      const previousPage = page > 1 ? page - 1 : null;

      return { status: RESPONSES.SUCCESS, error: false, message: RES_MSG.COMMON.RECORD_FETCH, data: { transactions: orders, currentPage: page, totalPages, totalCount, nextPage, previousPage } };
    } catch (error) {
      logger.error(error, 'fetchTransactions error');

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };
}

export default new TransactionsService();
