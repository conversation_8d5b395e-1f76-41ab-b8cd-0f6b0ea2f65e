import mongoose, { Schema, Types } from 'mongoose';
import { offeringStatusEnum } from '../../../utils/common.interface';

export interface IRequestedOffering {
  _id?: string;
  userId?: Types.ObjectId;
  subAdminId?: Types.ObjectId;
  status?: offeringStatusEnum;
}

const requestedOffering: Schema<IRequestedOffering> = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, ref: 'users', required: true },
    subAdminId: { type: Schema.Types.ObjectId, ref: 'users', required: false },
    status: { type: String, enum: Object.values(offeringStatusEnum), default: offeringStatusEnum.PENDING },
  },

  { timestamps: true, versionKey: false },
);

const requestedOfferingSchema = mongoose.model<IRequestedOffering>('requestedOfferings', requestedOffering);

export { requestedOfferingSchema };
