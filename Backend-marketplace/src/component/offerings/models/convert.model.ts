import mongoose, { Schema, Document } from 'mongoose';

// Interface for the Convert document
export interface IConvert extends Document {
  tokenAddress?: string;
  erc20Address?: string;
  createdAt?: Date;
}
export interface IUpdateConvert {
  tokenAddress?: string;
  erc20Address?: string;
  createdAt?: Date;
}
// Define the Convert Schema
const convertlist: Schema<IConvert> = new Schema(
  {
    tokenAddress: {
      type: String,
      required: true,
      trim: true,
    },
    erc20Address: {
      type: String,
      required: true,
      trim: true,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  },
);

// Create an index for `tokenAddress` to enforce uniqueness
convertlist.index({ tokenAddress: 1 }, { unique: true });

const convertlistSchema = mongoose.model<IConvert>('convertlist', convertlist);

export { convertlistSchema };
