import { FilterQuery, Types } from 'mongoose';
import { IPagination, PromiseResolve } from '../../utils/common.interface';
import { IOffering, IUpdateOffering } from './models/offerings.model';
import { IUpdateWhitelist, IWhitelist } from './models/whitelist.model';
import { IRequestedOffering } from './models/requestedOfferings.model';

export interface IOfferingsService {
  /**
   * @param {IOffering} searchDetails
   * @param {IOffering} fields
   * @param {IOffering} excludeFields
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */

  fetchOfferingDetails(searchDetails: FilterQuery<IOffering>, fields?: string[], excludeFields?: string[], userId?: string): Promise<PromiseResolve>;

  /**
   * @param {IUpdateOffering} body
   * @param {IOffering} filter
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  updateOfferingDetails(body: IUpdateOffering, filter: FilterQuery<IOffering>): Promise<PromiseResolve>;

  /**
   * @param {IUpdateWhitelist} body
   * @param {IWhitelist} filter
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  updateWhitelistDetails(body: IUpdateWhitelist, filter: FilterQuery<IWhitelist>): Promise<PromiseResolve>;

  /**
   * @param {IUpdateOffering} body
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  createOffering(body: IUpdateOffering): Promise<PromiseResolve>;

  /**
   * @param {IUpdateOffering} searchDetails
   * @param {Array} projection
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */

  fetchOfferingList(searchDetails: IUpdateOffering, projection?: string[], pagination?: IPagination, isCsv?: any): Promise<PromiseResolve>;

  /**
   * @param {IOffering} searchDetails
   * @param {Array} projection
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */

  fetchNavHistory(searchDetails: any, projection?: string[], pagination?: IPagination): Promise<PromiseResolve>;

  /**
   * @param {IOffering} searchDetails
   * @param {Array} projection
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */

  offeringList(searchDetails: any, projection?: string[], pagination?: IPagination): Promise<PromiseResolve>;

  /**
   * @param {IRequestedOffering} body
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  requestOffering(body: IRequestedOffering): Promise<PromiseResolve>;

  /**
   * @param {IRequestedOffering} body
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  requestedOfferings(filters: any, pagination: IPagination): Promise<PromiseResolve>;

  /**
   * @param {IUpdateWhitelist} body
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  createWhitelist(body: IUpdateWhitelist): Promise<PromiseResolve>;

  /**
   * @param {IOffering} searchDetails
   * @param {IOffering} fields
   * @param {IOffering} excludeFields
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */

  fetchSubscriberDetails(searchDetails: FilterQuery<IOffering>, fields?: string[], excludeFields?: string[]): Promise<PromiseResolve>;

  /**
   * @param {IOffering} searchDetails
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  getOfferingSubscriberList(searchDetails: any, isCsv: any, pagination?: IPagination): Promise<PromiseResolve>;

  /**
   * @param {IUpdateOffering} searchDetails
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  getTotalCount(searchDetails: IUpdateOffering): Promise<PromiseResolve>;

  /**
   * @param {IUpdateOffering} searchDetails
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  singleOfferingReport(searchDetails: IUpdateOffering): Promise<PromiseResolve>;

  /**
   * @param {IUpdateOffering} searchDetails
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  offeringReport(searchDetails: IUpdateOffering): Promise<PromiseResolve>;

  /**
   * @param {IUpdateOffering} searchDetails
   * @param {IPagination} pagination
   * @param {string} type
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  getTopOffering(searchDetails: IUpdateOffering, pagination: IPagination, type: string): Promise<PromiseResolve>;

  /**
   * @param {IUpdateOffering} searchDetails
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  investedOffering(searchDetails: IUpdateOffering, pagination: IPagination): Promise<PromiseResolve>;

  /**
   * @param {IUpdateOffering} whitelistId
   * @param {IPagination} status
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  rejectWalletWhiteList(whitelistId: Types.ObjectId, status: string): Promise<PromiseResolve>;

  /**
   * @param {IUpdateOffering} offeringId
   * @param {I} data
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  tokenValuation(offeringId: IUpdateOffering, data?: any): Promise<PromiseResolve>;

  fetchOrdersFromOffering(userId: string, offeringId: any, page: number, limit: number, status: string, search: string, isCsv: any): Promise<PromiseResolve>;
}
