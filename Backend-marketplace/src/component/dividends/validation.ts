import * as Jo<PERSON> from 'joi';
import { JoiValidationResult } from '../../utils/common.interface';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import logger from '../../helpers/logging/logger.helper';
import * as joiOptions from '../../helpers/joi.helper';
import CommonHelper from '../../helpers/common.helper';

export default class DividentsValidation {
  /**
   * This method creates a schema for validating the request body for creating a dividend.
   *
   * The schema consists of the following properties:
   * - offeringId: A string that represents the Offering Id and must be a valid MongoDB ObjectId.
   * - recordDate: A date that represents the record date for the dividend and must be a future date.
   * - dividendAmount: A number that represents the amount of the dividend and is a required field.
   * - paymentMethod: An object that represents the payment method for the dividend and must be either "ETH" or "USD".
   *
   * @returns {Joi.ObjectSchema} The schema for validating the request body.
   */
  static createDividendSchema() {
    return Joi.object({
      offeringId: Joi.string().length(24).hex().required().messages({ 'string.length': 'Invalid Offering Id', 'string.hex': '"offeringId" must be a valid MongoDB ObjectId', 'any.required': '"offeringId" is a required field' }),
      recordDate: Joi.date().greater('now').required().messages({ 'date.base': '"recordDate" must be a valid date', 'date.greater': '"recordDate" must be a future date', 'any.required': '"recordDate" is a required field' }),
      dividendAmount: Joi.number()
        .greater(0)
        .required()
        .custom((value, helpers) => {
          if (!/^\d+(\.\d{1,2})?$/.test(value.toString())) {
            return helpers.error('number.decimalPlaces', { limit: 2 });
          }

          return value;
        })
        .messages({
          'number.base': '"dividendAmount" must be a valid number',
          'number.greater': '"dividendAmount" must be greater than 0',
          'number.decimalPlaces': '"dividendAmount" must not have more than 2 decimal places',
          'any.required': '"dividendAmount" is a required field',
        }),
      paymentMethod: joiOptions.paymentTypeSchema,
    });
  }

  static async createDividend(params: Record<string, any>): Promise<JoiValidationResult> {
    try {
      const schema = this.createDividendSchema(); // Use the new schema here
      const { error, value } = schema.validate(params, joiOptions.options);

      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'createDividend');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }
  /**
   * Validates the request query for fetching dividends.
   * @param {Record<string, any>} query - The request query object.
   * @returns {Promise<JoiValidationResult>} A promise that resolves to a JoiValidationResult.
   */
  static async getDividendsValidation(query: { page?: number; limit?: number; search?: string }): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        offeringId: Joi.string().length(24).hex().required(),
        page: Joi.number().integer().min(1).default(1).optional(),
        limit: Joi.number().integer().min(1).default(10).optional(),
        search: Joi.string()
          .allow('')
          .optional()
          .custom((value) => CommonHelper.escapeRegex(value), 'Sanitize search string'),
        isCsv: Joi.boolean().default(false).optional(),
      });

      const { error, value } = schema.validate(query, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'getDividendsValidation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }

  /**
   * Validate the request query for fetching dividends by an investor.
   *
   * This function validates the query parameters for fetching dividends. It checks
   * if the offeringId is a valid MongoDB ObjectId, if dividendType is a valid string,
   * and if the page and limit parameters are valid numbers.
   *
   * @param {Object} query - The request query object containing the parameters.
   * @returns {Promise<JoiValidationResult>} - The result of the validation.
   */ static async getDividendsInvestorValidation(query: { offeringId?: string; dividendType?: string; page?: number; limit?: number }): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        offeringId: Joi.string().length(24).hex().required(),
        dividendType: Joi.string().min(3).max(3).required(),
        page: Joi.number().integer().min(1).default(1).optional(),
        limit: Joi.number().integer().min(1).default(10).optional(),
        search: Joi.string()
          .allow('')
          .optional()
          .custom((value) => CommonHelper.escapeRegex(value), 'Sanitize search string'),
      });

      const { error, value } = schema.validate(query, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'getDividendsValidation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }
  /**
   * Validates the request query for calculating dividends.
   *
   * This function validates the query parameters to ensure that the offeringId
   * is a valid MongoDB ObjectId. If the validation fails, it returns an error
   * response with a descriptive message.
   *
   * @param {Object} query - The request query object containing the parameters.
   * @returns {Promise<JoiValidationResult>} - The result of the validation.
   */
  static async calculate(query: { page?: number; limit?: number; search?: string }): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        offeringId: Joi.string().length(24).hex().required().messages({ 'string.length': 'Invalid Offering Id', 'string.hex': '"offeringId" must be a valid MongoDB ObjectId', 'any.required': '"offeringId" is a required field' }),
      });

      const { error, value } = schema.validate(query, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'calculate dividend Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }
  /**
   * Validates the request query for getting the dividend history for a user.
   *
   * This function validates the query parameters to ensure that the userAddress
   * is a valid Ethereum address. If the validation fails, it returns an error
   * response with a descriptive message.
   *
   * @param {Object} query - The request query object containing the userAddress.
   * @returns {Promise<JoiValidationResult>} - The result of the validation.
   */
  static async history(query: { page?: number; limit?: number; search?: string }): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        userAddress: Joi.string()
          .pattern(/^0x[a-fA-F0-9]{40}$/)
          .required()
          .messages({ 'string.pattern.base': '"userAddress" must be a valid Ethereum address', 'any.required': '"userAddress" is a required field' }),
      });

      const { error, value } = schema.validate(query, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'investor history Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }
  /**
   * Validates the request query for getting the dividend history for an investor.
   *
   * @param {Object} query - The request query object containing the status, page and limit.
   * @returns {Promise<JoiValidationResult>} - The result of the validation.
   */
  static async getinvestorDividendsHistoryValidation(query: { page?: number; limit?: number; search?: string }): Promise<JoiValidationResult> {
    try {
      const schema = Joi.object({
        status: Joi.string().length(24).valid('ALL', 'PENDING', 'COMPLETED').required(),
        page: Joi.number().integer().min(1).default(1).optional(),
        limit: Joi.number().integer().min(1).max(10).default(10).optional(),
        isCsv: Joi.boolean().default(false).optional(),
        search: Joi.string()
          .allow('')
          .optional()
          .custom((value) => CommonHelper.escapeRegex(value), 'Sanitize search string'),
      });

      const { error, value } = schema.validate(query, joiOptions.options);
      if (error) {
        return { error: true, value: '', message: error.details[0].message, status: RESPONSES.BAD_REQUEST };
      }

      return { error: false, value };
    } catch (error) {
      logger.error(error, 'getDividendsValidation Error');

      return { error: true, value: '', message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: RESPONSES.INTERNAL_SERVER_ERROR };
    }
  }
}
