import mongoose, { Schem<PERSON> } from 'mongoose';

const dividendHistory = new mongoose.Schema(
  {
    userAddress: { type: String, required: true, match: /^0x[a-fA-F0-9]{40}$/, lowercase: true },
    dividendId: { type: Schema.Types.ObjectId, required: true, unique: false },
    amount: { type: String, required: true, min: 0 },
    netAmount: { type: String, required: true, min: 0 },
    transactionHash: { type: String, required: true },
    event: { type: String, required: true },
    status: {
      type: String,
      enum: ['SUCCESS', 'ERROR'],
      required: false, // Optional
    },
  },
  { timestamps: true, versionKey: false, strict: true },
);

// Creating a unique index on userAddress and dividendId
dividendHistory.index({ userAddress: 1, dividendId: 1 }, { unique: true });

const DividendHistory = mongoose.model('DividendHistory', dividendHistory);

export { DividendHistory };
