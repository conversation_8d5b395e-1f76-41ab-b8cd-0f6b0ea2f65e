import mongoose, { Schema } from 'mongoose';
import { paymentTypeEnum } from '../../../utils/common.interface';

const dividendReqesutSchema = new Schema(
  {
    offeringId: { type: Schema.Types.ObjectId, ref: 'offerings', required: true },
    offeringName: { type: String, required: true },
    walletAddress: { type: String, set: (value: string) => (value ? value.toLowerCase() : value) },
    dividendAmount: { type: Number },
    status: { type: String, default: 'ACTIVE', enum: ['ACTIVE', 'COMPLETED'], required: false },
    dividendType: { type: String, required: true },
    paymentMethod: { type: String, enum: Object.values(paymentTypeEnum), default: paymentTypeEnum.USDT },
    documentUrl: { type: String, required: false },
    declarationDate: { type: Date, required: true, default: Date.now },
    recordDate: { type: Date, required: true },
    issuerId: { type: Schema.Types.ObjectId, ref: 'users', required: true },
  },
  { timestamps: true, versionKey: false },
);

const DividendReq = mongoose.model('dividendreq', dividendReqesutSchema);

export { DividendReq };
