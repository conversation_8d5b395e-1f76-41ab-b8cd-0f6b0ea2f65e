/* eslint-disable eqeqeq */
/* eslint-disable camelcase */
import { Request } from 'express';
import axios, { HttpStatusCode } from 'axios';
import { Types } from 'mongoose';
import { ethers } from 'ethers';
import CustomError from '../../helpers/customError.helper';
import CloudHelper from '../../helpers/cloud.helper';
import { DocumentTypesEnum, PromiseResolve } from '../../utils/common.interface';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import { DividendReq } from './models/dividend';
import { offeringSchema } from '../offerings/models/offerings.model';
import { whitelistSchema } from '../offerings/models/whitelist.model';
import config from '../../config/env';
import { userSchema } from '../userAuthentications/models/user.model';
import { DividendHistory } from './models/dividendHistory';
import contractABI from '../../utils/abi/fund';
// import usdcAbi from '../../utils/usdcAbi';
import logger from '../../helpers/logging/logger.helper';
import Big from 'big.js'; // Ensure Big.js is imported
import CommonHelper from '../../helpers/common.helper';
import dividendRecords from './models/dividendRecords';

class DividendsService {
  fundFactoryInstance: any;
  constructor() {
    const provider = new ethers.JsonRpcProvider(config.RPC_URL);
    this.fundFactoryInstance = new ethers.Contract(config.FUND_CONTRACT_ADDRESS, contractABI, provider);
  }

  getDividentAmount = async (address: string[], contractaddress: string, total_dividend_amount: number, whitelistData: any[], price: number) => {
    try {
      const { data } = await axios.post(config.CAP_TABLE_URL, { address, contractaddress, total_dividend_amount });

      if (data.success == false) {
        return {};
      }

      const mergedData = data?.map((token: { address: any; balance: any; dividend: any }) => {
        const matchingWhitelist = whitelistData.find((whitelist) => whitelist.address === token.address);

        const dividendBig = new Big(token.dividend);
        const priceBig = new Big(price);
        const yeildValue = dividendBig.div(priceBig).toFixed(6).toString();

        return { address: token.address, balance: token.balance, userId: matchingWhitelist ? matchingWhitelist.userId : null, dividentPerToken: token.dividend, yield: yeildValue };
      });

      return mergedData;
    } catch (error) {
      logger.error(error);
      throw new CustomError('Failed to get dividend Amount', HttpStatusCode.BadRequest);
    }
  };
  /**
   * Validates if the provided record date is in the future.
   * @param date - The record date to validate.
   * @returns {boolean} - Returns true if the record date is in the future, otherwise false.
   */
  validateRecordDate(date: string): boolean {
    return new Date(date) > new Date();
  }

  /**
   * Checks if all users received the dividend for a given offering.
   * @param offeringId - The id of the offering.
   * @param _id - The id of the dividend document.
   * @returns {Promise<boolean>} - Returns true if all users received the dividend, otherwise false.
   */
  checkDividendCompleted = async (offeringId: string, _id: any): Promise<boolean> => {
    try {
      const usersPresentForDividend = await whitelistSchema.find({ offeringId: new Types.ObjectId(offeringId), status: 'APPROVED' }).lean();
      if (!usersPresentForDividend || usersPresentForDividend.length === 0) {
        return false;
      }

      const allUserAddresses = usersPresentForDividend.map((user) => user.address);

      const offering = await offeringSchema.findById(offeringId).select('tokenAddress');
      // filter out address which has zero token holdings
      const response = await axios.post(config.CAP_TABLE_URL, { address: allUserAddresses, contractaddress: offering.tokenAddress.toString(), total_dividend_amount: 1 });

      if (response?.data?.status == 500) {
        return false;
      }
      const { data } = response;
      const userAddresses = data.filter((user: any) => user.balance !== '0').map((e: any) => e.address);

      const dividendDistributed = await DividendHistory.find({ userAddress: { $in: userAddresses }, dividendId: new Types.ObjectId(_id.toString()) });
      // Check if all users received the dividend
      if (dividendDistributed.length >= userAddresses.length) {
        await DividendReq.findOneAndUpdate({ _id }, { status: 'COMPLETED' });

        return true;
      }

      return false;
    } catch (error) {
      logger.error(error);
      console.error('error===>\n\n', error.response.data.error);
      throw new CustomError(error.response.data.error, HttpStatusCode.BadRequest);
    }
  };
  /**
   * Create a new dividend request.
   * @param req - The request object containing the dividend information.
   * @param userId - The id of the user creating the dividend.
   * @returns {Promise<PromiseResolve>} - Returns a promise that resolves to an object containing the status, error, message, and data of the response.
   */
  createDividendReq = async (req: Request, userId: string) => {
    try {
      const { file } = req;
      const { offeringId, dividendAmount, recordDate, paymentMethod } = req.body;

      // Validate record date
      const isValidRecordDate = this.validateRecordDate(recordDate);
      if (!isValidRecordDate) {
        throw new CustomError('Invalid record date!', HttpStatusCode.BadRequest);
      }

      // Check if offering exists
      const offering = await offeringSchema.findById(offeringId).lean();
      if (!offering) {
        throw new CustomError('Offering not found!', HttpStatusCode.NotFound);
      }

      // Step 1: Check record date of the last dividend
      const lastDividend = await DividendReq.findOne({ offeringId }).sort({ recordDate: -1 }).lean();

      if (lastDividend) {
        if (new Date(lastDividend.recordDate) >= new Date(recordDate)) {
          throw new CustomError('Record date must be greater than the previous dividend', HttpStatusCode.BadRequest);
        }

        // Step 2: Check if the previous dividend is completed
        const checkLastDividendStatus = await dividendRecords.findOne({ dividendId: new Types.ObjectId(lastDividend._id.toString()) });
        const { status } = checkLastDividendStatus;
        if (status == 'COMPLETED') {
          await DividendReq.findOneAndUpdate({ _id: new Types.ObjectId(lastDividend._id.toString()) }, { status: 'COMPLETED' });
        } else {
          throw new CustomError('Previous dividend is not distributed', HttpStatusCode.BadRequest);
        }
      }

      // Check if the offering is whitelisted
      const whitelistedUsers = await whitelistSchema.find({ offeringId, status: 'APPROVED' }).select('address userId').lean();

      if (!whitelistedUsers.length) {
        throw new CustomError('No whitelisted users found for this offering!', HttpStatusCode.BadRequest);
      }

      // Create the new dividend
      const newRecordDate = new Date(recordDate);
      const dividendType = `D-${(await DividendReq.countDocuments({ offeringId })) + 1}`;

      const _id = new Types.ObjectId();

      let documentUrl: string | undefined;
      if (file) {
        const uploadResult: PromiseResolve = await CloudHelper.uploadFiles(userId, file, DocumentTypesEnum.DIVIDEND, _id.toString());
        if (uploadResult.error) {
          throw new CustomError(uploadResult.message || 'File upload failed!', uploadResult.status || HttpStatusCode.BadGateway);
        }
        documentUrl = uploadResult?.data?.url;
      }

      const dividend = await DividendReq.create([{ _id, dividendAmount, recordDate: newRecordDate, paymentMethod, documentUrl, dividendType, offeringName: offering.projectDetails.offeringName, offeringId, issuerId: userId }]);

      return { status: RESPONSES.CREATED, error: false, message: RES_MSG.SUCCESS_MSG.CREATE_SUCCESS, data: { dividendId: dividend[0]._id } };
    } catch (error) {
      logger.error(error);

      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * Retrieves dividend requests for an offering
   * @param query - The query parameters to filter dividend requests
   * @returns {Promise<PromiseResolve>} - Returns a promise that resolves to an object containing the status, error, message, and data of the response
   */
  getDividentReq = async (query: Record<string, string>) => {
    try {
      const { page, limit, offeringId, isCsv } = query;

      // Validate offeringId format
      if (!Types.ObjectId.isValid(offeringId)) {
        throw new CustomError('Invalid offeringId format', HttpStatusCode.BadRequest);
      }

      const pageNumber = Number(page) || 1;
      const pageSize = Number(limit) || 10;

      let dividends = await DividendReq.aggregate([
        { $match: { offeringId: new Types.ObjectId(offeringId) } },
        { $lookup: { from: 'offerings', localField: 'offeringId', foreignField: '_id', as: 'offeringDetails' } },
        { $lookup: { from: 'dividendrecords', localField: '_id', foreignField: 'dividendId', as: 'dividendRecords' } },
        { $unwind: '$offeringDetails' },
        { $match: { 'offeringDetails.status': 'APPROVED', 'offeringDetails.isTokenDeploy': true } },
        { $lookup: { from: 'dividendrecords', localField: '_id', foreignField: 'dividendId', as: 'dividendRecords' } },
        {
          $project: {
            _id: 1,
            latestNav: {
              $cond: [
                { $eq: [{ $size: { $ifNull: ['$dividendRecords.navPrice', []] } }, 0] },
                {
                  $cond: [
                    { $ne: ['$offeringDetails.projectDetails.assetType', 'Real Estate'] },
                    {
                      $divide: ['$offeringDetails.projectDetails.launchValuation', '$offeringDetails.projectDetails.tokenSupply'],
                    },
                    {
                      $divide: ['$offeringDetails.projectDetails.latestNav', '$offeringDetails.projectDetails.tokenSupply'],
                    },
                  ],
                },
                { $arrayElemAt: ['$dividendRecords.navPrice', 0] }, // Use first value if array is not empty
              ],
            },
            offeringName: 1,
            dividendAmount: 1,
            offeringImage: '$offeringDetails.overview.icon',
            documentUrl: 1,
            dividendType: 1,
            paymentMethod: 1,
            recordDate: 1,
            issuerId: 1,
            declarationDate: 1,
            createdAt: 1,
            updatedAt: 1,
            status: 1,
            dividendPerToken: { $arrayElemAt: ['$dividendRecords.dividendPerToken', 0] },
            yield: { $arrayElemAt: ['$dividendRecords.yield', 0] },
            isTokenDeploy: '$offeringDetails.isTokenDeploy',
            tokenAddress: '$offeringDetails.tokenAddress',
          },
        },
        { $sort: { createdAt: -1 } },
        ...(!isCsv ? [{ $skip: (pageNumber - 1) * pageSize }, { $limit: pageSize }] : []),
      ]);

      if (!dividends.length) {
        return { status: RESPONSES.SUCCESS, error: false, message: 'Dividends not found!', data: { dividends } };
      }

      // Step: Update the previous dividend status if distribution is completed
      const lastDividend = await DividendReq.findOne(
        { offeringId: new Types.ObjectId(offeringId) },
        {},
        { sort: { declarationDate: -1 } }, // Get the latest declaration
      );

      if (lastDividend) {
        const findDividendInRecord = await dividendRecords.findOne({ dividendId: lastDividend._id });

        if (findDividendInRecord && findDividendInRecord.status) {
          await DividendReq.findOneAndUpdate(
            { _id: lastDividend._id },
            {
              $set: { status: findDividendInRecord.status },
            },
          );
        }
      }
      // fetch all token address and dividend amount
      const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));
      await delay(1000);

      // More concise version using filter + map
      const contractAddresses = dividends.filter((e) => e?.status !== 'COMPLETED').map((e) => e.tokenAddress);

      const dividendAmounts = dividends.filter((e) => e?.status !== 'COMPLETED').map((e) => e.dividendAmount ?? 0);

      let divPerTokenMap = new Map();
      if (contractAddresses.length > 0) {
        try {
          const yieldAPIUrl = config.CAP_TABLE_URL.replace('cap-table', 'yeild');
          const response = await axios.post(yieldAPIUrl, { contractaddress: contractAddresses, dividend_amount: dividendAmounts });
          divPerTokenMap = new Map(response?.data?.data?.map((e: any) => [e.dividendAmount, e.dividendPerToken]) || []);
        } catch (error) {
          console.error('❌ [ERROR] Yield API Request Failed:', error.message);
        }
      }

      // Fetch all token addresses in a single query
      const tokenAddresses = dividends.map((e) => e.tokenAddress);
      const offeringDataMap = await offeringSchema
        .find({ tokenAddress: { $in: tokenAddresses } })
        .select('tokenAddress projectDetails')
        .lean();

      const offeringDataLookup = new Map(offeringDataMap.map((offer) => [offer.tokenAddress, offer.projectDetails]));

      // Compute yield efficiently with precision
      dividends = await Promise.all(
        dividends.map(async (div) => {
          const projectDetails = offeringDataLookup.get(div.tokenAddress);
          if (!projectDetails) return div;

          // if yeild and dividendPerToken is present then return the div
          if (div?.dividendPerToken && div?.yield) {
            return div;
          }

          // if dividend is completed then return
          if (div?.status == 'COMPLETED') return div;

          const totalSupply = new Big(projectDetails.tokenSupply);

          const latestNav = projectDetails.assetType !== 'Real Estate' ? new Big(projectDetails.launchValuation) : new Big(projectDetails.latestNav);

          const price = latestNav.div(totalSupply).toFixed(8); // Keeping high precision

          const dividendAmount = new Big(div.dividendAmount);
          const dividendYield = dividendAmount.div(price).toFixed(8); // High precision yield

          const divPerToken = divPerTokenMap.get(dividendAmount.toString()) || divPerTokenMap.get(dividendAmount.toNumber());
          return {
            ...div,
            yield: dividendYield,
            dividendPerToken: divPerToken,
          };
        }),
      );

      const totalCount = await DividendReq.countDocuments({ offeringId: new Types.ObjectId(offeringId) });
      const totalPages = Math.ceil(totalCount / pageSize);
      const nextPage = pageNumber < totalPages ? pageNumber + 1 : null;
      const previousPage = pageNumber > 1 ? pageNumber - 1 : null;

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: 'Dividends details found successfully!',
        data: { dividends, currentPage: pageNumber, totalPages, totalCount, nextPage, previousPage },
      };
    } catch (error) {
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  /**
   * Function to get dividend details for the given offeringId
   * @param query - query object containing page, limit, offeringId, dividendType
   * @returns - Promise with the dividend details
   */
  investors = async (query: Record<string, string>) => {
    try {
      const { page = 1, limit = 10, offeringId, dividendType, search: escapedSearch } = query;

      const pageNumber = Number(page);
      const pageSize = Number(limit);

      const dividendDataFromCron = await DividendReq.findOne({
        offeringId: new Types.ObjectId(offeringId),
        dividendType,
      });
      const now = new Date();
      const IST_OFFSET = 5.5 * 60 * 60 * 1000; // Offset in milliseconds for IST
      const nowIST = new Date(now.getTime() + IST_OFFSET); // Convert UTC to IST
      const search = escapedSearch ? CommonHelper.escapeRegex(escapedSearch as string) : '';

      if (dividendDataFromCron.recordDate < nowIST) {

        const searchRegex = escapedSearch ? new RegExp(escapedSearch, 'i') : null;

        const matchStage = {
          offeringId: new Types.ObjectId(offeringId),
          dividendType,
        };

        const pipeline = [
          { $match: matchStage },
          { $unwind: '$userDetails' },
          ...(searchRegex
            ? [
                {
                  $match: {
                    $or: [{ 'userDetails.name': searchRegex }, { 'userDetails.email': searchRegex }],
                  },
                },
              ]
            : []),
          {
            $group: {
              _id: '$_id',
              offeringId: { $first: '$offeringId' },
              dividendId: { $first: '$dividendId' },
              offeringName: { $first: '$offeringName' },
              status: { $first: '$status' },
              dividendAmount: { $first: '$dividendAmount' },
              dividendType: { $first: '$dividendType' },
              paymentMethod: { $first: '$paymentMethod' },
              recordDate: { $first: '$recordDate' },
              declarationDate: { $first: '$declarationDate' },
              totalInvestor: { $first: '$totalInvestor' },
              totalTokenHeld: { $first: '$totalTokenHeld' },
              dividendPerToken: { $first: '$dividendPerToken' },
              yield: { $first: '$yield' },
              navPrice: { $first: '$navPrice' },
              createdAt: { $first: '$createdAt' },
              updatedAt: { $first: '$updatedAt' },
              userDetails: { $push: '$userDetails' },
            },
          },
          {
            $facet: {
              paginatedResults: [{ $skip: (pageNumber - 1) * pageSize }, { $limit: pageSize }],
              totalCount: [{ $count: 'count' }],
            },
          },
        ];

        const result = await dividendRecords.aggregate(pipeline).exec();
        const dividendDoc = result[0]?.paginatedResults?.[0] || null;
        const totalCount = result[0]?.totalCount?.[0]?.count || 0;

        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: 'Dividends Details Found Successfully!',
          data: {
            dividends: dividendDoc || [],
            currentPage: pageNumber,
            totalCount,
          },
        };
      }

      // Validate offeringId format
      if (!Types.ObjectId.isValid(offeringId)) {
        throw new CustomError('Invalid offeringId formate', HttpStatusCode.BadRequest);
      }

      // Step 1: Aggregate dividends and extract paginated user details
      const dividends = await DividendReq.aggregate([
        { $match: { offeringId: new Types.ObjectId(offeringId), dividendType } },
        { $lookup: { from: 'whitelists', localField: 'offeringId', foreignField: 'offeringId', as: 'whitelists' } },
        { $addFields: { whitelists: { $filter: { input: '$whitelists', as: 'whitelist', cond: { $eq: ['$$whitelist.status', 'APPROVED'] } } } } },
        { $lookup: { from: 'users', localField: 'whitelists.userId', foreignField: '_id', as: 'userDetails' } },
        {
          $addFields: {
            userDetails: {
              $filter: {
                input: '$userDetails',
                as: 'user',
                cond: {
                  $or: [
                    {
                      $regexMatch: {
                        input: '$$user.name',
                        regex: search, // use escaped search string
                        options: 'i',
                      },
                    },
                    {
                      $regexMatch: {
                        input: '$$user.email',
                        regex: search, // use escaped search string
                        options: 'i',
                      },
                    },
                  ],
                },
              },
            },
          },
        },
        {
          $project: {
            offeringId: 1,
            offeringName: 1,
            dividendAmount: 1,
            dividendType: 1,
            paymentMethod: 1,
            recordDate: 1,
            declarationDate: 1,
            createdAt: 1,
            updatedAt: 1,
            userDetails: { $slice: [{ $map: { input: '$userDetails', as: 'user', in: { name: '$$user.name', email: '$$user.email', images: '$$user.userImage' } } }, (pageNumber - 1) * pageSize, pageSize] },
          },
        },
      ]);
      if (!dividends || dividends.length === 0) {
        return { status: RESPONSES.SUCCESS, error: false, message: 'Dividends Not found! ', data: { dividends } };
      }

      // Extract user emails
      const userEmails = dividends[0]?.userDetails?.map((user: any) => user.email) || [];
      // Step 2: Find user addresses using emails
      const usersWithAddresses = await userSchema.aggregate([
        { $match: { email: { $in: userEmails } } },
        { $lookup: { from: 'whitelists', localField: '_id', foreignField: 'userId', as: 'whitelistDetails' } },
        { $project: { email: 1, address: { $arrayElemAt: ['$whitelistDetails.address', 0] } } },
      ]);

      // step 3: Calculate fees using contract
      const contractAddress = config.FUND_CONTRACT_ADDRESS;
      const provider = new ethers.JsonRpcProvider(config.RPC_URL);
      const contractInstance = new ethers.Contract(contractAddress, contractABI, provider);

      const isOfferingExist = await offeringSchema.findById(offeringId);
      const fee = await contractInstance.getDividendFee(String(isOfferingExist.tokenAddress) || null);
      const feeInt = Number(fee) / 100;

      // Step 4: Calculate dividend data
      const dividendData = await this.calculateDividendFunction(offeringId, dividendType);
      const dividendAmountMap = new Map(dividendData?.user?.map((val: any) => [val.address, val.balance]));
      // const dividendAmountMap1 = new Map(dividendData?.user?.map((val: any) => [val.address, val.balance]));

      const dividendFeesMap = new Map(dividendData?.user?.map((val: any) => [val.address, Number((val.dividentPerToken * feeInt) / 100).toFixed(9)]));
      const dividendPerTokenMap = new Map(dividendData?.user?.map((val: any) => [val.address, (Number(val.dividentPerToken) - Number((val.dividentPerToken * feeInt) / 100)).toFixed(9)]));

      const dividendPerTokenWithoutFeesMap = new Map(dividendData?.user?.map((val: any) => [val.address, val.dividentPerToken]));

      const dividendYeildMap = new Map(dividendData?.user?.map((val: any) => [val.address, val.yield]));

      // step 5: Calculate users having dividend distributed
      const addressArray = usersWithAddresses?.map((val) => val.address);

      // Fetch dividend history for the given dividendId
      const userAddressWithSuccess = await DividendHistory.find({ userAddress: { $in: addressArray }, dividendId: new Types.ObjectId(dividends[0]._id.toString()) }).select('transactionHash status userAddress');

      const statusMap = new Map(userAddressWithSuccess?.map((val: any) => [val.userAddress, val.status]));
      const transactionHashMap = new Map(userAddressWithSuccess?.map((val: any) => [val.userAddress, val.transactionHash]));

      // Map additional data into userDetails
      const addressMap = new Map(usersWithAddresses?.map((user: any) => [user.email, user.address]));

      // dividends[0].userDetails = await Promise.all(
      //   dividends[0].userDetails
      //     ?.map((user: any) => ({
      //       ...user,
      //       address: addressMap.get(user.email) || null,
      //       dividendAmount: dividendAmountMap.get(addressMap.get(user.email)) || null,
      //       dividendFees: dividendFeesMap.get(addressMap.get(user.email)) || 0,
      //       dividentPerToken: dividendPerTokenMap.get(addressMap.get(user.email)) || null,
      //       dividendPerTokenWithoutFees: dividendPerTokenWithoutFeesMap.get(addressMap.get(user.email)) || null,
      //       yield: dividendYeildMap.get(addressMap.get(user.email)) || null,
      //       status: statusMap.get(addressMap.get(user.email)) || 'PENDING',
      //       transactionHash: transactionHashMap.get(addressMap.get(user.email)) || null,
      //     }))
      //     .filter((user: any) => user.dividendAmount !== '0'),
      // );

      dividends[0].userDetails = await Promise.all(
        dividends[0].userDetails
          ?.map((user: any) => {
            const userAddress = addressMap.get(user.email);
            const dividendAmount = dividendAmountMap.get(userAddress) || null;
            const dividendFees = dividendFeesMap.get(userAddress) || 0;
            const dividendPerToken = dividendPerTokenMap.get(userAddress) || null;
            const dividendPerTokenWithoutFees = dividendPerTokenWithoutFeesMap.get(userAddress) || null;
            const yieldValue = dividendYeildMap.get(userAddress) || null;
            const status = statusMap.get(userAddress) || 'PENDING';
            const transactionHash = transactionHashMap.get(userAddress) || null;

            return {
              ...user,
              address: userAddress,
              dividendAmount,
              dividendFees,
              dividendPerToken,
              dividendPerTokenWithoutFees,
              yield: yieldValue,
              status,
              transactionHash,
            };
          })
          .filter((user: any) => {
            // Only include users with valid dividend amount and address
            return user.dividendAmount !== '0' && user.address !== null;
          }),
      );

      dividends[0].totalInvestor = dividends[0]?.userDetails.length;

      // Calculate totalTokenHeld (sum of all user dividendAmounts)
      const totalTokenHeld = dividends[0].userDetails
        .reduce((sum: any, user: any) => {
          return sum.plus(new Big(user.dividendAmount || 0));
        }, new Big(0))
        .toString();
      dividends[0].totalTokenHeld = totalTokenHeld;

      //returning in object form
      const formattedDataInObject = dividends[0];

      // Preparing final response
      return { status: RESPONSES.SUCCESS, error: false, message: 'Dividends details found successfully!', data: { dividends: formattedDataInObject, currentPage: pageNumber, totalCount: dividends[0]?.userDetails.length } };
    } catch (error) {
      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * This function calculates the dividend amount for each user whitelisted for the given offeringId
   * @param offeringId - The id of the offering
   * @returns - An object containing user data and dividend amount
   */
  calculateDividendFunction = async (offeringId: string, dividendType: string) => {
    try {
      const isOfferingExist = await offeringSchema.findById(offeringId);
      const isDividendReqExist = await DividendReq.findOne({ offeringId: new Types.ObjectId(offeringId), dividendType });

      if (!isOfferingExist) {
        throw new CustomError('Offering not found !', HttpStatusCode.NotFound);
      }
      if (!isDividendReqExist) {
        throw new CustomError('Dividend req for the provided offering not Found !', HttpStatusCode.NotFound);
      }
      const userWhitlistedforOffering = await whitelistSchema.find({ offeringId: new Types.ObjectId(offeringId), status: 'APPROVED' }).select('address userId');

      if (!userWhitlistedforOffering || userWhitlistedforOffering.length < 1) {
        throw new CustomError('Offering is not whitelisted !', RESPONSES.BAD_REQUEST);
      }
      const whitelistAddress = userWhitlistedforOffering?.map((offering) => offering.address);

      const whitelistData = userWhitlistedforOffering?.map((offering) => {
        return { userId: offering.userId, address: offering.address };
      });

      let latestNav;
      const offeringData = isOfferingExist;
      const totalSupply = offeringData?.projectDetails?.tokenSupply;

      if (offeringData?.projectDetails?.assetType !== 'Real Estate') {
        latestNav = offeringData?.projectDetails?.launchValuation;
      } else {
        latestNav = offeringData?.projectDetails?.latestNav;
      }

      const price = (latestNav / totalSupply).toFixed(2);

      const data = await this.getDividentAmount(whitelistAddress, isOfferingExist.tokenAddress, Number(isDividendReqExist.dividendAmount), whitelistData, Number(price));
      if (!data) {
        throw new CustomError('Failed to calculate dividend for the provided offering Id', HttpStatusCode.NotFound);
      }

      return { user: data, offeringId, dividendAmount: isDividendReqExist.dividendAmount };
    } catch (error) {
      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };
  /**
   * @description Get dividend history of an investor
   * @param {Request} req - The request object
   * @param {string} userId - The user ID
   * @returns {Promise<PromiseResolve>} - The dividend history of the investor
   */
  getInvestorDividendHistory = async (req: Request, userId: string) => {
    try {
      const { isCsv, search: escapedSearch } = req.query;

      const search = escapedSearch ? CommonHelper.escapeRegex(escapedSearch as string) : '';

      // Validate userId format
      if (!Types.ObjectId.isValid(userId)) {
        throw new CustomError('Invalid userId format', HttpStatusCode.BadRequest);
      }

      const { page, limit, status } = req.body;

      const pageNumber = Number(page) || 1;
      const pageSize = Number(limit) || 10;

      const matchStatus: any = { status: 'COMPLETED' };

      if (status === 'PENDING') {
        matchStatus.status = 'PENDING';
      } else if (status === 'COMPLETED') {
        matchStatus.status = 'COMPLETED';
      }

      // const decimal: any = await contractInstance.decimals();
      const historyData = await whitelistSchema.aggregate([
        {
          $match: {
            userId: new Types.ObjectId(userId),
            status: 'APPROVED',
          },
        },
        {
          $lookup: {
            from: 'userdetails',
            localField: 'userId',
            foreignField: '_id',
            as: 'userDetails',
          },
        },
        {
          $unwind: '$userDetails',
        },
        {
          $lookup: {
            from: 'dividendreqs',
            localField: 'offeringId',
            foreignField: 'offeringId',
            as: 'dividendDetails',
          },
        },
        {
          $unwind: {
            path: '$dividendDetails',
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $lookup: {
            from: 'dividendhistories',
            let: {
              address: { $arrayElemAt: ['$userDetails.wallets.address', 0] },
              dividendId: '$dividendDetails._id',
            },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [{ $eq: ['$userAddress', '$$address'] }, { $eq: ['$dividendId', '$$dividendId'] }],
                  },
                },
              },
            ],
            as: 'dividendHistory',
          },
        },
        {
          $lookup: {
            from: 'offerings',
            localField: 'offeringId',
            foreignField: '_id',
            as: 'offering',
          },
        },
        // Apply search filter using regex on userDetails.name
        {
          $match: {
            'offering.projectDetails.offeringName': {
              $regex: search || '', // safe fallback to empty string
              $options: 'i', // case-insensitive
            },
          },
        },
        {
          $unwind: {
            path: '$offering',
            preserveNullAndEmptyArrays: true,
          },
        },
        { $lookup: { from: 'dividendrecords', localField: 'dividendDetails._id', foreignField: 'dividendId', as: 'dividendRecords' } },
        { $unwind: '$dividendRecords' },
        {
          $project: {
            address: 1,
            dividendFees: '$offering.fee.dividendFee',
            offeringLogo: '$offering.overview.logo',
            offeringName: '$offering.projectDetails.offeringName',
            tokenAddress: '$offering.tokenAddress',
            amount: { $arrayElemAt: ['$dividendHistory.netAmount', 0] },
            declarationDate: '$dividendDetails.createdAt',
            distributionDate: { $arrayElemAt: ['$dividendHistory.createdAt', 0] },
            paymentType: '$dividendDetails.paymentMethod',
            dividendType: '$dividendDetails.dividendType',
            dividendPerToken: '$dividendRecords.dividendPerToken',
            yield: '$dividendRecords.yield',
            navPrice: '$dividendRecords.navPrice',
            // navPrice: {
            //   $cond: [
            //     { $ne: ['$offering.projectDetails.assetType', 'Real Estate'] },
            //     {
            //       $divide: ['$offering.projectDetails.previousValuation', '$offering.projectDetails.tokenSupply'],
            //     },
            //     {
            //       $divide: ['$offering.projectDetails.latestNav', '$offering.projectDetails.tokenSupply'],
            //     },
            //   ],
            // },
            status: {
              $cond: {
                if: {
                  $gt: [
                    {
                      $strLenCP: {
                        $ifNull: [{ $arrayElemAt: ['$dividendHistory.transactionHash', 0] }, ''],
                      },
                    },
                    0,
                  ],
                },
                then: 'COMPLETED',
                else: 'PENDING',
              },
            },
          },
        },
        { $match: matchStatus },
        { $sort: { distributionDate: -1 } },
        {
          $facet: {
            metadata: [{ $count: 'totalCount' }],
            paginatedResults: isCsv
              ? [] // Return all for CSV
              : [{ $skip: (pageNumber - 1) * pageSize }, { $limit: pageSize }],
          },
        },
      ]);


      const metadata = historyData[0]?.metadata[0] || { totalCount: 0 };
      const paginatedResults = historyData[0]?.paginatedResults || [];

      return { status: RESPONSES.SUCCESS, error: false, message: 'Investor Dividend History Found Successfully!', data: { paginatedResults, totalCount: metadata.totalCount } };
    } catch (error) {
      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  /**
   * Calculates the dividend amount for a given offering id
   * @param {object} req - Request object with offering id
   * @param {string} req.offeringId - Offering id
   * @returns {Promise<PromiseResolve>} - A promise that resolves with the calculated dividend amount
   */
  calculateDividend = async (req: Record<string, string>) => {
    try {
      // currently not in use
      const { offeringId } = req;
      const isOfferingExist = await offeringSchema.findById(offeringId);
      const isDividendReqExist = await DividendReq.findOne({ offeringId: new Types.ObjectId(offeringId) });

      if (!isOfferingExist) {
        throw new CustomError('Offering not found !', HttpStatusCode.NotFound);
      }
      if (!isDividendReqExist) {
        throw new CustomError('Dividend req for the provided offering not Found !', HttpStatusCode.NotFound);
      }
      const userWhitlistedforOffering = await whitelistSchema.find({ offeringId: new Types.ObjectId(offeringId), status: 'APPROVED' }).select('address userId');

      if (!userWhitlistedforOffering || userWhitlistedforOffering.length < 1) {
        throw new CustomError('Offering is not whitelisted !', RESPONSES.BAD_REQUEST);
      }
      const whitelistAddress = userWhitlistedforOffering?.map((offering) => offering.address);

      const whitelistData = userWhitlistedforOffering?.map((offering) => {
        return { userId: offering.userId, address: offering.address };
      });
      let latestNav;
      const offeringData = isOfferingExist;
      const totalSupply = offeringData?.projectDetails?.tokenSupply;

      if (offeringData?.projectDetails?.assetType !== 'Real Estate') {
        latestNav = offeringData?.projectDetails?.launchValuation;
      } else {
        latestNav = offeringData?.projectDetails?.latestNav;
      }

      const price = (latestNav / totalSupply).toFixed(2);

      const data = await this.getDividentAmount(whitelistAddress, isOfferingExist.tokenAddress, Number(isDividendReqExist.dividendAmount), whitelistData, Number(price));

      if (!data) {
        throw new CustomError('Failed to calculate dividend for the provided offering Id', HttpStatusCode.NotFound);
      }

      return { status: RESPONSES.SUCCESS, error: false, message: 'Dividends caculated successfully!', data: { user: data, offeringId, dividendAmount: isDividendReqExist.dividendAmount } };
    } catch (error) {
      return { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR };
    }
  };

  createDividendHistory = async (data: any) => {
    try {
      const query = await DividendHistory.create(data);

      // Destructure needed fields
      const { dividendId, userAddress, transactionHash } = data;

      // Step 1: Update user record inside dividendRecords
      const findDividendRecord = await dividendRecords.findOneAndUpdate(
        {
          dividendId: new Types.ObjectId(dividendId.toString()),
          'userDetails.address': userAddress.toLowerCase(), // Correct matching condition
        },
        {
          $set: {
            'userDetails.$.transactionHash': transactionHash,
            'userDetails.$.status': 'SUCCESS', // Consistent casing
          },
        },
        { new: true }, // return the updated document
      );

      if (!findDividendRecord) {
        return {
          status: 404,
          error: true,
          message: 'User detail not found for dividend update.',
        };
      }

      // Step 2: Check if all users have SUCCESS status
      const updatedRecord = await dividendRecords.findOne({ _id: findDividendRecord._id });

      const allSuccessful = updatedRecord.userDetails.every((user: any) => user.status === 'SUCCESS' && !!user.transactionHash);

      if (allSuccessful) {
        await dividendRecords.updateOne({ _id: updatedRecord._id }, { $set: { status: 'COMPLETED' } });
      }
      // Step 3: Send email to issuer
      if (query) {
        return {
          status: RESPONSES.CREATED,
          error: false,
          message: RES_MSG.USER.CREATED_SUCCESS,
          data: query,
        };
      }
      return {
        status: RESPONSES.BAD_REQUEST,
        error: true,
        message: RES_MSG.COMMON.SOMETHING_WRONG,
      };
    } catch (error) {
      logger.error(error, 'dividendHistory error');
      if (error.code === 11000) {
        return {
          status: RESPONSES.CONFLICT,
          error: true,
          message: 'Dividend Id already exists.',
        };
      }
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  public saveDividendInvestorData = async () => {
    try {
      const now = new Date();
      const IST_OFFSET = 5.5 * 60 * 60 * 1000; // Offset in milliseconds for IST
      const nowIST = new Date(now.getTime() + IST_OFFSET); // Convert UTC to IST

      // Step 1: Aggregate dividends whose recordDate is <= now and are ACTIVE
      const dividends = await DividendReq.aggregate([
        { $match: { recordDate: { $lte: nowIST }, status: 'ACTIVE' } },
        { $lookup: { from: 'whitelists', localField: 'offeringId', foreignField: 'offeringId', as: 'whitelists' } },
        {
          $addFields: {
            whitelists: {
              $filter: {
                input: '$whitelists',
                as: 'whitelist',
                cond: { $eq: ['$$whitelist.status', 'APPROVED'] },
              },
            },
          },
        },
        { $lookup: { from: 'users', localField: 'whitelists.userId', foreignField: '_id', as: 'userDetails' } },
        {
          $project: {
            offeringId: 1,
            offeringName: 1,
            dividendAmount: 1,
            dividendType: 1,
            paymentMethod: 1,
            recordDate: 1,
            declarationDate: 1,
            createdAt: 1,
            updatedAt: 1,
            status: 1,
            userDetails: {
              $map: {
                input: '$userDetails',
                as: 'user',
                in: {
                  name: '$$user.name',
                  email: '$$user.email',
                  images: '$$user.userImage',
                },
              },
            },
          },
        },
      ]);

      if (dividends.length === 0) return;

      for (const dividend of dividends) {
        // Step 2: Skip if dividend record already exists
        const existingDividendPresent = await dividendRecords.findOne({ _id: dividend._id });
        if (existingDividendPresent || dividend.status == 'COMPLETED') {
          continue;
        }


        // Extract user emails from the dividend
        const userEmail = dividend?.userDetails?.map((user: any) => user.email) || [];

        // Step 3: Fetch user addresses from whitelist using email
        const usersWithAddresses = await userSchema.aggregate([
          { $match: { email: { $in: userEmail.map((e: any) => e.trim().toLowerCase()) } } },
          {
            $lookup: {
              from: 'whitelists',
              localField: '_id',
              foreignField: 'userId',
              as: 'whitelistDetails',
            },
          },
          {
            $project: {
              email: 1,
              address: { $arrayElemAt: ['$whitelistDetails.address', 0] },
            },
          },
        ]);

        const { offeringId, dividendType, userDetails = [] } = dividend;

        // Step 4: fetch dividend fee percentage
        const offering = await offeringSchema.findById(offeringId);
        if (!offering) throw new Error('Offering not found');

        // store fees in Dividend
        const rawFee = await this.fundFactoryInstance.getDividendFee(String(offering.tokenAddress));
        const feePercentage = Number(rawFee) / 100;

        // Step 5: Calculate dividend distribution data
        const dividendResult = await this.calculateDividendFunction(offeringId, dividendType);

        const dividendUsers: any[] = dividendResult?.user || [];

        // Step 6: Prepare maps for calculations
        const dividendAmountMap = new Map();
        const dividendFeesMap = new Map();
        const dividendPerTokenMap = new Map();
        const dividendPerTokenWithoutFeesMap = new Map();
        const dividendYieldMap = new Map();

        // Step 7: Populate maps with dividend data and fee calculations
        dividendUsers.forEach(({ address, balance, dividentPerToken, yield: yieldVal }) => {
          const fee = (dividentPerToken * feePercentage) / 100;
          dividendAmountMap.set(address, balance);
          dividendFeesMap.set(address, fee.toFixed(9));
          dividendPerTokenMap.set(address, (dividentPerToken - fee).toFixed(9));
          dividendPerTokenWithoutFeesMap.set(address, dividentPerToken);
          dividendYieldMap.set(address, yieldVal);
        });

        // Step 8: Create a map of email -> address
        const addressMap = new Map(usersWithAddresses.map((user: any) => [user.email, user.address]));
        const addresses = [...addressMap.values()];

        // Step 9: Get existing history records for the same dividend & users
        const historyRecords = await DividendHistory.find({
          userAddress: { $in: addresses },
          dividendId: new Types.ObjectId(dividend._id),
        }).select('transactionHash status userAddress ');

        const statusMap: any = new Map(historyRecords.map((record) => [record.userAddress, record.status]));
        const transactionHashMap: any = new Map(historyRecords.map((record) => [record.userAddress, record.transactionHash]));

        // Step 10: Enrich user details with address, fee, yield, and status info
        const finalUserDetails = await Promise.all(
          userDetails
            .map((user: any) => {
              const address = addressMap.get(user.email) || null;
              return {
                ...user,
                address,
                dividendAmount: dividendAmountMap.get(address) || 0,
                dividendFees: dividendFeesMap.get(address) || 0,
                dividendPerToken: dividendPerTokenMap.get(address) || 0,
                dividendPerTokenWithoutFees: dividendPerTokenWithoutFeesMap.get(address) || 0,
                yield: dividendYieldMap.get(address) || 0,
                status: statusMap.get(address) || 'PENDING',
                transactionHash: transactionHashMap.get(address) || null,
              };
            })
            .filter((user: any) => user.dividendAmount !== '0'),
        ); // Remove zero dividend users

        // Calculate totalTokenHeld as sum of all dividendAmounts
        const totalTokenHeld = finalUserDetails.reduce((sum, user) => {
          return new Big(sum).plus(new Big(user.dividendAmount)).toString();
        }, '0');

        // calculate dividend Per Token
        const dividendPerToken = new Big(dividend.dividendAmount.toString()).div(new Big(totalTokenHeld.toString())).toFixed(9);

        // calculate yeild
        const { projectDetails }: any = offering;
        const totalSupply = new Big(projectDetails.tokenSupply.toString());
        const latestNav = projectDetails.assetType !== 'Real Estate' ? new Big(projectDetails.launchValuation) : new Big(projectDetails.latestNav);
        const price = latestNav.div(totalSupply).toFixed(8); // Keeping high precision

        const dividendAmount = new Big(dividend.dividendAmount);
        const dividendYield = dividendAmount.div(price).toFixed(9); // High precision yield

        // Step 11: Attach final user data to dividend object
        dividend.userDetails = finalUserDetails;
        dividend.totalInvestor = finalUserDetails.length;
        dividend.dividendId = dividend._id;
        dividend.totalTokenHeld = totalTokenHeld; // Add the calculated total
        dividend.dividendPerToken = dividendPerToken.toString();
        dividend.yield = dividendYield.toString();
        dividend.navPrice = price.toString();

        // Step 12: Save processed dividend data
        const existingDividend = await dividendRecords.findOne({ _id: dividend._id });

        if (existingDividend == null) {
          const saved = await dividendRecords.create([dividend]);

          if (saved) {
            // email
            console.log('✅ Dividend record saved successfully:', saved[0]?.dividendId);
          } else {
            console.log('❌ Dividend record saving failed');
          }
        }
      }
    } catch (error) {
      console.error('🚨 Error in saveDividendInvestorData: ', error);
    }
  };
}

export default new DividendsService();
