/* eslint-disable @typescript-eslint/no-empty-object-type */
import { Request, Response } from 'express';
import { ResponseHandler } from '../../helpers/response.helper';
import { PromiseResolve } from '../../utils/common.interface';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import DividendsService from './service';

/**
 * Class that contains methods for dividend operations
 *
 * @export
 * @class Dividents
 */
class Dividents {
  /**
   * Creates a new dividend request
   *
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise < PromiseResolve >}
   */
  createDividentReq = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { error, status, data, message } = await DividendsService.createDividendReq(req, req.userInfo.userId);

      return ResponseHandler.success(res, { status, error, message, data });
    } catch (error) {
      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  /**
   * Gets all dividend requests
   *
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise < PromiseResolve >}
   */
  getDividentReq = async (req: Request<{}, {}, {}, { page?: string; limit?: string; offeringId?: string }>, res: Response): Promise<PromiseResolve> => {
    try {
      const { error, status, data, message } = await DividendsService.getDividentReq(req.query);

      return ResponseHandler.success(res, { status, error, message, data });
    } catch (error) {
      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  investors = async (req: Request<{}, {}, {}, { page?: string; limit?: string; offeringId?: string; search?: string }>, res: Response): Promise<PromiseResolve> => {
    try {
      const { error, status, data, message } = await DividendsService.investors(req.query);

      return ResponseHandler.success(res, { status, error, message, data });
    } catch (error) {
      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  calculateDividend = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { error, status, data, message } = await DividendsService.calculateDividend(req.body);

      return ResponseHandler.success(res, { status, error, message, data });
    } catch (error) {
      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };
  /**
   * Gets the dividend history for a given investor
   *
   * @param {Request} req
   * @param {Response} res
   * @returns {Promise < PromiseResolve >}
   */
  dividendHistory = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { error, status, data, message } = await DividendsService.getInvestorDividendHistory(req, req.userInfo.userId);

      return ResponseHandler.success(res, { status, error, message, data });
    } catch (error) {
      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };
}

export default new Dividents();
