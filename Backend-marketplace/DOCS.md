# Libertum Marketplace Service – Documentation

## 1. Overview

The Libertum Marketplace Service powers the listing, trading, and management of tokenized assets.  
**Key features:**

- Asset/token listing and management
- Secure user authentication and KYC
- Order matching and transaction processing
- Dividends, notifications, and document signing (DocuSign)
- Real-time event streaming and cross-service workflows

**Primary users:**

- Investors, asset issuers, platform admins

---

## 2. Architecture

The service is a Node.js/TypeScript backend using Express, MongoDB, and Redis.  
It interacts with other Libertum microservices via **Kafka** (event streaming) and **gRPC** (direct RPC).

**Main modules:**

- Asset/NFT management
- Offerings & orders
- Transactions
- Dividends
- Notifications
- User authentication
- DocuSign integration

**External systems:**

- Kafka, gRPC, MongoDB, Redis, Google Cloud Storage, DocuSign, SendGrid

```mermaid
graph TD
  subgraph Marketplace Service
    A[API (Express)]
    B[Asset/NFT Module]
    C[Offerings/Orders]
    D[Transactions]
    E[Dividends]
    F[Notifications]
    G[User Auth]
    H[DocuSign]
  end
  A --> B
  A --> C
  A --> D
  A --> E
  A --> F
  A --> G
  A --> H
  B <--> |gRPC| I[Admin Service]
  F <--> |gRPC| J[Notification Service]
  G <--> |gRPC| K[User Service]
  D <--> |Kafka| L[Event Service]
  E <--> |Kafka| M[Dividend Service]
  A <--> N[MongoDB/Redis]
  A <--> O[Google Cloud Storage]
  H <--> P[DocuSign API]
```

---

## 3. API Surface

| Method | Path                               | Description               | Auth | Success | Error |
| ------ | ---------------------------------- | ------------------------- | ---- | ------- | ----- |
| POST   | /marketplace/v1/auth/login         | User login                | No   | 200     | 401   |
| POST   | /marketplace/v1/auth/register      | User registration         | No   | 201     | 400   |
| GET    | /marketplace/v1/auth/nft/list      | List NFTs                 | Yes  | 200     | 401   |
| POST   | /marketplace/v1/order/create       | Create order              | Yes  | 201     | 400   |
| GET    | /marketplace/v1/auth/offering      | List offerings            | Yes  | 200     | 401   |
| POST   | /marketplace/v1/auth/transactions  | Create transaction        | Yes  | 201     | 400   |
| GET    | /marketplace/v1/dividend/history   | Get dividend history      | Yes  | 200     | 401   |
| POST   | /marketplace/v1/auth/docusign/sign | Initiate DocuSign process | Yes  | 200     | 400   |
| GET    | /marketplace/v1/auth/notification  | List notifications        | Yes  | 200     | 401   |
| GET    | /marketplace/v1/health-check       | Health check              | No   | 200     | 500   |

_See Swagger at `/marketplace/v1/api-docs` for full details._

---

## 4. Event Contracts

**Kafka Topics:**

- `user.events` – user registration, KYC, block/unblock
- `asset.events` – asset creation, update, transfer
- `order.events` – order placed, matched, cancelled
- `dividend.events` – dividend declared, paid
- `notification.events` – notification triggers

**Sample payload (user.events):**

```json
{
  "type": "USER_REGISTERED",
  "userId": "abc123",
  "timestamp": "2024-06-01T12:00:00Z"
}
```

**Publish/Subscribe Flow:**

- Publishes events on user, asset, order actions
- Subscribes to events for cross-service workflows (e.g., dividend calculation, notifications)

---

## 5. File/Folder Structure

```text
Backend-marketplace/
├─ src/
│  ├─ component/
│  │  ├─ dividends/
│  │  ├─ docuSign/
│  │  ├─ nft/
│  │  ├─ notification/
│  │  ├─ offerings/
│  │  ├─ order/
│  │  ├─ transactions/
│  │  ├─ transfer/
│  │  └─ userAuthentications/
│  ├─ config/
│  │  ├─ connection/
│  │  ├─ env/
│  │  ├─ error/
│  │  ├─ middleware/
│  │  └─ server/
│  ├─ _grpc/
│  ├─ helpers/
│  ├─ middleware/
│  ├─ public/
│  ├─ routes/
│  ├─ service/
│  └─ utils/
```

---

## 6. Environment Variables

`.env.example` (partial, see file for all):

```env
NODE_ENV=development         # App environment
PORT=3000                   # API port
MONGODB_HOST=localhost      # MongoDB host
MONGODB_DATABASE=mvp_db     # MongoDB database
REDIS_HOST=localhost        # Redis host
KAFKA_BROKER=localhost:9092 # Kafka broker address
JWT_AUTH_SECRET=supersecret # JWT signing key
SENDGRID_API_KEY=...        # SendGrid email
GOOGLE_CLIENT_ID=...        # Google Cloud project
BUCKET_NAME=...             # GCS bucket
DOCUSIGN_CLIENT_ID=...      # DocuSign integration
# ...more, see .env.example
```

---

## 7. Local Development Workflow

1. **Clone & install:**
   ```bash
   git clone <repo-url>
   cd Backend-marketplace
   cp env.example .env
   npm install
   ```
2. **Spin up dependencies:**
   ```bash
   docker-compose up -d  # (MongoDB, Redis, Kafka, Zookeeper)
   ```
3. **Seed database (if needed):**
   ```bash
   npm run seed
   ```
4. **Start in dev mode:**
   ```bash
   npm run dev
   ```
5. **Debugging:**
   - Use VS Code launch config (`.vscode/launch.json`)
   - Hot-reload via `nodemon`/`ts-node`
6. **Lint & test:**
   ```bash
   npm run format
   npm test
   ```

---

## 8. Test Strategy

- **Unit tests:** For business logic, run with `npm test`
- **Integration tests:** For API endpoints and DB, run with `npm test`
- **Contract tests:** For Kafka/gRPC event contracts (see `/test/` folder)
- **Coverage:** View with `npm run coverage` (if configured)

---

## 9. CI/CD Flow

- **Pipeline stages:**
  1. Lint & static analysis
  2. Build (TypeScript → JS)
  3. Run tests (unit/integration)
  4. Security scan (e.g., SonarQube)
  5. Build/push Docker image
  6. Deploy to environment (auto by branch: stage/qa/sandbox/prod)

---

## 10. Security & Performance Notes

- **Auth:** JWT-based, with optional 2FA
- **Rate limiting:** Express middleware
- **OWASP mitigations:** Helmet, input validation, XSS/CSRF protection
- **Caching:** Redis for sessions and hot data
- **Observability:** Sentry, Winston logs, health endpoints
- **gRPC/Kafka:** Secure channels, topic ACLs

---

## 11. Future Enhancements

- Advanced asset analytics
- Multi-chain asset support
- Enhanced admin dashboards
- Automated compliance checks

---
