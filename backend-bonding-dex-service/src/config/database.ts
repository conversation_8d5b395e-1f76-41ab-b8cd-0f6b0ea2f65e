import mongoose from 'mongoose';
import * as dotenv from 'dotenv';
import { <PERSON>rror<PERSON>and<PERSON> } from '../helpers/sentry.helper';

dotenv.config();
console.log('process.env.MONGO_URI ****', process.env.MONGO_URI);
const MONGO_URI = process.env.MONGO_URI || 'mongodb://localhost:27017/bondingDex';

export const connectDB = async () => {
  try {
    await mongoose.connect(MONGO_URI, {
      // Connection pool settings to prevent memory leaks
      maxPoolSize: 10, // Maintain up to 10 socket connections
      serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
      socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
      family: 4, // Use IPv4, skip trying IPv6
    });

    // Connection event handlers
    mongoose.connection.on('connected', () => {
      console.log('✅ MongoDB Connected');
    });

    mongoose.connection.on('error', (err) => {
      console.error('❌ MongoDB Connection Error:', err);
      ErrorHandler.handleError(err as Error, {
        context: 'MongoDB Connection Error',
        mongoUri: MONGO_URI.replace(/\/\/[^:]+:[^@]+@/, '//***:***@'), // Hide credentials
      });
    });

    mongoose.connection.on('disconnected', () => {
      console.log('📡 MongoDB Disconnected');
    });
  } catch (error) {
    console.error('MongoDB Connection Failed:', error);
    ErrorHandler.handleError(error as Error, {
      context: 'connectDB',
      mongoUri: MONGO_URI.replace(/\/\/[^:]+:[^@]+@/, '//***:***@'), // Hide credentials
    });
    process.exit(1);
  }
};

export const closeDB = async () => {
  try {
    await mongoose.connection.close();
    console.log('✅ MongoDB connection closed');
  } catch (error) {
    console.error('❌ Error closing MongoDB connection:', error);
    ErrorHandler.handleError(error as Error, {
      context: 'closeDB',
    });
    throw error;
  }
};
