import * as crypto from 'crypto';

const SECRET_KEY = process.env.HMAC_SECRET || 'your-secret-key';
/**
 * Generate an HMAC signature
 * @param message The message to sign
 * @returns The HMAC signature (hex format)
 */
export function generateHmac(message: string): string {
  return crypto.createHmac('sha256', SECRET_KEY).update(message).digest('hex');
}

/**
 * Verify an HMAC signature
 * @param message The original message
 * @param providedHmac The HMAC signature from the request
 * @returns Boolean indicating validity
 */
export function verifyHmac(message: string, providedHmac: string): boolean {
  const expectedHmac = generateHmac(message);
  return crypto.timingSafeEqual(Buffer.from(expectedHmac, 'hex'), Buffer.from(providedHmac, 'hex'));
}
