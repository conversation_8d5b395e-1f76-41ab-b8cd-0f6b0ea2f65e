import axios from 'axios';
import { errorServiceResponse } from '../common/response.handler';
import { getTokenHolderData, getUniqueTokenHolderCount } from '../services/bondingdex.service';
import { <PERSON>rror<PERSON>andler } from '../helpers/sentry.helper';

const ETHERSCAN_API_KEY = process.env.ETHERSCAN_API_KEY;
const ETHERSCAN_BASE_URL = process.env.BASE_URL || 'https://api.etherscan.io/api';

// Configure axios with timeout to prevent hanging requests
const axiosInstance = axios.create({
  timeout: 30000, // 30 second timeout
  headers: {
    'User-Agent': 'bondingdex-service/1.0.0',
  },
});

export interface TokenHolder {
  address: string;
  quantity: string;
}

export interface TokenHoldersPaginated {
  holders: TokenHolder[];
  currentPage: number;
  totalPages: number;
  totalCount: number;
  nextPage: number | null;
  previousPage: number | null;
}

// Helper to fetch token decimals using Etherscan's eth_call
async function getTokenDecimals(contractAddress: string): Promise<number> {
  if (!ETHERSCAN_API_KEY) throw errorServiceResponse('ETHERSCAN_API_KEY not set');

  const url = `${ETHERSCAN_BASE_URL}&module=proxy&action=eth_call&to=${contractAddress}&data=0x313ce567&tag=latest&apikey=${ETHERSCAN_API_KEY}`;
  try {
    const { data } = await axiosInstance.get(url);
    if (data.result) {
      return parseInt(data.result, 16);
    }
    throw errorServiceResponse('Unable to fetch token decimals due to an invalid token address. Please provide a valid Bonding DEX token address.');
  } catch (error: any) {
    if (error.code === 'ECONNABORTED') {
      throw errorServiceResponse('Request timeout while fetching token decimals');
    }
    throw error;
  }
}

/**
 * Fetch token holders from Etherscan (using ERC20 token holder endpoint)
 * @param contractAddress ERC20 contract address
 * @param page Page number (1-based)
 * @param offset Number of results per page
 */
export async function getTokenHoldersFromEtherscan(contractAddress: string, page: number = 1, offset: number = 10): Promise<TokenHoldersPaginated> {
  if (!ETHERSCAN_API_KEY) throw errorServiceResponse('ETHERSCAN_API_KEY not set');

  // Normalize contract address for comparison
  const normalizedContractAddress = contractAddress.toLowerCase();

  // Fetch decimals automatically
  const tokenDecimals = await getTokenDecimals(contractAddress);

  // Etherscan does not provide a direct endpoint for token holders, so we use token transfers and aggregate
  const url = `${ETHERSCAN_BASE_URL}&module=account&action=tokentx&contractaddress=${contractAddress}&page=1&offset=10000&sort=desc&apikey=${ETHERSCAN_API_KEY}`;
  // We fetch a large number of txs to aggregate balances, then paginate holders in-memory
  const { data } = await axios.get(url);

  if (data.status !== '1' || !data.result) {
    return {
      holders: [],
      currentPage: page,
      totalPages: 0,
      totalCount: 0,
      nextPage: null,
      previousPage: null,
    };
  }

  // Aggregate balances from latest transactions
  const holdersMap: Record<string, { quantity: bigint }> = {};
  for (const tx of data.result) {
    const to = tx.to.toLowerCase();
    const value = BigInt(tx.value);
    if (!holdersMap[to]) holdersMap[to] = { quantity: BigInt(0) };
    holdersMap[to].quantity += value;
  }

  // Convert to array, filter out the contract address itself, and calculate percentage
  const holders: TokenHolder[] = Object.entries(holdersMap)
    .filter(([address]) => address !== normalizedContractAddress) // Filter out contract address
    .map(([address, { quantity }]) => ({
      address,
      quantity: (Number(quantity) / Math.pow(10, tokenDecimals)).toString(),
    }));

  // Sort by quantity desc
  holders.sort((a, b) => Number(b.quantity) - Number(a.quantity));

  const totalCount = holders.length;
  const totalPages = Math.ceil(totalCount / offset);
  const paginatedHolders = holders.slice((page - 1) * offset, page * offset);

  return {
    holders: paginatedHolders,
    currentPage: page,
    totalPages,
    totalCount,
    nextPage: page < totalPages ? page + 1 : null,
    previousPage: page > 1 ? page - 1 : null,
  };
}

/**
 * Fetch token holders from Etherscan Pro API
 * @param contractAddress ERC20 contract address
 * @param page Page number (1-based)
 * @param offset Number of results per page
 */
export async function getTokenHoldersFromEtherscanPro(contractAddress: string, page: number = 1, offset: number = 10): Promise<TokenHoldersPaginated> {
  if (!ETHERSCAN_API_KEY) throw errorServiceResponse('ETHERSCAN_API_KEY not set');

  try {
    // Use the Pro API endpoint for token holders
    const url = `${ETHERSCAN_BASE_URL}&module=token&action=tokenholderlist&contractaddress=${contractAddress}&page=${page}&offset=${offset}&apikey=${ETHERSCAN_API_KEY}`;
    const { data } = await axios.get(url);
    if (data.status !== '1' || !data.result) {
      const result: any = await getTokenHolderData(contractAddress, page, offset);
      const chart = result?.data;
      return {
        holders: chart?.holderData || [],
        currentPage: page,
        totalPages: chart?.pagination.pages || 0,
        totalCount: chart?.pagination.total || 0,
        nextPage: page + 1,
        previousPage: page > 1 ? page - 1 : null,
      };
    }
    const tokenDecimals = await getTokenDecimals(contractAddress);

    // Transform the data to match our expected format
    const holders: TokenHolder[] = data.result.map((holder: any) => ({
      address: holder.TokenHolderAddress.toLowerCase(),
      quantity: holder.TokenHolderQuantity,
    }));

    // Get token decimals for proper quantity display
    try {
      // Convert quantities to proper decimal representation
      holders.forEach((holder) => {
        holder.quantity = (Number(holder.quantity) / Math.pow(10, tokenDecimals)).toString();
      });
    } catch (error) {
      console.warn('Could not fetch token decimals, using raw values:', error);
    }

    // Sort by quantity desc (in case API doesn't provide sorted data)
    holders.sort((a, b) => Number(b.quantity) - Number(a.quantity));

    // Filter out zero balances and contract itself, if necessary
    const filteredHolders = holders.filter((holder) => Number(holder.quantity) > 0 && holder.address.toLowerCase() !== contractAddress.toLowerCase());

    // Calculate pagination info
    // Note: Etherscan API might not provide total counts directly
    // We're assuming the API handles pagination correctly
    const totalCount = filteredHolders.length; // This might not be accurate for large tokens
    const totalPages = Math.ceil(totalCount / offset);

    return {
      holders: filteredHolders,
      currentPage: page,
      totalPages,
      totalCount,
      nextPage: page < totalPages ? page + 1 : null,
      previousPage: page > 1 ? page - 1 : null,
    };
  } catch (error) {
    console.error('Error fetching token holders from Etherscan Pro API:', error);
    throw errorServiceResponse('Failed to fetch token holders from Etherscan Pro API');
  }
}

/**
 * Fetch total token holder count from Etherscan Pro API
 * @param contractAddress ERC20 contract address
 */
export async function getHolderCountFromEtherscan(contractAddress: string): Promise<any> {
  try {
    if (!ETHERSCAN_API_KEY) {
      ErrorHandler.handleError(new Error('ETHERSCAN_API_KEY not set'), {
        context: 'getHolderCountFromEtherscan',
      });
      throw errorServiceResponse('ETHERSCAN_API_KEY not set');
    }
    const url = `${ETHERSCAN_BASE_URL}&module=token&action=tokenholdercount&contractaddress=${contractAddress}&apikey=${ETHERSCAN_API_KEY}`;
    const { data } = await axios.get(url);
    console.log('data============>>>>>>>>>>>>', data);
    if (data.status !== '1' || !data.result) {
      ErrorHandler.handleWarning('No result from Etherscan, falling back to unique holder count from db', {
        contractAddress,
        response: data,
      });
      const totalCount = await getUniqueTokenHolderCount(contractAddress);
      return {
        totalCount: totalCount,
      };
    }

    const totalCount = Number(data.result);
    ErrorHandler.handleInfo('Successfully fetched token holder count', {
      contractAddress,
      totalCount,
    });

    return {
      totalCount,
    };
  } catch (error) {
    ErrorHandler.handleError(error as Error, {
      context: 'getHolderCountFromEtherscan',
      contractAddress,
    });
    throw errorServiceResponse('Failed to fetch token holder count from Etherscan Pro API');
  }
}
