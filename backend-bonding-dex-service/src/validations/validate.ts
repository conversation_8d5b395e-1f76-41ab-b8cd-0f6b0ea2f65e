import { Request, Response, NextFunction } from 'express';
import { ZodSchema, ZodError } from 'zod';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../helpers/sentry.helper';

export const validateRequest =
  (schema: ZodSchema) =>
  (req: Request, res: Response, next: NextFunction): any => {
    try {
      const reqs = req?.body?.data ?? req?.body;
      const parsed = schema.parse(reqs);
      req.body = parsed;
      next();
    } catch (error: any) {
      if (error instanceof ZodError) {
        ErrorHandler.handleWarning('Validation error', {
          errors: error.errors,
          path: req.path,
          method: req.method,
        });

        return res.status(400).json({
          error: true,
          message: { errors: error.errors },
          errors: error.errors,
          data: null,
        });
      }

      ErrorHandler.handleError(error, {
        context: 'validateRequest',
        path: req.path,
        method: req.method,
      });

      return res.status(500).json({
        error: true,
        message: 'Internal Server Error',
        data: null,
      });
    }
  };
