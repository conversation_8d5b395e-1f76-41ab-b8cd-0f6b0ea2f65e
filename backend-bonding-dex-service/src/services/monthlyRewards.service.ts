import stakingTransactionsModel from '../models/stakingTransactions.model';
import monthlyRewardsRepo, { MonthlyRewardInsertParams, AdminRewardUpdateParams } from '../repos/monthlyRewards.repo';
import { Bondingdex } from '../models/bondingdexs.model';
import { Web3Helper } from '../helpers/web3.helper';
import { contractsConfig } from '../utils/event.interface';

const provider = new Web3Helper(process.env.BASE_SEPOLIA_URL);
interface DailyBalance {
  date: string;
  balance: number;
}

interface UserTokenBalance {
  userAddress: string;
  token: string;
  dailyBalances: DailyBalance[];
  averageStaked: number;
}

interface MonthlyCalculationResult {
  month: string;
  token: string;
  userBalances: UserTokenBalance[];
  totalAverageStaked: number;
}

class MonthlyRewardsService {
  // /**
  //  * Calculate monthly average staked amounts for the previous month
  //  */
  public async calculateMonthlyAverages(targetMonth?: string): Promise<void> {
    try {
      const month = targetMonth || this.getPreviousMonth();
      console.log(`Starting monthly average calculation for ${month}`);

      // Get all unique tokens that had staking activity in the target month
      const tokens = await this.getActiveTokensForMonth(month);

      if (tokens.length === 0) {
        console.log(`No staking activity found for month ${month}`);
        return;
      }

      // Process each token separately
      for (const token of tokens) {
        // Check if calculation already exists for this month/token
        const exists = await monthlyRewardsRepo.hasMonthlyCalculation(month, token);
        if (exists) {
          console.log(`Monthly calculation already exists for ${month} - ${token}, skipping...`);
          continue;
        }

        console.log(`Calculating averages for token ${token} in month ${month}`);
        const calculation = await this.calculateTokenMonthlyAverages(month, token);

        if (calculation.userBalances.length > 0) {
          await this.saveMonthlyCalculation(calculation);
          console.log(`Saved calculation for ${token}: ${calculation.userBalances.length} users, total average: ${calculation.totalAverageStaked}`);
        }
      }

      console.log(`Monthly average calculation completed for ${month}`);
    } catch (error) {
      console.error('Error in calculateMonthlyAverages:', error);
      throw error;
    }
  }

  /**
   * Calculate averages for a specific token in a specific month
   */
  private async calculateTokenMonthlyAverages(month: string, token: string): Promise<MonthlyCalculationResult> {
    const { startDate, endDate, daysInMonth } = this.getMonthDateRange(month);

    // Get all users who have ever staked this token (up to end of month)
    const allUsers = await stakingTransactionsModel.distinct('userAddress', {
      token: token,
      timestamp: { $lte: Math.floor(endDate.getTime() / 1000) },
    });

    // Calculate daily balances for each user
    const userBalances: UserTokenBalance[] = [];
    let totalAverageStaked = 0;

    for (const userAddress of allUsers) {
      // Get all transactions for this user and token up to end of month
      const userTransactions = await stakingTransactionsModel
        .find({
          token: token,
          userAddress: userAddress,
          timestamp: { $lte: Math.floor(endDate.getTime() / 1000) },
        })
        .sort({ timestamp: 1 });
      const dailyBalances = this.calculateUserDailyBalancesFromTotal(userTransactions, startDate, endDate, daysInMonth);
      const averageStaked = this.calculateAverage(dailyBalances);

      if (averageStaked >= 0) {
        // Include users with 0 balance too
        userBalances.push({
          userAddress,
          token,
          dailyBalances,
          averageStaked,
        });
        totalAverageStaked += averageStaked;
      }
    }

    return {
      month,
      token,
      userBalances,
      totalAverageStaked: Number(totalAverageStaked.toFixed(8)),
    };
  }

  /**
   * Calculate daily balances for a user throughout the month based on userTotalStaked
   */
  private calculateUserDailyBalancesFromTotal(transactions: any[], startDate: Date, endDate: Date, daysInMonth: number): DailyBalance[] {
    const dailyBalances: DailyBalance[] = [];

    // Get the user's total staked amount at the start of the month
    let monthStartBalance = 0;

    // Find the last transaction before the month starts to get initial balance
    for (const tx of transactions) {
      const txDate = new Date(tx.timestamp * 1000);
      if (txDate < startDate) {
        monthStartBalance = Number(tx.userTotalStaked || 0);
      } else {
        break;
      }
    }

    // If no transactions before month start, user had 0 balance
    let currentBalance = monthStartBalance;

    // Calculate balance for each day of the month
    for (let day = 0; day < daysInMonth; day++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + day);

      // Check if any transactions happened on this day and update balance
      for (const tx of transactions) {
        const txDate = new Date(tx.timestamp * 1000);
        if (txDate.getFullYear() === currentDate.getFullYear() && txDate.getMonth() === currentDate.getMonth() && txDate.getDate() === currentDate.getDate()) {
          // Update balance to the userTotalStaked after this transaction
          currentBalance = Number(tx.userTotalStaked || 0);
        }
      }

      dailyBalances.push({
        date: currentDate.toISOString().split('T')[0],
        balance: currentBalance,
      });
    }

    return dailyBalances;
  }

  // /**
  //  * Calculate daily balances for a user throughout the month (legacy method)
  //  */
  // private calculateUserDailyBalances(transactions: any[], startDate: Date, endDate: Date, daysInMonth: number): DailyBalance[] {
  //   // This method is kept for backward compatibility but uses the new logic
  //   return this.calculateUserDailyBalancesFromTotal(transactions, startDate, endDate, daysInMonth);
  // }

  /**
   * Calculate average from daily balances
   */
  private calculateAverage(dailyBalances: DailyBalance[]): number {
    if (dailyBalances.length === 0) return 0;

    const sum = dailyBalances.reduce((total, day) => total + day.balance, 0);
    return Number((sum / dailyBalances.length).toFixed(8));
  }

  // /**
  //  * Group transactions by user address
  //  */
  // private groupTransactionsByUser(transactions: any[]): Map<string, any[]> {
  //   const userTransactions = new Map<string, any[]>();

  //   for (const tx of transactions) {
  //     if (!userTransactions.has(tx.userAddress)) {
  //       userTransactions.set(tx.userAddress, []);
  //     }
  //     userTransactions.get(tx.userAddress)!.push(tx);
  //   }

  //   return userTransactions;
  // }

  /**
   * Save monthly calculation results to database
   */
  private async saveMonthlyCalculation(calculation: MonthlyCalculationResult): Promise<void> {
    const rewards: MonthlyRewardInsertParams[] = calculation.userBalances.map((userBalance) => ({
      month: calculation.month,
      token: calculation.token,
      userAddress: userBalance.userAddress,
      averageStaked: userBalance.averageStaked,
      totalAverageStaked: calculation.totalAverageStaked,
    }));

    await monthlyRewardsRepo.bulkUpsertMonthlyRewards(rewards);
  }

  /**
   * Admin function to set reward amounts and distribute
   */
  public async setRewardAmount(params: AdminRewardUpdateParams): Promise<void> {
    try {
      const data = await Bondingdex.findOne({ bondingAddress: params.token }).select('projectDetails.bondingPrice projectDetails.acquisitionCosts.total');
      console.log(data);
      console.log('projectDetails');
      const price = data?.projectDetails?.bondingPrice || 1;
      const totalSupply = Number(data?.projectDetails?.acquisitionCosts?.total) / price;
      console.log('totalSupply', totalSupply);
      const bondingContract = contractsConfig.find((obj) => obj.name === 'BondingFactory');
      const bondingInstance = provider.createContractInstance(bondingContract.abi as any, bondingContract.address);
      const LBMPercentage = await bondingInstance.methods.getLBMPercentage().call();
      console.log('LBMPercentage', LBMPercentage);
      const finalSupply = totalSupply + totalSupply / (Number(LBMPercentage) / 100) / 100;
      console.log('finalSupply', finalSupply);

      const perToken = params.totalRewardAmount / finalSupply;
      console.log('perToken', perToken);

      // params.totalRewardAmount = params.totalRewardAmount * perToken > params.totalRewardAmount ? params.totalRewardAmount : params.totalRewardAmount * perToken;
      // console.log('totalRewardAmount', params.totalRewardAmount);
      await monthlyRewardsRepo.updateRewardDistribution(params, perToken);
      console.log(`Reward distribution updated for ${params.month} - ${params.token}: ${params.totalRewardAmount} tokens`);
    } catch (error) {
      console.error('Error setting reward amount:', error);
      throw error;
    }
  }

  /**
   * Get reward summary for admin review
   */
  public async getRewardSummary(month: string, token: string) {
    return await monthlyRewardsRepo.getMonthlyRewardSummary(month, token);
  }

  /**
   * Get all pending reward distributions
   */
  public async getPendingRewards(token: string, month: string) {
    return await monthlyRewardsRepo.getPendingRewards(token, month);
  }

  /**
   * Get user's reward history
   */
  public async getUserRewards(userAddress: string) {
    return await monthlyRewardsRepo.getUserRewards(userAddress);
  }

  /**
   * Get active tokens for a specific month
   */
  private async getActiveTokensForMonth(month: string): Promise<string[]> {
    const { startDate, endDate } = this.getMonthDateRange(month);
    const tokens = await stakingTransactionsModel.distinct('token', {
      timestamp: {
        $gte: Math.floor(startDate.getTime() / 1000),
        $lte: Math.floor(endDate.getTime() / 1000),
      },
    });

    return tokens;
  }

  /**
   * Get previous month in YYYY-MM format
   */
  private getPreviousMonth(): string {
    const now = new Date();
    // const now = new Date('2025-08-01T07:21:18.857Z');
    console.log('now=====>>>>>', now);
    const prevMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    return `${prevMonth.getFullYear()}-${String(prevMonth.getMonth() + 1).padStart(2, '0')}`;
  }

  /**
   * Get date range and days count for a month
   */
  private getMonthDateRange(month: string): { startDate: Date; endDate: Date; daysInMonth: number } {
    const [year, monthNum] = month.split('-').map(Number);
    const startDate = new Date(Date.UTC(year, monthNum - 1, 1));
    const endDate = new Date(Date.UTC(year, monthNum, 0, 23, 59, 59, 999));
    const daysInMonth = endDate.getUTCDate();

    return { startDate, endDate, daysInMonth };
  }

  /**
   * Force recalculation for a specific month (admin function)
   */
  public async recalculateMonth(month: string): Promise<void> {
    console.log(`Force recalculating averages for ${month}`);

    // Get all tokens that had activity in this month
    const tokens = await this.getActiveTokensForMonth(month);

    for (const token of tokens) {
      console.log(`Recalculating ${token} for ${month}`);
      const calculation = await this.calculateTokenMonthlyAverages(month, token);

      if (calculation.userBalances.length > 0) {
        await this.saveMonthlyCalculation(calculation);
        console.log(`Recalculated ${token}: ${calculation.userBalances.length} users`);
      }
    }
  }
}

export default new MonthlyRewardsService();
