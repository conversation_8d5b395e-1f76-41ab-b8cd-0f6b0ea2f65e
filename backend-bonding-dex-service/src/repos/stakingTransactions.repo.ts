import stakingTransactionsModel, { StakingTransactionType } from '../models/stakingTransactions.model';
import { Bondingdex } from '../models/bondingdexs.model';
import { ethers } from 'ethers';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../helpers/sentry.helper';

class StakingTransactionsRepo {
  /**
   * Insert staking or unstaking transaction
   */
  public async insertStakingTransaction(params: {
    type: StakingTransactionType;
    token: string;
    userAddress: string;
    stakedAmount?: string;
    userTotalStaked: string;
    totalTokenStaked: string;
    unstakedAmount?: string;
    timestamp: number;
    actionID: string;
    transactionHash: string;
  }): Promise<void> {
    try {
      if (params.token === '******************************************' || params.token === '******************************************') {
        params.token = '******************************************';
      }
      const bondingData: any = await Bondingdex.findOne({
        bondingAddress: new RegExp(`^${params.token}$`, 'i'),
      });
      const decimals = bondingData?.projectDetails?.tokenDecimals || 18;

      params.stakedAmount = params.stakedAmount ? ethers.formatUnits(params.stakedAmount.toString(), decimals) : '0';
      params.unstakedAmount = params.unstakedAmount ? ethers.formatUnits(params.unstakedAmount.toString(), decimals) : '0';
      params.userTotalStaked = ethers.formatUnits(params.userTotalStaked.toString(), decimals);
      params.totalTokenStaked = ethers.formatUnits(params.totalTokenStaked.toString(), decimals);
      await stakingTransactionsModel.create(params);
    } catch (error) {
      ErrorHandler.handleError(error as Error, {
        context: 'StakingTransactionsRepo.insertStakingTransaction',
        params: {
          type: params.type,
          token: params.token,
          userAddress: params.userAddress,
          actionID: params.actionID,
        },
      });
      throw error;
    }
  }
}

export default new StakingTransactionsRepo();
