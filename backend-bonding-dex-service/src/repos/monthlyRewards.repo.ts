import { ObjectId } from 'mongodb';
import monthlyTokenRewardsModel, { IMonthlyTokenReward } from '../models/monthlyRewards.model';
import { ErrorHandler } from '../helpers/sentry.helper';

export interface MonthlyRewardInsertParams {
  month: string;
  token: string;
  userAddress: string;
  averageStaked: number;
  totalAverageStaked: number;
}

export interface AdminRewardUpdateParams {
  month: string;
  token: string;
  totalRewardAmount: number;
}

export interface UserRewardData {
  userAddress: string;
  averageStaked: number;
  sharePercentage: number;
  userReward: number;
}

export interface MonthlyRewardSummary {
  month: string;
  token: string;
  totalAverageStaked: number;
  totalRewardAmount: number;
  rewardCalculated: boolean;
  userCount: number;
  users: UserRewardData[];
}

class MonthlyRewardsRepo {
  // /**
  //  * Insert or update monthly reward calculation for a user
  //  */
  // public async upsertMonthlyReward(params: MonthlyRewardInsertParams): Promise<void> {
  //   await monthlyTokenRewardsModel.updateOne(
  //     {
  //       month: params.month,
  //       token: params.token,
  //       userAddress: params.userAddress,
  //     },
  //     {
  //       $set: {
  //         averageStaked: params.averageStaked,
  //         totalAverageStaked: params.totalAverageStaked,
  //         updatedAt: new Date(),
  //       },
  //       $setOnInsert: {
  //         userReward: 0,
  //         totalRewardAmount: 0,
  //         rewardCalculated: false,
  //         calculatedAt: new Date(),
  //       },
  //     },
  //     { upsert: true },
  //   );
  // }

  /**
   * Bulk insert monthly rewards for multiple users
   */
  public async bulkUpsertMonthlyRewards(rewards: MonthlyRewardInsertParams[]): Promise<void> {
    try {
      const bulkOps = rewards.map((reward) => ({
        updateOne: {
          filter: {
            month: reward.month,
            token: reward.token,
            userAddress: reward.userAddress,
          },
          update: {
            $set: {
              averageStaked: reward.averageStaked,
              totalAverageStaked: reward.totalAverageStaked,
              updatedAt: new Date(),
            },
            $setOnInsert: {
              userReward: 0,
              totalRewardAmount: 0,
              rewardCalculated: false,
              calculatedAt: new Date(),
            },
          },
          upsert: true,
        },
      }));

      if (bulkOps.length > 0) {
        await monthlyTokenRewardsModel.bulkWrite(bulkOps);
      }
    } catch (error) {
      ErrorHandler.handleError(error as Error, {
        context: 'MonthlyRewardsRepo.bulkUpsertMonthlyRewards',
        rewardCount: rewards.length,
      });
      throw error;
    }
  }

  /**
   * Update reward amounts when admin sets total reward
   */
  public async updateRewardDistribution(params: AdminRewardUpdateParams, perToken: number): Promise<void> {
    try {
      // First, get all users for this month/token
      const users = await monthlyTokenRewardsModel.find({
        month: params.month,
        token: params.token,
      });

      if (users.length === 0) {
        throw new Error(`No reward data found for month ${params.month} and token ${params.token}`);
      }

      const totalAverageStaked = users[0].totalAverageStaked;
      console.log('totalAverageStaked', totalAverageStaked);

      // Calculate individual rewards
      const finalToDistribute = perToken * totalAverageStaked;
      console.log('final to distribute', perToken * totalAverageStaked);
      const bulkOps = users.map((user) => {
        const userReward = totalAverageStaked > 0 ? (user.averageStaked / totalAverageStaked) * finalToDistribute : 0;

        return {
          updateOne: {
            filter: { _id: user._id },
            update: {
              $set: {
                userReward: Number(userReward.toFixed(6)),
                totalRewardAmount: finalToDistribute,
                rewardCalculated: true,
                updatedAt: new Date(),
              },
            },
          },
        };
      });

      await monthlyTokenRewardsModel.bulkWrite(bulkOps);
    } catch (error) {
      ErrorHandler.handleError(error as Error, {
        context: 'MonthlyRewardsRepo.updateRewardDistribution',
        params,
        perToken,
      });
      throw error;
    }
  }

  /**
   * Get reward summary for a specific month and token
   */
  public async getMonthlyRewardSummary(month: string, token: string): Promise<MonthlyRewardSummary | null> {
    try {
      const rewards = await monthlyTokenRewardsModel.find({ month, token }).sort({ averageStaked: -1 });

      if (rewards.length === 0) {
        return null;
      }

      const firstReward = rewards[0];
      const users: UserRewardData[] = rewards.map((reward) => ({
        userAddress: reward.userAddress,
        averageStaked: reward.averageStaked,
        sharePercentage: firstReward.totalAverageStaked > 0 ? (reward.averageStaked / firstReward.totalAverageStaked) * 100 : 0,
        userReward: reward.userReward,
      }));

      return {
        month,
        token,
        totalAverageStaked: firstReward.totalAverageStaked,
        totalRewardAmount: firstReward.totalRewardAmount,
        rewardCalculated: firstReward.rewardCalculated,
        userCount: rewards.length,
        users,
      };
    } catch (error) {
      ErrorHandler.handleError(error as Error, {
        context: 'MonthlyRewardsRepo.getMonthlyRewardSummary',
        month,
        token,
      });
      throw error;
    }
  }

  /**
   * Get all pending reward distributions (not yet calculated by admin)
   */
  public async getPendingRewards(token: string, month: string): Promise<{ month: string; token: string; userCount: number; totalAverageStaked: number; users: { userAddress: string; averageStaked: number }[] }[]> {
    try {
      const pipeline = [
        { $match: { rewardCalculated: false, token, month } },
        {
          $group: {
            _id: { month: '$month', token: '$token' },
            userCount: { $sum: 1 },
            totalAverageStaked: { $sum: '$averageStaked' },
            users: {
              $push: {
                userAddress: '$userAddress',
                averageStaked: '$averageStaked',
              },
            },
          },
        },
        {
          $project: {
            _id: 0,
            month: '$_id.month',
            token: '$_id.token',
            userCount: 1,
            totalAverageStaked: 1,
            users: 1,
          },
        },
      ];

      return await monthlyTokenRewardsModel.aggregate(pipeline);
    } catch (error) {
      ErrorHandler.handleError(error as Error, {
        context: 'MonthlyRewardsRepo.getPendingRewards',
        token,
        month,
      });
      throw error;
    }
  }

  /**
   * Get rewards for a specific user across all months and tokens
   */
  public async getUserRewards(userAddress: string): Promise<IMonthlyTokenReward[]> {
    try {
      return await monthlyTokenRewardsModel.find({ userAddress }).sort({ month: -1, token: 1 });
    } catch (error) {
      ErrorHandler.handleError(error as Error, {
        context: 'MonthlyRewardsRepo.getUserRewards',
        userAddress,
      });
      throw error;
    }
  }

  /**
   * Check if monthly calculation already exists
   */
  public async hasMonthlyCalculation(month: string, token: string): Promise<boolean> {
    try {
      const count = await monthlyTokenRewardsModel.countDocuments({ month, token });
      return count > 0;
    } catch (error) {
      ErrorHandler.handleError(error as Error, {
        context: 'MonthlyRewardsRepo.hasMonthlyCalculation',
        month,
        token,
      });
      throw error;
    }
  }

  //   /**
  //    * Get all unique months with calculations
  //    */
  //   public async getAllCalculatedMonths(): Promise<string[]> {
  //     const months = await monthlyTokenRewardsModel.distinct('month');
  //     return months.sort().reverse();
  //   }

  //   /**
  //    * Get all unique tokens for a specific month
  //    */
  //   public async getTokensForMonth(month: string): Promise<string[]> {
  //     return await monthlyTokenRewardsModel.distinct('token', { month });
  //   }

  public async updateDividendStatus(params: { caller: string; stableCoin: string; bondingToken: string; to: string[]; amounts: string[]; actionIDs: string[] }): Promise<void> {
    try {
      const operations = params.actionIDs.map((id, index) => ({
        updateOne: {
          // filter: { _id: id },
          filter: { _id: new ObjectId(id) },
          update: { $set: { onchainAmount: params.amounts[index], rewardDistributed: true } },
        },
      }));
      const result = await monthlyTokenRewardsModel.bulkWrite(operations);
      console.log(result);
    } catch (error) {
      ErrorHandler.handleError(error as Error, {
        context: 'MonthlyRewardsRepo.updateDividendStatus',
        params: {
          caller: params.caller,
          stableCoin: params.stableCoin,
          bondingToken: params.bondingToken,
          recipientCount: params.to.length,
        },
      });
      throw error;
    }
  }
}

export default new MonthlyRewardsRepo();
