import eventBlocksModel from '../models/eventBlocks.model';
import { <PERSON>rror<PERSON>and<PERSON> } from '../helpers/sentry.helper';

class BlocksRepo {
  /**
   * Get the last fetched block number for a given chain and address.
   */
  public async getLastFetchedBlock(chain: string, address: string): Promise<number | null> {
    try {
      const data = await eventBlocksModel.findOne({ chain, address }).lean();
      return data?.blockNumber ?? null;
    } catch (error) {
      ErrorHandler.handleError(error as Error, {
        context: 'BlocksRepo.getLastFetchedBlock',
        chain,
        address,
      });
      throw error;
    }
  }

  /**
   * Upsert the block number for a given chain and address.
   */
  public async upsertBlockNumber(params: { chain: string; address: string; contractName: string; blockNumber: number }): Promise<void> {
    try {
      const { chain, address, contractName, blockNumber } = params;

      await eventBlocksModel.findOneAndUpdate(
        { chain, address },
        {
          $set: {
            contractName,
            blockNumber,
          },
        },
        {
          new: true,
          upsert: true,
          setDefaultsOnInsert: true,
        },
      );
    } catch (error) {
      ErrorHandler.handleError(error as Error, {
        context: 'BlocksRepo.upsertBlockNumber',
        params,
      });
      throw error;
    }
  }
}

export default new BlocksRepo();
