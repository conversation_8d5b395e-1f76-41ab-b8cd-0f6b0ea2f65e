/* eslint-disable prefer-const */
import transactionsModel from '../models/transactions.model';
import { Bondingdex } from '../models/bondingdexs.model';

class TransactionsRepo {
  /**
   * Create the buy/sell transaction.
   */
  public async insertTransaction(params: { type: string; token: string; amount: string; tradeID: string; userAddress: string; transactionHash: string; createdAt: Date }): Promise<void> {
    let { type, token, amount, tradeID, userAddress, transactionHash, createdAt } = params;
    if (token === '0x1bb595d1fB82597035365BbF8A2bf1B5AD78092b' || token === '0xfFa1Ed9c565a4e635543123b29889e96BcaFA184') {
      token = '0xfFa1Ed9c565a4e635543123b29889e96BcaFA184';
    }
    const bondingData: any = await Bondingdex.findOne({
      bondingAddress: new RegExp(`^${token}$`, 'i'),
    });
    const price = bondingData?.projectDetails?.bondingPrice || 1;
    await transactionsModel.create({
      type: type,
      token: token,
      amount: amount,
      tradeID: tradeID,
      userAddress: userAddress,
      transactionHash: transactionHash,
      price: price,
      createdAt,
    });
  }

  public async insertStakingTransaction(params: { bondingToken: string; stakingContract: string; salt: string; transactionHash: string }): Promise<void> {
    let { bondingToken, stakingContract } = params;
    if (bondingToken === '0x1bb595d1fB82597035365BbF8A2bf1B5AD78092b' || bondingToken === '0xfFa1Ed9c565a4e635543123b29889e96BcaFA184') {
      bondingToken = '0xfFa1Ed9c565a4e635543123b29889e96BcaFA184';
    }

    await Bondingdex.updateOne(
      {
        bondingAddress: new RegExp(`^${bondingToken}$`, 'i'),
      },
      { $set: { stakingAddress: stakingContract, isStaking: true } },
    );
  }
}

export default new TransactionsRepo();
