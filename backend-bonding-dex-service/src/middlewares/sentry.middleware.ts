/* eslint-disable @typescript-eslint/no-unused-vars */
import { Application, Request, Response, NextFunction } from 'express';
import * as Sentry from '@sentry/node';
import sentryService from '../helpers/sentry.helper';

export const initSentryMiddleware = (app: Application) => {
  // The request handler must be the first middleware on the app
  app.use(Sentry.Handlers.requestHandler());

  // The error handler must be before any other error middleware and after all controllers
  app.use(Sentry.Handlers.errorHandler());

  // Optional fallthrough error handler
  app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
    console.error('Unhandled error:', err);

    // Capture the error in Sentry
    sentryService.captureError(err, {
      request: {
        method: req.method,
        url: req.url,
        headers: req.headers,
        body: req.body,
        query: req.query,
        params: req.params,
      },
    });

    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  });
};
