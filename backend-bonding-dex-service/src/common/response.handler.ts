import { Response as ExpressResponse } from 'express';
export interface ErrorResponseData {
  error: boolean;
  status?: number;
  message: string;
}
export interface SuccessResponseData<T> {
  error: boolean;
  message: string;
  data?: T;
}

export const failResponse = async (error?: boolean, msg?: string, response?: ExpressResponse, status = 400) => {
  if (response) {
    response.status(status).send({ message: msg, data: null, error });
  }
};

export const successResponse = async (message: string, data?: object | null | '' | unknown | undefined, response?: ExpressResponse, status = 200) => {
  if (response) {
    response.status(status).send({ message, data });
  }
};

/*
 * @desc: Success Response Handler
 *
 * @param {string} message - A descriptive message indicating the success.
 * @param {T} data - The data associated with the success, with a generic type T.
 *   const successResult = successResponse<User>({ message: 'User found', data: user });
 */

export const successServiceResponse = <T>(message: string, data?: T): SuccessResponseData<T> => {
  // Sentry.captureMessage(message);
  return {
    error: false,
    message,
    data,
  };
};

/*
 * @description: Generates an error response object.
 * @param {string} message - A descriptive error message.
 * @param {number} status - The HTTP status code indicating the nature of the error.
 * Example Usage:
 * const response = errorResponse('Resource not found', HttpStatus.NOT_FOUND);
 */
export const errorServiceResponse = (message: string, status?: number): ErrorResponseData => {
  return {
    error: true,
    message,
    status,
  };
};
