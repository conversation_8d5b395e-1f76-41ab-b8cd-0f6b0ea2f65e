import * as grpc from '@grpc/grpc-js';
import * as protoLoader from '@grpc/proto-loader';
import path from 'path';
import { getStakingTransactions, getUserStakingSummary, getStakingDividendList } from '../services/staking.service';
import monthlyRewardsService from '../services/monthlyRewards.service';

const PROTO_PATH = path.join(__dirname, '../_grpc/proto/bonding.proto');

// Load proto definition
const packageDefinition = protoLoader.loadSync(PROTO_PATH, {
  keepCase: true,
  longs: String,
  enums: String,
  defaults: true,
  oneofs: true,
});

const bondingProto = grpc.loadPackageDefinition(packageDefinition) as any;

/**
 * gRPC handler for GetStakingTransactions
 * @param call - gRPC call object
 * @param callback - gRPC callback function
 */
const handleGetStakingTransactions = async (call: any, callback: any) => {
  try {
    const { token, pagination, userAddress, type } = call.request;
    const { page = 1, limit = 10, search = '' } = pagination || {};

    console.log('GetStakingTransactions called with:', { token, page, limit, search });

    const result: any = await getStakingTransactions(token, {
      page,
      limit,
      search,
      userAddress,
      type,
    });

    if (result.error) {
      return callback({
        code: grpc.status.INTERNAL,
        message: result.message || 'Internal server error',
      });
    }

    callback(null, result.data);
  } catch (error: any) {
    console.error('GetStakingTransactions error:', error);
    callback({
      code: grpc.status.INTERNAL,
      message: error.message || 'Internal server error',
    });
  }
};

/**
 * gRPC handler for GetUserStakingSummary
 * @param call - gRPC call object
 * @param callback - gRPC callback function
 */
const handleGetUserStakingSummary = async (call: any, callback: any) => {
  try {
    const { token, pagination } = call.request;
    const { page = 1, limit = 10, search = '' } = pagination || {};

    console.log('GetUserStakingSummary called with:', { token, page, limit, search });

    const result: any = await getUserStakingSummary(token, {
      page,
      limit,
      search,
    });

    if (result.error) {
      return callback({
        code: grpc.status.INTERNAL,
        message: result.message || 'Internal server error',
      });
    }

    callback(null, result.data);
  } catch (error: any) {
    console.error('GetUserStakingSummary error:', error);
    callback({
      code: grpc.status.INTERNAL,
      message: error.message || 'Internal server error',
    });
  }
};

/**
 * gRPC handler for GetStakingDividendList
 * @param call - gRPC call object
 * @param callback - gRPC callback function
 */
const handleGetStakingDividendList = async (call: any, callback: any) => {
  try {
    const { token, monthString, pagination } = call.request;
    const { page = 1, limit = 10, search = '' } = pagination || {};

    console.log('GetStakingDividendList called with:', { token, monthString, page, limit, search });

    const result: any = await getStakingDividendList(token, monthString, {
      page,
      limit,
      search,
    });

    if (result.error) {
      return callback({
        code: grpc.status.INTERNAL,
        message: result.message || 'Internal server error',
      });
    }

    callback(null, result.data);
  } catch (error: any) {
    console.error('GetStakingDividendList error:', error);
    callback({
      code: grpc.status.INTERNAL,
      message: error.message || 'Internal server error',
    });
  }
};

/**
 * gRPC handler for UpdateStakingRewardAmount
 * @param call - gRPC call object
 * @param callback - gRPC callback function
 */
const handleUpdateStakingRewardAmount = async (call: any, callback: any) => {
  try {
    const { token, month, totalRewardAmount } = call.request;

    console.log('UpdateStakingRewardAmount called with:', { token, month, totalRewardAmount });

    // Validate input
    if (!token || !month || totalRewardAmount === undefined) {
      return callback({
        code: grpc.status.INVALID_ARGUMENT,
        message: 'Token, month, and totalRewardAmount are required',
      });
    }

    if (totalRewardAmount < 0) {
      return callback({
        code: grpc.status.INVALID_ARGUMENT,
        message: 'Total reward amount must be non-negative',
      });
    }

    // Update reward distribution using existing service
    await monthlyRewardsService.setRewardAmount({
      month,
      token,
      totalRewardAmount: Number(totalRewardAmount),
    });

    // Get count of updated records
    const summary = await monthlyRewardsService.getRewardSummary(month, token);
    const updatedCount = summary ? summary.userCount : 0;

    callback(null, {
      success: true,
      message: 'Staking reward amount updated successfully and distributed to users',
      updatedCount: updatedCount,
      totalRewardAmount: Number(totalRewardAmount),
    });
  } catch (error: any) {
    console.error('UpdateStakingRewardAmount error:', error);
    callback({
      code: grpc.status.INTERNAL,
      message: error.message || 'Internal server error',
    });
  }
};

/**
 * Start the gRPC server for bonding service
 */
export const startBondingGrpcServer = () => {
  const server = new grpc.Server();

  // Add service implementation
  server.addService(bondingProto.bonding.BondingService.service, {
    GetStakingTransactions: handleGetStakingTransactions,
    GetUserStakingSummary: handleGetUserStakingSummary,
    GetStakingDividendList: handleGetStakingDividendList,
    UpdateStakingRewardAmount: handleUpdateStakingRewardAmount,
  });

  const PORT = process.env.BONDING_SERVICE_GRPC_PORT || '40003';
  const HOST = process.env.BONDING_SERVICE_GRPC_CONTAINER_NAME || '0.0.0.0';

  server.bindAsync(`${HOST}:${PORT}`, grpc.ServerCredentials.createInsecure(), (error, port) => {
    if (error) {
      console.error('Failed to start gRPC server:', error);
      return;
    }
    server.start();
    console.log(`🚀 Bonding gRPC server running at ${HOST}:${port}`);
  });

  return server;
};
