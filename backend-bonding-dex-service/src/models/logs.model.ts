import mongoose, { Schema } from 'mongoose';
import { ILogs } from './interface';

const LogsSchema: Schema = new Schema(
  {
    controller: { type: String, required: false },
    service: { type: String, required: false },
    functionName: { type: String, required: false },
    message: { type: String, required: false },
    data: { type: String, required: false },
    payload: { type: String, default: null },
    status: {
      type: String,
      enum: ['success', 'error', 'pending'],
      default: 'pending',
    },
  },
  { versionKey: false, timestamps: true },
);

export const Logs = mongoose.model<ILogs>('logs', LogsSchema);
