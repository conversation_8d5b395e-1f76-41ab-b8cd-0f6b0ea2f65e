import { Types } from 'mongoose';

export interface IBondingdex extends Document {
  _id: Types.ObjectId | string;
  userId: Types.ObjectId;
  overview: {
    title: string;
    subTitle?: string;
    description: string;
    entityName: string;
    entityType: string;
    webUrl?: string;
    lineOfBusiness: string;
    sourceOfFunds: string;
    location: string;
    companyDescription: string;
    icon?: string;
    cover?: string;
    logo: string;
    propertyImages?: string[];
  };

  projectDetails: {
    assetType: string;
    blockChainType: string;
    bondingdexType: string;
    tokenStandard: string;
    bondingdexName: string;
    CUSIP: string;
    isAuthorized: boolean;
    authorizedCountries: IAuthorizedCountry[];
    startDate: Date;
    endDate: Date;
    minInvestment: number;
    maxInvestment: number;
    assetName: string;
    tokenTicker: string;
    tokenSupply: number;
    tokenDecimals: number;
    lockupMonths: number;
    holdTime: Date;
    maxTokenHolding: number;
    navLaunchPrice: number;
    latestNav: number;
    // aumSize: number;
    // spvValuation: number;
    propertyType: string;
    isTransferAgent: boolean;
    taId?: string;
    issuerId: string;
    issuerWallet: string;
    customFields: Array<{
      label: string;
      type: string;
      value: string;
    }>;
    isPrivate?: boolean;
    bondingdexMembers?: string[];
    propertySubtype?: string;
    yearBuilt?: number;
    lotSize?: number;
    occupancy?: number;
    projectedYield?: number;
    launchValuation?: number;
    previousValuation?: number;
    deRatio?: number;
    acquisitionCosts?: {
      legalCost?: number;
      estateAgent?: string;
      stampDuty?: string;
      tax?: string;
      platformFees?: number;
      maintenance?: string;
      total?: number;
    };
    agentName?: string;
    agentFunctions?: string[];
    poweredBy?: string;
    poweredByLogo?: string;
    bondingPrice?: number;
    roi?: number;
  };
  documents: {
    assetType?: string;
    eSign?: string;
    pitchDeck?: string;
    confidentialInformationMemorendum?: string;
    landRegistration?: string;
    titleDocs?: string;
    bankApproval?: string;
    encumbranceCertificate?: string;
    propertyTaxReceipt?: string;
    articlesOfAssociation?: string;
    operatingAgreement?: string;
    taxAssignmentLetter?: string;
    certificateOfRegistration?: string;
    registerOfManagers?: string;
    customDocs: Array<{
      docsLabel: string;
      value: string;
    }>;
  };
  team: Array<{
    name: string;
    title: string;
    summary?: string;
    email?: string;
    url?: string;
    linkedInUrl?: string;
    twitterUrl?: string;
  }>;
  isFinalSubmission?: boolean;
  currentStep: number;
  isTokenDeploy?: boolean;
  isFundDeploy?: boolean;
  iserc20?: boolean;
  tokenAddress?: string;
  erc20Address?: string;
  identityRegistry?: string;
  fundAddress?: string;
  fee?: {
    escrowFee?: number;
    wrapFee?: number;
    dividendFee?: number;
    redemptionFee?: number;
  };
  deployedDate?: Date;
  wrapperDeployedAt?: Date;

  isActive: boolean;
  isDelete?: boolean;
  status?: bondingdexStatusEnum;
  createdBy?: Types.ObjectId;
  template_id: string;
  envelopeId: string;
  bondingdexFeeStatus?: boolean;
  reason?: string;
  proposalHoldingPercentage?: [string];
  dividends?: Array<{
    _id: Types.ObjectId | string;
    bondingdexId: Types.ObjectId | string;
    bondingdexName: string;
    dividendAmount: number;
    status: string;
    dividendType: string;
    paymentMethod: string;
    documentUrl: string;
    recordDate: Date;
    issuerId: Types.ObjectId | string;
    declarationDate: Date;
    createdAt: Date;
    updatedAt: Date;
  }>;
}
export enum bondingdexStatusEnum {
  STARTED = 'STARTED',
  // MINTED = 'MINTED',
  IN_PROGRESS = 'IN_PROGRESS',
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  RESUBMIT = 'RESUBMIT',
  DELETED = 'DELETED',
  REVIEW = 'REVIEW',
}
interface IAuthorizedCountry {
  name: string;
  isoCode: string;
  countryCode: string;
}
export interface IBondingdex extends Document {
  _id: Types.ObjectId | string;
  bondingdexId: Types.ObjectId | string;
  userId: Types.ObjectId;
  overview: {
    title: string;
    subTitle?: string;
    description: string;
    entityName: string;
    entityType: string;
    webUrl?: string;
    lineOfBusiness: string;
    sourceOfFunds: string;
    location: string;
    companyDescription: string;
    icon?: string;
    cover?: string;
    logo: string;
    propertyImages?: string[];
  };

  projectDetails: {
    assetType: string;
    blockChainType: string;
    bondingdexType: string;
    tokenStandard: string;
    bondingdexName: string;
    CUSIP: string;
    isAuthorized: boolean;
    authorizedCountries: IAuthorizedCountry[];
    startDate: Date;
    endDate: Date;
    minInvestment: number;
    maxInvestment: number;
    assetName: string;
    tokenTicker: string;
    tokenSupply: number;
    tokenDecimals: number;
    lockupMonths: number;
    holdTime: Date;
    maxTokenHolding: number;
    navLaunchPrice: number;
    latestNav: number;
    // aumSize: number;
    // spvValuation: number;
    propertyType: string;
    isTransferAgent: boolean;
    taId?: string;
    issuerId: string;
    issuerWallet: string;
    customFields: Array<{
      label: string;
      type: string;
      value: string;
    }>;
    isPrivate?: boolean;
    bondingdexMembers?: string[];
    propertySubtype?: string;
    yearBuilt?: number;
    lotSize?: number;
    occupancy?: number;
    projectedYield?: number;
    launchValuation?: number;
    previousValuation?: number;
    deRatio?: number;
    acquisitionCosts?: {
      legalCost?: number;
      estateAgent?: string;
      stampDuty?: string;
      tax?: string;
      platformFees?: number;
      maintenance?: string;
      total?: number;
    };
    agentName?: string;
    agentFunctions?: string[];
    poweredBy?: string;
    poweredByLogo?: string;
    bondingPrice?: number;
    roi?: number;
  };
  documents: {
    assetType?: string;
    eSign?: string;
    pitchDeck?: string;
    confidentialInformationMemorendum?: string;
    landRegistration?: string;
    titleDocs?: string;
    bankApproval?: string;
    encumbranceCertificate?: string;
    propertyTaxReceipt?: string;
    articlesOfAssociation?: string;
    operatingAgreement?: string;
    taxAssignmentLetter?: string;
    certificateOfRegistration?: string;
    registerOfManagers?: string;
    customDocs: Array<{
      docsLabel: string;
      value: string;
    }>;
  };
  team: Array<{
    name: string;
    title: string;
    summary?: string;
    email?: string;
    url?: string;
    linkedInUrl?: string;
    twitterUrl?: string;
  }>;
  isFinalSubmission?: boolean;
  currentStep: number;
  isTokenDeploy?: boolean;
  isFundDeploy?: boolean;
  iserc20?: boolean;
  tokenAddress?: string;
  erc20Address?: string;
  identityRegistry?: string;
  fundAddress?: string;
  fee?: {
    escrowFee?: number;
    wrapFee?: number;
    dividendFee?: number;
    redemptionFee?: number;
  };
  deployedDate?: Date;
  wrapperDeployedAt?: Date;

  isActive: boolean;
  isNft: boolean;
  isDelete?: boolean;
  status?: bondingdexStatusEnum;
  createdBy?: Types.ObjectId;
  template_id: string;
  envelopeId: string;
  bondingdexFeeStatus?: boolean;
  reason?: string;
  proposalHoldingPercentage?: [string];
  dividends?: Array<{
    _id: Types.ObjectId | string;
    bondingdexId: Types.ObjectId | string;
    bondingdexName: string;
    dividendAmount: number;
    status: string;
    dividendType: string;
    paymentMethod: string;
    documentUrl: string;
    recordDate: Date;
    issuerId: Types.ObjectId | string;
    declarationDate: Date;
    createdAt: Date;
    updatedAt: Date;
  }>;
}

export interface ILogs extends Document {
  controller: string;
  service: string;
  functionName: string;
  message: string;
  data: string;
  payload?: string;
  status?: 'success' | 'error' | 'pending';
}
