import * as mongoose from 'mongoose';

interface ITransaction {
  type: string;
  token: string;
  amount: string;
  tradeID: string;
  userAddress: string;
  price: string;
  transactionHash: string;
}

const transactionsModels = new mongoose.Schema<ITransaction>(
  {
    type: { type: String, require: true },
    token: { type: String, require: true },
    amount: { type: String, require: true },
    price: { type: String, require: false },
    tradeID: { type: String, require: true, unique: true },
    userAddress: { type: String, require: true },
    transactionHash: { type: String, require: false },
  },
  { timestamps: true, versionKey: false },
);
export default mongoose.model('transactions', transactionsModels);
