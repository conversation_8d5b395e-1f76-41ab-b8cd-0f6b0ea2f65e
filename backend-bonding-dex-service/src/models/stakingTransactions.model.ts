import { Schema, model } from 'mongoose';

export type StakingTransactionType = 'TokenStaked' | 'TokenUnstaked';

export interface IStakingTransaction extends Document {
  type: StakingTransactionType;
  token: string;
  userAddress: string;
  stakedAmount?: string;
  userTotalStaked?: string;
  totalTokenStaked?: string;
  unstakedAmount?: string;
  timestamp: number;
  actionID: string;
  transactionHash: string;
}

const stakingTransactionsSchema = new Schema<IStakingTransaction>(
  {
    type: { type: String, required: true },
    token: { type: String, required: true },
    userAddress: { type: String, required: true },
    stakedAmount: { type: String, required: false },
    userTotalStaked: { type: String, required: false },
    totalTokenStaked: { type: String, required: false },
    unstakedAmount: { type: String, required: false },
    timestamp: { type: Number, required: true },
    actionID: { type: String, required: true, unique: true },
    transactionHash: { type: String, required: true },
  },
  { timestamps: true, versionKey: false },
);

export default model<IStakingTransaction>('stakingtransactions', stakingTransactionsSchema);
