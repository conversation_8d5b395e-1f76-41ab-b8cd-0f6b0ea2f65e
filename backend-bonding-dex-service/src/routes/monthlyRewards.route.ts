import { Router } from 'express';
import { getPendingR<PERSON><PERSON>, getRewardSummary, setRewardAmount, getUserRewards, triggerMonthly<PERSON>alculation, getSchedulerStatus, recalculateMonth } from '../controllers/monthlyRewards.controller';

const router = Router();

// Admin routes for managing monthly rewards
/**
 * @swagger
 * /rewards/pending:
 *   get:
 *     summary: Get all pending reward distributions
 *     description: Retrieve all month/token combinations that have calculated averages but no admin-set reward amounts
 *     tags: [Monthly Rewards Admin]
 *     responses:
 *       200:
 *         description: Pending rewards retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       month:
 *                         type: string
 *                         example: "2025-05"
 *                       token:
 *                         type: string
 *                         example: "0xTokenAddress..."
 *                       userCount:
 *                         type: number
 *                         example: 15
 *                       totalAverageStaked:
 *                         type: number
 *                         example: 1250.75
 */
router.get('/pending/:token/:month', getPendingRewards);

/**
 * @swagger
 * /rewards/summary:
 *   get:
 *     summary: Get reward summary for specific month and token
 *     description: Get detailed reward information including user distributions for a specific month/token combination
 *     tags: [Monthly Rewards Admin]
 *     parameters:
 *       - in: query
 *         name: month
 *         required: true
 *         schema:
 *           type: string
 *           example: "2025-05"
 *         description: Month in YYYY-MM format
 *       - in: query
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *           example: "0xTokenAddress..."
 *         description: Token contract address
 *     responses:
 *       200:
 *         description: Reward summary retrieved successfully
 *       400:
 *         description: Missing required parameters
 *       404:
 *         description: No reward data found
 */
router.get('/summary', getRewardSummary);

/**
 * @swagger
 * /rewards/set-amount:
 *   post:
 *     summary: Set reward amount for month/token (Admin)
 *     description: Set the total reward amount for a specific month/token and automatically distribute to users based on their staking averages
 *     tags: [Monthly Rewards Admin]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - month
 *               - token
 *               - totalRewardAmount
 *             properties:
 *               month:
 *                 type: string
 *                 example: "2025-05"
 *                 description: Month in YYYY-MM format
 *               token:
 *                 type: string
 *                 example: "0xTokenAddress..."
 *                 description: Token contract address
 *               totalRewardAmount:
 *                 type: number
 *                 example: 1000
 *                 description: Total reward amount to distribute
 *     responses:
 *       200:
 *         description: Reward amount set and distributed successfully
 *       400:
 *         description: Invalid input parameters
 */
router.post('/set-amount', setRewardAmount);

/**
 * @swagger
 * /rewards/user/{userAddress}:
 *   get:
 *     summary: Get user's reward history
 *     description: Retrieve all monthly rewards for a specific user across all tokens
 *     tags: [Monthly Rewards]
 *     parameters:
 *       - in: path
 *         name: userAddress
 *         required: true
 *         schema:
 *           type: string
 *           example: "0xUserAddress..."
 *         description: User wallet address
 *     responses:
 *       200:
 *         description: User rewards retrieved successfully
 *       400:
 *         description: Invalid user address
 */
router.get('/user/:userAddress', getUserRewards);

/**
 * @swagger
 * /rewards/admin/trigger-calculation:
 *   post:
 *     summary: Manually trigger monthly calculation (Admin)
 *     description: Force trigger the monthly average calculation for a specific month or previous month
 *     tags: [Monthly Rewards Admin]
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               targetMonth:
 *                 type: string
 *                 example: "2025-05"
 *                 description: Optional month in YYYY-MM format, defaults to previous month
 *     responses:
 *       200:
 *         description: Monthly calculation completed successfully
 *       400:
 *         description: Invalid month format
 */
router.post('/admin/trigger-calculation', triggerMonthlyCalculation);

/**
 * @swagger
 * /rewards/admin/scheduler-status:
 *   get:
 *     summary: Get scheduler status
 *     description: Check if the monthly rewards scheduler is running and when it will run next
 *     tags: [Monthly Rewards Admin]
 *     responses:
 *       200:
 *         description: Scheduler status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     isRunning:
 *                       type: boolean
 *                       example: true
 *                     nextRun:
 *                       type: string
 *                       example: "2025-06-01T01:00:00.000Z"
 */
router.get('/admin/scheduler-status', getSchedulerStatus);

/**
 * @swagger
 * /rewards/admin/recalculate:
 *   post:
 *     summary: Recalculate rewards for a specific month (Admin)
 *     description: Force recalculate monthly averages for a specific month, overwriting existing data
 *     tags: [Monthly Rewards Admin]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - month
 *             properties:
 *               month:
 *                 type: string
 *                 example: "2025-05"
 *                 description: Month in YYYY-MM format to recalculate
 *     responses:
 *       200:
 *         description: Month recalculated successfully
 *       400:
 *         description: Invalid month format
 */
router.post('/admin/recalculate', recalculateMonth);

export default router;
