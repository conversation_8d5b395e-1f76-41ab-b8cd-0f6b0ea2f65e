import * as express from 'express';
import { getStakingTransactionsController, getUserStakingSummaryController, getUserStakedOfferingsController } from '../controllers/staking.controller';

const router = express.Router();

/**
 * @swagger
 * /bonding/api/staking-transactions/{token}:
 *   get:
 *     summary: Get staking transactions for a specific token
 *     description: Retrieve paginated staking transactions (stakes and unstakes) for a specific token with optional filtering
 *     tags:
 *       - Staking
 *     parameters:
 *       - in: path
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *           pattern: '^0x[a-fA-F0-9]{40}$'
 *         description: Token contract address
 *         example: "0x1234567890123456789012345678901234567890"
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of transactions per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by user address, transaction hash, or action ID
 *       - in: query
 *         name: userAddress
 *         schema:
 *           type: string
 *           pattern: '^0x[a-fA-F0-9]{40}$'
 *         description: Filter transactions by specific user address
 *     responses:
 *       200:
 *         description: Staking transactions retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Data fetched successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     transactions:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           type:
 *                             type: string
 *                             enum: [TokenStaked, TokenUnstaked]
 *                             example: "TokenStaked"
 *                           token:
 *                             type: string
 *                             example: "0x1234567890123456789012345678901234567890"
 *                           userAddress:
 *                             type: string
 *                             example: "******************************************"
 *                           stakedAmount:
 *                             type: string
 *                             example: "100.50"
 *                           unstakedAmount:
 *                             type: string
 *                             example: "0"
 *                           userTotalStaked:
 *                             type: string
 *                             example: "100.50"
 *                           totalTokenStaked:
 *                             type: string
 *                             example: "10000.00"
 *                           timestamp:
 *                             type: integer
 *                             example: 1640995200
 *                           actionID:
 *                             type: string
 *                             example: "action_123"
 *                           transactionHash:
 *                             type: string
 *                             example: "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"
 *                     currentPage:
 *                       type: integer
 *                       example: 1
 *                     totalPages:
 *                       type: integer
 *                       example: 5
 *                     totalCount:
 *                       type: integer
 *                       example: 50
 *                     nextPage:
 *                       type: integer
 *                       nullable: true
 *                       example: 2
 *                     previousPage:
 *                       type: integer
 *                       nullable: true
 *                       example: null
 *       400:
 *         description: Invalid request parameters
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/staking-transactions/:token', getStakingTransactionsController);

/**
 * @swagger
 * /bonding/api/staking-summary/{token}:
 *   get:
 *     summary: Get user staking summary for a specific token
 *     description: Retrieve aggregated staking summary showing all users who have staked a specific token with their total amounts
 *     tags:
 *       - Staking
 *     parameters:
 *       - in: path
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *           pattern: '^0x[a-fA-F0-9]{40}$'
 *         description: Token contract address
 *         example: "0x1234567890123456789012345678901234567890"
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of users per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by user address
 *     responses:
 *       200:
 *         description: User staking summary retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Data fetched successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     users:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           userAddress:
 *                             type: string
 *                             example: "******************************************"
 *                           lastActivity:
 *                             type: string
 *                             enum: [Stake, Unstake]
 *                             example: "Stake"
 *                           lastActivityTime:
 *                             type: integer
 *                             example: 1640995200
 *                           totalStakedAmount:
 *                             type: string
 *                             example: "500.25"
 *                           averageAmount:
 *                             type: string
 *                             example: "100.05"
 *                     currentPage:
 *                       type: integer
 *                       example: 1
 *                     totalPages:
 *                       type: integer
 *                       example: 3
 *                     totalCount:
 *                       type: integer
 *                       example: 25
 *                     nextPage:
 *                       type: integer
 *                       nullable: true
 *                       example: 2
 *                     previousPage:
 *                       type: integer
 *                       nullable: true
 *                       example: null
 *       400:
 *         description: Invalid request parameters
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/staking-summary/:token', getUserStakingSummaryController);

/**
 * @swagger
 * /bonding/api/user-staked-offerings/{userAddress}:
 *   get:
 *     summary: Get all unique offerings where user has staked
 *     description: Retrieve all unique bonding offerings where a specific user has staked tokens (showing user's stake portfolio)
 *     tags:
 *       - Staking
 *     parameters:
 *       - in: path
 *         name: userAddress
 *         required: true
 *         schema:
 *           type: string
 *           pattern: '^0x[a-fA-F0-9]{40}$'
 *         description: User's Ethereum wallet address
 *         example: "******************************************"
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of offerings per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by offering title, name, or ticker symbol
 *     responses:
 *       200:
 *         description: User's staked offerings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Data fetched successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     offerings:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           token:
 *                             type: string
 *                             example: "0x1234567890123456789012345678901234567890"
 *                           offeringName:
 *                             type: string
 *                             example: "Real Estate Token A"
 *                           tokenTicker:
 *                             type: string
 *                             example: "RETA"
 *                           totalStakedAmount:
 *                             type: string
 *                             example: "1500.00"
 *                           totalUnstakedAmount:
 *                             type: string
 *                             example: "200.00"
 *                           netStakedAmount:
 *                             type: string
 *                             example: "1300.00"
 *                           lastActivityTime:
 *                             type: integer
 *                             example: 1640995200
 *                           transactionCount:
 *                             type: integer
 *                             example: 5
 *                           overview:
 *                             type: object
 *                             properties:
 *                               title:
 *                                 type: string
 *                                 example: "Premium Office Building"
 *                               description:
 *                                 type: string
 *                                 example: "A premium office building in downtown Manhattan"
 *                               logo:
 *                                 type: string
 *                                 example: "https://example.com/logo.png"
 *                               icon:
 *                                 type: string
 *                                 example: "https://example.com/icon.png"
 *                     currentPage:
 *                       type: integer
 *                       example: 1
 *                     totalPages:
 *                       type: integer
 *                       example: 2
 *                     totalCount:
 *                       type: integer
 *                       example: 15
 *                     nextPage:
 *                       type: integer
 *                       nullable: true
 *                       example: 2
 *                     previousPage:
 *                       type: integer
 *                       nullable: true
 *                       example: null
 *       400:
 *         description: Invalid user address format or request parameters
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               error: true
 *               message: "Invalid user address format"
 *               data: null
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/user-staked-offerings/:userAddress', getUserStakedOfferingsController);

export default router;
