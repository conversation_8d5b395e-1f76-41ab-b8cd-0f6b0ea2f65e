import express from 'express';
import * as dotenv from 'dotenv';
import cors from 'cors';
import helmet from 'helmet';
import { connectDB, closeDB } from './config/database';
import bondingRouter from './routes/bondingdex.route';
import calculateRouter from './routes/calculate.route';
import stakingRouter from './routes/staking.route';
import monthlyRewardsRouter from './routes/monthlyRewards.route';
import { Scheduler } from './services/scheduler.service';
import monthlyRewardsScheduler from './services/monthlyRewardsScheduler.service';
import sentryService from './helpers/sentry.helper';
import { initSentryMiddleware } from './middlewares/sentry.middleware';
import { setupSwagger } from './config/swagger';
import { startBondingGrpcServer } from './_grpc/bonding.grpc.server';

dotenv.config();
const app = express();
const PORT = process.env.PORT || 5000;

// Initialize Sentry
sentryService.init();

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cors());
app.use(helmet());

// Initialize Sentry middleware
initSentryMiddleware(app);

// Setup Swagger documentation
setupSwagger(app);

// Routes
app.use('/bonding/api/rewards', monthlyRewardsRouter);
app.use('/bonding/api', bondingRouter);
app.use('/bonding/api', stakingRouter);
app.use('/bonding/api', calculateRouter);

// Initialize schedulers
const schedulers = [
  new Scheduler('Base', process.env.BASE_SEPOLIA_URL),
  //  new Scheduler('BSC', process.env.BSC_URL)
];
schedulers.forEach((scheduler) => scheduler.start());

// Initialize monthly rewards scheduler
monthlyRewardsScheduler.start();

// Graceful shutdown handling
const gracefulShutdown = (signal: string) => {
  console.log(`🔄 Received ${signal}. Starting graceful shutdown...`);

  // Stop all schedulers
  schedulers.forEach((scheduler) => {
    if (scheduler.isRunning()) {
      scheduler.stop();
    }
  });

  // Stop monthly rewards scheduler
  if (monthlyRewardsScheduler.isRunning()) {
    monthlyRewardsScheduler.stop();
  }

  // Close database connection
  closeDB()
    .then(() => {
      // Close Sentry
      sentryService.close(2000).finally(() => {
        console.log('✅ Graceful shutdown completed');
        process.exit(0);
      });
    })
    .catch((error) => {
      console.error('❌ Error during graceful shutdown:', error);
      process.exit(1);
    });
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions to prevent memory leaks
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  sentryService.captureError(error);
  gracefulShutdown('uncaughtException');
});

// eslint-disable-next-line @typescript-eslint/no-unused-vars
process.on('unhandledRejection', (reason, promise) => {
  // console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  sentryService.captureError(new Error(`Unhandled Rejection: ${reason}`));
});

startBondingGrpcServer();

// Connect to MongoDB
connectDB()
  .then(() => {
    app.listen(PORT, () => {
      console.log(`✅ Server is running on port ${PORT}`);
    });
  })
  .catch((error) => {
    console.error('❌ Failed to connect to MongoDB:', error);
    process.exit(1);
  });
