import { Request, Response } from 'express';
import { failResponse, successResponse } from '../common/response.handler';
import { ErrorHandler } from '../helpers/sentry.helper';
import { getStakingTransactions, getUserStakingSummary, getUserStakedOfferings } from '../services/staking.service';

/**
 * Controller to get staking transactions for a specific token
 */
export const getStakingTransactionsController = async (req: Request, res: Response) => {
  try {
    const { token } = req.params;
    console.log('token', token);
    const limit = !isNaN(Number(req.query.limit)) ? Number(req.query.limit) : 10;
    const page = !isNaN(Number(req.query.page)) ? Number(req.query.page) : 1;
    const search = req.query.search?.toString() || '';
    const userAddress = req.query.userAddress?.toString();
    const type = req.query?.type?.toString();

    const result = await getStakingTransactions(token, {
      page,
      limit,
      search,
      userAddress,
      type,
    });

    if (!result.error) {
      return successResponse(result.message, result.data, res);
    } else {
      throw new Error(result.message);
    }
  } catch (error: any) {
    ErrorHandler.handleError(error, {
      context: 'getStakingTransactionsController',
      params: req.params,
      query: req.query,
    });
    return failResponse(true, error?.message, res);
  }
};

/**
 * Controller to get user staking summary for a specific token
 */
export const getUserStakingSummaryController = async (req: Request, res: Response) => {
  try {
    const { token } = req.params;
    const limit = !isNaN(Number(req.query.limit)) ? Number(req.query.limit) : 10;
    const page = !isNaN(Number(req.query.page)) ? Number(req.query.page) : 1;
    const search = req.query.search?.toString() || '';

    const result = await getUserStakingSummary(token, {
      page,
      limit,
      search,
    });

    if (!result.error) {
      return successResponse(result.message, result.data, res);
    } else {
      throw new Error(result.message);
    }
  } catch (error: any) {
    ErrorHandler.handleError(error, {
      context: 'getUserStakingSummaryController',
      params: req.params,
      query: req.query,
    });
    return failResponse(true, error?.message, res);
  }
};

/**
 * Controller to get all unique offerings where user has staked
 */
export const getUserStakedOfferingsController = async (req: Request, res: Response) => {
  try {
    const { userAddress } = req.params;
    const limit = !isNaN(Number(req.query.limit)) ? Number(req.query.limit) : 10;
    const page = !isNaN(Number(req.query.page)) ? Number(req.query.page) : 1;
    const search = req.query.search?.toString() || '';

    // Validate userAddress
    if (!userAddress || !userAddress.match(/^0x[a-fA-F0-9]{40}$/)) {
      return failResponse(true, 'Invalid user address format', res);
    }

    const result = await getUserStakedOfferings(userAddress, {
      page,
      limit,
      search,
    });

    if (!result.error) {
      return successResponse(result.message, result.data, res);
    } else {
      throw new Error(result.message);
    }
  } catch (error: any) {
    ErrorHandler.handleError(error, {
      context: 'getUserStakedOfferingsController',
      params: req.params,
      query: req.query,
    });
    return failResponse(true, error?.message, res);
  }
};
