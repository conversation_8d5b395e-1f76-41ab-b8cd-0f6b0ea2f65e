{"name": "bondingdex", "version": "1.0.0", "description": "Manage bonding curve DEX operations, asset , and staking for the Libertum platform.", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc && npm run copy-proto --skipLibCheck", "copy-proto": "cp -r ./src/_grpc/proto dist/_grpc/proto", "start": "node dist/index.js", "dev": "npm run format && nodemon src/index.ts", "format": "npx prettier --write . './src/**/*.{ts,tsx,js,jsx,json,ejs}' && npx eslint --fix './src/**/*.{ts,tsx,js}'", "sonar": "dotenv -e .env -- sh -c 'sonar-scanner -Dsonar.host.url=\"$SONAR_HOST\" -Dsonar.token=\"$SONAR_TOKEN\"'"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@eslint/js": "^9.20.0", "@types/cors": "^2.8.17", "@types/dotenv": "^8.2.3", "@types/express": "^5.0.1", "@types/mongoose": "^5.11.97", "@types/node": "^22.13.10", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@typescript-eslint/eslint-plugin": "^8.25.0", "@typescript-eslint/parser": "^8.25.0", "eslint": "^8.57.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.31.0", "nodemon": "^3.1.9", "sonarqube-scanner": "^4.3.0", "ts-node": "^10.9.2", "typescript": "^5.8.2", "typescript-eslint": "^8.24.0"}, "dependencies": {"@grpc/grpc-js": "^1.13.4", "@grpc/proto-loader": "^0.7.15", "@sentry/node": "^7.44.1", "@sentry/profiling-node": "^1.0.0", "@types/express-validator": "^3.0.2", "axios": "^1.9.0", "big.js": "^7.0.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "dotenv-cli": "^8.0.0", "eslint": "^9.4.0", "ethers": "^6.14.4", "express": "^4.21.2", "helmet": "^8.1.0", "kafkajs": "^2.2.4", "mongoose": "^8.12.1", "node-cron": "^4.0.5", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "web3": "^4.16.0", "zod": "^3.24.2"}}