{"openapi": "3.0.0", "info": {"title": "Libertum Bonding DEX API", "version": "1.0.0", "description": "Comprehensive API documentation for the Libertum Bonding DEX service.\nThis API manages bonding curve DEX operations, asset tokenization, staking,\nand monthly rewards distribution for the Libertum platform.", "contact": {"name": "API Support", "email": "<EMAIL>", "url": "https://libertum.io/support"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}}, "servers": [{"url": "http://localhost:5000", "description": "Development server"}, {"url": "https://api-dev.libertum.io", "description": "Staging server"}, {"url": "https://api.libertum.io", "description": "Production server"}], "security": [{"hmacAuth": []}], "tags": [{"name": "Bonding DEX", "description": "Core bonding curve DEX operations for asset tokenization"}, {"name": "Token Data", "description": "Token-related data and analytics endpoints"}, {"name": "Staking", "description": "Staking operations and transaction management"}, {"name": "Calculate", "description": "Calculation and testing utilities"}, {"name": "Monthly Rewards", "description": "Monthly rewards calculation and distribution (Admin)"}], "paths": {"/bonding/api/bondingdex/create": {"post": {"tags": ["Bonding DEX"], "summary": "Create a new bonding DEX offering", "description": "Create a new bonding curve DEX offering with comprehensive project details", "security": [{"hmacAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BondingdexRequest"}, "example": {"userId": "60d5ecb54b24c73d88f11234", "currentStep": 1, "overview": {"title": "Premium Office Building Token", "description": "A tokenized premium office building in Manhattan", "entityName": "Manhattan Properties LLC", "entityType": "LLC", "lineOfBusiness": "Real Estate", "sourceOfFunds": "Private Investment", "location": "New York, NY", "companyDescription": "Leading real estate tokenization company", "logo": "https://example.com/logo.png"}, "projectDetails": {"assetType": "Real Estate", "blockChainType": "Ethereum", "offeringType": "Security Token", "tokenStandard": "ERC-1400", "offeringName": "Manhattan Office Token", "minInvestment": 1000, "maxInvestment": 100000, "assetName": "Office Building A", "tokenTicker": "MOT", "tokenSupply": 1000000, "tokenDecimals": 18, "holdTime": "2024-12-31T23:59:59Z", "maxTokenHolding": 50000, "isTransferAgent": true, "issuerId": "issuer123", "issuerWallet": "******************************************", "isPrivate": false, "projectedYield": 8.5}, "isTokenDeploy": false, "iserc20": false, "isFundDeploy": false, "isActive": true, "isDelete": false, "isFinalSubmission": false, "isBondingDeploy": false, "isNft": false}}}}, "responses": {"200": {"description": "Bonding DEX created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}, "example": {"message": "Bonding DEX created successfully", "data": {"_id": "60d5ecb54b24c73d88f11234", "userId": "60d5ecb54b24c73d88f11234", "status": "IN_PROGRESS"}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/bonding/api/bondingdex-list": {"get": {"tags": ["Bonding DEX"], "summary": "List all bonding DEX offerings", "description": "Retrieve a paginated list of all bonding DEX offerings with optional search", "parameters": [{"name": "limit", "in": "query", "description": "Number of items per page", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "offset", "in": "query", "description": "Page number (1-based)", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "search", "in": "query", "description": "Search term for filtering offerings", "schema": {"type": "string"}}], "responses": {"200": {"description": "List of bonding DEX offerings retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Data fetched successfully"}, "data": {"type": "object", "properties": {"count": {"type": "integer", "example": 25}, "rows": {"type": "array", "items": {"$ref": "#/components/schemas/BondingdexResponse"}}}}}}}}}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/bonding/api/bondingdex": {"get": {"tags": ["Bonding DEX"], "summary": "Get bonding DEX by ID", "description": "Retrieve detailed information about a specific bonding DEX offering", "parameters": [{"name": "id", "in": "query", "required": true, "description": "Bonding DEX unique identifier", "schema": {"type": "string"}}], "responses": {"200": {"description": "Bonding DEX details retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/bonding/api/tokenChart/{tokenAddress}": {"get": {"tags": ["Token Data"], "summary": "Get token chart data", "description": "Retrieve historical price and volume data for token charting", "parameters": [{"name": "tokenAddress", "in": "path", "required": true, "description": "Token contract address", "schema": {"type": "string", "pattern": "^0x[a-fA-F0-9]{40}$", "example": "******************************************"}}, {"name": "period", "in": "query", "description": "Time period for chart data", "schema": {"type": "string", "enum": ["1d", "7d", "1m", "1y", "all"], "default": "1d"}}, {"name": "page", "in": "query", "description": "Page number for pagination", "schema": {"type": "integer", "minimum": 1}}, {"name": "limit", "in": "query", "description": "Number of data points per page", "schema": {"type": "integer", "minimum": 1, "maximum": 1000}}], "responses": {"200": {"description": "Token chart data retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object", "properties": {"chartData": {"type": "array", "items": {"type": "object", "properties": {"timestamp": {"type": "integer", "description": "Unix timestamp"}, "price": {"type": "number", "format": "float"}, "volume": {"type": "number", "format": "float"}}}}, "period": {"type": "string"}, "tokenAddress": {"type": "string"}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/bonding/api/token-holders": {"get": {"tags": ["Token Data"], "summary": "Get token holders", "description": "Retrieve a paginated list of token holders with their balances", "parameters": [{"name": "contractAddress", "in": "query", "required": true, "description": "Token contract address", "schema": {"type": "string", "pattern": "^0x[a-fA-F0-9]{40}$"}}, {"name": "page", "in": "query", "description": "Page number", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Number of holders per page", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}], "responses": {"200": {"description": "Token holders retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object", "properties": {"holders": {"type": "array", "items": {"$ref": "#/components/schemas/TokenHolder"}}, "totalHolders": {"type": "integer"}, "currentPage": {"type": "integer"}, "totalPages": {"type": "integer"}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/bonding/api/staking-transactions/{token}": {"get": {"tags": ["Staking"], "summary": "Get staking transactions", "description": "Retrieve paginated staking transactions for a specific token", "parameters": [{"name": "token", "in": "path", "required": true, "description": "Token contract address", "schema": {"type": "string", "pattern": "^0x[a-fA-F0-9]{40}$"}}, {"name": "page", "in": "query", "description": "Page number for pagination", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Number of transactions per page", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "search", "in": "query", "description": "Search by user address, transaction hash, or action ID", "schema": {"type": "string"}}, {"name": "userAddress", "in": "query", "description": "Filter by specific user address", "schema": {"type": "string", "pattern": "^0x[a-fA-F0-9]{40}$"}}], "responses": {"200": {"description": "Staking transactions retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object", "properties": {"transactions": {"type": "array", "items": {"$ref": "#/components/schemas/StakingTransaction"}}, "currentPage": {"type": "integer"}, "totalPages": {"type": "integer"}, "totalCount": {"type": "integer"}, "nextPage": {"type": "integer", "nullable": true}, "previousPage": {"type": "integer", "nullable": true}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/bonding/api/staking-summary/{token}": {"get": {"tags": ["Staking"], "summary": "Get user staking summary", "description": "Retrieve aggregated staking summary for all users of a specific token", "parameters": [{"name": "token", "in": "path", "required": true, "description": "Token contract address", "schema": {"type": "string", "pattern": "^0x[a-fA-F0-9]{40}$"}}, {"name": "page", "in": "query", "description": "Page number for pagination", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Number of users per page", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "search", "in": "query", "description": "Search by user address", "schema": {"type": "string"}}], "responses": {"200": {"description": "User staking summary retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object", "properties": {"users": {"type": "array", "items": {"$ref": "#/components/schemas/UserStakingSummary"}}, "currentPage": {"type": "integer"}, "totalPages": {"type": "integer"}, "totalCount": {"type": "integer"}, "nextPage": {"type": "integer", "nullable": true}, "previousPage": {"type": "integer", "nullable": true}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/bonding/api/user-staked-offerings/{userAddress}": {"get": {"tags": ["Staking"], "summary": "Get user's staked offerings", "description": "Retrieve all unique offerings where a specific user has staked tokens", "parameters": [{"name": "userAddress", "in": "path", "required": true, "description": "User's Ethereum wallet address", "schema": {"type": "string", "pattern": "^0x[a-fA-F0-9]{40}$"}}, {"name": "page", "in": "query", "description": "Page number for pagination", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Number of offerings per page", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}, {"name": "search", "in": "query", "description": "Search by offering title, name, or ticker", "schema": {"type": "string"}}], "responses": {"200": {"description": "User's staked offerings retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object", "properties": {"offerings": {"type": "array", "items": {"$ref": "#/components/schemas/UserOfferingStake"}}, "currentPage": {"type": "integer"}, "totalPages": {"type": "integer"}, "totalCount": {"type": "integer"}, "nextPage": {"type": "integer", "nullable": true}, "previousPage": {"type": "integer", "nullable": true}}}}}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/bonding/api/calculate": {"get": {"tags": ["Calculate"], "summary": "Execute calculation function", "description": "Trigger monthly calculation process for testing purposes", "responses": {"200": {"description": "Calculate function executed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Calculate function executed successfully"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/bonding/api/test-monthly-calculation": {"post": {"tags": ["Calculate"], "summary": "Test monthly calculation", "description": "Test the monthly calculation logic with specific parameters", "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "properties": {"targetMonth": {"type": "string", "pattern": "^[0-9]{4}-[0-9]{2}$", "example": "2025-01", "description": "Month to calculate in YYYY-MM format"}}}}}}, "responses": {"200": {"description": "Monthly calculation test completed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "description": {"type": "string"}, "month": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"securitySchemes": {"hmacAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "x-hmac-signature", "description": "HMAC signature for API authentication"}}, "schemas": {"SuccessResponse": {"type": "object", "properties": {"message": {"type": "string", "description": "Success message"}, "data": {"type": "object", "description": "Response data", "nullable": true}}}, "ErrorResponse": {"type": "object", "properties": {"message": {"type": "string", "description": "Error message"}, "data": {"type": "object", "nullable": true}, "error": {"type": "boolean", "default": true}}}, "BondingdexRequest": {"type": "object", "required": ["userId", "currentStep", "overview", "projectDetails", "isTokenDeploy", "iserc20", "isFundDeploy", "isActive", "isDelete", "isFinalSubmission", "isBondingDeploy", "isNft"], "properties": {"userId": {"type": "string", "description": "User ID who created the offering"}, "currentStep": {"type": "integer", "minimum": 1, "description": "Current step in the creation process"}, "overview": {"$ref": "#/components/schemas/Overview"}, "projectDetails": {"$ref": "#/components/schemas/ProjectDetails"}, "documents": {"$ref": "#/components/schemas/Documents"}, "team": {"type": "array", "items": {"$ref": "#/components/schemas/TeamMember"}}, "fee": {"$ref": "#/components/schemas/Fee"}, "isTokenDeploy": {"type": "boolean"}, "iserc20": {"type": "boolean"}, "isFundDeploy": {"type": "boolean"}, "isActive": {"type": "boolean"}, "isDelete": {"type": "boolean"}, "isFinalSubmission": {"type": "boolean"}, "isBondingDeploy": {"type": "boolean"}, "isNft": {"type": "boolean"}, "tokenAddress": {"type": "string", "pattern": "^0x[a-fA-F0-9]{40}$"}, "bondingAddress": {"type": "string", "pattern": "^0x[a-fA-F0-9]{40}$"}}}, "BondingdexResponse": {"allOf": [{"$ref": "#/components/schemas/BondingdexRequest"}, {"type": "object", "properties": {"_id": {"type": "string", "description": "Unique identifier"}, "status": {"type": "string", "enum": ["IN_PROGRESS", "PENDING", "APPROVED", "REJECTED"]}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "totalHolders": {"type": "integer", "description": "Total number of token holders"}}}]}, "Overview": {"type": "object", "required": ["title", "description", "entityName", "entityType", "lineOfBusiness", "sourceOfFunds", "location", "companyDescription", "logo"], "properties": {"title": {"type": "string", "maxLength": 255}, "subTitle": {"type": "string", "maxLength": 255}, "description": {"type": "string", "maxLength": 2000}, "entityName": {"type": "string", "maxLength": 255}, "entityType": {"type": "string", "maxLength": 100}, "webUrl": {"type": "string", "format": "uri"}, "lineOfBusiness": {"type": "string", "maxLength": 255}, "sourceOfFunds": {"type": "string", "maxLength": 255}, "location": {"type": "string", "maxLength": 255}, "companyDescription": {"type": "string", "maxLength": 2000}, "icon": {"type": "string", "format": "uri"}, "cover": {"type": "string", "format": "uri"}, "logo": {"type": "string", "format": "uri"}, "propertyImages": {"type": "array", "items": {"type": "string", "format": "uri"}}}}, "ProjectDetails": {"type": "object", "required": ["assetType", "blockChainType", "offeringType", "tokenStandard", "offeringName", "minInvestment", "maxInvestment", "assetName", "tokenSupply", "tokenDecimals", "holdTime", "maxTokenHolding", "isTransferAgent", "issuerId", "issuerWallet", "isPrivate", "projectedYield"], "properties": {"assetType": {"type": "string"}, "blockChainType": {"type": "string"}, "offeringType": {"type": "string"}, "tokenStandard": {"type": "string"}, "offeringName": {"type": "string"}, "CUSIP": {"type": "string"}, "isAuthorized": {"type": "boolean"}, "authorizedCountries": {"type": "array", "items": {"$ref": "#/components/schemas/AuthorizedCountry"}}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "minInvestment": {"type": "number", "minimum": 0}, "maxInvestment": {"type": "number", "minimum": 0}, "assetName": {"type": "string"}, "tokenTicker": {"type": "string", "maxLength": 10}, "tokenSupply": {"type": "number", "minimum": 1}, "tokenDecimals": {"type": "integer", "minimum": 0, "maximum": 18}, "lockupMonths": {"type": "integer", "minimum": 0}, "holdTime": {"type": "string", "format": "date-time"}, "maxTokenHolding": {"type": "number", "minimum": 0}, "navLaunchPrice": {"type": "number", "minimum": 0}, "latestNav": {"type": "number", "minimum": 0}, "isTransferAgent": {"type": "boolean"}, "taId": {"type": "string"}, "issuerId": {"type": "string"}, "issuerWallet": {"type": "string", "pattern": "^0x[a-fA-F0-9]{40}$"}, "isPrivate": {"type": "boolean"}, "offeringMembers": {"type": "array", "items": {"type": "string"}}, "customFields": {"type": "array", "items": {"$ref": "#/components/schemas/CustomField"}}, "propertyType": {"type": "string"}, "propertySubtype": {"type": "string"}, "yearBuilt": {"type": "integer", "minimum": 1, "maximum": 9999}, "lotSize": {"type": "number", "minimum": 1}, "occupancy": {"type": "number", "minimum": 0, "maximum": 100}, "projectedYield": {"type": "number", "minimum": 0, "maximum": 100}, "launchValuation": {"type": "number", "minimum": 0}, "previousValuation": {"type": "number", "minimum": 0}, "deRatio": {"type": "number", "minimum": 0}, "acquisitionCosts": {"$ref": "#/components/schemas/AcquisitionCosts"}, "agentName": {"type": "string"}, "agentFunctions": {"type": "array", "items": {"type": "string"}}, "poweredBy": {"type": "string"}, "poweredByLogo": {"type": "string", "format": "uri"}, "bondingPrice": {"type": "number", "minimum": 0}}}, "Documents": {"type": "object", "properties": {"eSign": {"type": "string", "format": "uri"}, "pitchDeck": {"type": "string", "format": "uri"}, "confidentialInformationMemorendum": {"type": "string", "format": "uri"}, "landRegistration": {"type": "string", "format": "uri"}, "titleDocs": {"type": "string", "format": "uri"}, "bankApproval": {"type": "string", "format": "uri"}, "encumbranceCertificate": {"type": "string", "format": "uri"}, "propertyTaxReceipt": {"type": "string", "format": "uri"}, "articlesOfAssociation": {"type": "string", "format": "uri"}, "operatingAgreement": {"type": "string", "format": "uri"}, "taxAssignmentLetter": {"type": "string", "format": "uri"}, "certificateOfRegistration": {"type": "string", "format": "uri"}, "registerOfManagers": {"type": "string", "format": "uri"}, "customDocs": {"type": "array", "items": {"$ref": "#/components/schemas/CustomDocument"}}}}, "AuthorizedCountry": {"type": "object", "properties": {"name": {"type": "string"}, "isoCode": {"type": "string", "minLength": 2, "maxLength": 2}, "countryCode": {"type": "string"}}}, "CustomField": {"type": "object", "properties": {"label": {"type": "string"}, "type": {"type": "string"}, "value": {"type": "string"}}}, "CustomDocument": {"type": "object", "properties": {"docsLabel": {"type": "string"}, "value": {"type": "string", "format": "uri"}}}, "TeamMember": {"type": "object", "required": ["name", "title"], "properties": {"name": {"type": "string"}, "title": {"type": "string"}, "summary": {"type": "string"}, "email": {"type": "string", "format": "email"}, "url": {"type": "string", "format": "uri"}, "linkedInUrl": {"type": "string", "format": "uri"}, "twitterUrl": {"type": "string", "format": "uri"}}}, "AcquisitionCosts": {"type": "object", "properties": {"legalCost": {"type": "number", "minimum": 0}, "estateAgent": {"type": "string"}, "stampDuty": {"type": "string"}, "tax": {"type": "string"}, "platformFees": {"type": "number", "minimum": 0}, "maintenance": {"type": "string"}, "total": {"type": "number", "minimum": 0}}}, "Fee": {"type": "object", "properties": {"escrowFee": {"type": "number", "minimum": 0}, "wrapFee": {"type": "number", "minimum": 0}, "dividendFee": {"type": "number", "minimum": 0}, "redemptionFee": {"type": "number", "minimum": 0}}}, "TokenHolder": {"type": "object", "properties": {"address": {"type": "string", "pattern": "^0x[a-fA-F0-9]{40}$"}, "balance": {"type": "string", "description": "Token balance as string to avoid precision issues"}, "percentage": {"type": "number", "format": "float", "description": "Percentage of total supply held"}}}, "StakingTransaction": {"type": "object", "properties": {"type": {"type": "string", "enum": ["TokenStaked", "TokenUnstaked"]}, "token": {"type": "string", "pattern": "^0x[a-fA-F0-9]{40}$"}, "userAddress": {"type": "string", "pattern": "^0x[a-fA-F0-9]{40}$"}, "stakedAmount": {"type": "string", "description": "Amount staked (formatted with decimals)"}, "unstakedAmount": {"type": "string", "description": "Amount unstaked (formatted with decimals)"}, "userTotalStaked": {"type": "string", "description": "User's total staked amount after transaction"}, "totalTokenStaked": {"type": "string", "description": "Total amount staked for this token"}, "timestamp": {"type": "integer", "description": "Unix timestamp"}, "actionID": {"type": "string", "description": "Unique action identifier"}, "transactionHash": {"type": "string", "pattern": "^0x[a-fA-F0-9]{64}$"}}}, "UserStakingSummary": {"type": "object", "properties": {"userAddress": {"type": "string", "pattern": "^0x[a-fA-F0-9]{40}$"}, "lastActivity": {"type": "string", "enum": ["Stake", "Unstake"]}, "lastActivityTime": {"type": "integer", "description": "Unix timestamp"}, "totalStakedAmount": {"type": "string", "description": "Net staked amount (staked - unstaked)"}, "averageAmount": {"type": "string", "description": "Average stake amount per transaction"}}}, "UserOfferingStake": {"type": "object", "properties": {"token": {"type": "string", "pattern": "^0x[a-fA-F0-9]{40}$"}, "offeringName": {"type": "string"}, "tokenTicker": {"type": "string"}, "totalStakedAmount": {"type": "string", "description": "Total amount staked by user"}, "totalUnstakedAmount": {"type": "string", "description": "Total amount unstaked by user"}, "netStakedAmount": {"type": "string", "description": "Net amount currently staked"}, "lastActivityTime": {"type": "integer", "description": "Unix timestamp of last activity"}, "transactionCount": {"type": "integer", "description": "Total number of staking transactions"}, "overview": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "logo": {"type": "string", "format": "uri"}, "icon": {"type": "string", "format": "uri"}}}}}}, "responses": {"BadRequest": {"description": "Bad request - Invalid parameters or validation errors", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"message": "Invalid request parameters", "data": null, "error": true}}}}, "Unauthorized": {"description": "Unauthorized - Missing or invalid authentication", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"message": "Missing HMAC signature", "data": null, "error": true}}}}, "Forbidden": {"description": "Forbidden - Invalid HMAC signature", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"message": "Invalid HMAC signature", "data": null, "error": true}}}}, "NotFound": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"message": "Resource not found", "data": null, "error": true}}}}, "InternalServerError": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"message": "Internal server error", "data": null, "error": true}}}}}}}