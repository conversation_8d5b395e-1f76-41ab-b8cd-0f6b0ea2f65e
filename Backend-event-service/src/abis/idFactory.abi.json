[{"inputs": [{"internalType": "address", "name": "implementationAuthority", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_agent", "type": "address"}], "name": "AgentAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_agent", "type": "address"}], "name": "AgentRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_addr", "type": "address"}], "name": "Deployed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "wallet", "type": "address"}, {"indexed": false, "internalType": "address", "name": "onchainID", "type": "address"}, {"indexed": false, "internalType": "string", "name": "salt", "type": "string"}], "name": "IDCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "factory", "type": "address"}], "name": "TokenFactoryAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "factory", "type": "address"}], "name": "TokenFactoryRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": true, "internalType": "address", "name": "identity", "type": "address"}], "name": "TokenLinked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "wallet", "type": "address"}, {"indexed": true, "internalType": "address", "name": "identity", "type": "address"}], "name": "WalletLinked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "wallet", "type": "address"}, {"indexed": true, "internalType": "address", "name": "identity", "type": "address"}], "name": "WalletUnlinked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_agent", "type": "address"}], "name": "taAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_agent", "type": "address"}], "name": "taRemoved", "type": "event"}, {"inputs": [{"internalType": "address", "name": "_agent", "type": "address"}], "name": "addAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_agent", "type": "address"}], "name": "addTA", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_factory", "type": "address"}], "name": "addTokenFactory", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_wallet", "type": "address"}, {"internalType": "string", "name": "_salt", "type": "string"}], "name": "createIdentity", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_wallet", "type": "address"}, {"internalType": "string", "name": "_salt", "type": "string"}, {"internalType": "bytes32[]", "name": "_managementKeys", "type": "bytes32[]"}], "name": "createIdentityWithManagementKeys", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "address", "name": "_tokenOwner", "type": "address"}, {"internalType": "string", "name": "_salt", "type": "string"}], "name": "createTokenIdentity", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_wallet", "type": "address"}], "name": "getIdentity", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_identity", "type": "address"}], "name": "getToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_identity", "type": "address"}], "name": "getWallets", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_agent", "type": "address"}], "name": "isAgent", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_salt", "type": "string"}], "name": "isSaltTaken", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_agent", "type": "address"}], "name": "isTA", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_factory", "type": "address"}], "name": "isTokenFactory", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_newWallet", "type": "address"}], "name": "linkWallet", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_agent", "type": "address"}], "name": "removeAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_agent", "type": "address"}], "name": "removeTA", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_factory", "type": "address"}], "name": "removeTokenFactory", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_old<PERSON><PERSON><PERSON>", "type": "address"}], "name": "unlinkWallet", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]