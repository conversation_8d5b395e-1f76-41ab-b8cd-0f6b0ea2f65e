{"IDCreated": {"type": "user", "wallet": "wallet", "onchainID": "onchainID", "_id": "salt", "kycStatus": "APPROVED", "isKyc": true}, "TREXSuiteDeployed": {"type": "offering", "tokenAddress": "_token", "identityRegistry": "_ir", "_irs": "_irs", "_tir": "_tir", "_ctr": "_ctr", "_mc": "_mc", "_id": "_salt"}, "Purchase": {"type": "offering", "purchaser": "purchaser", "stableCoin": "stableCoin", "collection": "collection", "isSold": true, "uri": "uri", "price": "price", "txHash": "transactionHash", "_id": "actionID"}, "BondingTokenCreated": {"type": "offering", "bondingAddress": "bondingToken", "owner": "owner", "name": "name", "symbol": "symbol", "transactionHash": "transactionHash", "event": "event", "_id": "mappingValue", "status": "APPROVED", "isBondingDeployed": true, "deployedDate": "deploymentDate"}, "CollectionCreated": {"type": "NftCollection", "collection": "collection", "txHash": "transactionHash", "event": "event", "isActive": true, "isDeploy": true, "status": "APPROVED", "_id": "mappingValue", "stableCoin": "stableCoin_"}, "EquityConfigCreated": {"type": "offering", "fundAddress": "_EquityConfigProxy", "_id": "mappingValue", "deployedDate": "deploymentDate"}, "OrderCreated": {"type": "order", "_id": "orderID", "id": "id", "event": "event", "txHash": "transactionHash", "orderReceived": "deploymentDate"}, "ForceTransferred": {"type": "ForceTransferred", "fromAddress": "fromAddress", "toAddress": "to<PERSON><PERSON><PERSON>", "amount": "amount", "event": "event", "_id": "orderID", "tokenAddress": "token", "txHash": "transactionHash"}, "DividendDistributed": {"type": "DividendDistributed", "dividendId": "_dividendID", "status": "SUCCESS", "userAddress": "_userID", "amount": "amount", "event": "event", "transactionHash": "transactionHash", "netAmount": "netAmount", "adminFeeAmount": "adminFeeAmount"}, "OrderSettled": {"type": "order", "_id": "orderID", "id": "id", "event": "event", "mintTxHash": "transactionHash", "orderMinted": "deploymentDate"}, "OrderCancelled": {"type": "order", "_id": "orderID", "id": "id", "event": "event", "txHash": "transactionHash", "status": "CANCELLED"}, "UserTokensFrozen": {"_id": "offeringId", "type": "issuerAction", "fromAddress": "fromAddress", "amount": "amount", "event": "event", "txHash": "transactionHash"}, "UserTokensUnFrozen": {"_id": "offeringId", "type": "issuerAction", "fromAddress": "fromAddress", "amount": "amount", "event": "event", "txHash": "transactionHash"}, "RedemptionAndBurn": {"type": "offeringNav", "_id": "actionID", "txHash": "transactionHash", "newValuation": "newValuation", "event": "ValuationUpdated"}, "NAVUpdated": {"type": "offeringNav", "_id": "actionID", "txHash": "transactionHash", "newValuation": "latestNAV", "event": "NAVUpdated"}, "ValuationUpdated": {"type": "offeringNav", "_id": "actionID", "txHash": "transactionHash", "newValuation": "latestValuation", "event": "ValuationUpdated"}, "WrapTokenCreated": {"type": "offering", "erc20Address": "_erc20", "tokenAddress": "_erc3643", "event": "event", "iserc20": true, "txHash": "transactionHash", "wrapperDeployedAt": "wrapperDeployedAt"}, "TokenUnlocked": {"type": "issuerAction", "erc20Address": "_erc20", "tokenAddress": "_erc3643", "event": "event", "paymentMethod": "ERC20", "txHash": "transactionHash", "fromAddress": "fromAddress", "amount": "amount"}, "TokenLocked": {"type": "issuerAction", "erc20Address": "_erc20", "tokenAddress": "_erc3643", "event": "event", "paymentMethod": "ERC3643", "txHash": "transactionHash", "fromAddress": "fromAddress", "amount": "amount"}, "UserIdentityRegistered": {"type": "whitelist", "address": "userAddress", "OfferingAddress": "OfferingAddress", "transactionHash": "transactionHash", "_id": "userID", "txHash": "transactionHash"}, "FundCreated": {"type": "offering", "fundAddress": "_FundProxy", "_id": "mappingValue", "deployedDate": "deploymentDate"}, "AdminFeeUpdated": {"type": "fee", "walletAddress": "newAdminWallet", "fee": {"escrowFee": "escrowFee", "wrapFee": "wrapFee", "dividendFee": "dividendFee", "redemptionFee": "redemptionFee"}, "timeStamp": "timeStamp", "_id": "id", "event": "event", "txHash": "transactionHash"}, "AdminWalletUpdated": {"type": "fee", "walletAddress": "newAdminWallet", "fee": {"escrowFee": "escrowFee", "wrapFee": "wrapFee", "dividendFee": "dividendFee", "redemptionFee": "redemptionFee"}, "timeStamp": "timeStamp", "_id": "id", "event": "event", "txHash": "transactionHash"}, "AgentAdded": {"type": "user", "wallet": "_agent", "onchainID": "_agent", "kycStatus": "APPROVED", "event": "event", "isKyc": true}}