import IdFactoryAbi from '../abis/idFactory.abi.json';
import TREXFactoryAbi from '../abis/TREXFactory.abi.json';
import FundFactoryAbi from '../abis/fundFactory.abi.json';
import EscrowFactoryAbi from '../abis/escrowController.abi.json';
import NFTFactoryAbi from '../abis/NFTFactory.abi.json';
import BondingFactoryAbi from '../abis/bondingFactory.abi.json';
import MarketplaceProxyAbi from '../abis/marketplaceProxy.abi.json';
import { ContractAbi } from 'web3';

export interface IContract {
  name: string;
  address: string;
  events: string[];
  startBlock: number;
  abi: ContractAbi;
  chain: string;
}

// Combined contracts configuration for all chains
export const contractsConfig: IContract[] = [
  // Base chain contracts
  {
    name: 'IDFACTORY',
    address: process.env.IDFACTORY, //IdFactory
    events: ['IDCreated', 'AgentAdded'],
    startBlock: Number(process.env.START_BLOCK) || 0,
    abi: IdFactoryAbi,
    chain: 'Base',
  },
  {
    name: 'TREXFACTORY',
    address: process.env.TREXFACTORY, //TREX Factory
    events: ['TREXSuiteDeployed'],
    startBlock: Number(process.env.START_BLOCK) || 0,
    abi: TREXFactoryAbi,
    chain: 'Base',
  },
  {
    name: 'FUNDFACTORYPROXY',
    address: process.env.FUNDFACTORYPROXY, // Fund Proxy
    events: ['EquityConfigCreated', 'FundCreated', 'AdminFeeUpdated', 'AdminWalletUpdated'],
    startBlock: Number(process.env.START_BLOCK) || 0,
    abi: FundFactoryAbi,
    chain: 'Base',
  },
  {
    name: 'ESCROWCONTROLLERPROXY',
    address: process.env.ESCROWCONTROLLERPROXY, // EscrowControllerProxy
    events: ['OrderCreated', 'OrderSettled', 'UserTokensFrozen', 'UserTokensUnFrozen', 'ForceTransferred', 'DividendDistributed', 'RedemptionAndBurn', 'ValuationUpdated', 'NAVUpdated', 'OrderCancelled', 'UserIdentityRegistered'],
    startBlock: Number(process.env.START_BLOCK) || 0,
    abi: EscrowFactoryAbi,
    chain: 'Base',
  },
  {
    name: 'NFTFACTORYPROXY',
    address: process.env.NFTFACTORYPROXY, //   NFT Factory Proxy:
    events: ['CollectionCreated'],
    startBlock: Number(process.env.START_BLOCK) || 0,
    abi: NFTFactoryAbi,
    chain: 'Base',
  },
  {
    name: 'BONDINGFACTORYPROXY',
    address: process.env.BONDINGFACTORYPROXY, //Bonding Factory Proxy
    events: ['BondingTokenCreated'],
    startBlock: Number(process.env.START_BLOCK) || 0,
    abi: BondingFactoryAbi,
    chain: 'Base',
  },
  {
    name: 'NFTMARKETPLACEPROXY',
    address: process.env.NFTMARKETPLACEPROXY, //Market Place Proxy
    events: ['Purchase'],
    startBlock: Number(process.env.START_BLOCK) || 0,
    abi: MarketplaceProxyAbi,
    chain: 'Base',
  },
  // BSC chain contracts
  {
    name: 'BONDINGFACTORYPROXY',
    address: process.env.BSC_BONDING_FACTORY_PROXY, //Bonding Factory Proxy
    events: ['BondingTokenCreated'],
    startBlock: Number(process.env.BSC_START_BLOCK) || 0,
    abi: BondingFactoryAbi,
    chain: 'BSC',
  },
];

// Function to get contracts by chain
export const getContractsByChain = (chain: string): IContract[] => {
  const chainContracts = contractsConfig.filter((contract) => contract.chain === chain);

  if (chainContracts.length === 0) {
    throw new Error(`No contracts found for chain: ${chain}`);
  }

  return chainContracts;
};
