import { ErrorHandler } from './helpers/sentry.helper';
import app from './app';

// Initialize error tracking system
ErrorHandler.init();

// Set global tags for this service
ErrorHandler.setTag('service', 'event-service');
ErrorHandler.setTag('component', 'main');

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  ErrorHandler.logError(error, {
    component: 'Main',
    operation: 'uncaughtException',
    type: 'uncaughtException',
    process: 'main',
  });
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  const error = reason instanceof Error ? reason : new Error(String(reason));
  ErrorHandler.logError(error, {
    component: 'Main',
    operation: 'unhandledRejection',
    type: 'unhandledRejection',
    process: 'main',
    promise: promise.toString(),
  });
  process.exit(1);
});

app.init().catch((err) => {
  ErrorHandler.logError(err, {
    component: 'Main',
    operation: 'appInitialization',
    type: 'appInitialization',
  });
  process.exit(1);
});
