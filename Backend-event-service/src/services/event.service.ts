import { Web3Helper } from '../helpers/web3.helper';
import { IContract } from '../configs/contract.config';
import { config } from '../config';
import BlocksRepo from '../repos/blocks.repo';
import EventFormatterService from './eventFormatter.service';
import { ErrorHandler } from '../helpers/sentry.helper';

export class EventService {
  constructor(private web3Helper: Web3Helper) {}

  public async fetch(chain: string, Contract: IContract, contractInstance: any) {
    try {
      const address = Contract.address;
      //Block batch size  & start and end block calculation begins
      const eventBatchSize: number = config.EVENT_BATCH_SIZE || 1000;
      //getting current block number from blockchain
      const currentBlock = await this.web3Helper.getBlockNumber();
      //getting block info from database
      const lastFetchedBlock = await BlocksRepo.getLastFetchedBlock(chain, address);
      console.log('chain', chain, 'lastFetchedBlock', lastFetchedBlock, 'env start block', Contract.startBlock);
      if (lastFetchedBlock <= currentBlock) { 
        const fromBlock = lastFetchedBlock ? lastFetchedBlock : Contract.startBlock;
        // if start block plus batch size is greater than current block then it will set end block to current block
        const toBlock = fromBlock + eventBatchSize > currentBlock ? currentBlock : fromBlock + eventBatchSize;
        console.log({ fromBlock: fromBlock, toBlock: toBlock });
        // if fromblick is greater than current block then it will return
        if (fromBlock > currentBlock) {
          console.warn('\n ⚠️  fromBlock is greater than currentBlock\n');
          return;
        }
        const event = await this.web3Helper.getPastEvents(contractInstance, fromBlock, toBlock);
        // console.log(event);
        //format data and send in queue
        EventFormatterService.processFormattedEvents(event, this.web3Helper);
        await BlocksRepo.upsertBlockNumber({
          chain: chain,
          address: Contract.address,
          contractName: Contract.name,
          blockNumber: toBlock + 1,
        });
        console.log('===============================');
      } else {
        console.log('waiting for blockNumber to update!!!');
      }
    } catch (error) {
      console.error('❌ Failed to fetch events in Event Service:', error);
      ErrorHandler.logError(error as Error, {
        component: 'EventService',
        operation: 'fetch',
        chain,
        contractName: Contract.name,
        contractAddress: Contract.address,
      });
      throw error;
    }
  }
}
