import eventFormats from '../configs/eventFormats.json';
import kafkaHelper from '../helpers/kafka.helper';
import { Web3Helper } from '../helpers/web3.helper';
import { calculate } from '../helpers/CalculateBigNumber.helper';
import { ErrorHandler } from '../helpers/sentry.helper';
class EventFormatterService {
  private getTransactionTimestamp = async (blockNumber: number, web3Instance: Web3Helper) => {
    try {
      const block = await web3Instance.getBlock(blockNumber);
      return Number(block.timestamp);
    } catch (error) {
      ErrorHandler.logError(error as Error, {
        component: 'EventFormatterService',
        operation: 'getTransactionTimestamp',
        blockNumber,
      });
      throw error;
    }
  };
  public processFormattedEvents = async (events, web3Instance): Promise<void> => {
    try {
      for (const event of events) {
        // console.log(event);
        const formatTemplate = (eventFormats as any)[event.event];
        if (!formatTemplate) continue;
        const formatted: Record<string, any> = {};
        for (const [key, value] of Object.entries(formatTemplate)) {
          if (value === 'event') {
            formatted[key] = event.event;
          } else if (value === 'transactionHash') {
            formatted[key] = event.transactionHash;
          } else if (value === 'deploymentDate') {
            // Fetch block timestamp
            const blockNumber = Number(event.blockNumber);
            const timestamp = await this.getTransactionTimestamp(blockNumber, web3Instance);
            formatted[key] = new Date(timestamp * 1000).toISOString(); // or raw timestamp if preferred
          } else if (value === 'timeStamp') {
            formatted[key] = Number(event.returnValues?.[value as string]);
          } else if (key === 'amount' || key === 'adminFeeAmount' || key === 'netAmount' || key === 'price') {
            const rawAmount = event.returnValues?.[value as string];
            if (event.event === 'DividendDistributed' || event.event === 'Purchase') {
              formatted[key] = calculate('div', rawAmount, 10 ** 6);
            } else {
              if (rawAmount) {
                formatted[key] = calculate('div', rawAmount, 10 ** 18);
              }
            }
          } else if ((event.event === 'UserTokensFrozen' || event.event === 'UserTokensUnFrozen') && key == '_id') {
            const orderID = event.returnValues?.orderID;
            if (orderID && typeof orderID === 'string') {
              formatted[key] = orderID.split('/')[0];
            }
          } else if (event.event === 'DividendDistributed' && key === 'dividendId') {
            formatted[key] = (event.returnValues?.[value as string]).split('-')[0];
          } else if ((event.event === 'AdminFeeUpdated' || event.event === 'AdminWalletUpdated') && key === 'fee') {
            formatted[key] = {
              escrowFee: Number(event.returnValues?.escrowFee),
              wrapFee: Number(event.returnValues?.wrapFee),
              dividendFee: Number(event.returnValues?.dividendFee),
              redemptionFee: Number(event.returnValues?.redemptionFee),
            };
          } else if ((event.event === 'NAVUpdated' || event.event === 'ValuationUpdated') && key === 'newValuation') {
            const rawAmount = event.returnValues?.[value as string];
            formatted[key] = calculate('div', rawAmount, 10 ** 18);
          } else {
            formatted[key] = event.returnValues?.[value as string] ?? value;
          }
          // if (event.event === 'ValuationUpdated') {
          //   console.log(event);
          // }
        }
        console.log(`Formatted Event: ${event.event}`, formatted);
        kafkaHelper.emit('cron-to-user', { value: formatted }).catch((err: any) => {});

        if (event.event === 'AdminFeeUpdated' || event.event === 'AdminWalletUpdated' || event.event === 'AgentAdded') {
          kafkaHelper.emit('user-to-admin', { value: formatted }).catch((err: any) => {
            ErrorHandler.logError(err, {
              component: 'EventFormatterService',
              operation: 'emitUserToAdmin',
              eventType: event.event,
              topic: 'user-to-admin',
            });
          });
        }
      }
    } catch (error) {
      ErrorHandler.logError(error as Error, {
        component: 'EventFormatterService',
        operation: 'processFormattedEvents',
        eventsCount: events?.length || 0,
      });
    }
  };
}

export default new EventFormatterService();
