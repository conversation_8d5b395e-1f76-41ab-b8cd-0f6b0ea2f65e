import cron from 'node-cron';
import { Web3Helper } from '../helpers/web3.helper';
import { EventService } from './event.service';
import { getContractsByChain } from '../configs/contract.config';
import { Error<PERSON>and<PERSON> } from '../helpers/sentry.helper';

export class Scheduler {
  private eventService: EventService;
  private chain: string;
  private web3Helper: Web3Helper;
  private cronTask: cron.ScheduledTask | null = null;

  constructor(chain: string, private rpcUrls: string[]) {
    this.chain = chain;
    this.web3Helper = new Web3Helper(this.rpcUrls);
    this.eventService = new EventService(this.web3Helper);
  }

  public start(): void {
    this.cronTask = cron.schedule('*/10 * * * * *', async () => {
      try {
        // Get chain-specific contracts
        const chainContracts = getContractsByChain(this.chain);

        for (const contract of chainContracts) {
          try {
            const abi: any = contract.abi;
            const address = contract.address;

            // Skip if address is not defined for this chain
            if (!address) {
              console.log(`⚠️ Skipping ${contract.name} for ${this.chain} - address not configured`);
              ErrorHandler.addBreadcrumb(`Skipping contract - address not configured`, 'scheduler', {
                contractName: contract.name,
                chain: this.chain,
              });
              continue;
            }

            const contractInstance = this.web3Helper.createContractInstance(abi, address);

            console.log(`🔄 Processing ${contract.name} on ${this.chain} chain`);
            await this.eventService.fetch(this.chain, contract, contractInstance);
          } catch (error) {
            console.error(`❌ Failed to fetch events for ${contract.name} on ${this.chain}:`, error);
            ErrorHandler.logError(error as Error, {
              component: 'Scheduler',
              operation: 'processContract',
              chain: this.chain,
              contractName: contract.name,
              contractAddress: contract.address,
            });
          }
        }
      } catch (error) {
        console.error(`❌ Critical error in scheduler for ${this.chain}:`, error);
        ErrorHandler.logError(error as Error, {
          component: 'Scheduler',
          operation: 'cronJob',
          chain: this.chain,
          critical: true,
        });
      }
    });
  }

  public stop(): void {
    if (this.cronTask) {
      this.cronTask.stop();
      this.cronTask = null;
      console.log(`🛑 Stopped scheduler for ${this.chain} chain`);
    }
  }

  public async cleanup(): Promise<void> {
    try {
      // Ensure cron task is stopped and destroyed
      this.stop();

      // Clear references to allow garbage collection
      this.web3Helper = null;
      this.eventService = null;
      
      console.log(`🧹 Cleaned up resources for ${this.chain} chain`);
    } catch (error) {
      console.error(`❌ Error cleaning up ${this.chain} scheduler:`, error);
      ErrorHandler.logError(error as Error, {
        component: 'Scheduler',
        operation: 'cleanup',
        chain: this.chain,
      });
    }
  }
}
