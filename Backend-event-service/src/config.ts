import * as dotenv from 'dotenv';
dotenv.config();

interface Kafka {
  clientId: string;
  brokers: [string];
  groupId: string;
}

interface Config {
  BASE_SEPOLIA_URL: string[];
  BSC_RPC: string[];
  MONGO_URI: string;
  KAFKA: Kafka;
  EVENT_BATCH_SIZE: number;
  SENTRY_DSN?: string;
  SENTRY_ENVIRONMENT?: string;
  SENTRY_RELEASE?: string;
}

export const config: Config = {
  BASE_SEPOLIA_URL: process.env.BASE_SEPOLIA_URL?.split(',').map((url) => url.trim()) || [],
  BSC_RPC: process.env.BSC_RPC?.split(',').map((url) => url.trim()) || [],
  MONGO_URI: process.env.MONGO_URI,
  KAFKA: {
    clientId: process.env.KAFKA_CLIENT_ID,
    brokers: [process.env.KAFKA_BROKER],
    groupId: process.env.KAFKA_GROUP_ID,
  },
  EVENT_BATCH_SIZE: Number(process.env.EVENT_BATCH_SIZE) || 1000,
  SENTRY_DSN: process.env.SENTRY_DSN,
  SENTRY_ENVIRONMENT: process.env.SENTRY_ENVIRONMENT || 'development',
  SENTRY_RELEASE: process.env.SENTRY_RELEASE || '1.0.0',
};
