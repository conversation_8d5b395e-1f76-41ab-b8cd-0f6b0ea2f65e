import MongoService from './helpers/mongo.helper';
import KafkaService from './helpers/kafka.helper';
import { Scheduler } from './services/scheduler';
import { config } from './config';
import { ErrorHandler } from './helpers/sentry.helper';

class App {
  private schedulers: Array<{ start: () => void; stop: () => void; cleanup: () => Promise<void> }>;
  private isShuttingDown: boolean = false;
  private signalHandlers: Map<string, (...args: any[]) => void> = new Map();

  constructor() {
    this.schedulers = [
      new Scheduler('Base', config.BASE_SEPOLIA_URL),
      // new Scheduler('BSC', config.BSC_RPC)
    ];

    // Register signal handlers
    this.registerSignalHandlers();
  }

  private registerSignalHandlers(): void {
    // Create bound handlers to allow removal later
    const sigtermHandler = () => {
      console.log('📥 Received SIGTERM signal');
      this.shutdown();
    };

    const sigintHandler = () => {
      console.log('📥 Received SIGINT signal');
      this.shutdown();
    };

    const uncaughtExceptionHandler = (error: Error) => {
      console.error('❌ Uncaught Exception:', error);
      this.shutdown();
    };

    const unhandledRejectionHandler = (reason: any, promise: Promise<any>) => {
      console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
      this.shutdown();
    };

    // Store handlers for cleanup
    this.signalHandlers.set('SIGTERM', sigtermHandler);
    this.signalHandlers.set('SIGINT', sigintHandler);
    this.signalHandlers.set('uncaughtException', uncaughtExceptionHandler);
    this.signalHandlers.set('unhandledRejection', unhandledRejectionHandler);

    // Register handlers
    process.on('SIGTERM', sigtermHandler);
    process.on('SIGINT', sigintHandler);
    process.on('uncaughtException', uncaughtExceptionHandler);
    process.on('unhandledRejection', unhandledRejectionHandler);
  }

  private removeSignalHandlers(): void {
    // Remove all event listeners to prevent memory leaks
    this.signalHandlers.forEach((handler, event) => {
      process.removeListener(event, handler);
    });
    this.signalHandlers.clear();
  }

  public async init(): Promise<void> {
    await MongoService.connect();
    await KafkaService.connect();
    this.schedulers.forEach((scheduler) => scheduler.start());

    console.log('🚀 Cron service initialized');
  }

  public async shutdown(): Promise<void> {
    // Prevent multiple shutdown attempts
    if (this.isShuttingDown) {
      console.log('⚠️ Shutdown already in progress...');
      return;
    }
    this.isShuttingDown = true;

    try {
      console.log('🛑 Starting graceful shutdown...');

      // Stop all schedulers
      this.schedulers.forEach(scheduler => scheduler.stop());

      // Cleanup Web3 instances
      for (const scheduler of this.schedulers) {
        await scheduler.cleanup();
      }

      // Remove signal handlers to prevent memory leaks
      this.removeSignalHandlers();

      // Cleanup connections
      await MongoService.disconnect();
      await KafkaService.disconnect();
      await ErrorHandler.close();

      console.log('✅ Service shutdown complete');
      process.exit(0);
    } catch (error) {
      console.error('❌ Error during shutdown:', error);
      process.exit(1);
    }
  }
}

export default new App();
