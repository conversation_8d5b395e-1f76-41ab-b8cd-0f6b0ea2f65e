import { Ka<PERSON><PERSON>, Producer, Consumer } from 'kafkajs';
import { config } from '../config';
import { ErrorHandler } from './sentry.helper';

class KafkaService {
  private kafka: Kafka;
  public producer: Producer;
  public consumer: Consumer;

  constructor() {
    this.kafka = new Kafka({
      clientId: config.KAFKA.clientId,
      brokers: config.KAFKA.brokers,
    });

    this.producer = this.kafka.producer();
    this.consumer = this.kafka.consumer({ groupId: config.KAFKA.groupId });
  }

  public async connect(): Promise<void> {
    try {
      await this.producer.connect();
      await this.consumer.connect();
      console.log('✅ Kafka connected');
    } catch (error) {
      console.error('❌ Kafka connection error:', error);
      ErrorHandler.logError(error as Error, {
        component: 'KafkaService',
        operation: 'connect',
        kafkaConfig: {
          clientId: config.KAFKA.clientId,
          brokersCount: config.KAFKA.brokers?.length || 0,
          groupId: config.KAFKA.groupId,
        },
      });
      process.exit(1);
    }
  }

  public async emit(topic: string, message: any): Promise<void> {
    try {
      await this.producer.send({
        topic,
        messages: [
          {
            value: JSON.stringify(message),
          },
        ],
      });
      console.log(`📤 Message emitted to topic "${topic}"`);
    } catch (error) {
      console.error(`❌ Failed to emit message to topic "${topic}":`, error);
      ErrorHandler.logError(error as Error, {
        component: 'KafkaService',
        operation: 'emit',
        topic,
        messageType: typeof message,
        messageKeys: message && typeof message === 'object' ? Object.keys(message) : [],
      });
    }
  }

  public async disconnect(): Promise<void> {
    try {
      await this.producer.disconnect();
      await this.consumer.disconnect();
      console.log('✅ Kafka disconnected');
    } catch (error) {
      console.error('❌ Kafka disconnection error:', error);
      ErrorHandler.logError(error as Error, {
        component: 'KafkaService',
        operation: 'disconnect',
      });
    }
  }
}

export default new KafkaService();
