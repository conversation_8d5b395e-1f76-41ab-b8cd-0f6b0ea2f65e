import mongoose from 'mongoose';
import { config } from '../config';
import { <PERSON>rrorHandler } from './sentry.helper';

class MongoService {
  public async connect(): Promise<void> {
    try {
      await mongoose.connect(config.MONGO_URI);
      console.log('✅ MongoDB connected');
    } catch (error) {
      ErrorHandler.logError(error as Error, {
        component: 'MongoService',
        operation: 'connect',
        mongoUri: config.MONGO_URI ? 'configured' : 'missing',
      });
      process.exit(1);
    }
  }

  public async disconnect(): Promise<void> {
    try {
      await mongoose.disconnect();
      console.log('✅ MongoDB disconnected');
    } catch (error) {
      console.error('❌ MongoDB disconnection error:', error);
      ErrorHandler.logError(error as Error, {
        component: 'MongoService',
        operation: 'disconnect',
      });
    }
  }
}

export default new MongoService();
