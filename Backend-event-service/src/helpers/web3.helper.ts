import Web3 from 'web3';
import { AbiItem } from 'web3-utils';
import { Contract } from 'web3';
import { <PERSON>rror<PERSON>and<PERSON> } from './sentry.helper';

export class Web3Helper {
  private rpcUrls: string[];
  private currentRpcIndex: number;
  private web3: Web3;
  private maxRetries: number;

  constructor(rpcUrls: string[]) {
    if (!rpcUrls || rpcUrls.length === 0) {
      const error = new Error('At least one RPC URL must be provided');
      ErrorHandler.logError(error, {
        component: 'Web3Helper',
        operation: 'constructor',
        rpcUrlsProvided: rpcUrls?.length || 0,
      });
      throw error;
    }
    this.rpcUrls = rpcUrls;
    this.currentRpcIndex = 0;
    this.maxRetries = rpcUrls.length;
    this.web3 = new Web3(this.rpcUrls[this.currentRpcIndex]);
    console.log(`🌐 Initialized Web3Helper with ${rpcUrls.length} RPC URLs`);
  }

  private switchToNextRpc(): boolean {
    this.currentRpcIndex = (this.currentRpcIndex + 1) % this.rpcUrls.length;
    const newRpcUrl = this.rpcUrls[this.currentRpcIndex];
    this.web3 = new Web3(newRpcUrl);
    console.log(`🔄 Switched to RPC: ${newRpcUrl}`);
    return true;
  }

  private async executeWithFailover<T>(operation: () => Promise<T>, operationName: string): Promise<T> {
    let lastError: any;

    for (let attempt = 0; attempt < this.maxRetries; attempt++) {
      try {
        const result = await operation();
        if (attempt > 0) {
          console.log(`✅ ${operationName} succeeded on attempt ${attempt + 1} with RPC: ${this.rpcUrls[this.currentRpcIndex]}`);
        }
        return result;
      } catch (error) {
        lastError = error;
        console.error(`❌ ${operationName} failed on attempt ${attempt + 1} with RPC: ${this.rpcUrls[this.currentRpcIndex]}`, error);

        ErrorHandler.addBreadcrumb(`${operationName} failed on attempt ${attempt + 1}`, 'web3', {
          operation: operationName,
          attempt: attempt + 1,
          currentRpc: this.rpcUrls[this.currentRpcIndex],
          errorMessage: error instanceof Error ? error.message : String(error),
        });

        if (attempt < this.maxRetries - 1) {
          this.switchToNextRpc();
          console.log(`🔄 Retrying ${operationName} with next RPC...`);
        }
      }
    }

    console.error(`💥 All ${this.maxRetries} RPC endpoints failed for ${operationName}`);
    ErrorHandler.logError(lastError, {
      component: 'Web3Helper',
      operation: operationName,
      allRpcsFailed: true,
      rpcUrls: this.rpcUrls,
      maxRetries: this.maxRetries,
    });
    throw lastError;
  }

  public async getBlockNumber(): Promise<number> {
    return this.executeWithFailover(async () => {
      const blockNumber = await this.web3.eth.getBlockNumber();
      console.log(`🔢 Current block number: ${blockNumber}`);
      return Number(blockNumber);
    }, 'getBlockNumber');
  }

  public async getBlock(blockNumber: number): Promise<any> {
    return this.executeWithFailover(async () => {
      const block = await this.web3.eth.getBlock(blockNumber);
      console.log(`🔢 Block Info: ${block}`);
      return block;
    }, 'getBlock');
  }

  public isAddress(address: string): boolean {
    return this.web3.utils.isAddress(address);
  }

  public getCurrentRpcUrl(): string {
    return this.rpcUrls[this.currentRpcIndex];
  }

  public getAllRpcUrls(): string[] {
    return [...this.rpcUrls];
  }

  public createContractInstance<T extends AbiItem[]>(abi: T, address: string): Contract<T> {
    if (!this.isAddress(address)) {
      const error = new Error(`Invalid contract address: ${address}`);
      ErrorHandler.logError(error, {
        component: 'Web3Helper',
        operation: 'createContractInstance',
        invalidAddress: address,
        currentRpc: this.rpcUrls[this.currentRpcIndex],
      });
      throw error;
    }

    try {
      const contract = new this.web3.eth.Contract(abi, address);
      console.log(`✅ Contract instance created at ${address}`);
      return contract;
    } catch (error) {
      ErrorHandler.logError(error as Error, {
        component: 'Web3Helper',
        operation: 'createContractInstance',
        address,
        currentRpc: this.rpcUrls[this.currentRpcIndex],
      });
      throw error;
    }
  }

  public async getPastEvents<T extends AbiItem[]>(contract: Contract<T>, fromBlock: number, toBlock: number) {
    return this.executeWithFailover(async () => {
      const events = await contract.getPastEvents('allEvents', {
        fromBlock,
        toBlock,
      });
      return events;
    }, 'getPastEvents');
  }
}
