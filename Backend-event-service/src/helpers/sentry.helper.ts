import * as Sentry from '@sentry/node';
import { nodeProfilingIntegration } from '@sentry/profiling-node';
import { config } from '../config';

class SentryService {
  private initialized = false;

  public init(): void {
    if (!config.SENTRY_DSN) {
      console.warn('⚠️ Sentry DSN not provided. Error tracking will be disabled.');
      return;
    }

    if (this.initialized) {
      console.warn('⚠️ Sentry already initialized');
      return;
    }

    try {
      Sentry.init({
        dsn: config.SENTRY_DSN,
        environment: config.SENTRY_ENVIRONMENT,
        release: config.SENTRY_RELEASE,
        integrations: [nodeProfilingIntegration()],
        // Performance Monitoring
        tracesSampleRate: 1.0,
        // Profiling
        profilesSampleRate: 1.0,
        // Additional options
        beforeSend(event) {
          // Filter out sensitive information
          if (event.exception) {
            event.exception.values?.forEach((exception) => {
              if (exception.stacktrace?.frames) {
                exception.stacktrace.frames.forEach((frame) => {
                  // Remove sensitive data from stack traces
                  if (frame.vars) {
                    Object.keys(frame.vars).forEach((key) => {
                      if (key.toLowerCase().includes('password') || key.toLowerCase().includes('secret') || key.toLowerCase().includes('token') || key.toLowerCase().includes('key')) {
                        frame.vars![key] = '[Filtered]';
                      }
                    });
                  }
                });
              }
            });
          }
          return event;
        },
      });

      this.initialized = true;
      console.log('✅ Sentry initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Sentry:', error);
    }
  }

  public captureError(error: Error, context?: Record<string, any>): void {
    if (!this.initialized) {
      console.error('❌ Sentry not initialized. Error:', error);
      return;
    }

    try {
      if (context) {
        Sentry.withScope((scope) => {
          // Add context information
          Object.keys(context).forEach((key) => {
            scope.setContext(key, context[key]);
          });
          Sentry.captureException(error);
        });
      } else {
        Sentry.captureException(error);
      }
    } catch (sentryError) {
      console.error('❌ Failed to capture error with Sentry:', sentryError);
      console.error('❌ Original error:', error);
    }
  }

  public captureMessage(message: string, level: 'debug' | 'info' | 'warning' | 'error' | 'fatal' = 'info', context?: Record<string, any>): void {
    if (!this.initialized) {
      console.log(`${level.toUpperCase()}: ${message}`);
      return;
    }

    try {
      if (context) {
        Sentry.withScope((scope) => {
          Object.keys(context).forEach((key) => {
            scope.setContext(key, context[key]);
          });
          Sentry.captureMessage(message, level);
        });
      } else {
        Sentry.captureMessage(message, level);
      }
    } catch (sentryError) {
      console.error('❌ Failed to capture message with Sentry:', sentryError);
      console.log(`${level.toUpperCase()}: ${message}`);
    }
  }

  public setUser(user: { id?: string; email?: string; username?: string; [key: string]: any }): void {
    if (!this.initialized) {
      return;
    }

    try {
      Sentry.setUser(user);
    } catch (error) {
      console.error('❌ Failed to set Sentry user:', error);
    }
  }

  public setTag(key: string, value: string): void {
    if (!this.initialized) {
      return;
    }

    try {
      Sentry.setTag(key, value);
    } catch (error) {
      console.error('❌ Failed to set Sentry tag:', error);
    }
  }

  public setContext(key: string, context: Record<string, any>): void {
    if (!this.initialized) {
      return;
    }

    try {
      Sentry.setContext(key, context);
    } catch (error) {
      console.error('❌ Failed to set Sentry context:', error);
    }
  }

  public addBreadcrumb(breadcrumb: { message: string; category?: string; level?: 'debug' | 'info' | 'warning' | 'error' | 'fatal'; data?: Record<string, any> }): void {
    if (!this.initialized) {
      return;
    }

    try {
      Sentry.addBreadcrumb({
        message: breadcrumb.message,
        category: breadcrumb.category,
        level: breadcrumb.level as any,
        data: breadcrumb.data,
      });
    } catch (error) {
      console.error('❌ Failed to add Sentry breadcrumb:', error);
    }
  }

  public async close(timeout = 2000): Promise<boolean> {
    if (!this.initialized) {
      return true;
    }

    try {
      return await Sentry.close(timeout);
    } catch (error) {
      console.error('❌ Failed to close Sentry:', error);
      return false;
    }
  }

  public isInitialized(): boolean {
    return this.initialized;
  }
}

export default new SentryService();

// Common error handling functions that abstract Sentry implementation
export class ErrorHandler {
  private static sentryService = new SentryService();

  /**
   * Log an error with context information
   * @param error - The error object or message
   * @param context - Additional context information
   */
  public static logError(
    error: Error | string,
    context?: {
      component?: string;
      operation?: string;
      [key: string]: any;
    },
  ): void {
    const errorObj = typeof error === 'string' ? new Error(error) : error;

    // Always log to console
    console.error(`❌ Error in ${context?.component || 'Unknown'}:`, errorObj.message);

    // Send to Sentry if available
    if (this.sentryService.isInitialized()) {
      this.sentryService.captureError(errorObj, context);
    }
  }

  /**
   * Log a warning message
   * @param message - Warning message
   * @param context - Additional context information
   */
  public static logWarning(
    message: string,
    context?: {
      component?: string;
      operation?: string;
      [key: string]: any;
    },
  ): void {
    console.warn(`⚠️ Warning in ${context?.component || 'Unknown'}: ${message}`);

    if (this.sentryService.isInitialized()) {
      this.sentryService.captureMessage(message, 'warning', context);
    }
  }

  /**
   * Log an info message
   * @param message - Info message
   * @param context - Additional context information
   */
  public static logInfo(
    message: string,
    context?: {
      component?: string;
      operation?: string;
      [key: string]: any;
    },
  ): void {
    console.log(`ℹ️ ${message}`);

    if (this.sentryService.isInitialized()) {
      this.sentryService.captureMessage(message, 'info', context);
    }
  }

  /**
   * Add a breadcrumb for debugging
   * @param message - Breadcrumb message
   * @param category - Category of the breadcrumb
   * @param data - Additional data
   */
  public static addBreadcrumb(message: string, category?: string, data?: Record<string, any>): void {
    if (this.sentryService.isInitialized()) {
      this.sentryService.addBreadcrumb({
        message,
        category: category || 'general',
        level: 'info',
        data,
      });
    }
  }

  /**
   * Initialize the error tracking system
   */
  public static init(): void {
    this.sentryService.init();
  }

  /**
   * Set user context for error tracking
   * @param user - User information
   */
  public static setUser(user: { id?: string; email?: string; [key: string]: any }): void {
    if (this.sentryService.isInitialized()) {
      this.sentryService.setUser(user);
    }
  }

  /**
   * Set a tag for filtering errors
   * @param key - Tag key
   * @param value - Tag value
   */
  public static setTag(key: string, value: string): void {
    if (this.sentryService.isInitialized()) {
      this.sentryService.setTag(key, value);
    }
  }

  /**
   * Close the error tracking connection
   * @param timeout - Timeout in milliseconds
   */
  public static async close(timeout = 2000): Promise<boolean> {
    if (this.sentryService.isInitialized()) {
      return await this.sentryService.close(timeout);
    }
    return true;
  }
}
