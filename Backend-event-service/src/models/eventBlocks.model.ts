import * as mongoose from "mongoose";
const EventBlock = new mongoose.Schema(
  {
    chain: { type: String, require: true, default: "1" },
    address: { type: String, require: true, default: "1" },
    blockNumber: { type: Number, default: 1 },
    contractName: { type: String, require: true, default: "1" },
  },
  { timestamps: true, versionKey: false }
);
export default mongoose.model("eventBlocks", EventBlock);
