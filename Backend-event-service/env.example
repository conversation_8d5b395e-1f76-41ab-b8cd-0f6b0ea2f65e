# Database Configuration
MONGO_URI=mongodb://localhost:27017/event-service

# Blockchain RPC URLs (comma-separated for failover)
BASE_SEPOLIA_URL=https://sepolia.base.org,https://base-sepolia.blockpi.network/v1/rpc/public
BSC_RPC=https://bsc-dataseed.binance.org,https://bsc-dataseed1.defibit.io

# Kafka Configuration
KAFKA_CLIENT_ID=event-service
KAFKA_BROKER=localhost:9092
KAFKA_GROUP_ID=event-service-group

# Event Processing Configuration
EVENT_BATCH_SIZE=1000

# Contract Addresses - Base Chain
IDFACTORY=0x...
TREXFACTORY=0x...
FUNDFACTORYPROXY=0x...
BONDINGFACTORYPROXY=0x...
NFTMARKETPLACEPROXY=0x...
START_BLOCK=0

# Contract Addresses - BSC Chain
BSC_BONDING_FACTORY_PROXY=0x...
BSC_START_BLOCK=0

# Sentry Configuration for Error Tracking
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ENVIRONMENT=development
SENTRY_RELEASE=1.0.0

# Optional: Additional Sentry Configuration
# SENTRY_TRACES_SAMPLE_RATE=1.0
# SENTRY_PROFILES_SAMPLE_RATE=1.0

SONAR_HOST=your_sonar_host_url
SONAR_TOKEN=your_sonar_token