{"name": "event_s", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "npx tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "format": "npx prettier --write . './src/**/*.{ts,tsx,js,jsx,json,ejs}' && npx eslint --fix './src/**/*.{ts,tsx,js}'", "sonar": "dotenv -e .env -- sh -c 'sonar-scanner -Dsonar.host.url=\"$SONAR_HOST\" -Dsonar.token=\"$SONAR_TOKEN\"'"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/big.js": "^6.2.2", "@types/node": "^22.14.0", "@types/node-cron": "^3.0.11", "@types/web3": "^1.0.20", "sonarqube-scanner": "^4.3.0", "typescript": "^5.8.3"}, "dependencies": {"@sentry/node": "^9.23.0", "@sentry/profiling-node": "^9.23.0", "bcryptjs": "^3.0.2", "big.js": "^6.2.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csurf": "^1.11.0", "dotenv": "^16.4.7", "dotenv-cli": "^8.0.0", "express-brute": "^1.0.1", "express-brute-memcached": "^0.0.1", "express-brute-mongoose": "^1.1.0", "express-brute-redis": "^0.0.1", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "hpp": "^0.2.3", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "mongoose": "^8.13.2", "node-cron": "^3.0.3", "web3": "^4.16.0", "xss-clean": "^0.1.4"}}