import { Module } from '../component/subadmin/models/module.model';
import mongoose from 'mongoose';

// Data to be migrated
const moduleDataWithId = [
  { _id: new mongoose.Types.ObjectId('672c5bed05e65803246ffc2f'), title: 'Dashboard' },
  { _id: new mongoose.Types.ObjectId('672c5c4a05e65803246ffc33'), title: 'Manage SubAdmin' },
  { _id: new mongoose.Types.ObjectId('672c705305e65803246ffc40'), title: 'Request Issuer' },
  { _id: new mongoose.Types.ObjectId('672c707d05e65803246ffc41'), title: 'Request Offering Creation' },
  { _id: new mongoose.Types.ObjectId('672c70af05e65803246ffc42'), title: 'Manage Issuers' },
  { _id: new mongoose.Types.ObjectId('672c70c205e65803246ffc43'), title: 'Users' },
  { _id: new mongoose.Types.ObjectId('672c70ce05e65803246ffc44'), title: 'Offerings' },
  { _id: new mongoose.Types.ObjectId('672c70e305e65803246ffc45'), title: 'Transfer Agent' },
  { _id: new mongoose.Types.ObjectId('672c70ee05e65803246ffc46'), title: 'Transactions' },
  { _id: new mongoose.Types.ObjectId('672c70f605e65803246ffc47'), title: 'Settings' },
  { _id: new mongoose.Types.ObjectId('672c70f605e65803246ffc48'), title: 'Fee Settings' },
  { _id: new mongoose.Types.ObjectId('672c70f605e65803246ffc49'), title: 'Bonding Settings' },
];

// 5. Migrate data into the 'modules' collection, only if the _id does not exist
async function migrateModules() {
  try {
    // Step 1: Find the existing records in the collection
    const existingModules = await Module.find({
      _id: { $in: moduleDataWithId.map((item) => item._id) },
    });

    // Step 2: Extract the existing IDs
    const existingIds = existingModules.map((module) => module._id.toString());

    // Step 3: Filter out the items that already exist based on the _id
    const newModulesToInsert = moduleDataWithId.filter((item) => !existingIds.includes(item._id.toString()));

    // Step 4: If there are new modules to insert, insert them into the collection
    if (newModulesToInsert.length > 0) {
      await Module.insertMany(newModulesToInsert);
    } else {
      console.log('No new data to insert. All records already exist.');
    }
  } catch (error) {
    console.error('Error migrating data:', error);
  }
}

// 6. Call the migration function
module.exports = migrateModules;
