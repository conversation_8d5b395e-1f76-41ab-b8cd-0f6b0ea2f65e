import { userSchema } from '../component/userAuthentications/models/user.model';
import * as bcrypt from 'bcrypt';
import emailHelper from '../helpers/email.helper';
import { UserType } from '../utils/common.interface';
import logger from '../helpers/logging/logger.helper';
const seed = process.env.SEED;
const walletAddress = process.env.ADMINADDRESS;

const users = {
  name: 'Admin',
  email: process.env.EMAIL,
  countryCode: '+91',
  mobile: process.env.MOBILE,
  userType: UserType.Admin,
  isEmailVerify: true,
  isOtpActive: false,
  walletAddress: walletAddress,
};
// Helper function to generate a random password
export function generateRandomPassword(length: number) {
  const chars = seed;
  let password = '';
  for (let i = 0; i < length; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
}

async function seedUsers() {
  try {
    // Check for a specific user
    const user = await userSchema.findOne({ email: users.email }).exec();
    if (!user) {
      // Generate a random password
      const randomPassword = generateRandomPassword(12);
      const hashedPassword = await bcrypt.hash(randomPassword, 10);
      // Add the password to the user data
      const newUser = {
        ...users,
        password: hashedPassword,
      };
      // Create the user
      const createdUser = await userSchema.create(newUser);
      if (createdUser) {
        const emailDetail = {
          name: process.env.NAME,
          password: randomPassword,
          email: users.email,
        };
        // Send the email with the random password
        emailHelper.sendEmailTemplate(users.email, 'Adminemail', emailDetail);
      }
    }
  } catch (err) {
    logger.error('Error seeding user data:', err);
  }
}

// Export the seedUsers function
module.exports = seedUsers;
