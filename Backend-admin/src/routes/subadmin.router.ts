import { Router } from 'express';
import { SubAdminController } from '../component';
import { CreateSubAdmin, UpdateSubAdmin } from '../middleware';
import { checkPermissions } from '../middleware/checkAdminPermissions';

/**
 * @constant {express.Router}
 */
const router: Router = Router();

/**
 * @route POST /createSubadmin
 * @desc Creates a new subadmin user
 * @access Private (requires admin permissions)
 */
router.post('/createSubadmin', checkPermissions, CreateSubAdmin, SubAdminController.createSubadmin);

/**
 * @route GET /loggedInUserModuleList
 * @desc Retrieves module list for logged in user
 * @access Private (requires authentication)
 */
router.get('/loggedInUserModuleList', SubAdminController.moduleList);

/**
 * @route GET /getModuleList
 * @desc Retrieves module list by ID
 * @access Private (requires admin permissions)
 */
router.get('/getModuleList', checkPermissions, SubAdminController.getModuleListById);

/**
 * @route POST /changeSubadminStatus
 * @desc Changes subadmin status (block/unblock)
 * @access Private (requires admin permissions)
 */
router.post('/changeSubadminStatus', checkPermissions, SubAdminController.unblock);

/**
 * @route POST /updateSubAdminPermissions
 * @desc Updates subadmin permissions
 * @access Private (requires admin permissions)
 */
router.post('/updateSubAdminPermissions', checkPermissions, UpdateSubAdmin, SubAdminController.updateStatus);

/**
 * @route GET /subAdminList
 * @desc Retrieves list of subadmins
 * @access Private (requires admin permissions)
 */
router.get('/subAdminList', checkPermissions, SubAdminController.subAdminList);

/**
 * @route GET /getModulesListById
 * @desc Retrieves permission list by ID
 * @access Private (requires admin permissions)
 */
router.get('/getModulesListById', checkPermissions, SubAdminController.getPermissionListById);

/**
 * @route GET /getSubadminListCsv
 * @desc Retrieves subadmin list in CSV format
 * @access Private (requires admin permissions)
 */
router.get('/getSubadminListCsv', checkPermissions, SubAdminController.getSubadminListControllerCsv);

/**
 * @export {express.Router}
 */
export default router;
