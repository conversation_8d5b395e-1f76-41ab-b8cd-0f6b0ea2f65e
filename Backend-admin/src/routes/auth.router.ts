import { Router } from 'express';
import { AuthComponent } from '../component';
import { validateEmailReq, validateVerificationReq, validateLoginReq, resendOtpValidationReq, validateResetPasswordReq, verify2FAReq, tokenValidationReq } from '../middleware';
import authMiddleware from '../config/middleware/jwtTokenAuth';

/**
 * @constant {express.Router}
 */
const router: Router = Router();

/**
 * @route POST /login
 * @desc Authenticates a user and returns JWT tokens
 * @access Public
 */
router.post('/login', validateLoginReq, AuthComponent.loginController);

/**
 * @route POST /verify
 * @desc Verifies OTP for user authentication
 * @access Public
 */
router.post('/verify', validateVerificationReq, AuthComponent.verifyController);

/**
 * @route POST /resend-otp
 * @desc Resends OTP for user authentication
 * @access Public
 */
router.post('/resend-otp', resendOtpValidationReq, AuthComponent.reSendOtpController);

/**
 * @route POST /forgot-password
 * @desc Initiates password reset process
 * @access Public
 */
router.post('/forgot-password', validateEmailReq, AuthComponent.forgotPasswordController);

/**
 * @route POST /verify-2faBeforelogin
 * @desc Verifies 2FA before login
 * @access Private (requires JWT token)
 */
router.post('/verify-2faBeforelogin', authMiddleware, AuthComponent.verify2FALoginController);

/**
 * @route POST /reset-password
 * @desc Resets user password
 * @access Private (requires JWT token)
 */
router.post('/reset-password', validateResetPasswordReq, authMiddleware, AuthComponent.resetPasswordController);

/**
 * @route POST /verify-2fas
 * @desc Verifies 2FA during login
 * @access Private (requires JWT token)
 */
router.post('/verify-2fas', verify2FAReq, authMiddleware, AuthComponent.verifyLogin2FAController);

/**
 * @route PATCH /forgot-2fa
 * @desc Forgets 2FA
 * @access Private (requires JWT token)
 */
router.patch('/forgot-2fa', tokenValidationReq, authMiddleware, AuthComponent.forgot2FAController);

/**
 * @route POST /reset2FA
 * @desc Resets 2FA
 * @access Private (requires JWT token)
 */
router.post('/reset2FA', tokenValidationReq, authMiddleware, AuthComponent.reSet2FA);

/**
 * @export {express.Router}
 */
export default router;
