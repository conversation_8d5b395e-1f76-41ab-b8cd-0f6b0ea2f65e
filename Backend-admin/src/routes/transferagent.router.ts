import { Router } from 'express';
import { TransferAgentController } from '../component';
import { BlockTransferAgent, CreateTransferAgent } from '../middleware';
import { checkPermissions } from '../middleware/checkAdminPermissions';

/**
 * @constant {express.Router}
 */
const router: Router = Router();
router.post('/createTransferAgent', checkPermissions, CreateTransferAgent, TransferAgentController.createTransferAgent);
router.get('/transferAgentList', checkPermissions, TransferAgentController.getTransferAgentListController);
router.get('/transferAgentList/:userId', TransferAgentController.getTransferAgentController);
router.post('/changeStatus', checkPermissions, BlockTransferAgent, TransferAgentController.unblock);
router.get('/transferAgentListCsv', checkPermissions, TransferAgentController.transferAgentListCsv);

/**
 * @export {express.Router}
 */

export default router;
