import { Router } from 'express';
import * as jwtAuthenticated from '../config/middleware/jwtAuthenticated';
import { NftComponent } from '../component';
import { nftCollectionValidationReq } from '../middleware';
import { checkPermissions } from '../middleware/checkAdminPermissions';

/**
 * @constant {express.Router}
 */
const router: Router = Router();
router.use(jwtAuthenticated.isAuthenticated);

/**
 * @route GET /getCollection
 * @desc Retrieves NFT collections
 * @access Private (requires authentication)
 */
router.get('/getCollection', checkPermissions, nftCollectionValidationReq, NftComponent.getCollection);

/**
 * @route POST /updateNFTStatus
 * @desc Updates NFT status
 * @access Private (requires admin permissions)
 */
router.post('/updateNFTStatus', checkPermissions, NftComponent.updateNFTStatus);

/**
 * @route GET /getNftById
 * @desc Retrieves NFT details by ID
 * @access Private (requires admin permissions)
 */
router.get('/getNftById', checkPermissions, NftComponent.getNftById);

/**
 * @export {express.Router}
 */
export default router;
