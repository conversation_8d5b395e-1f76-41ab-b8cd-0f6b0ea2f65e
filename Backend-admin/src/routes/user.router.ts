import { Router } from 'express';
import { AuthComponent } from '../component';
import { validateGetUsersList, validateGetissuerUsersList } from '../middleware/validateGetUsersList';
import { approveIssuer, approveUser, changePasswordReq, docsValidationReq, tokenValidationReq } from '../middleware';
import { checkPermissions } from '../middleware/checkAdminPermissions';
import * as file from '../middleware/googleCloud.middleware';

/**
 * @constant {express.Router}
 */
const router: Router = Router();

/**
 * @route GET /getUserslist
 * @desc Retrieves a list of users with optional filtering
 * @access Private (requires admin permissions)
 */
router.get('/getUserslist', checkPermissions, validateGetUsersList, AuthComponent.getUsersListController);

/**
 * @route GET /getUsersListCsv
 * @desc Retrieves a list of users in CSV format
 * @access Private (requires admin permissions)
 */
router.get('/getUsersListCsv', checkPermissions, AuthComponent.getUsersListControllerCsv);

/**
 * @route POST /approve
 * @desc Approves a user account
 * @access Private (requires admin permissions)
 */
router.post('/approve', checkPermissions, approveUser, AuthComponent.approve);

/**
 * @route GET /getFeeList
 * @desc Retrieves a list of fees
 * @access Private (requires authentication)
 */
router.get('/getFeeList', AuthComponent.getFeeList);

/**
 * @route GET /getWalletDetails
 * @desc Retrieves wallet details
 * @access Private (requires authentication)
 */
router.get('/getWalletDetails', AuthComponent.currentFee);

/**
 * @route POST /approve-issuer
 * @desc Approves an issuer account
 * @access Private (requires admin permissions)
 */
router.post('/approveIssuer', checkPermissions, approveIssuer, AuthComponent.approveIssuer);

/**
 * @route GET /getUserdata
 * @desc Retrieves user data
 * @access Private (requires authentication)
 */
router.get('/getUserdata', AuthComponent.getUserdata);

/**
 * @route POST /unblock
 * @desc Unblocks a user
 * @access Private (requires admin permissions)
 */
router.post('/unblock', checkPermissions, AuthComponent.unblock);

/**
 * @route PATCH /change-password
 * @desc Changes user password
 * @access Private (requires authentication)
 */
router.patch('/change-password', changePasswordReq, AuthComponent.changePasswordController);

/**
 * @route GET /enable-2fa
 * @desc Enables 2FA for a user
 * @access Private (requires authentication)
 */
router.get('/enable-2fa', AuthComponent.enable2FAController);

/**
 * @route POST /verify-2fa
 * @desc Verifies 2FA setup
 * @access Private (requires authentication)
 */
router.post('/verify-2fa', AuthComponent.verify2FAController);

/**
 * @route PATCH /disable-2fa
 * @desc Disables 2FA for a user
 * @access Private (requires authentication)
 */
router.patch('/disable-2fa', tokenValidationReq, AuthComponent.disable2FAController);

/**
 * @route GET /log-out
 * @desc Logs out the current user
 * @access Private (requires authentication)
 */
router.get('/log-out', AuthComponent.logOutController);

/**
 * @route GET /getissuer
 * @desc Retrieves issuer data
 * @access Private (requires admin permissions)
 */
router.get('/getissuer', checkPermissions, validateGetissuerUsersList, AuthComponent.getIssuerController);

/**
 * @route GET /getIssuerListCsv
 * @desc Retrieves a list of issuers in CSV format
 * @access Private (requires admin permissions)
 */
router.get('/getIssuerListCsv', checkPermissions, checkPermissions, AuthComponent.getIssuerListControllerCsv);

/**
 * @route GET /getManageIssuerListCsv
 * @desc Retrieves a list of managed issuers in CSV format
 * @access Private (requires admin permissions)
 */
router.get('/getManageIssuerListCsv', checkPermissions, AuthComponent.getManageIssuerListCsv);

/**
 * @route GET /dashboard
 * @desc Retrieves dashboard data
 * @access Private (requires authentication)
 */
router.get('/dashboard', AuthComponent.dashboard);

/**
 * @route GET /user
 * @desc Retrieves current user information
 * @access Private (requires authentication)
 */
router.get('/user', AuthComponent.user);

/**
 * @route GET /topInvestors
 * @desc Retrieves top investors
 * @access Private (requires authentication)
 */
router.get('/topInvestors', AuthComponent.topInvestors);

/**
 * @route GET /topHoldings
 * @desc Retrieves top holdings
 * @access Private (requires authentication)
 */
router.get('/topHoldings', AuthComponent.topInvestors);

/**
 * @route POST /upload-docs
 * @desc Uploads user documents
 * @access Private (requires authentication)
 */
router.post('/upload-docs', file.validateFiles, docsValidationReq, AuthComponent.uploadDocs);

/**
 * @route GET /report
 * @desc Retrieves a single offering report
 * @access Private (requires authentication)
 */
router.get('/report', AuthComponent.singleOfferingReportController);

/**
 * @export {express.Router}
 */
export default router;
