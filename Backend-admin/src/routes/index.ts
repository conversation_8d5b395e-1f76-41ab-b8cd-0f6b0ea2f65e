import * as express from 'express';
import AuthRouter from './auth.router';
import UserRouter from './user.router';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import { ResponseHandler } from '../helpers/response.helper';
import * as jwtAuthenticated from '../config/middleware/jwtAuthenticated';
import * as swaggerUi from 'swagger-ui-express';
import { swaggerDefinition } from '../utils/swaggerDef';
import OfferingRouter from './offering.router';
import transferAgentRouter from './transferagent.router';
import subadminRouter from './subadmin.router';
import transactionsRouter from './transactions.router';
import notification from './notification.router';
// import notificationClient from '../_grpc/clients/notification.client';
import nft from './nft.router';

/**
 * @export
 * @param {express.Application} app
 */
export function init(app: express.Application): void {
  const router: express.Router = express.Router();

  /**
   * @description Auth Router
   * @constructs
   */
  app.use('/admin/v1', AuthRouter);

  /**
   * @description Authenticated user routes
   */

  //app.use('/admin/v1/auth', UserRouter);
  app.use('/admin/v1/auth', jwtAuthenticated.isAuthenticated, UserRouter);
  app.use('/admin/v1/auth', jwtAuthenticated.isAuthenticated, subadminRouter);
  app.use('/admin/v1/auth', jwtAuthenticated.isAuthenticated, OfferingRouter);
  app.use('/admin/v1/auth', jwtAuthenticated.isAuthenticated, transferAgentRouter);
  app.use('/admin/v1/auth', jwtAuthenticated.isAuthenticated, transactionsRouter);
  app.use('/admin/v1/auth/notification', jwtAuthenticated.isAuthenticated, notification);
  app.use('/admin/v1/auth/nft', jwtAuthenticated.isAuthenticated, nft);

  // app.use("/admin/v1/offerings'", OfferingRouter);

  //   app.use('/admin/v1/', KycRouter);
  /**
   * @description Swagger Routes
   */
  app.use('/admin/v1/apis-docs', swaggerUi.serve, swaggerUi.setup(swaggerDefinition));

  /**
   * @description HealthCheck
   * @constructs
   */
  app.use('/admin/v1/health-check', async (req, res) => {
    try {
      // const payload = { page: 1123, userId: '677f95225bf44d745a2c95ad' };
      // await notificationClient.client.getOfferingRequestNotification(payload, async (error: any, response: any) => {
      //   if (error || response?.error) {
      //     const errorMessage = error?.message || response?.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR;
      //     const errorStatus = error?.status || RESPONSES.INTERNAL_SERVER_ERROR;

      //     console.error('gRPC Error:', error || response);
      //     return ResponseHandler.error(res, {
      //       status: errorStatus,
      //       error: true,
      //       message: errorMessage,
      //     });
      //   }
      // });

      return ResponseHandler.success(res, {
        message: RES_MSG?.SUCCESS_MSG.HEALTH_CHECK,
        status: RESPONSES.SUCCESS,
        error: false,
        data: {
          timestamp: new Date(),
        },
      });
    } catch (error: any) {
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  });

  /**
   * @constructs all routes
   */

  app.use(router);

  /**
   * @description if page not found
   * @constructs
   */
  app.use((req, res) => {
    ResponseHandler.error(res, {
      error: true,
      message: RES_MSG.ERROR_MSG.PAGE_NOT_FOUND,
      status: RESPONSES.NOTFOUND,
    });
  });
}
