import { Router } from 'express';
import { OfferingComponent } from '../component';
import { checkPermissions } from '../middleware/checkAdminPermissions';
import { createOfferingsValidationReq, offeringReportValidationReq, checkDividendStatusValidationReq, scheduleOfferingValidationReq } from '../middleware/offering.middleware';

/**
 * @constant {express.Router}
 */
const router: Router = Router();

router.get('/offering/manage-issuers', checkPermissions, OfferingComponent.getIssuerOfferingController);
router.get('/offeringList', checkPermissions, OfferingComponent.getOfferingListController);
router.get('/getOfferingList', checkPermissions, OfferingComponent.getOfferingList);
router.get('/getOfferingListCsv', checkPermissions, OfferingComponent.getOfferingList);
router.patch('/schedule-offering', scheduleOfferingValidationReq, OfferingComponent.scheduleOffering);
router.get('/offering', checkPermissions, OfferingComponent.getOfferingDetailsController);
router.post('/rejectOffering', checkPermissions, OfferingComponent.rejectOffering);
router.get('/adminGraph', offeringReportValidationReq, OfferingComponent.offeringReportController);
router.get('/offering/requested-offerings', OfferingComponent.requestedOfferings);
router.get('/offering/requested-offeringsCsv', OfferingComponent.requestedOfferingsCsv);
router.put('/offering/assign', OfferingComponent.assignOfferings);
router.post('/offering', checkPermissions, createOfferingsValidationReq, OfferingComponent.createOfferingController);
router.get('/dividend/status', checkDividendStatusValidationReq, OfferingComponent.checkDividendStatus);

/**
 * @export {express.Router}
 */

export default router;
