import { Router } from 'express';
import { getOfferingNotification, seenOfferingNotification } from '../middleware';
import { Notification } from '../component/index';

/**
 * @constant {express.Router}
 */
const router: Router = Router();

/**
 * @route GET /
 * @desc Retrieves offering notifications
 * @access Private (requires authentication)
 */
router.get('/', getOfferingNotification, Notification.getOfferingNotification);

/**
 * @route post /seen
 * @desc Marks offering notifications as seen
 * @access Private (requires authentication)
 */
router.post('/seen', seenOfferingNotification, Notification.seenOfferingNotification);

/**
 * @export {express.Router}
 */
export default router;
