/* eslint-disable no-case-declarations */
import { KafkaMessage, Consumer } from 'kafkajs';
import { kafkaHelperService } from '../helpers/kafka.helper';
import UserService from '../component/userAuthentications/service';
import logger from '../helpers/logging/logger.helper';
import OfferingService from '../component/offerings/service';
import emailHelper from '../helpers/email.helper';
import { KycStatus } from '../utils/common.interface';
import { offeringSchema } from '../component/offerings/models/offerings.model';
// import EventLogSchemaModel from '../component/transactions/model/EventLogSchema.model';
import TransferRequestService from '../component/transfer/service';
import NftCollectionService from '../component/nft/service';
const baseurl = process.env.BASEURL;
import { userSchema } from '../component/userAuthentications/models/user.model';

class KafkaService {
  private consumers: Consumer | null = null;

  constructor() {
    this.initialize();
  }
  /**
   * Initializes the Kafka consumer and sets up message handling.
   */
  private async initialize() {
    try {
      this.consumers = await this.setupConsumer('user-to-admin', this.handleUserMessage.bind(this));
    } catch (error) {
      logger.error('Error initializing Kafka consumer for admin:', error);
      throw error;
    }
  }
  /**
   * Sets up a Kafka consumer for the specified topic and message handler.
   * @param topic - Kafka topic to subscribe to
   * @param messageHandler - Function to handle incoming messages
   * @returns Consumer instance
   */
  private async setupConsumer(topic: string, messageHandler: (message: KafkaMessage, topic: string, partition: number, offset: string) => void): Promise<Consumer> {
    try {
      const consumer = await kafkaHelperService.createConsumer('admin-user-group', topic, messageHandler);
      this.consumers = consumer;
      return consumer;
    } catch (error) {
      logger.error(`Error setting up consumer for topic ${topic}:`, error);
      throw error;
    }
  }
  /**
   * Handles incoming messages for the 'user-to-admin' topic.
   * @param message - Incoming Kafka message
   * @param topic - Kafka topic
   * @param partition - Kafka partition number
   * @param offset - Kafka offset for the message
   */
  private async handleUserMessage(message: KafkaMessage, topic: string, partition: number, offset: any) {
    try {
      let response: any;
      const body = JSON.parse(message.value.toString());
      const value = typeof body.value === 'string' ? JSON.parse(body.value) : body.value;
      const valueWithId = { ...value };
      const filter = { _id: value?._id || value?.offeringId };
      console.log('\n\n\n\n Type', value?.type, '====== Event', value?.event, 'data =====>>>>\n\n\n', value);
      if (value?._id) {
        delete value?._id;
      }
      switch (value?.type) {
        case 'fee':
          if (value?.event == 'AdminWalletUpdated') {
            response = await userSchema.findOneAndUpdate({ userType: 'admin' }, value, {
              new: false,
              runValidators: true,
            });
          } else {
            // const fee = ((value.fee)/100)
            // value.fee = fee;
            response = await offeringSchema.findOneAndUpdate(filter, value, {
              new: false,
              runValidators: true,
              upsert: true,
            });
          }
          break;
        case 'offering':
          if (value?.projectDetails && value?.projectDetails?.isPrivate) {
            const { data } = await OfferingService.fetchOfferingDetails(filter);
            const oldOfferingMembers = data?.projectDetails?.offeringMembers || [];
            const newOfferingMembers = value?.projectDetails?.offeringMembers || [];
            // Find the extra (newly added) emails
            const newEmails = newOfferingMembers.filter((email: string) => !oldOfferingMembers.includes(email));
            if (newEmails.length > 0) {
              // Only send emails if there are new users
              const emailString = oldOfferingMembers.length === newOfferingMembers.length && newEmails.length === 0 ? newOfferingMembers : newEmails.join(', ');

              const emailDetail = {
                offeringName: value?.projectDetails?.offeringName,
                tokenTicker: value?.projectDetails?.tokenTicker,
                assetName: value?.projectDetails?.assetName,
                icon: value?.overview?.icon,
                baseurl,
              };

              emailHelper.sendEmailTemplate(emailString, 'investor', emailDetail);
            }
          }

          response = await OfferingService.updateOfferingDetails(value, filter);
          break;
        case 'reqOffering':
          response = await OfferingService.requestOffering(value, filter);
          break;
        case 'reqWhitelist':
          response = await OfferingService.createWhitelistDetails(value, filter);
          break;
        case 'ForceTransferred':
          response = await TransferRequestService.updateForceTransferDetails(value, filter);
          break;
        case 'order':
          response = await OfferingService.updateorderDetails(value, filter);
          break;
        // case 'saveEvent':
        //   const { transactionHash } = value;
        //   if (transactionHash) {
        //     await EventLogSchemaModel.findOneAndUpdate(
        //       { transactionHash }, // Search by uniqueId (salt)
        //       value, // Update the fields
        //       { new: true, upsert: true }, // `new: true` returns the updated document, `upsert: true` creates a new document if no match is found
        //     );
        //   }
        //   break;
        case 'whitelist':
          response = await OfferingService.updateWhitelistDetails(value, filter);
          break;
        case 'user':
          if (value?.onchainID) {
            value.kycStatus = KycStatus.APPROVED;
          }
          const filters = filter?._id ? filter : { walletAddress: value?.onchainID };
          response = await UserService.updateUserDetails(value, filters);
          break;

        case 'NftCollection':
          response = await NftCollectionService.updateCollection(filter, valueWithId);
          break;

        case 'Nft':
          delete valueWithId?.type;
          response = await NftCollectionService.createNft({ ...valueWithId, type: valueWithId?.nftType });
          break;

        default:
          logger.warn(`Unrecognized or missing type`);
          return;
      }

      if (!response?.error && this?.consumers) {
        const nextOffset = (parseInt(offset) + 1).toString();
        await this.consumers.commitOffsets([{ topic, partition, offset: nextOffset }]);
        logger.info(`Committed offset ${offset} for partition ${partition} on topic ${topic}`);
      } else {
        logger.warn('Consumer is not initialized or operation failed.');
      }
    } catch (error) {
      logger.error('handleUserMessage:', error);
      throw error;
    }
  }
  /**
   * Sends a message from admin to user.
   * @param message - Message data to send
   */
  public async sendMessageToUser(message: any) {
    try {
      await kafkaHelperService.sendMessage('admin-to-user', [{ value: JSON.stringify(message) }]);
    } catch (error) {
      logger.error('Error sending message from admin to user:', error);
      throw error;
    }
  }
  /**
   * Sends a message from admin to transfer agent.
   * @param message - Message data to send
   */ public async sendMessageToTransferagnet(message: any) {
    try {
      console.log('sendMessageToTransferAgent============>>>>>>>', message);
      await kafkaHelperService.sendMessage('admin-to-transferagent', [{ value: JSON.stringify(message) }]);
    } catch (error) {
      logger.error('Error sending message from admin-to-transferagent:', error);
      throw error;
    }
  }

  public async sendMessageToNotification(message: any) {
    try {
      await kafkaHelperService.sendMessage('notifications', [{ value: JSON.stringify(message) }]);
    } catch (error) {
      logger.error(error, 'Error sending message from user to admin:');
      throw error;
    }
  }
}

export default new KafkaService();
