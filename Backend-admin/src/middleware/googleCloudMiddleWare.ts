import * as multer from 'm<PERSON>'; // Import multer for handling file uploads
import CONFIG from '../config/env'; // Import configuration values from the environment configuration

// Define memory storage for uploaded files
const storage = multer.memoryStorage();

// Define a file filter function to allow only specific file types
const imageFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Check if the file has a valid extension (JPG, PNG, or PDF)
  if (!file.originalname.match(/\.(JPG|jpg|jpeg|JPEG|png|PNG|PDF|pdf)$/)) {
    return cb(null, false); // Reject the file if it's not an allowed type
  }
  cb(null, true); // Accept the file if it matches the allowed types
};

// Define the maximum allowed file size based on the environment configuration (in megabytes)
const maxSize = { fileSize: Number(CONFIG.GOOGLE.MAX_SIZE) * 1024 * 1024 };

// Create a multer instance to handle file uploads with defined storage, file filter, and file size limits
export const validateFiles = multer({ storage, fileFilter: imageFilter, limits: maxSize });
