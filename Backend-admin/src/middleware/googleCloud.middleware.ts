import * as multer from 'm<PERSON>';
import { Request, Response, NextFunction } from 'express';
import <PERSON>NF<PERSON> from '../config/env';
import { ResponseHandler } from '../helpers/response.helper';
import { RESPONSES } from '../utils/responseUtils';

/**
 * Memory storage configuration for multer
 */
const storage = multer.memoryStorage();

/**
 * Type definition for the file filter callback function
 */
type FileFilterCallback = (error: Error | null, acceptFile: boolean) => void;

/**
 * Filter function to validate JPG, PNG, and PDF files
 * @param {Request} req - Express request object
 * @param {Express.Multer.File} file - The uploaded file
 * @param {FileFilterCallback} cb - Callback function to handle the filter result
 */
const imageFilter = (req: Request, file: Express.Multer.File, cb: FileFilterCallback) => {
  if (!file.originalname.match(/\.(JPG|jpg|jpeg|JPEG|png|PNG|PDF|pdf)$/)) {
    return cb(null, false); // Reject the file
  }
  cb(null, true);
};

/**
 * Filter function to validate only JPG and PNG files
 * @param {Request} req - Express request object
 * @param {Express.Multer.File} file - The uploaded file
 * @param {FileFilterCallback} cb - Callback function to handle the filter result
 */
const imageOnlyFilter = (req: Request, file: Express.Multer.File, cb: FileFilterCallback) => {
  if (!file.originalname.match(/\.(JPG|jpg|jpeg|JPEG|png|PNG)$/)) {
    return cb(null, false); // Reject the file
  }
  cb(null, true);
};

/**
 * Maximum file size configuration in bytes
 */
const maxSize = { fileSize: Number(CONFIG.GOOGLE.MAX_SIZE) * 1024 * 1024 };

/**
 * Middleware factory function to handle file uploads with custom filters
 * @param {Function} fileFilter - The file filter function to use
 * @returns {Function} Express middleware function for handling file uploads
 */
const uploadHandler = (fileFilter: (req: Request, file: Express.Multer.File, cb: FileFilterCallback) => void) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const upload = multer({
      storage,
      fileFilter,
      limits: maxSize,
    }).single('file');

    upload(req, res, (err) => {
      // Handle multer-specific errors
      if (err instanceof multer.MulterError) {
        if (err.code === 'LIMIT_FILE_SIZE') {
          return ResponseHandler.error(res, {
            message: `File size should not exceed ${CONFIG.GOOGLE.MAX_SIZE} MB.`,
            status: RESPONSES.BAD_REQUEST,
            error: true,
          });
        }
      } else if (err) {
        return ResponseHandler.error(res, {
          message: err.message || 'An unknown error occurred during file upload.',
          status: RESPONSES.BAD_REQUEST,
          error: true,
        });
      }
      next();
    });
  };
};

/**
 * Middleware to validate and handle JPG, PNG, and PDF file uploads
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 */
export const validateFiles = uploadHandler(imageFilter);

/**
 * Middleware to validate and handle only JPG and PNG file uploads
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 */
export const validateImageFiles = uploadHandler(imageOnlyFilter);
