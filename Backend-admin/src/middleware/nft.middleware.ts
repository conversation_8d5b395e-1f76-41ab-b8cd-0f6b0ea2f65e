/* eslint-disable @typescript-eslint/no-explicit-any */
import { Request, Response, NextFunction } from 'express';
import { JoiValidationResult, PromiseResolve } from '../utils/common.interface';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import { ResponseHandler } from '../helpers/response.helper';
import logger from '../helpers/logging/logger.helper';
import CustomError from '../helpers/customError.helper';
import NftCollectionValidation from '../component/nft/validation';

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */

/**
 * @exports
 * @param {Request} req
 * @param {Response} res
 * @param {NextFunction} next
 * @returns {Promise<PromiseResolve>}
 */
export async function nftCollectionValidationReq(req: Request, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const validateRequest: JoiValidationResult = await NftCollectionValidation.getNftCollectionValidation(req.query as Record<string, any>);
    if (validateRequest.error) throw new CustomError(validateRequest.message, validateRequest.status);
    req.query = validateRequest.value;
    next();
  } catch (error: any) {
    logger.error(error, 'nftCollectionValidationReq Error');
    ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
  }
}
