import TransferRequest, { ITransferRequest } from './models/transfer';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import { FilterQuery } from 'mongoose';

import { PromiseResolve } from '../../utils/common.interface';

export default class TransferRequestService {
  static updateForceTransferDetails = async (data: any, filter: FilterQuery<ITransferRequest>): Promise<PromiseResolve> => {
    try {
      // Use the filter directly, no need to extract _id separately
      const updateUserResp = await TransferRequest.findOneAndUpdate(filter, data, {
        new: true, // Return the modified document
        runValidators: true, // Validate before updating
        upsert: true, // Uncomment if you want to create a new document if not found
      });

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USER_UPDATION_SUCCESS,
        data: updateUserResp,
      };
    } catch (error) {
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };
}
