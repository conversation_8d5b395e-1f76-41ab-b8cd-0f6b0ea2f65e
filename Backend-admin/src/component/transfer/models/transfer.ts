/* eslint-disable no-useless-escape */
import mongoose, { Schema, Document, Types } from 'mongoose';
import { transferStatusEnum } from '../../../utils/common.interface';
export interface ITransferRequest extends Document {
  securityName: string;
  registeredName: string;
  TranferBy: boolean;
  registeredEmailId: string;
  walletAddress: string;
  newRegisteredName: string;
  newRegisteredEmailId: string;
  status: transferStatusEnum;
  newWalletAddress: string;
  reason?: string;
  tokenQuantity: number;
  remark: string;
  userId: Types.ObjectId;
  taId: Types.ObjectId;
  offeringId: Types.ObjectId;
  createdAt?: Date;
  updatedAt?: Date;
  txHash?: string;
}

export interface IUpdateTransferRequest extends Document {
  securityName: string;
  registeredName: string;
  registeredEmailId: string;
  walletAddress: string;
  newRegisteredName: string;
  newRegisteredEmailId: string;
  status: transferStatusEnum;
  newWalletAddress: string;
  reason?: string;
  TranferBy: boolean;
  tokenQuantity: number;
  remark: string;
  txHash?: string;
  userId: Types.ObjectId;
  offeringId: Types.ObjectId;
  taId: Types.ObjectId;
  createdAt?: Date;
  updatedAt?: Date;
}

const TransferRequestSchema: Schema = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, ref: 'users', required: false },
    taId: { type: Schema.Types.ObjectId, ref: 'itransferagents', required: false },
    offeringId: {
      type: Schema.Types.ObjectId,
      ref: 'offerings',
      required: true,
    },
    securityName: {
      type: String,
      required: true,
    },
    registeredName: {
      type: String,
      required: true,
    },
    reason: {
      type: String,
      required: false,
    },
    status: {
      type: String,
      enum: Object.values(transferStatusEnum),
      default: transferStatusEnum.PENDING,
    },
    isForceTransfer: {
      type: Boolean,
      default: false,
    },
    TranferBy: {
      type: Boolean,
      default: false,
    },

    registeredEmailId: {
      type: String,
      required: true,
      match: [/.+\@.+\..+/, 'Please enter a valid email address'],
    },
    walletAddress: {
      type: String,
      required: true,
      set: (value: string) => (value ? value.toLowerCase() : value),
    },
    newRegisteredName: {
      type: String,
      required: true,
    },
    newRegisteredEmailId: {
      type: String,
      required: true,
      match: [/.+\@.+\..+/, 'Please enter a valid email address'],
    },
    newWalletAddress: {
      type: String,
      required: true,
      set: (value: string) => (value ? value.toLowerCase() : value),
    },
    tokenQuantity: {
      type: Number,
      required: false,
      min: [0, 'Token quantity must be a positive number'],
    },
    txHash: { type: String, required: false },
    remark: {
      type: String,
      required: false,
    },
  },
  {
    timestamps: true,
  },
);

const TransferRequest = mongoose.model<ITransferRequest>('TransferRequest', TransferRequestSchema);

export default TransferRequest;
