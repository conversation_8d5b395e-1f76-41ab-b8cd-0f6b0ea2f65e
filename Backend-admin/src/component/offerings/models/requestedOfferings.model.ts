import mongoose, { Schema, Types } from 'mongoose';
import { offeringStatusEnum } from '../../../utils/common.interface';

export interface IRequestedOffering {
  _id?: string;
  userId?: Types.ObjectId;
  subAdminId?: string | Types.ObjectId;
  status?: offeringStatusEnum;
}

const requestedOffering: Schema<IRequestedOffering> = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, require: true },
    subAdminId: { type: Schema.Types.ObjectId, ref: 'users', require: false },
    status: {
      type: String,
      enum: Object.values(offeringStatusEnum),
      default: offeringStatusEnum.PENDING,
    },
  },

  {
    timestamps: true,
    versionKey: false,
    strict: true,
  },
);

const requestedOfferingSchema = mongoose.model<IRequestedOffering>('requestedOfferings', requestedOffering);

export { requestedOfferingSchema };
