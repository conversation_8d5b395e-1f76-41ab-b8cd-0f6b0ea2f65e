import mongoose, { Schema, Document, Types } from 'mongoose';
import { offeringStatusEnum } from '../../../utils/common.interface';

interface IAuthorizedCountry {
  name: string;
  isoCode: string;
  countryCode: string;
}

export interface IOffering extends Document {
  [x: string]: any;
  _id: Types.ObjectId | string;
  userId: Types.ObjectId;
  overview: {
    title: string;
    subTitle?: string;
    description: string;
    entityName: string;
    entityType: string;
    webUrl?: string;
    lineOfBusiness: string;
    sourceOfFunds: string;
    location: string;
    companyDescription: string;
    icon?: string;
    cover?: string;
    logo: string;
    propertyImages?: string[];
  };
  projectDetails: {
    assetType: string;
    blockChainType: string;
    offeringType: string;
    tokenStandard: string;
    offeringName: string;
    CUSIP: string;
    isAuthorized: boolean;
    authorizedCountries: IAuthorizedCountry[];
    startDate: Date;
    endDate: Date;
    minInvestment: number;
    maxInvestment: number;
    assetName: string;
    tokenTicker: string;
    tokenSupply: number;
    tokenDecimals: number;
    lockupMonths: number;
    holdTime: Date;
    maxTokenHolding: number;
    navLaunchPrice: number;
    latestNav: number;
    // aumSize: number;
    // spvValuation: number;
    propertyType: string;
    isTransferAgent: boolean;
    taId?: string;
    issuerId: string;
    issuerWallet: string;
    customFields: Array<{
      label: string;
      type: string;
      value: string;
    }>;
    isPrivate?: boolean;
    offeringMembers?: string[];
    propertySubtype?: string;
    yearBuilt?: number;
    lotSize?: number;
    occupancy?: number;
    projectedYield?: number;
    launchValuation?: number;
    previousValuation?: number;
    deRatio?: number;
    acquisitionCosts?: {
      legalCost?: number;
      estateAgent?: string;
      stampDuty?: string;
      tax?: string;
      platformFees?: string;
      maintenance?: string;
      total?: string;
    };
    agentName?: string;
    agentFunctions?: string[];
    poweredBy?: string;
    poweredByLogo?: string;
    bondingPrice?: number;
    roi?: number;
  };
  documents: {
    assetType?: string;
    eSign?: string;
    pitchDeck?: string;
    confidentialInformationMemorendum?: string;
    landRegistration?: string;
    titleDocs?: string;
    bankApproval?: string;
    encumbranceCertificate?: string;
    propertyTaxReceipt?: string;
    articlesOfAssociation?: string;
    operatingAgreement?: string;
    taxAssignmentLetter?: string;
    certificateOfRegistration?: string;
    registerOfManagers?: string;
    customDocs: Array<{
      docsLabel: string;
      value: string;
    }>;
  };
  team: Array<{
    name: string;
    title: string;
    summary?: string;
    email?: string;
    url?: string;
    linkedInUrl?: string;
    twitterUrl?: string;
  }>;
  isFinalSubmission?: boolean;
  currentStep: number;
  isTokenDeploy?: boolean;
  isFundDeploy?: boolean;
  iserc20?: boolean;
  tokenAddress?: string;
  erc20Address?: string;
  identityRegistry?: string;
  fundAddress?: string;
  fee?: {
    escrowFee?: number;
    wrapFee?: number;
    dividendFee?: number;
    redemptionFee?: number;
  };
  deployedDate?: Date;
  wrapperDeployedAt?: Date;

  isActive: boolean;
  isDelete?: boolean;
  status?: offeringStatusEnum;
  createdBy?: Types.ObjectId;
  template_id: string;
  envelopeId: string;
  offeringFeeStatus?: boolean;
  reason?: string;
  proposalHoldingPercentage?: [string];
  dividends?: Array<{
    _id: Types.ObjectId | string;
    offeringId: Types.ObjectId | string;
    offeringName: string;
    dividendAmount: number;
    status: string;
    dividendType: string;
    paymentMethod: string;
    documentUrl: string;
    recordDate: Date;
    issuerId: Types.ObjectId | string;
    declarationDate: Date;
    createdAt: Date;
    updatedAt: Date;
  }>;

  //nft
  image?: string;
  name?: string;
  nftDescription?: string;
  externalLink?: string;
  creatorId?: Types.ObjectId;
  royalty?: number;
  currency?: 'usdc' | 'usdt';
  traits?: string[];
  floorPrice?: number;
  launchDate?: Date;
  launchTime?: string;
  category?: string;
  type?: string;
  collectionId?: Types.ObjectId;
  txHash?: string;
  isNft?: boolean;
  isSchedule?: boolean;
  scheduleTime?: number;
}

export interface IUpdateOffering {
  // tokenAddress: any;
  template_id?: string;
  isTokenDeploy?: boolean;
  isFundDeploy?: boolean;
  tokenAddress?: string;
  erc20Address?: string;
  fee?: {
    escrowFee?: number;
    wrapFee?: number;
    dividendFee?: number;
    redemptionFee?: number;
  };
  iserc20?: boolean;
  fundAddress?: string;
  _id?: Types.ObjectId | string;
  userId?: Types.ObjectId;
  overview?: {
    title?: string;
    subTitle?: string;
    description?: string;
    entityName?: string;
    entityType?: string;
    webUrl?: string;
    lineOfBusiness?: string;
    sourceOfFunds?: string;
    location?: string;
    companyDescription?: string;
    icon?: string;
    cover?: string;
    logo?: string;
    propertyImages?: string[];
  };
  projectDetails?: {
    assetType?: string;
    blockChainType?: string;
    offeringType?: string;
    tokenStandard?: string;
    offeringName?: string;
    CUSIP?: string;
    isAuthorized?: boolean;
    authorizedCountries?: IAuthorizedCountry[];
    startDate?: Date;
    endDate?: Date;
    minInvestment?: number;
    maxInvestment?: number;
    assetName?: string;
    tokenTicker?: string;
    tokenSupply?: number;
    tokenDecimals?: number;
    lockupMonths?: number;
    holdTime?: Date;
    maxTokenHolding?: number;
    navLaunchPrice?: number;
    latestNav?: number;
    // aumSize?: number;
    // spvValuation?: number;
    propertyType?: string;
    isTransferAgent?: boolean;
    taId?: string;
    issuerId?: string;
    issuerWallet?: string;
    customFields?: Array<{
      label?: string;
      type?: string;
      value?: string;
    }>;
    isPrivate?: boolean;
    isDelete?: boolean;
    offeringMembers?: string[];
    propertySubtype?: string;
    yearBuilt?: number;
    lotSize?: number;
    occupancy?: number;
    projectedYield?: number;
    launchValuation?: number;
    previousValuation?: number;
    deRatio?: number;
    acquisitionCosts?: {
      legalCost?: number;
      estateAgent?: string;
      stampDuty?: string;
      tax?: string;
      platformFees?: string;
      maintenance?: string;
      total?: string;
    };
    agentName?: string;
    agentFunctions?: string[];
    poweredBy?: string;
    poweredByLogo?: string;
    bondingPrice?: number;
    roi?: number;
  };
  documents?: {
    assetType?: string;
    eSign?: string;
    pitchDeck?: string;
    confidentialInformationMemorendum?: string;
    landRegistration?: string;
    titleDocs?: string;
    bankApproval?: string;
    encumbranceCertificate?: string;
    propertyTaxReceipt?: string;
    articlesOfAssociation?: string;
    operatingAgreement?: string;
    taxAssignmentLetter?: string;
    certificateOfRegistration?: string;
    registerOfManagers?: string;
    customDocs?: Array<{
      docsLabel?: string;
      value?: string;
    }>;
  };
  team?: Array<{
    name?: string;
    title?: string;
    summary?: string;
    email?: string;
    url?: string;
    linkedInUrl?: string;
    twitterUrl?: string;
  }>;
  isFinalSubmission?: boolean;
  currentStep?: number;
  deployedDate?: Date;
  wrapperDeployedAt?: Date;
  isActive?: boolean;
  isDelete?: boolean;
  status?: offeringStatusEnum;
  createdBy?: Types.ObjectId;
  offeringId?: Types.ObjectId | string;
  period?: string;
  offeringFeeStatus?: boolean;
  reason?: string;
  proposalHoldingPercentage?: [string];
  //nft
  image?: string;
  name?: string;
  nftDescription?: string;
  externalLink?: string;
  creatorId?: Types.ObjectId;
  royalty?: number;
  currency?: 'usdc' | 'usdt';
  traits?: string[];
  floorPrice?: number;
  launchDate?: Date;
  launchTime?: string;
  category?: string;
  type?: string;
  collectionId?: Types.ObjectId;
  txHash?: string;
  isNft?: boolean;
  isBondingDeploy?: boolean;
  bondingAddress?: string;
  isStaking?: boolean;
  stakingAddress?: string;
  isSchedule?: boolean;
  scheduleTime?: number;
}

const offerings: Schema<IOffering> = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, ref: 'users', required: true },
    overview: {
      type: {
        title: { type: String, required: false },
        subTitle: { type: String },
        description: { type: String, required: false },
        entityName: { type: String, required: false },
        entityType: { type: String, required: false },
        webUrl: { type: String },
        lineOfBusiness: { type: String, required: false },
        sourceOfFunds: { type: String, required: false },
        location: { type: String, required: false },
        companyDescription: { type: String, required: false },
        icon: { type: String },
        cover: { type: String },
        logo: { type: String, required: false },
        propertyImages: {
          type: [String],
          required: false,
          // default: []
        },
      },
      _id: false,
      required: false,
    },
    projectDetails: {
      type: {
        assetType: { type: String, required: false },
        blockChainType: { type: String, required: false },
        offeringType: { type: String, required: false },
        tokenStandard: { type: String, required: false },
        offeringName: { type: String, required: false },
        CUSIP: {
          type: String,
          required: false,
          unique: true,
          sparse: true, // This allows multiple documents with null values
        },
        isAuthorized: { type: Boolean, required: false },
        authorizedCountries: {
          type: [{ name: { type: String, required: false }, isoCode: { type: String, required: false }, countryCode: { type: String, required: false } }],
          required: false,
          // default: [],
          _id: false,
        },
        startDate: { type: Date, required: false },
        endDate: { type: Date, required: false },
        minInvestment: { type: Number, required: false },
        maxInvestment: { type: Number, required: false },
        assetName: { type: String, required: false },
        tokenTicker: { type: String, required: false, unique: true, sparse: true },
        tokenSupply: { type: Number, required: false },
        tokenDecimals: { type: Number, required: false },
        lockupMonths: { type: Number, required: false },
        holdTime: { type: Date, required: false },
        maxTokenHolding: { type: Number, required: false },
        navLaunchPrice: { type: Number, required: false },
        latestNav: { type: Number, required: false },
        // aumSize: { type: Number, required: false },
        // spvValuation: { type: Number, required: false },
        isTransferAgent: { type: Boolean, required: false },
        taId: { type: String, required: false },
        issuerId: { type: String, required: false },
        issuerWallet: { type: String, required: false },
        isPrivate: { type: Boolean, required: false },
        offeringMembers: {
          type: [String],
          required: false,
          // default: []
        },
        customFields: { type: [{ label: { type: String, required: false }, type: { type: String, required: false }, value: { type: String, required: false } }], _id: false, default: undefined },
        propertyType: { type: String, required: false },
        propertySubtype: { type: String, trim: false, required: false },
        yearBuilt: { type: Number, required: false, min: 1, max: 9999 },
        lotSize: { type: Number, required: false, min: 1 },
        occupancy: { type: Number, required: false, min: 0, max: 100 },
        projectedYield: { type: Number, required: false, min: 0, max: 100 },
        launchValuation: { type: Number, required: false, min: 0 },
        previousValuation: { type: Number, required: false, min: 0 },
        deRatio: { type: Number, required: false, min: 0 },
        acquisitionCosts: {
          legalCost: { type: Number, required: false },
          estateAgent: { type: String, required: false },
          stampDuty: { type: String, required: false },
          tax: { type: String, required: false },
          platformFees: { type: Number, required: false },
          maintenance: { type: String, required: false },
          total: { type: Number, required: false },
        },
        agentName: { type: String, required: false },
        agentFunctions: {
          type: [String],
          required: false,
          // default: []
        },
        poweredBy: { type: String, required: false },
        poweredByLogo: { type: String, required: false },
        bondingPrice: { type: Number, required: false },
        roi: { type: Number, required: false },
      },
      _id: false,
      required: false,
    },
    documents: {
      type: {
        eSign: { type: String, required: false },
        pitchDeck: { type: String, required: false },
        confidentialInformationMemorendum: { type: String, required: false },
        landRegistration: { type: String, required: false },
        titleDocs: { type: String, required: false },
        bankApproval: { type: String, required: false },
        encumbranceCertificate: { type: String, required: false },
        propertyTaxReceipt: { type: String, required: false },
        articlesOfAssociation: { type: String, required: false },
        operatingAgreement: { type: String, required: false },
        taxAssignmentLetter: { type: String, required: false },
        certificateOfRegistration: { type: String, required: false },
        registerOfManagers: { type: String, required: false },
        customDocs: { type: [{ docsLabel: { type: String, required: false }, value: { type: String, required: false } }], _id: false, default: undefined },
      },
      _id: false,
      required: false,
    },
    team: {
      type: [
        {
          name: { type: String, required: false },
          title: { type: String, required: false },
          summary: { type: String, required: false },
          email: { type: String, required: false },
          url: { type: String, required: false },
          linkedInUrl: { type: String, required: false },
          twitterUrl: { type: String, required: false },
        },
      ],
      _id: false,
      default: undefined,
      required: false,
    },
    template_id: { type: String, required: false },
    fee: {
      escrowFee: { type: Number, required: false },
      wrapFee: { type: Number, required: false },
      dividendFee: { type: Number, required: false },
      redemptionFee: { type: Number, required: false },
    },
    tokenAddress: { type: String, required: false },
    isTokenDeploy: { type: Boolean, required: false, default: false },
    erc20Address: { type: String, required: false },
    iserc20: { type: Boolean, required: false, default: false },
    isFundDeploy: { type: Boolean, required: false, default: false },
    identityRegistry: { type: String, required: false },
    fundAddress: { type: String, required: false },
    bondingAddress: { type: String, required: false },
    deployedDate: { type: Date, required: false },
    wrapperDeployedAt: { type: Date, required: false },
    currentStep: { type: Number, required: false },
    isActive: { type: Boolean, required: false, default: true },
    isDelete: { type: Boolean, required: false, default: false },
    isFinalSubmission: { type: Boolean, required: false, default: false },
    status: { type: String, enum: Object.values(offeringStatusEnum), default: offeringStatusEnum.IN_PROGRESS },
    offeringFeeStatus: { type: Boolean, required: false, default: false },
    proposalHoldingPercentage: { type: [String], required: false },
    createdBy: { type: Schema.Types.ObjectId, ref: 'users', required: false },
    // deployedDate: { type: Date, required: false },
    reason: { type: String, required: false },

    //nft
    image: { type: String, required: false },
    name: { type: String, required: false },
    nftDescription: { type: String, required: false },
    externalLink: { type: String, required: false },
    creatorId: { type: Schema.Types.ObjectId, ref: 'users', required: false },
    royalty: { type: Number, required: false },
    currency: { type: String, enum: ['usdc', 'usdt'], required: false },
    traits: { type: [String], required: false },
    floorPrice: { type: Number, required: false },
    launchDate: { type: Date, required: false },
    launchTime: { type: String, required: false },
    category: { type: String, required: false },
    type: { type: String, required: false },
    collectionId: { type: Schema.Types.ObjectId, ref: 'nftcollections', required: false },
    isNft: { type: Boolean, required: true, default: false },
    isSold: { type: Boolean, required: true, default: false },
    isBondingDeploy: { type: Boolean, required: true, default: false },
    isStaking: { type: Boolean, required: false, default: false },
    stakingAddress: { type: String, required: false },
    isSchedule: { type: Boolean, required: false, default: false },
    scheduleTime: { type: Number, required: false },
  },
  { versionKey: false, timestamps: true },
);

const offeringSchema = mongoose.model<IOffering>('offerings', offerings);

export { offeringSchema };
