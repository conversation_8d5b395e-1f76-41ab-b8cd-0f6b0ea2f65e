import mongoose, { Schema, Document, Types } from 'mongoose';
import { orderStatusEnum, paymentTypeEnum } from '../../../utils/common.interface';

export interface IOrder extends Document {
  userId?: Types.ObjectId | string;
  offeringId?: Types.ObjectId | string;
  walletAddress?: string;
  amount?: string;
  amountBeforeFee?: string;
  status?: orderStatusEnum;
  isMinted?: boolean;
  feesInPercentage?: string;
  isSettled?: boolean;
  isFreezed?: boolean;
  price?: string;
  currentPrice?: string;
  adminFeeAmount?: string;
  orderReceived?: Date;
  orderMinted?: Date;
  quantity?: string;
  txHash?: string;
  mintTxHash?: string;
  emailSent?: boolean;
  mintEmailSent?: boolean;
  paymentMethod?: paymentTypeEnum;
  orderType?: string;
  principleAmount?: string;
  wap?: string;
  profit?: string;
  reason?: string;
}

export interface IUpdateOrder {
  _id?: string;
  userId?: Types.ObjectId | string;
  offeringId?: Types.ObjectId | string;
  walletAddress?: string;
  amount?: string;
  amountBeforeFee?: string;
  status?: orderStatusEnum;
  isMinted?: boolean;
  feesInPercentage?: string;
  isSettled?: boolean;
  isFreezed?: boolean;
  price?: string;
  currentPrice?: string;
  adminFeeAmount?: string;
  orderReceived?: Date;
  orderMinted?: Date;
  quantity?: string;
  txHash?: string;
  mintTxHash?: string;
  emailSent?: boolean;
  mintEmailSent?: boolean;
  paymentMethod?: paymentTypeEnum;
  orderType?: string;
  principleAmount?: string;
  wap?: string;
  profit?: string;
  reason?: string;
}

const order: Schema<IOrder> = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, ref: 'users', required: true },
    offeringId: { type: Schema.Types.ObjectId, ref: 'offerings', required: true },
    walletAddress: { type: String, required: false },
    amount: { type: String, required: true },
    amountBeforeFee: { type: String, required: true },
    isMinted: { type: Boolean, required: true, default: false },
    isSettled: { type: Boolean, required: true, default: false },
    isFreezed: { type: Boolean, required: true, default: false },
    feesInPercentage: { type: String, required: true },
    quantity: { type: String, required: true },
    price: { type: String, required: true },
    currentPrice: { type: String, required: true },
    adminFeeAmount: { type: String, required: false },
    status: { type: String, enum: Object.values(orderStatusEnum), default: orderStatusEnum.PENDING },
    emailSent: { type: Boolean, default: false },
    mintEmailSent: { type: Boolean, default: false },
    orderReceived: { type: Date, required: false },
    orderMinted: { type: Date, required: false },
    paymentMethod: { type: String, enum: Object.values(paymentTypeEnum), default: paymentTypeEnum.USDT },
    txHash: { type: String, required: false, unique: true, sparse: true, default: null },
    mintTxHash: { type: String, required: false, unique: false, sparse: true, default: null },
    orderType: { type: String, required: false, default: orderStatusEnum.MINTED },
    principleAmount: { type: String, required: false },
    wap: { type: String, required: false },
    profit: { type: String, required: false },
    reason: { type: String, required: false },
  },

  { timestamps: true, versionKey: false },
);

order.pre<IOrder>('save', async function (next) {
  // If txHash is null, skip the uniqueness check
  if (!this.txHash) {
    return next();
  }

  // Check if txHash already exists in the database
  const existingOrder = await mongoose.models.order.findOne({ txHash: this.txHash });

  if (existingOrder) {
    // Prevent insertion if a duplicate txHash is found
    return next(new Error('Duplicate txHash: This transaction hash already exists.'));
  }

  // Proceed with saving the document
  next();
});

const OrderSchema = mongoose.model<IOrder>('order', order);
export { OrderSchema };
