import { Request, Response } from 'express';
import { offeringStatusEnum, PromiseResolve, UserType } from '../../utils/common.interface';
import OfferingService from './service';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import CustomError from '../../helpers/customError.helper';
import { ResponseHandler } from '../../helpers/response.helper';
import logger from '../../helpers/logging/logger.helper';
import { IOffering } from './models/offerings.model';
import CommonHelper from '../../helpers/common.helper';
import mongoose, { Types } from 'mongoose';
import UserService from '../userAuthentications/service';
import { offeringDocs } from '../../utils/constant';
import userClient from '../../_grpc/clients/user.client';
import { transferAgentSchema } from '../transferagent/transferagent.model';
import { requestedOfferingSchema } from './models/requestedOfferings.model';
import offeringValidation from '../../component/offerings/validation';
import emailHelper from '../../helpers/email.helper';
import kafkaService from '../../services/kafkaService';
import { permissionSchema } from '../subadmin/models/permission.model';

/**
 * OfferingController handles offering related operations.
 */
class OfferingController {
  /**
   * Retrieves detailed information about a specific offering.
   * Fetches offering data, formats documents, and includes related
   * information such as transfer agent details.
   *
   * @param {Request} req Express request object containing the offering ID in query params
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to offering details
   * @memberof OfferingController
   */
  public getOfferingDetailsController = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const offeringId: any = req.query.offerId;
      const searchQuery = { _id: offeringId };
      let formattedDocuments;
      const offeringDetails: PromiseResolve = await OfferingService.fetchOfferingDetails(searchQuery);
      if (offeringDetails.error) {
        throw new CustomError(offeringDetails.message, offeringDetails.status);
      }

      const data: IOffering | any = offeringDetails.data;
      const { _id, overview, projectDetails, documents, team, currentStep, isActive, status, isTokenDeploy, tokenAddress, isBondingDeploy, bondingAddress, fundAddress, isFundDeploy, stakingAddress, isStaking, isSchedule, scheduleTime } = data;
      const taId = projectDetails?.taId;
      let taWalletAddress;
      if (taId) {
        const taDetails = await transferAgentSchema.findOne({ _id: taId }, 'walletAddress name email');
        taWalletAddress = taDetails || {};
      } else {
        taWalletAddress = {};
      }

      // if (status === 'REJECTED') {
      //   throw new CustomError(RES_MSG.SUCCESS_MSG.NO_OFFERING, RESPONSES.NOTFOUND);
      // }
      const getFileType = (url: string): string => {
        const extension = url.split('.').pop()?.toLowerCase();
        return extension;
      };

      if (documents) {
        formattedDocuments = offeringDocs.map((doc) => {
          const url = documents[doc?.name];
          if (url) {
            return {
              title: doc.title,
              url: url || null,
              type: url ? getFileType(url) : 'N/A',
            };
          }
        });

        if (documents.customDocs && documents.customDocs.length > 0) {
          documents.customDocs.forEach((customDoc: { docsLabel: string; value: string }) => {
            formattedDocuments.push({
              title: customDoc.docsLabel,
              url: customDoc.value,
              type: getFileType(customDoc.value),
            });
          });
        }
      }

      const cleanedDocuments = formattedDocuments?.filter(function (doc: any) {
        return doc !== null && doc !== undefined;
      });
      const profileUser = {
        _id,
        overview,
        projectDetails,
        taWalletAddress,
        documents: cleanedDocuments,
        team,
        currentStep,
        isActive,
        status,
        isTokenDeploy,
        tokenAddress,
        isBondingDeploy,
        bondingAddress,
        fundAddress,
        isFundDeploy,
        stakingAddress,
        isStaking,
        document: documents,
        isSchedule,
        scheduleTime,
      };
      return ResponseHandler.success(res, {
        status: offeringDetails.status || RESPONSES.SUCCESS,
        error: false,
        message: offeringDetails.message || RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: profileUser,
      });
    } catch (error: any) {
      logger.error(error, 'getOffering Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Rejects an offering with a specific reason.
   * Updates the offering status to rejected, notifies relevant parties,
   * and sends rejection email to the issuer.
   *
   * @param {Request} req Express request object containing offering ID and rejection reason
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to rejection result
   * @memberof OfferingController
   */
  public rejectOffering = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { offeringId, reason } = req.body;
      const searchQuery = { _id: offeringId };
      // Fetch offering details
      const offeringDetails: PromiseResolve = await OfferingService.fetchOfferingDetails(searchQuery);
      const _id = offeringDetails?.data?.createdBy;
      const date = offeringDetails?.data?.updatedAt;

      const offeringName = offeringDetails?.data?.projectDetails?.offeringName;
      const data: any = await UserService.fetchUserDetails({ _id });
      const { email, name }: any = data.data;
      if (offeringDetails.error) {
        throw new CustomError(offeringDetails.message, offeringDetails.status);
      }

      // Prepare update data
      const updateData: any = {
        status: offeringStatusEnum.REJECTED,
        reason,
      };

      // send reject offering to kafka notification topic
      await kafkaService.sendMessageToNotification({
        value: {
          type: 'offering-rejected',
          details: {
            offeringId,
            reason,
            status: offeringStatusEnum.REJECTED,
          },
        },
      });

      // gRPC call to reject offering
      const payload = { id: offeringId, status: offeringStatusEnum.REJECTED, reason };
      userClient.client.rejectOffering(payload, async (error: any, response: any) => {
        if (error) {
          logger.error('gRPC Error:', error);
          return ResponseHandler.error(res, {
            status: RESPONSES.INTERNAL_SERVER_ERROR,
            error: true,
            message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
          });
        }

        if (response.error) {
          return ResponseHandler.error(res, {
            message: response.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
            status: RESPONSES.BAD_REQUEST,
            error: true,
          });
        }

        // If gRPC response is successful, update offering details
        await OfferingService.updateOfferingDetails(updateData, { _id: offeringId });
        const emailDetail = {
          email,
          name,
          offeringId,
          date,
          reason,
          offeringName,
        };
        emailHelper.sendEmailTemplate(email, 'rejectoffering', emailDetail);
        // Send success response
        return ResponseHandler.success(res, {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.SUCCESS_MSG.OFFERING_REJECT || RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
          data: '',
        });
      });
    } catch (error: any) {
      logger.error(error, 'rejectOffering Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Generates offering reports with metrics and statistics.
   * Provides data about offerings' performance and investment activity.
   *
   * @param {Request} req Express request object containing report parameters
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to offering report data
   * @memberof OfferingController
   */
  public offeringReportController = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { offeringId, period } = req.body;

      await Promise.all(
        offeringId.map(async (id: string) => {
          const response: PromiseResolve = await OfferingService.fetchOfferingDetails({ _id: new Types.ObjectId(id) });

          if (response.error) throw new CustomError(response.message, response.status);
        }),
      );

      const result: PromiseResolve = await OfferingService.offeringReport({ offeringId, period });
      if (result.error) throw new CustomError(result.message, result.status);

      return ResponseHandler.success(res, { status: result.status || RESPONSES.SUCCESS, error: false, message: result.message || RES_MSG.USER.USERS_FETCH, data: result.data || [] });
    } catch (error) {
      logger.error(error, 'offeringReport Error');

      return ResponseHandler.error(res, { message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true });
    }
  };

  /**
   * Creates a new offering in the system.
   * Processes offering details, validates data, and stores the new offering.
   * Handles various offering types and their specific requirements.
   *
   * @param {Request} req Express request object containing offering details
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to the created offering data
   * @memberof OfferingController
   */
  public createOfferingController = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { userId: subAdminId, email } = req.userInfo;
      const filteredPermissions = await permissionSchema.aggregate([
        {
          $match: {
            userId: new mongoose.Types.ObjectId(subAdminId),
          },
        },
        {
          $project: {
            permission: {
              $filter: {
                input: '$permission',
                as: 'perm',
                cond: {
                  $or: [{ $eq: ['$$perm.moduelsId', new mongoose.Types.ObjectId('672c70ce05e65803246ffc44')] }, { $eq: ['$$perm.moduelsId', new mongoose.Types.ObjectId('672c70e305e65803246ffc45')] }],
                },
              },
            },
          },
        },
      ]);

      const perms = filteredPermissions[0]?.permission || [];

      const hasInvalidPermission = perms.some((p: { read: any; moduelsId: { toString: () => string }; write: any }) => !p.read || (p.moduelsId.toString() === '672c70ce05e65803246ffc44' && !p.write));

      if (hasInvalidPermission) {
        throw new CustomError(RES_MSG.USER.UNAUTHORIZE, RESPONSES.CONFLICT);
      }
      const { offeringId: offeringIdRaw, userId, ...offeringDetails } = req.body;
      const offeringId = offeringIdRaw ? new Types.ObjectId(offeringIdRaw) : null;
      const isAssigned = await OfferingService.fetchAssignedOfferingDetails({ _id: offeringId, subAdminId: new Types.ObjectId(subAdminId) });
      if (isAssigned.error) {
        throw new CustomError(isAssigned.message, isAssigned.status);
      }

      const userDetails: PromiseResolve = await UserService.fetchUserDetails({
        _id: userId,
      });

      let updateOfferingResp: PromiseResolve, currentOfferingResp: PromiseResolve;

      if (offeringId) {
        // Fetch current offering if offeringId exists
        currentOfferingResp = await OfferingService.fetchOfferingDetails({ _id: offeringId, userId });
        if (currentOfferingResp.error && currentOfferingResp.status === RESPONSES.NOTFOUND) {
          // If no offeringId, insert new offering
          updateOfferingResp = await OfferingService.createOffering({
            ...offeringDetails,
            userId,
            currentStep: 0,
            createdBy: subAdminId,
            _id: offeringId,
          });
          await OfferingService.requestOffering({ status: offeringStatusEnum.IN_PROGRESS }, { _id: offeringId });
          if (updateOfferingResp.error) {
            throw new CustomError(updateOfferingResp.message || RES_MSG.ERROR_MSG.UPDATE_FAILED, updateOfferingResp.status);
          }
          return ResponseHandler.success(res, {
            status: RESPONSES.SUCCESS,
            error: false,
            message: RES_MSG.SUCCESS_MSG.OFFERING_CREATE_SUCCESS,
            data: updateOfferingResp.data,
          });
        }
        const { data: currentOfferingData } = currentOfferingResp;
        const { currentStep = 0, status } = currentOfferingData;
        const requestedStep = offeringDetails?.currentStep || 0;
        const updateData = { ...offeringDetails };
        // Step-based validation
        if (requestedStep === currentStep + 1) {
          updateData.currentStep = requestedStep;
        } else if (requestedStep <= currentStep) {
          delete updateData.currentStep;
        } else {
          return ResponseHandler.error(res, {
            status: RESPONSES.BAD_REQUEST,
            error: true,
            message: `Invalid step progression. Current step: ${currentStep}. Proceed to step ${currentStep + 1}.`,
          });
        }
        // Status validations
        if (status === offeringStatusEnum.APPROVED) {
          throw new CustomError(RES_MSG.USER.ALREADY_APPROVED_OFFERING, RESPONSES.CONFLICT);
        }
        if (status === offeringStatusEnum.PENDING) {
          throw new CustomError(RES_MSG.USER.ALREADY_PENDING_OFFERING, RESPONSES.CONFLICT);
        }

        if (status === offeringStatusEnum.REVIEW) {
          throw new CustomError(RES_MSG.USER.ALREADY_REVIEW_OFFERING, RESPONSES.CONFLICT);
        }
        // Merge and validate fields
        const { projectDetails } = offeringDetails;
        const { projectDetails: currentProjectDetails = {} } = currentOfferingData;

        if (projectDetails?.isPrivate && projectDetails?.offeringMembers) {
          projectDetails.offeringMembers = CommonHelper.mergeWithoutDuplicates(projectDetails.offeringMembers, currentProjectDetails.offeringMembers || []);
        }

        if (projectDetails?.offeringMembers?.includes(email)) {
          throw new CustomError(RES_MSG.ERROR_MSG.OFFERING_OWN_EMAIL, RESPONSES.BAD_REQUEST);
        }
        if (projectDetails?.offeringMembers?.includes(userDetails.data?.email)) {
          throw new CustomError(RES_MSG.ERROR_MSG.OFFERING_ISSUER_EMAIL, RESPONSES.BAD_REQUEST);
        }

        // if (offeringDetails?.team) {
        //   offeringDetails.team = CommonHelper.mergeWithoutDuplicates(
        //     offeringDetails.team,
        //     currentOfferingData.team || [],
        //     ['email']
        //   );
        // }

        // if (offeringDetails?.documents?.customDocs) {
        //   offeringDetails.documents.customDocs = CommonHelper.mergeWithoutDuplicates(
        //     offeringDetails.documents.customDocs,
        //     currentOfferingData.documents?.customDocs || [],
        //     ['docsLabel']
        //   );
        // }

        if (projectDetails?.customFields) {
          projectDetails.customFields = CommonHelper.mergeWithoutDuplicates(projectDetails.customFields, currentProjectDetails.customFields || [], ['label', 'type']);
        }

        if (offeringDetails?.documents) {
          req.body.documents.assetType = currentProjectDetails.assetType;
          const validateRequest = await offeringValidation.createOfferingsValidation({ ...req.body });
          if (validateRequest.error) {
            throw new CustomError(validateRequest.message, validateRequest.status);
          }
          offeringDetails.documents = validateRequest.value.documents;
        }
        if (projectDetails && !projectDetails?.isTransferAgent) {
          projectDetails.taId = '';
        }

        // Update the offering details
        updateOfferingResp = await OfferingService.updateOfferingDetails(updateData, { _id: offeringId });
        await OfferingService.requestOffering({ status: offeringStatusEnum.IN_PROGRESS }, { _id: offeringId });
        if (updateOfferingResp.error) {
          throw new CustomError(updateOfferingResp.message || RES_MSG.ERROR_MSG.UPDATE_FAILED, updateOfferingResp.status);
        }
        if (offeringDetails.isFinalSubmission) {
          updateOfferingResp = await OfferingService.updateOfferingDetails({ status: offeringStatusEnum.REVIEW }, { _id: offeringId });
          await new Promise((resolve, reject) => {
            userClient.client.sendOffering({ data: JSON.stringify(updateOfferingResp.data) }, async (error: any, response: PromiseResolve) => {
              if (error || response?.error) {
                const errorMessage = error?.message || response?.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR;
                const errorStatus = error?.status || RESPONSES.INTERNAL_SERVER_ERROR;
                await OfferingService.updateOfferingDetails({ status: offeringStatusEnum.IN_PROGRESS, isFinalSubmission: false, currentStep: 4 }, { _id: offeringId });
                logger.error('gRPC Error:', error || response);
                return reject(new CustomError(errorMessage, errorStatus));
              }

              await OfferingService.requestOffering({ status: offeringStatusEnum.REVIEW }, { _id: offeringId });
              resolve(response);
            });
          });
        }
      }

      const getFileType = (url: string): string => {
        const extension = url.split('.').pop()?.toLowerCase();
        return extension;
      };
      const document = updateOfferingResp?.data?.documents;

      let formattedDocuments: any[] = []; // Ensure it's always an array

      if (document) {
        formattedDocuments = offeringDocs
          .map((doc) => {
            const url = document[doc?.name];
            return url
              ? {
                  title: doc.title,
                  url: url,
                  type: getFileType(url),
                }
              : null;
          })
          .filter(Boolean); // Remove null/undefined values

        if (document.customDocs && document.customDocs.length > 0) {
          document.customDocs.forEach((customDoc: { docsLabel: string; value: string }) => {
            formattedDocuments.push({
              title: customDoc.docsLabel,
              url: customDoc.value,
              type: getFileType(customDoc.value),
            });
          });
        }
      }

      updateOfferingResp.data = {
        ...JSON.parse(JSON.stringify(updateOfferingResp.data)), // Deep clone to prevent mutation
        document: formattedDocuments, // Replace documents
      };

      // Return the response with updated data
      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.OFFERING_CREATE_SUCCESS,
        data: updateOfferingResp?.data,
      });
    } catch (error) {
      logger.error(error, 'createOffering Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Retrieves a paginated list of offerings with optional filtering and sorting.
   * Used for admin dashboard to manage offerings.
   *
   * @param {Request} req Express request object containing pagination, filter, and sort parameters
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to a paginated list of offerings
   * @memberof OfferingController
   */
  public getOfferingListController = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, sort, userId = '', status = '', search = '', isNft } = req.query;

      if (userId && !Types.ObjectId.isValid(userId as string)) {
        return ResponseHandler.error(res, {
          message: RES_MSG.COMMON.NO_FOUND,
          status: RESPONSES.BAD_REQUEST,
          error: true,
        });
      }
      const userIdObj = userId ? new Types.ObjectId(userId as string) : null;
      const filters = {
        ...(status && { status: status }),
        ...(isNft && { isNft: isNft }),
        ...(userIdObj && { userId: userIdObj }),
      };
      const userDetails: PromiseResolve = await UserService.fetchUserDetails({ _id: userIdObj }, ['name', 'email', 'userImage'], ['_id']);
      const projection = [
        'overview.title',
        'overview.subTitle',
        'overview.icon',
        'overview.logo',
        'overview.cover',
        'projectDetails.assetName',
        'projectDetails.blockChainType',
        'projectDetails.assetType',
        'projectDetails.offeringName',
        'tokenAddress',
        'fundAddress',
        'stakingAddress',
        'isStaking',
        'offeringFeeStatus',
        'createdAt',
        'isNft',
      ];
      const offeringList: PromiseResolve = await OfferingService.fetchOfferingList(filters, projection, {
        page: Number(page),
        limit: Number(limit),
        ...(search && { search: search }),
        ...(sort && { sort: JSON.parse(sort as string) }),
      });

      return ResponseHandler.success(res, {
        status: offeringList.status || RESPONSES.SUCCESS,
        error: false,
        message: offeringList.message || RES_MSG.USER.USERS_FETCH,
        data: {
          user: userDetails.data,
          ...offeringList.data,
        },
      });
    } catch (error) {
      logger.error(error, 'fetch Offering List Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Retrieves a list of offerings with specific formatting and filtering options.
   * Provides a customized view of offerings for different platform interfaces.
   *
   * @param {Request} req Express request object containing filter parameters
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to the formatted offering list
   * @memberof OfferingController
   */
  public getOfferingList = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, sort, userId = '', status = '', search = '', startDate = '', endDate = '', filterBy = '' } = req.query;

      if (userId && !Types.ObjectId.isValid(userId as string)) {
        return ResponseHandler.error(res, {
          message: RES_MSG.COMMON.NO_FOUND,
          status: RESPONSES.BAD_REQUEST,
          error: true,
        });
      }

      let parsedFilterBy = {};
      try {
        parsedFilterBy = filterBy;
      } catch (error) {
        return ResponseHandler.error(res, {
          message: error?.message || 'Invalid filterBy format. Expected a JSON object.',
          status: RESPONSES.BAD_REQUEST,
          error: true,
        });
      }

      const filters = {
        ...(status && { status }),
        ...(startDate && endDate
          ? {
              createdAt: {
                $gte: new Date(startDate as string),
                $lte: new Date(endDate as string),
              },
            }
          : startDate
          ? { createdAt: { $gte: new Date(startDate as string) } }
          : endDate
          ? { createdAt: { $lte: new Date(endDate as string) } }
          : {}),
        parsedFilterBy,
      };

      const projection = [
        'overview.title',
        'fee',
        'status',
        'overview.subTitle',
        'overview.icon',
        'overview.logo',
        'createdAt',
        'overview.cover',
        'projectDetails.assetName',
        'projectDetails.startDate',
        'projectDetails.endDate',
        'createdAt',
        'projectDetails.tokenTicker',
        'projectDetails.blockChainType',
        'projectDetails.assetType',
        'projectDetails.offeringName',
        'tokenAddress',
        'fundAddress',
        'stakingAddress',
        'isStaking',
        'erc20Address',
        'userId',
        'offeringFeeStatus',
        'iserc20',
        'category',
        'type',
        'creatorDetails',
        'bondingAddress',
        'isSchedule',
        'scheduleTime',
      ];

      const offeringList: PromiseResolve = await OfferingService.getOfferingList(filters, projection, {
        page: Number(page),
        limit: Number(limit),
        ...(search && { search: search }),
        ...(sort && { sort: JSON.parse(sort as string) }),
      });

      return ResponseHandler.success(res, {
        status: offeringList.status || RESPONSES.SUCCESS,
        error: false,
        message: offeringList.message || RES_MSG.USER.USERS_FETCH,
        data: {
          ...offeringList.data,
        },
      });
    } catch (error) {
      logger.error(error, 'fetch Offering List Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Schedule an offering with specific time
   * Only allows scheduling for APPROVED offerings
   * @param {Request} req Express request object containing _id, isSchedule, and scheduleTime
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to schedule result
   * @memberof OfferingController
   */
  public scheduleOffering = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { _id, isSchedule, scheduleTime } = req.body;
      // Check if offering exists
      const currentOffering = await OfferingService.fetchOfferingDetails({ _id });
      if (currentOffering.error) {
        throw new CustomError(currentOffering.message, currentOffering.status);
      }

      // Check if offering status is APPROVED
      if (currentOffering.data?.status === offeringStatusEnum.APPROVED) {
        throw new CustomError('Scheduling is not allowed for deployed offerings ', RESPONSES.BAD_REQUEST);
      } else if (currentOffering.data?.status !== offeringStatusEnum.PENDING) {
        throw new CustomError('Scheduling is allowed for pending offerings only', RESPONSES.BAD_REQUEST);
      }

      // Update only isSchedule and scheduleTime
      const updateData = {
        isSchedule,
        scheduleTime,
        _id,
      };

      // const updateResult = await OfferingService.updateOfferingDetails(updateData, { _id });
      // if (updateResult.error) {
      //   throw new CustomError(updateResult.message, updateResult.status);
      // }

      await new Promise((resolve, reject) => {
        userClient.client.scheduleOffering({ data: JSON.stringify(updateData) }, async (error: any, response: PromiseResolve) => {
          if (error || response?.error) {
            const errorMessage = error?.message || response?.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR;
            const errorStatus = error?.status || RESPONSES.INTERNAL_SERVER_ERROR;
            logger.error('gRPC Error:', error || response);
            return reject(new CustomError(errorMessage, errorStatus));
          }
          const updateResult = await OfferingService.updateOfferingDetails(updateData, { _id });
          if (updateResult.error) {
            throw new CustomError(updateResult.message, updateResult.status);
          }
          resolve(response);
        });
      });

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: 'Offering scheduled successfully',
        data: {
          _id,
          ...updateData,
        },
      });
    } catch (error: any) {
      logger.error(error, 'scheduleOffering Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Retrieves offerings associated with a specific issuer.
   * Filters offerings by issuer ID and provides detailed offering data.
   *
   * @param {Request} req Express request object containing issuer ID
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to issuer's offerings
   * @memberof OfferingController
   */
  public getIssuerOfferingController = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, sort, search = '', issuerStatus = '' } = req.query;
      const disallowedSymbolsRegex = /[!+#$%^*(),?":{}|<>]/;
      const validateSearchResult = await CommonHelper.validateStringRegex(search as string, disallowedSymbolsRegex, RES_MSG.ERROR_MSG.DISALLOWED_SYMBOLS);

      // If validation fails, return the error response immediately
      if (validateSearchResult.error) {
        return ResponseHandler.error(res, {
          status: validateSearchResult.status,
          error: true,
          message: validateSearchResult.message,
        });
      }
      const filters = {
        isKyc: true,
        isIssuer: true,
        ...(issuerStatus && { issuerStatus }),
      };

      const projection = ['name', 'email', '_id', 'wallets', 'userImage', 'offeringStatusCounts', 'createdAt'];

      const userDetails = await UserService.fetchUserListWithOfferings(filters, projection, {
        page: Number(page),
        limit: Number(limit),
        ...(search && { search: search }),
        ...(sort && { sort: JSON.parse(sort as string) }),
      });
      return ResponseHandler.success(res, {
        status: userDetails.status,
        error: false,
        message: RES_MSG?.SUCCESS_MSG.ISSUER_LIST,
        data: userDetails.data,
      });
    } catch (error: any) {
      logger.error(error, 'getIssuerOfferingController');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Retrieves a list of offerings that have been requested but not yet approved.
   * Provides data for admin review and approval workflows.
   *
   * @param {Request} req Express request object containing filter parameters
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to requested offerings list
   * @memberof OfferingController
   */
  public requestedOfferings = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, sort = '', search = '', status = '' } = req.query;
      const { userId, userType: userRole } = req.userInfo;
      const disallowedSymbolsRegex = /[!+#$%^*(),?":{}|<>]/;
      const validateSearchResult = await CommonHelper.validateStringRegex(search as string, disallowedSymbolsRegex, RES_MSG.ERROR_MSG.DISALLOWED_SYMBOLS);

      // If validation fails, return the error response immediately
      if (validateSearchResult.error) {
        return ResponseHandler.error(res, {
          status: validateSearchResult.status,
          error: true,
          message: validateSearchResult.message,
        });
      }
      // Build filters dynamically
      const filters: Record<string, any> = {
        ...(status && { status }),
      };
      if (userRole !== UserType.Admin) {
        filters.subAdminId = new Types.ObjectId(userId);
      }

      // Fetch requested offerings
      const userDetails = await OfferingService.requestedOfferings(filters, {
        page: Number(page),
        limit: Number(limit),
        ...(search && { search }),
        ...(sort && { sort }),
      });

      // Handle successful response
      return ResponseHandler.success(res, {
        status: userDetails.status || RESPONSES.SUCCESS,
        error: false,
        message: userDetails.message || RES_MSG.USER.USERS_FETCH,
        data: userDetails.data || [],
      });
    } catch (error) {
      // Log the error and send error response
      logger.error(error, 'Error in requestedOfferings');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Exports requested offerings data to CSV format.
   * Retrieves filtered requested offering data and formats it for CSV download.
   *
   * @param {Request} req Express request object containing filter parameters
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to CSV export data
   * @memberof OfferingController
   */
  public requestedOfferingsCsv = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { status = '' } = req.query; // Removed pagination and search
      const { userId, userType: userRole } = req.userInfo;

      // Build filters dynamically
      const filters: Record<string, any> = {
        ...(status && { status }),
      };
      if (userRole !== UserType.Admin) {
        filters.subAdminId = new Types.ObjectId(userId);
      }

      // Fetch requested offerings
      const userDetails = await OfferingService.requestedOfferingsCsv(filters);

      // Handle successful response
      return ResponseHandler.success(res, {
        status: userDetails.status || RESPONSES.SUCCESS,
        error: false,
        message: userDetails.message || RES_MSG.USER.USERS_FETCH,
        data: userDetails.data || [],
      });
    } catch (error) {
      // Log the error and send error response
      logger.error(error, 'Error in requestedOfferings');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Assigns offerings to specific entities such as transfer agents.
   * Updates offering assignment data and notifies relevant parties.
   *
   * @param {Request} req Express request object containing assignment details
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to assignment result
   * @memberof OfferingController
   */
  public assignOfferings = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { offeringId, subAdminId } = req.body;
      const userIdObj = subAdminId ? new Types.ObjectId(subAdminId as string) : null;

      const { error, status } = await UserService.fetchUserDetails({ _id: userIdObj });
      if (error) {
        throw new CustomError('Invalid sub admin id', status);
      }

      const isApproved = await requestedOfferingSchema.findOne({
        _id: new Types.ObjectId(offeringId),
        status: offeringStatusEnum.ASSIGNED,
      });
      if (isApproved) {
        throw new CustomError('Already assigned sub admin', RESPONSES.CONFLICT);
      }

      // Permission Check for Sub Admin
      if (subAdminId) {
        const permission: any = await permissionSchema.findOne({ userId: userIdObj });

        const OFFERINGS_MODULE_ID = new Types.ObjectId('672c70ce05e65803246ffc44'); // Offerings
        const TRANSFER_AGENT_MODULE_ID = new Types.ObjectId('672c70e305e65803246ffc45'); // Transfer Agent

        const hasOfferingsPermission = permission.permission.find((p: any) => p.moduelsId.equals(OFFERINGS_MODULE_ID) && p.write === true);

        if (!hasOfferingsPermission) {
          throw new CustomError('This sub admin does not have write permission for Offerings Module', RESPONSES.CONFLICT);
        }

        const hasTransferAgentPermission = permission.permission.find((p: any) => p.moduelsId.equals(TRANSFER_AGENT_MODULE_ID) && p.write === true);

        if (!hasTransferAgentPermission) {
          throw new CustomError('This sub admin does not have write permission for Transfer Agent Module', RESPONSES.CONFLICT);
        }
      }

      // Assign the offering
      const offeringDetails: PromiseResolve = await OfferingService.requestOffering(
        {
          subAdminId: subAdminId.toString(),
          status: offeringStatusEnum.ASSIGNED,
        },
        { _id: offeringId },
      );

      return ResponseHandler.success(res, {
        status: offeringDetails.status || RESPONSES.SUCCESS,
        error: false,
        message: offeringDetails.message || RES_MSG.USER.USERS_FETCH,
        data: offeringDetails.data || [],
      });
    } catch (error) {
      logger.error(error, 'assignedOfferings Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Checks the dividend distribution status for an offering.
   * Verifies if dividend payments have been processed and their current state.
   *
   * @param {Request} req Express request object containing offering ID
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to dividend status data
   * @memberof OfferingController
   */
  public checkDividendStatus = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { offeringId } = req.query;

      const payload = { _id: offeringId.toString() };

      userClient.client.checkDividendStatus(payload, (error: any, response: any) => {
        if (error) {
          logger.error('gRPC Error:', error);
          return ResponseHandler.error(res, {
            status: RESPONSES.INTERNAL_SERVER_ERROR,
            error: true,
            message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
          });
        }

        if (!response.success) {
          return ResponseHandler.error(res, {
            message: response.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
            status: RESPONSES.BAD_REQUEST,
            error: true,
          });
        }

        return ResponseHandler.success(res, {
          status: RESPONSES.SUCCESS,
          error: false,
          message: response.message || RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
          data: '',
        });
      });
    } catch (error) {
      logger.error(error, 'checkDividendStatus Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };
}
export default new OfferingController();
