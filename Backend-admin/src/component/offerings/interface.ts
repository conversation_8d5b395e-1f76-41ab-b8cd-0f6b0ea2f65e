import { FilterQuery } from 'mongoose';
import { IPagination, PromiseResolve } from '../../utils/common.interface';
import { IOffering, IUpdateOffering } from './models/offerings.model';
import { IUpdateWhitelist, IWhitelist } from './models/whitelist.model';
import { IOrder, IUpdateOrder } from './models/order.model';
import { IRequestedOffering } from './models/requestedOfferings.model';

export interface IOfferingsService {
  /**
   * @param {IOffering} searchDetails
   * @param {IOffering} fields
   * @param {IOffering} excludeFields
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */

  fetchOfferingDetails(searchDetails: FilterQuery<IOffering>, fields?: string[], excludeFields?: string[]): Promise<PromiseResolve>;

  /**
   * @param {IRequestedOffering} searchDetails

   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */

  fetchAssignedOfferingDetails(searchDetails: FilterQuery<IRequestedOffering>): Promise<PromiseResolve>;

  /**
   * @param {IUpdateOffering} body
   * @param {IOffering} filter
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  updateOfferingDetails(body: IUpdateOffering, filter: FilterQuery<IOffering>): Promise<PromiseResolve>;
  updateWhitelistDetails(body: IUpdateWhitelist, filter: FilterQuery<IWhitelist>): Promise<PromiseResolve>;
  createWhitelistDetails(body: IUpdateWhitelist, filter: FilterQuery<IWhitelist>): Promise<PromiseResolve>;
  // createWhitelistDetails(body: IWhitelist, filter: FilterQuery<IWhitelist>): Promise<PromiseResolve>;

  updateorderDetails(body: IUpdateOrder, filter: FilterQuery<IOrder>): Promise<PromiseResolve>;

  offeringReport(searchDetails: IUpdateOffering): Promise<PromiseResolve>;

  /**
   * @param {IUpdateOffering} searchDetails
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  /**
   * @param {IUpdateOffering} body
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  createOffering(body: IUpdateOffering): Promise<PromiseResolve>;

  /**
   * @param {IOffering} searchDetails
   * @param {Array} projection
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */

  fetchOfferingList(searchDetails: any, projection?: string[], pagination?: IPagination): Promise<PromiseResolve>;

  getOfferingList(searchDetails: any, projection?: string[], pagination?: IPagination): Promise<PromiseResolve>;

  dashboardList(searchDetails: any, projection?: string[], pagination?: IPagination): Promise<PromiseResolve>;
  /**
   * @param {IRequestedOffering} body
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  requestOffering(body: IRequestedOffering, filter: FilterQuery<IRequestedOffering>): Promise<PromiseResolve>;

  /**
   * @param {IRequestedOffering} filters
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  requestedOfferings(filters: any, pagination: IPagination): Promise<PromiseResolve>;
  requestedOfferingsCsv(filters: any): Promise<PromiseResolve>;
}
