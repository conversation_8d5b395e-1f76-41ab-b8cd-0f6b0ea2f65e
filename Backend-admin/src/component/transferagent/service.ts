import { ITransferAgentService } from './interface';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { PromiseResolve } from '../../utils/common.interface';
import { FilterQuery } from 'mongoose';
import logger from '../../helpers/logging/logger.helper';
import { ITransferAgent, transferAgentSchema, IUpdateTransferAgent } from './transferagent.model';
import { IPagination } from '../../utils/common.interface';
import { Types } from 'mongoose';

const seed = process.env.SEED;

import * as bcrypt from 'bcrypt';
class TransferService implements ITransferAgentService {
  /**
   * @param {IOffering} searchDetails
   * @param {IOffering} fields
   * @param {IOffering} excludeFields
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  async fetchOfferingDetails(searchDetails: FilterQuery<ITransferAgent>, fields?: string[], excludeFields?: string[]): Promise<PromiseResolve> {
    try {
      const query = transferAgentSchema.findOne(searchDetails);
      if (fields && fields.length > 0) {
        const fieldsString = fields.join(' ');
        query.select(fieldsString);
      }

      if (excludeFields && excludeFields.length > 0) {
        const excludeFieldsString = excludeFields.map((field) => `-${field}`).join(' ');
        query.select(excludeFieldsString);
      }

      const offeringDetails: ITransferAgent | null = await query.exec();

      if (offeringDetails) {
        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
          data: offeringDetails.toObject(),
        };
      } else {
        return {
          status: RESPONSES.NOTFOUND,
          error: true,
          message: RES_MSG.COMMON.NO_OFFERING,
        };
      }
    } catch (error) {
      logger.error(error, 'fetchOfferingDetails error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * @param {IOffering} filter
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  async updateOfferingDetails(noOfOfferings: IUpdateTransferAgent, filter: FilterQuery<ITransferAgent>): Promise<PromiseResolve> {
    try {
      const updateUserResp = await transferAgentSchema.findOneAndUpdate(filter, noOfOfferings, {
        new: false,
        runValidators: true,
        upsert: true,
      });
      if (updateUserResp) {
        // if (updateUserResp.isFinalSubmission) {
        //     await kafkaService.sendMessageToAdmin({ value: JSON.stringify({ ...updateUserResp, type: 'offering' }) });
        // }
        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.USER.USER_UPDATION_SUCCESS,
          data: updateUserResp,
        };
      }

      return {
        status: RESPONSES.BAD_REQUEST,
        error: true,
        message: RES_MSG.ERROR_MSG.USER_UPDATION_ERROR,
      };
    } catch (error) {
      logger.error(error, 'updateOfferingDetails error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * @param {I} data
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  async createOffering(data: IUpdateTransferAgent): Promise<PromiseResolve> {
    try {
      // const seed = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()"; // Example seed string
      const length = 12; // Password length (you can adjust this)

      // Ensure seed and length are defined properly
      if (!seed || seed.length === 0) {
        throw new Error('Seed for password generation is not defined or empty.');
      }

      // Ensure password length is at least 8
      const passwordLength = length >= 8 ? length : 8; // If the provided length is less than 8, use 8

      let password = '';

      // Generate password
      for (let i = 0; i < passwordLength; i++) {
        password += seed.charAt(Math.floor(Math.random() * seed.length));
      }

      // Hash the generated password using bcrypt
      const hashedPassword = await bcrypt.hash(password, 10);

      const newUser = {
        ...data,
        password: hashedPassword,
      };
      const query = await transferAgentSchema.create(newUser);

      if (query) {
        const userObject = query.toObject();
        userObject.password = password;
        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.USER.TA_SUCCESS,
          data: userObject,
        };
      }

      return {
        status: RESPONSES.BAD_REQUEST,
        error: true,
        message: RES_MSG.ERROR_MSG.USER_UPDATION_ERROR,
      };
    } catch (error) {
      logger.error(error, 'updateOfferingDetails error');
      if (error.code === 11000) {
        return { status: RESPONSES.BAD_REQUEST, error: true, message: RES_MSG.USER.USER_ALREADY_EXIST };
      }
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async fetchTransferAgentList(filters: any, projection: any[], pagination: IPagination): Promise<PromiseResolve> {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 }, search } = pagination;
      const skip = (page - 1) * limit;
      const searchQuery: FilterQuery<any> = {};

      const addFilter = (key: keyof IUpdateTransferAgent, value: any) => {
        if (typeof value !== 'undefined') searchQuery[key] = value;
      };

      if (search) {
        searchQuery.$or = [{ name: { $regex: search, $options: 'i' } }, { email: { $regex: search, $options: 'i' } }];
      }

      const projectionObject = projection.reduce((acc: any, field: string) => ({ ...acc, [field]: 1 }), {});

      addFilter('isActive', filters.isActive);
      addFilter('status', filters.status);

      const pipeline = [
        {
          $match: searchQuery,
        },
        {
          $lookup: {
            from: 'offerings',
            let: { taId: { $toString: '$_id' } }, // Convert `_id` to string (if needed)
            pipeline: [
              {
                $match: {
                  isDelete: false,
                  $expr: { $eq: ['$projectDetails.taId', '$$taId'] }, // Match using `$expr`
                },
              },
            ],
            as: 'offerings',
          },
        },
        {
          $addFields: {
            offeringStatusCounts: {
              PENDING: {
                $size: {
                  $filter: {
                    input: '$offerings',
                    as: 'offering',
                    cond: { $eq: ['$$offering.status', 'PENDING'] },
                  },
                },
              },
              REJECTED: {
                $size: {
                  $filter: {
                    input: '$offerings',
                    as: 'offering',
                    cond: { $eq: ['$$offering.status', 'REJECTED'] },
                  },
                },
              },
              APPROVED: {
                $size: {
                  $filter: {
                    input: '$offerings',
                    as: 'offering',
                    cond: { $eq: ['$$offering.status', 'APPROVED'] },
                  },
                },
              },
              RESUBMIT: {
                $size: {
                  $filter: {
                    input: '$offerings',
                    as: 'offering',
                    cond: { $eq: ['$$offering.status', 'RESUBMIT'] },
                  },
                },
              },
            },
          },
        },
        {
          $addFields: {
            noOfOfferings: {
              $add: ['$offeringStatusCounts.PENDING', '$offeringStatusCounts.REJECTED', '$offeringStatusCounts.APPROVED', '$offeringStatusCounts.RESUBMIT'],
            },
          },
        },
        { $sort: sort }, // Sort the result based on the given sort parameter
        { $skip: skip }, // Skip to implement pagination
        { $limit: limit }, // Limit the result based on pagination
      ];

      // Clone the pipeline to count total users after filtering
      const countPipeline = [...pipeline, { $count: 'totalCount' }];
      // Execute both queries
      const [userWithOfferings, totalCountResult] = await Promise.all([
        transferAgentSchema.aggregate([...pipeline, { $project: { ...projectionObject, offeringStatusCounts: 1 } }, { $sort: { createdAt: -1 } }, { $skip: skip }, { $limit: limit }]),
        transferAgentSchema.aggregate(countPipeline),
      ]);

      const totalCount = totalCountResult.length > 0 ? totalCountResult[0].totalCount : 0;
      const totalPages = Math.ceil(totalCount / limit);

      return {
        data: {
          users: userWithOfferings,
          currentPage: page,
          totalPages,
          totalCount,
          nextPage: page < totalPages ? page + 1 : null,
          previousPage: page > 1 ? page - 1 : null,
        },
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      logger.error(error, 'fetchUserListWithOfferings error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  async fetchUserListcsv(filters: IUpdateTransferAgent, projection: any[]): Promise<PromiseResolve> {
    try {
      const sort = { createdAt: -1 };
      const query: FilterQuery<any> = {};
      const addFilter = (key: keyof IUpdateTransferAgent, value: any) => {
        if (typeof value !== 'undefined') query[key] = value;
      };

      query.email = {
        $ne: process.env.EMAIL,
      };

      addFilter('createdAt', filters.createdAt);
      const searchQuery: FilterQuery<any> = {};

      const projectionObject = projection.reduce((acc: any, field: string) => ({ ...acc, [field]: 1 }), {});

      addFilter('isActive', filters.isActive);
      addFilter('status', filters.status);

      const pipeline: any = [
        {
          $match: searchQuery,
        },
        {
          $lookup: {
            from: 'offerings',
            let: { taId: { $toString: '$_id' } }, // Convert `_id` to string (if needed)
            pipeline: [
              {
                $match: {
                  isDelete: false,
                  $expr: { $eq: ['$projectDetails.taId', '$$taId'] }, // Match using `$expr`
                },
              },
            ],
            as: 'offerings',
          },
        },
        {
          $addFields: {
            offeringStatusCounts: {
              PENDING: {
                $size: {
                  $filter: {
                    input: '$offerings',
                    as: 'offering',
                    cond: { $eq: ['$$offering.status', 'PENDING'] },
                  },
                },
              },
              REJECTED: {
                $size: {
                  $filter: {
                    input: '$offerings',
                    as: 'offering',
                    cond: { $eq: ['$$offering.status', 'REJECTED'] },
                  },
                },
              },
              APPROVED: {
                $size: {
                  $filter: {
                    input: '$offerings',
                    as: 'offering',
                    cond: { $eq: ['$$offering.status', 'APPROVED'] },
                  },
                },
              },
              RESUBMIT: {
                $size: {
                  $filter: {
                    input: '$offerings',
                    as: 'offering',
                    cond: { $eq: ['$$offering.status', 'RESUBMIT'] },
                  },
                },
              },
            },
          },
        },
        {
          $addFields: {
            noOfOfferings: {
              $add: ['$offeringStatusCounts.PENDING', '$offeringStatusCounts.REJECTED', '$offeringStatusCounts.APPROVED', '$offeringStatusCounts.RESUBMIT'],
            },
          },
        },
        {
          $sort: sort,
        },
      ];

      // Clone the pipeline to count total users after filtering
      const countPipeline = [...pipeline, { $count: 'totalCount' }];
      // Execute both queries
      const [userWithOfferings] = await Promise.all([transferAgentSchema.aggregate([...pipeline, { $project: { ...projectionObject, offeringStatusCounts: 1 } }]), transferAgentSchema.aggregate(countPipeline)]);

      return {
        data: {
          user: userWithOfferings,
        },
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      throw new Error(`Error fetching users: ${error.message}`);
    }
  }

  /**
   * @param {IOffering} filters
   * @param {any[]} projection
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  async fetchOfferingList(filters: ITransferAgent, projection: any[], pagination: IPagination): Promise<PromiseResolve> {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 }, search } = pagination;
      const skip = (page - 1) * limit;
      const query: FilterQuery<any> = {};

      const addFilter = (key: keyof ITransferAgent, value: any) => {
        if (typeof value !== 'undefined') query[key] = value;
      };

      if (search) {
        query.$or = [{ title: { $regex: search, $options: 'i' } }, { subTitle: { $regex: search, $options: 'i' } }];
      }

      addFilter('isActive', true);
      addFilter('status', filters.status);
      // addFilter('userId', filters.userId);

      const totalCount = await transferAgentSchema.countDocuments(query).exec();

      const users = await transferAgentSchema.find(query).select(projection).sort(sort).skip(skip).limit(limit).exec();

      const totalPages = Math.ceil(totalCount / limit);

      return {
        data: {
          offering: users,
          currentPage: page,
          totalPages,
          totalCount,
          nextPage: page < totalPages ? page + 1 : null,
          previousPage: page > 1 ? page - 1 : null,
        },
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
      };
    } catch (error) {
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }

  /**
   * @param {string} taId
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  async fetchTaDetails(taId: string): Promise<PromiseResolve> {
    try {
      // Convert taId to Mongo ObjectId
      const objectId = new Types.ObjectId(taId);
      // Find the Transfer Agent details, excluding the password field
      const taDetails = await transferAgentSchema.findOne({ _id: objectId }).select('-password').lean(); // Use lean() for plain JS object

      if (taDetails) {
        return {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
          data: taDetails,
        };
      } else {
        return {
          status: RESPONSES.NOTFOUND,
          error: true,
          message: RES_MSG.COMMON.NO_OFFERING,
        };
      }
    } catch (error) {
      logger.error(error, 'fetchTaDetails error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  }
}

export default new TransferService();
