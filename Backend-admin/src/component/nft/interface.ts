import { FilterQuery } from 'mongoose';
import { IPagination, PromiseResolve } from '../../utils/common.interface';

export interface INftCollectionService {
  /**
   * @param {EventLog} searchDetails
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  fetchCollection(searchDetails: FilterQuery<any>, pagination?: IPagination): Promise<PromiseResolve>;
}
