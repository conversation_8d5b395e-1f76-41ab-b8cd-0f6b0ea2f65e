import { Request, Response } from 'express';
import { Types } from 'mongoose';
import { PromiseResolve } from '../../utils/common.interface';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { ResponseHandler } from '../../helpers/response.helper';
import logger from '../../helpers/logging/logger.helper';
import CollectionService from './service';

class NftCollectionController {
  /**
   * Retrieves a paginated list of NFT collections for a specific user.
   * Supports filtering by active status and searching within collections.
   * Used for displaying user's NFT collections in the interface.
   *
   * @param {Request} req Express request object containing pagination, search, and sort parameters
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve|void>} A promise that resolves to filtered collection data
   * @memberof NftCollectionController
   */
  public getCollection = async (req: Request, res: Response): Promise<PromiseResolve | void> => {
    try {
      const { page = 1, limit = 10, sort = '', search = '' } = req.query;
      const userId = new Types.ObjectId(req.userInfo.userId);

      const filters = { isActive: true, userId };

      const result: PromiseResolve = await CollectionService.fetchCollection(filters, { page: Number(page), limit: Number(limit), ...(search && { search }), ...(sort && { sort }) });

      return ResponseHandler.success(res, { status: result.status || RESPONSES.SUCCESS, error: false, message: result.message || RES_MSG.USER.USERS_FETCH, data: result.data || [] });
    } catch (error) {
      logger.error(error, 'getCollection Error');

      return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
    }
  };

  /**
   * Updates the status of a specific NFT in the collection.
   * Used to activate or deactivate NFTs based on business rules.
   *
   * @param {Request} req Express request object containing NFT ID
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve|void>} A promise that resolves to NFT status update result
   * @memberof NftCollectionController
   */
  public updateNFTStatus = async (req: Request, res: Response): Promise<PromiseResolve | void> => {
    try {
      const { nftId } = req.query;
      // const userId = new Types.ObjectId(req.userInfo.userId);

      const result: PromiseResolve = await CollectionService.updateNFTStatus(nftId);

      return ResponseHandler.success(res, { status: result.status || RESPONSES.SUCCESS, error: false, message: result.message || RES_MSG.USER.USERS_FETCH, data: result.data || [] });
    } catch (error) {
      logger.error(error, 'updateNFTStatus Error');

      return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
    }
  };

  /**
   * Retrieves detailed information about a specific NFT by its ID.
   * Used for viewing individual NFT details and metadata.
   *
   * @param {Request} req Express request object containing NFT ID
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve|void>} A promise that resolves to NFT details
   * @memberof NftCollectionController
   */
  public getNftById = async (req: Request, res: Response): Promise<PromiseResolve | void> => {
    try {
      const { nftId } = req.query;

      const result: PromiseResolve = await CollectionService.getNftById(nftId);

      return ResponseHandler.success(res, { status: result.status || RESPONSES.SUCCESS, error: false, message: result.message || RES_MSG.USER.USERS_FETCH, data: result.data || [] });
    } catch (error) {
      logger.error(error, 'updateNFTStatus Error');

      return ResponseHandler.error(res, { status: error.status || RESPONSES.INTERNAL_SERVER_ERROR, error: true, message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR });
    }
  };
}

export default new NftCollectionController();
