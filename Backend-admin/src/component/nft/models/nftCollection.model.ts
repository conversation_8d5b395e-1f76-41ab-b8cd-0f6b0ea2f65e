import mongoose, { Schema, Types } from 'mongoose';
import { nftStatusEnum } from '../../../utils/common.interface';

export interface INftCollection {
  _id?: Types.ObjectId;
  collectionSymbol: string;
  paymentMethod: 'usdc' | 'usdt';
  description: string;
  txHash: string;
  userId: Types.ObjectId;
  isActive: boolean;
  isDelete: boolean;
  isDeploy: boolean;
  status?: nftStatusEnum;
  createdAt?: Date;
  updatedAt?: Date;
}

const NftCollection = new Schema<INftCollection>(
  {
    collectionSymbol: { type: String, required: true },
    paymentMethod: { type: String, enum: ['usdc', 'usdt'], required: true },
    description: { type: String, required: true },
    txHash: { type: String, required: false, unique: true, sparse: true },
    userId: { type: Schema.Types.ObjectId, ref: 'users', required: true },
    isActive: { type: Boolean, default: true },
    isDelete: { type: Boolean, default: false },
    isDeploy: { type: Boolean, default: false },
    status: { type: String, enum: Object.values(nftStatusEnum), default: nftStatusEnum.PENDING },
  },
  { timestamps: true, versionKey: false },
);

const nftCollectionSchema = mongoose.model<INftCollection>('nftcollections', NftCollection);
export { nftCollectionSchema };
