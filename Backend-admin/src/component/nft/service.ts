import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { PromiseResolve, IPagination } from '../../utils/common.interface';
import { INftCollectionService } from './interface';
import logger from '../../helpers/logging/logger.helper';
import mongoose, { FilterQuery } from 'mongoose';
import { INftCollection, nftCollectionSchema } from './models/nftCollection.model';
import userClient from '../../_grpc/clients/user.client';
import { offeringSchema } from '../offerings/models/offerings.model';
class NftCollectionService implements INftCollectionService {
  createNft = async (valueWithId: any): Promise<PromiseResolve> => {
    try {
      // Validate input
      if (!valueWithId || typeof valueWithId !== 'object') {
        throw { status: RESPONSES.BAD_REQUEST, message: 'Invalid NFT data' };
      }

      // Insert data into the database
      const query = await offeringSchema.create(valueWithId);

      // Return success response
      return { status: RESPONSES.SUCCESS, error: false, message: 'NFT created successfully', data: query };
    } catch (error) {
      logger.error(error, 'createNft error');

      // Handle specific database errors
      if (error.name === 'SequelizeValidationError') {
        return { status: RESPONSES.BAD_REQUEST, error: true, message: 'Validation failed', data: error.errors };
      }
      if (error.name === 'SequelizeUniqueConstraintError') {
        return { status: RESPONSES.CONFLICT, error: true, message: 'Duplicate entry', data: error.errors };
      }

      // Default error response
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  fetchCollection = async (filters: FilterQuery<INftCollection>, pagination: IPagination): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, search } = pagination;
      const skip = (page - 1) * limit;
      const sort = { updatedAt: -1 };

      const match: FilterQuery<INftCollection> = {};

      if (search) {
        match.$or = [{ collectionSymbol: { $regex: search, $options: 'i' } }, { description: { $regex: search, $options: 'i' } }];
      }

      if (filters.paymentMethod) {
        match.paymentMethod = filters.paymentMethod;
      }

      if (filters.isActive !== undefined) {
        match.isActive = filters.isActive;
      }

      if (filters.isDelete !== undefined) {
        match.isDelete = filters.isDelete;
      }

      const pipeline = [
        { $match: match },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'userDetails',
          },
        },
        { $unwind: { path: '$userDetails', preserveNullAndEmptyArrays: true } },
        {
          $addFields: {
            userName: '$userDetails.name',
            userEmail: '$userDetails.email',
            userImage: '$userDetails.userImage',
          },
        },
        { $project: { userDetails: 0 } },
        {
          $facet: {
            metadata: [{ $count: 'totalCount' }],
            collections: [{ $skip: skip }, { $limit: limit }, { $sort: sort }],
          },
        },
      ];

      const results = await nftCollectionSchema.aggregate(pipeline as any);
      const metadata = results[0]?.metadata[0] || { totalCount: 0 };
      const collections = results[0]?.collections || [];
      const { totalCount } = metadata;
      const totalPages = Math.ceil(totalCount / limit);
      const nextPage = page < totalPages ? page + 1 : null;
      const previousPage = page > 1 ? page - 1 : null;

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.COMMON.RECORD_FETCH,
        data: {
          collections,
          currentPage: page,
          totalPages,
          totalCount,
          nextPage,
          previousPage,
        },
      };
    } catch (error) {
      logger.error(error, 'fetchCollection error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  updateNFTStatus = async (nftId: any): Promise<any> => {
    if (!nftId) {
      return {
        status: RESPONSES.BAD_REQUEST,
        error: true,
        message: 'NFT ID is required.',
      };
    }

    try {
      const getNft = await offeringSchema.findById({ _id: nftId, isNft: true }).lean();
      if (!getNft) {
        return {
          status: RESPONSES.NOTFOUND,
          error: true,
          message: 'NFT not found.',
        };
      }

      const getCollection = await nftCollectionSchema.findById(getNft.collectionId).lean();
      if (!getCollection) {
        return {
          status: RESPONSES.NOTFOUND,
          error: true,
          message: 'Collection not found.',
        };
      }

      if (!getCollection.isDeploy) {
        return {
          status: RESPONSES.BAD_REQUEST,
          error: true,
          message: 'Collection is not deployed yet. You cannot update the NFT status.',
        };
      }

      if (getNft.status === 'APPROVED') {
        return {
          status: RESPONSES.BAD_REQUEST,
          error: true,
          message: 'NFT is already deployed.',
        };
      }

      // Call gRPC service
      const grpcResponse = await this.updateNftStatusGrpc(nftId);

      if (grpcResponse.status !== '200') {
        return {
          status: RESPONSES.INTERNAL_SERVER_ERROR,
          error: true,
          message: 'gRPC failed to update NFT status.',
        };
      }

      // Update NFT status only if gRPC is successful
      await offeringSchema.updateOne({ _id: nftId, isNft: true }, { $set: { status: 'APPROVED' } });

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: 'NFT status updated successfully.',
      };
    } catch (error) {
      logger.error(error, 'updateNFTStatus error');
      return {
        status: RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: 'An unexpected error occurred while updating NFT status.',
      };
    }
  };

  updateNftStatusGrpc = async (nftId: string): Promise<any> => {
    return new Promise((resolve, reject) => {
      userClient.client.updateNftStatus({ id: nftId }, async (error: any, response: any) => {
        if (error) {
          logger.error(error, 'gRPC updateNftStatus error');
          return reject({
            status: RESPONSES.INTERNAL_SERVER_ERROR,
            error: true,
            message: 'Failed to update NFT status via gRPC',
          });
        }

        if (!response) {
          return reject({
            status: RESPONSES.INTERNAL_SERVER_ERROR,
            error: true,
            message: 'gRPC updateNftStatus returned an empty response',
          });
        }

        resolve(response);
      });
    });
  };

  updateCollection = async (collectionId: any, updateData: Partial<INftCollection>): Promise<PromiseResolve> => {
    try {
      const _id = new mongoose.Types.ObjectId(collectionId?._id);
      const updatedCollection = await nftCollectionSchema.findByIdAndUpdate(_id, { $set: updateData }, { new: true, upsert: true });
      if (!updatedCollection) {
        return {
          status: RESPONSES.NOTFOUND,
          error: true,
          message: 'NFT Collection not found',
        };
      }
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: 'NFT Collection updated successfully',
        data: updatedCollection,
      };
    } catch (error) {
      logger.error(error, 'updateCollection error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  getNftById = async (nftId: any): Promise<any> => {
    if (!nftId) {
      return {
        status: RESPONSES.BAD_REQUEST,
        error: true,
        message: 'NFT ID is required.',
      };
    }

    try {
      const pipeline: any[] = [
        { $match: { _id: new mongoose.Types.ObjectId(nftId), isNft: true } },
        {
          $lookup: {
            from: 'nftcollections',
            localField: 'collectionId',
            foreignField: '_id',
            as: 'collection',
          },
        },
        { $unwind: { path: '$collection', preserveNullAndEmptyArrays: true } },
        {
          $lookup: {
            from: 'users',
            localField: 'creatorId',
            foreignField: '_id',
            as: 'creatorDetails',
          },
        },
        { $unwind: { path: '$creatorDetails', preserveNullAndEmptyArrays: true } },
        {
          $project: {
            _id: 1,
            image: 1,
            name: 1,
            nftDescription: 1,
            externalLink: 1,
            creatorId: 1,
            royalty: 1,
            currency: 1,
            traits: 1,
            floorPrice: 1,
            launchDate: 1,
            launchTime: 1,
            category: 1,
            type: 1,
            collectionId: 1,
            team: 1,
            status: 1,
            isNft: 1,
            'collection._id': 1,
            'collection.collectionSymbol': 1,
            'collection.description': 1,
            'collection.isActive': 1,
            'collection.isDeploy': 1,
            'creatorDetails._id': 1,
            'creatorDetails.name': 1,
            'creatorDetails.email': 1,
            'creatorDetails.userImage': 1,
            'creatorDetails.wallets': 1,
          },
        },
      ];

      const nfts = await offeringSchema.aggregate(pipeline).exec();

      if (!nfts.length) {
        return {
          status: RESPONSES.NOTFOUND,
          error: true,
          message: 'NFT not found.',
        };
      }

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: 'NFT retrieved successfully.',
        data: nfts[0],
      };
    } catch (error) {
      logger.error(error, 'getNftById error');
      return {
        status: RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: 'An unexpected error occurred while retrieving NFT.',
      };
    }
  };
}

export default new NftCollectionService();
