import { JoiValidationResult } from '../../utils/common.interface';
import * as joiOptions from '../../helpers/joi.helper';
import { RES_MSG, RESPONSES } from '../../utils/responseUtils';
import logger from '../../helpers/logging/logger.helper';

class NftCollectionValidation {
  /**
   * Collection list Validate .
   * @returns {Promise<JoiValidationResult>}
   */
  async getNftCollectionValidation(params: Record<string, any>): Promise<JoiValidationResult> {
    try {
      const schema = joiOptions.paginationSchema; // Disallow any keys not explicitly defined in the schema

      const { error, value } = schema.validate(params, joiOptions.options);

      if (error) {
        return {
          error: true,
          value: '',
          message: error.details[0].message, // Provide specific error message
          status: RESPONSES.BAD_REQUEST,
        };
      }

      return {
        error: false,
        value, // Return the validated value
      };
    } catch (error) {
      logger.error(error, 'getNftCollectionValidation Error');

      return {
        error: true,
        value: '',
        message: RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, // General error message
        status: RESPONSES.INTERNAL_SERVER_ERROR,
      };
    }
  }
}

export default new NftCollectionValidation();
