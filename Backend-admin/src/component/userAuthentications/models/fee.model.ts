import mongoose, { Schema, Document } from 'mongoose';

export interface feemodel extends Document {
  _id: string;
  fee?: {
    escrowFee?: number;
    wrapFee?: number;
    dividendFee?: number;
    redemptionFee?: number;
  };
  timeStamp: any;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IUpdatefeemodel {
  _id: string;
  fee?: {
    escrowFee?: number;
    wrapFee?: number;
    dividendFee?: number;
    redemptionFee?: number;
  };
  timeStamp: any;
  createdAt?: Date;
  updatedAt?: Date;
}

const feemodel: Schema<feemodel> = new Schema(
  {
    _id: { type: String, required: true },
    fee: {
      escrowFee: { type: Number, required: false, default: 0 },
      wrapFee: { type: Number, required: false, default: 0 },
      dividendFee: { type: Number, required: false, default: 0 },
      redemptionFee: { type: Number, required: false, default: 0 },
    },
    timeStamp: { type: Number, required: true },
  },
  { versionKey: false, timestamps: true },
);

const feeSchema = mongoose.model<feemodel>('feemodel', feemodel);

export { feeSchema };
