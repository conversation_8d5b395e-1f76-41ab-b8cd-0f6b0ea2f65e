/* eslint-disable no-case-declarations */
import { Request, Response } from 'express';
import { DocumentFolderTypesEnum, IssuerStatus, KycStatus, orderStatusEnum, PromiseResolve, UserType } from '../../utils/common.interface';
import UserService from './service';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { IUserModel, userSchema } from './models/user.model';
import CustomError from '../../helpers/customError.helper';
import RedisHelper from '../../helpers/redis.helper';
import { ResponseHandler } from '../../helpers/response.helper';
import CommonHelper from '../../helpers/common.helper';
import * as bcrypt from 'bcrypt';
import * as geoip from 'geoip-lite';
import { authenticator } from 'otplib';
import { toDataURL } from 'qrcode';
import CONFIG from '../../config/env';
import logger from '../../helpers/logging/logger.helper';
import { otpType } from '../../utils/common.interface';
import userClient from '../../_grpc/clients/user.client';
import mongoose from 'mongoose';
import emailHelper from '../../helpers/email.helper';
import OfferingService from '../offerings/service';
import { OrderSchema } from '../offerings/models/order.model';
import { Types } from 'mongoose';
import CloudHelper from '../../helpers/cloud.helper';
import * as moment from 'moment';
import { feeSchema } from './models/fee.model';

const kycCount = Number(process.env.KYCCOUNT) || 3;
const walletAddress = process.env.ADMINADDRESS;

class UserAuthenticationController {
  /**
   * Handles user authentication login process. Verifies user credentials, checks account status,
   * manages OTP and 2FA verification flows if active, and generates authentication tokens.
   *
   * @param {Request} req Express request object containing user login details (email, password)
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to authentication response with tokens and user status
   * @memberof UserAuthenticationController
   */
  public loginController = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { email, password } = req.body;
      const userDetails: PromiseResolve = await UserService.fetchUserDetails({ email }, [], ['isIssuer', 'issuerStatus', 'createdAt', 'updatedAt']);

      const user: IUserModel = userDetails?.data;

      if (userDetails?.error || !user?.password || (user?.userType !== UserType.Admin && user?.userType !== UserType.Subadmin)) {
        throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.BAD_REQUEST);
      }
      let role;
      if (user.userType == UserType.Admin) {
        role = '1';
      } else {
        role = '2';
      }
      const verifyPassResp: PromiseResolve = await CommonHelper.verifyPassword(password, user.password);

      if (verifyPassResp.error) {
        throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, verifyPassResp.status);
      }
      if (!user.isActive) {
        throw new CustomError(RES_MSG.COMMON.ADMIN_BLOCK_USER, RESPONSES.FORBIDDEN);
      } else if (!user.isEmailVerify) {
        const otpResponse = await CommonHelper.sendOTP(user._id, otpType.LOGIN, user.email);
        if (otpResponse.error) throw new CustomError(otpResponse.message, otpResponse.status);
        return ResponseHandler.success(res, {
          status: userDetails.status || RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.USER.USER_NOT_VERIFIED,
          data: {
            isOtpActive: user.isOtpActive,
          },
        });
      } else if (user.isOtpActive) {
        const otpResponse = await CommonHelper.sendOTP(user._id, otpType.LOGIN, user.email);
        if (otpResponse.error) throw new CustomError(otpResponse.message, otpResponse.status);
        return ResponseHandler.success(res, {
          status: userDetails.status || RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.SUCCESS_MSG.OTP_SENT_SUCCESS,
          data: {
            isOtpActive: user.isOtpActive,
            role,
          },
        });
      } else if (user.is2FAActive) {
        //token for twofa verification
        const tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email }, false, 'twoFA', CONFIG.JWT_AUTH.JWT_2FA_EXPIRE);
        if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);
        return ResponseHandler.success(res, {
          status: userDetails.status || RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.TWO_FA.PENDING,
          data: {
            is2FAActive: user.is2FAActive,
            role,
            ...tokenResp.data,
          },
        });
      }
      await userSchema.updateOne({ _id: user._id }, { $set: { lastLogin: new Date() } });
      const tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email, userType: user.userType }, true);
      if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);

      const emailDetail = {
        name: 'Admin',
      };

      emailHelper.sendEmailTemplate(email, 'login', emailDetail);
      return ResponseHandler.success(res, {
        status: userDetails.status || RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.LOGIN_SUCCESS,
        data: {
          ...tokenResp.data,
          walletAddress: user.walletAddress || walletAddress,
          //  isOtpActive: user.isOtpActive,
          role,
          is2FAActive: user.is2FAActive,
          isWalletPublished: user.userType ? true : user.onchainID ? true : false,
        },
      });
    } catch (error: any) {
      logger.error(error, 'loginController Error');

      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Verifies OTP codes sent to users for different verification purposes like login,
   * email verification, or other secured actions. Handles different verification flows
   * based on the OTP type.
   *
   * @param {Request} req Express request object containing verification data (email, otp, type)
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to verification result with appropriate tokens
   * @memberof UserAuthenticationController
   */
  public verifyController = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { email, otp, type } = req.body;

      const userDetails = await UserService.fetchUserDetails({
        email,
        isActive: true,
      });

      if (userDetails.error) {
        throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
      }

      const user = userDetails.data;
      const storedOTP = await CommonHelper.getOTP(user._id, type);

      if (storedOTP.error || !storedOTP.data.otp || storedOTP.data.otp != otp) {
        throw new CustomError(RES_MSG.ERROR_MSG.INVALID_OTP, RESPONSES.BAD_REQUEST);
      }

      let tokenResp;
      switch (type) {
        // Verify during login
        case otpType.LOGIN:
          if (!user.isEmailVerify) {
            const otpResponse = await CommonHelper.sendOTP(user._id, otpType.LOGIN, user.email);
            if (otpResponse.error) throw new CustomError(otpResponse.message, otpResponse.status);

            return ResponseHandler.success(res, {
              status: userDetails.status || RESPONSES.SUCCESS,
              error: false,
              message: RES_MSG.USER.USER_NOT_VERIFIED,
              data: { isOtpActive: user.isOtpActive },
            });
          }

          if (user.is2FAActive) {
            tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email }, false, 'twoFA', CONFIG.JWT_AUTH.JWT_2FA_EXPIRE);
            if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);

            return ResponseHandler.success(res, {
              status: userDetails.status || RESPONSES.SUCCESS,
              error: false,
              message: RES_MSG.TWO_FA.PENDING,
              data: {
                is2FAActive: user.is2FAActive,
                ...tokenResp.data,
              },
            });
          }

          const ip: any = req.headers['x-forwarded-for'] || req.ip;
          const geo = geoip.lookup(ip);
          const time = new Date().toLocaleString();

          const emailDetail = { name: process.env.NAME, ip, geo, time };
          tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email, userType: user.userType }, true);
          if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);

          emailHelper.sendEmailTemplate(email, 'login', emailDetail);
          return ResponseHandler.success(res, {
            status: userDetails.status || RESPONSES.SUCCESS,
            error: false,
            message: RES_MSG.USER.LOGIN_SUCCESS,
            data: {
              ...tokenResp.data,
              is2FAActive: user.is2FAActive,
            },
          });

        case otpType.FORGOT:
          tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email }, false, 'forgetToken', CONFIG.JWT_AUTH.FORGOT_EXPIRE_TIME);
          if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);

          return ResponseHandler.success(res, {
            status: userDetails.status || RESPONSES.SUCCESS,
            error: false,
            message: RES_MSG.USER.OTP_SUCCESS,
            data: {
              userType: UserType.Admin,
              //  isOtpActive: user.isOtpActive,
              walletAddress: walletAddress,
              accessToken: tokenResp.data.accessToken,
            },
          });

        default:
          throw new CustomError(RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR, RESPONSES.INTERNAL_SERVER_ERROR);
      }
    } catch (error) {
      logger.error(error, 'verifyController Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Resends OTP to user's email for various verification purposes. Manages
   * OTP generation, storage, and delivery for authentication flows.
   *
   * @param {Request} req Express request object containing user data for OTP delivery
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to the OTP resend result
   * @memberof UserAuthenticationController
   */
  public reSendOtpController = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { email, type } = req.body;
      const userLockResp: PromiseResolve = await CommonHelper.userLock(email);
      if (userLockResp.error) throw new CustomError(userLockResp.message, userLockResp.status);

      const userDetails: PromiseResolve = await UserService.fetchUserDetails({
        email,
      });
      if (type == 'forgot') {
        if (userDetails.error)
          return ResponseHandler.success(res, {
            status: RESPONSES.SUCCESS,
            error: false,
            message: RES_MSG.ERROR_MSG.FORGOT_CRED,
          });
      }

      if (userDetails.error) throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);

      const otpResponse = await CommonHelper.sendOTP(userDetails.data._id, type, userDetails.data.email);
      if (otpResponse.error) throw new CustomError(otpResponse.message, otpResponse.status);
      return ResponseHandler.success(res, {
        status: userDetails.status || RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.ERROR_MSG.FORGOT_CRED,
        data: null,
      });
    } catch (error) {
      logger.error(error, 'loginController Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Retrieves a paginated list of users with optional filtering and sorting.
   * Used for admin dashboard to manage user accounts.
   *
   * @param {Request} req Express request object containing pagination, filter, and sort parameters
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to a paginated list of users
   * @memberof UserAuthenticationController
   */
  public getUsersListController = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const {
        page = 1,
        limit = 10,
        sort = JSON.stringify({ createdAt: -1 }),
        search = '',

        isActive,
        isDeleted,
        kycStatus = '',
        userType = '',
        countryCode = '',
        isKyc,
      } = req.query;
      const disallowedSymbolsRegex = /[!+#$%^*(),?":{}|<>]/;
      const validateSearchResult = await CommonHelper.validateStringRegex(search as string, disallowedSymbolsRegex, RES_MSG.ERROR_MSG.DISALLOWED_SYMBOLS);

      // If validation fails, return the error response immediately
      if (validateSearchResult.error) {
        return ResponseHandler.error(res, {
          status: validateSearchResult.status,
          error: true,
          message: validateSearchResult.message,
        });
      }
      const filters = {
        ...(isActive !== undefined && { isActive: isActive === 'true' }),
        ...(isDeleted !== undefined && { isDeleted: isDeleted === 'true' }),
        ...(kycStatus !== '' && kycStatus && { kycStatus }), // Only include kycStatus if it's not an empty string
        ...(userType && { userType }),
        ...(countryCode && { countryCode }),
        ...(isKyc !== undefined && { isKyc: isKyc === 'true' }),
      };

      const sortCriteria = JSON.parse(sort as string);
      const projection = ['name', 'email', 'userImage', 'kycStatus', 'mobile', 'isKyc', 'mainInformation.nationality', 'institutions.companyInformation', 'isActive', 'userType', 'countryCode', 'createdAt', 'sumSubKycStatus', 'levelName'];

      const userDetails = await UserService.fetchUserList(filters, projection, {
        page: Number(page),
        limit: Number(limit),
        sort: sortCriteria,
        search,
      });

      return ResponseHandler.success(res, {
        status: 200,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
        data: userDetails.data,
      });
    } catch (error) {
      logger.error(error, 'getUserslist Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Exports user list data to CSV format. Retrieves filtered user data
   * and formats it for CSV download.
   *
   * @param {Request} req Express request object containing filter parameters
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to CSV export data
   * @memberof UserAuthenticationController
   */
  public getUsersListControllerCsv = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { startDate = '', endDate = '' } = req.query;

      // Initialize filters object
      const filters: any = {
        ...(startDate && endDate
          ? {
              createdAt: {
                $gte: new Date(startDate as string),
                $lte: new Date(endDate as string),
              },
            }
          : startDate
            ? { createdAt: { $gte: new Date(startDate as string) } }
            : endDate
              ? { createdAt: { $lte: new Date(endDate as string) } }
              : {}),
      };

      // Parse sort criteria
      // let sortCriteria = JSON.parse(sort as string);

      // Define the projection fields
      const projection = [
        'name',
        'email',
        'userImage',
        'kycStatus',
        'mobile',
        'isKyc',
        'mainInformation.nationality',
        'institutions.companyInformation',
        'isActive',
        'userType',
        'countryCode',
        'sumSubKycStatus',
        'levelName',
        'createdAt',

        'startDate',
        'endDate',
      ];

      // Fetch user list with the applied filters and sorting
      const userDetails = await UserService.fetchUserListcsv(filters, projection);

      return ResponseHandler.success(res, {
        status: 200,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
        data: userDetails.data,
      });
    } catch (error) {
      logger.error(error, 'getUserslist Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Exports issuer list data to CSV format. Retrieves filtered issuer data
   * and formats it for CSV download.
   *
   * @param {Request} req Express request object containing filter parameters
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to CSV export data
   * @memberof UserAuthenticationController
   */
  public getIssuerListControllerCsv = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { startDate = '', endDate = '', issuerStatus = '', userType = '' } = req.query;

      // Initialize filters object
      const filters: any = {
        isKyc: true,
        ...(userType && { userType }),
        ...(issuerStatus
          ? { IssuerStatus }
          : {
              issuerStatus: { $ne: IssuerStatus.NOT_APPLIED },
            }),
        ...(startDate && endDate
          ? {
              createdAt: {
                $gte: new Date(startDate as string),
                $lte: new Date(endDate as string),
              },
            }
          : startDate
            ? { createdAt: { $gte: new Date(startDate as string) } }
            : endDate
              ? { createdAt: { $lte: new Date(endDate as string) } }
              : {}),
      };

      // Parse sort criteria
      // let sortCriteria = JSON.parse(sort as string);

      // Define the projection fields
      const projection = [
        'name',
        'email',
        'userImage',
        'kycStatus',
        'mobile',
        'isKyc',
        'mainInformation.nationality',
        'institutions.companyInformation',
        'isActive',
        'userType',
        'countryCode',
        'sumSubKycStatus',
        'levelName',
        'createdAt',
        'issuerStatus',
        'startDate',
        'endDate',
      ];

      // Fetch user list with the applied filters and sorting
      const userDetails = await UserService.fetchUserListcsv(filters, projection);

      return ResponseHandler.success(res, {
        status: 200,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
        data: userDetails.data,
      });
    } catch (error) {
      logger.error(error, 'getUserslist Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Exports managed issuer list data to CSV format. Retrieves filtered managed issuer data
   * and formats it for CSV download.
   *
   * @param {Request} req Express request object containing filter parameters
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to CSV export data
   * @memberof UserAuthenticationController
   */
  public getManageIssuerListCsv = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, sort, search = '', issuerStatus = '', startDate, endDate } = req.query;

      const filters: any = {
        isKyc: true,
        isIssuer: true,
        ...(issuerStatus && { issuerStatus }),
        ...(startDate && endDate
          ? {
              createdAt: {
                $gte: new Date(startDate as string),
                $lte: new Date(endDate as string),
              },
            }
          : startDate
            ? { createdAt: { $gte: new Date(startDate as string) } }
            : endDate
              ? { createdAt: { $lte: new Date(endDate as string) } }
              : {}),
      };

      const projection = ['name', 'email', '_id', 'wallets', 'userImage', 'offeringStatusCounts', 'createdAt', 'isActive', 'isKyc', 'isIssuer'];

      const userDetails = await UserService.fetchUserListWithOfferingscsv(filters, projection, {
        page: Number(page),
        limit: Number(limit),
        ...(search && { search: search }),
        ...(sort && { sort: JSON.parse(sort as string) }),
      });
      return ResponseHandler.success(res, {
        status: userDetails.status,
        error: false,
        message: RES_MSG?.SUCCESS_MSG.ISSUER_LIST,
        data: userDetails.data,
      });
    } catch (error: any) {
      logger.error(error, 'getIssuerOfferingController');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Retrieves dashboard metrics and statistics for admin interface.
   * Provides summary data of platform activity and user statistics.
   *
   * @param {Request} req Express request object
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to dashboard statistics data
   * @memberof UserAuthenticationController
   */
  public dashboard = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, sort = JSON.stringify({ createdAt: -1 }), issuerStatus = '' } = req.query;

      // Add date filtering based on startDate and endDate
      const filters = {
        isKyc: true,
        isIssuer: true,
        ...(issuerStatus && { issuerStatus }),
      };

      const sortCriteria = JSON.parse(sort as string);
      const projection = ['projectDetails.offeringName', 'projectDetails.tokenTicker', 'projectDetails.startDate', 'projectDetails.endDate', 'projectDetails.authorizedCountries', 'createdAt', 'createdBy', 'status'];

      const userDetails = await OfferingService.dashboardList(filters, projection, {
        page: Number(page),
        limit: Number(limit),
        sort: sortCriteria,
      });

      return ResponseHandler.success(res, {
        status: 200,
        error: false,
        message: RES_MSG?.SUCCESS_MSG.ISSUER_LIST,
        data: userDetails.data,
      });
    } catch (error: any) {
      logger.error(error, 'getIssuerList Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Retrieves a list of top investors based on investment metrics.
   * Provides sorted data of users with highest investment activity.
   *
   * @param {Request} req Express request object with filter and sort parameters
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to top investors data
   * @memberof UserAuthenticationController
   */
  public topInvestors = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const pipeline: any = [
        // Step 1: Filter only MINTED orders
        {
          $match: { status: orderStatusEnum.MINTED }, // Ensure this is the correct enum or string value
        },

        // Step 2: Compute the total investment per user
        {
          $group: {
            _id: '$userId',
            totalInvestment: { $sum: { $toDouble: '$amount' } }, // Convert and sum
          },
        },

        // Step 3: Sort users by total investment (Descending) & limit to top 5
        { $sort: { totalInvestment: -1 } },
        { $limit: 5 },

        // Step 4: Compute the grand total investment of all users (inside $facet)
        {
          $facet: {
            topInvestors: [
              // This will process only the top 5 users
              {
                $lookup: {
                  from: 'users',
                  localField: '_id',
                  foreignField: '_id',
                  as: 'userData',
                  pipeline: [{ $project: { name: 1, email: 1, userImage: 1 } }],
                },
              },
              // Convert userData from array to a single object
              {
                $project: {
                  userId: '$_id',
                  totalInvestment: 1,
                  userData: { $arrayElemAt: ['$userData', 0] },
                },
              },
            ],
            grandTotal: [{ $group: { _id: null, grandTotalInvestment: { $sum: '$totalInvestment' } } }],
          },
        },

        // Step 5: Merge the grand total investment into each investor record
        {
          $unwind: '$grandTotal',
        },

        // Step 6: Compute the invested percentage for each user
        {
          $project: {
            topInvestors: {
              $map: {
                input: '$topInvestors',
                as: 'investor',
                in: {
                  userId: '$$investor.userId',
                  name: '$$investor.userData.name',
                  email: '$$investor.userData.email',
                  userImage: '$$investor.userData.userImage',
                  totalInvestment: '$$investor.totalInvestment',
                  investedPercentage: {
                    $multiply: [
                      {
                        $cond: {
                          if: { $gt: ['$grandTotal.grandTotalInvestment', 0] }, // Check if grandTotalInvestment > 0
                          then: { $divide: ['$$investor.totalInvestment', '$grandTotal.grandTotalInvestment'] },
                          else: 0, // If 0, return 0% to avoid division error
                        },
                      },
                      100,
                    ],
                  },
                },
              },
            },
          },
        },

        // Step 7: Unwind topInvestors for final output
        {
          $unwind: '$topInvestors',
        },

        {
          $replaceRoot: { newRoot: '$topInvestors' },
        },
      ];

      const result = await OrderSchema.aggregate(pipeline);
      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
        data: {
          topInvestors: result || [],
        },
      });
    } catch (error: any) {
      logger.error(error, 'topInvestors Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Retrieves detailed user information for a specific user.
   * Used for user profile viewing and management.
   *
   * @param {Request} req Express request object containing user identifier
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to user profile data
   * @memberof UserAuthenticationController
   */
  public user = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      // Aggregation pipeline to get investor count and pending investor count
      const investorAggregation = await userSchema.aggregate([
        {
          $match: { userType: { $nin: [UserType.Admin, UserType.Subadmin] } },
        },
        {
          $group: {
            _id: null,
            investorCount: { $sum: 1 },
            approvedInvestorCount: {
              $sum: {
                $cond: [{ $eq: ['$kycStatus', KycStatus.APPROVED] }, 1, 0],
              },
            },
            rejectedInvestorCount: {
              $sum: {
                $cond: [{ $eq: ['$kycStatus', KycStatus.REJECTED] }, 1, 0],
              },
            },
            pendingInvestorCount: {
              $sum: {
                $cond: [{ $eq: ['$kycStatus', KycStatus.PENDING] }, 1, 0],
              },
            },
            inProgressKycInvestorCount: {
              $sum: {
                $cond: [{ $eq: ['$kycStatus', KycStatus.IN_PROGRESS] }, 1, 0],
              },
            },
            reSubmittedKycInvestorCount: {
              $sum: {
                $cond: [{ $eq: ['$kycStatus', KycStatus.RESUBMIT] }, 1, 0],
              },
            },
          },
        },
      ]);

      // Aggregation pipeline to get issuer counts
      const issuerAggregation = await userSchema.aggregate([
        {
          $match: { userType: { $nin: [UserType.Admin, UserType.Subadmin] } },
        },
        {
          $group: {
            _id: null,
            issuerCount: {
              $sum: { $cond: [{ $eq: ['$issuerStatus', IssuerStatus.APPROVED] }, 1, 0] },
            },
            rejectedIssuerCount: {
              $sum: { $cond: [{ $eq: ['$issuerStatus', IssuerStatus.REJECTED] }, 1, 0] },
            },
            pendingIssuerCount: {
              $sum: { $cond: [{ $and: [{ $eq: ['$isKyc', true] }, { $eq: ['$issuerStatus', 'PENDING'] }] }, 1, 0] },
            },
          },
        },
      ]);

      // Aggregation pipeline to get total investment amount
      const investmentAggregation = await OrderSchema.aggregate([
        {
          $match: { status: 'MINTED' },
        },
        {
          $group: {
            _id: null,
            totalAmount: { $sum: { $toDecimal: '$amount' } },
          },
        },
        {
          $project: {
            _id: 0,
            totalAmount: { $toString: '$totalAmount' },
          },
        },
      ]);

      // Extracting values from aggregations
      const totalAmount = investmentAggregation.length > 0 ? investmentAggregation[0].totalAmount : 0;

      // Returning response
      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG?.SUCCESS_MSG.INVESTOR_COUNT_SUCCESS,
        data: {
          totalAmount,
          ...investorAggregation[0],
          ...issuerAggregation[0],
        },
      });
    } catch (error: any) {
      logger.error(error, 'user API Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Retrieves issuer details for management and review purposes.
   * Provides comprehensive data about an issuer and their offerings.
   *
   * @param {Request} req Express request object containing issuer identifier
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to issuer profile data
   * @memberof UserAuthenticationController
   */
  public getIssuerController = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, sort = JSON.stringify({ createdAt: -1 }), issuerStatus = '', userType = '', search = '', countryCode = '' } = req.query;
      const disallowedSymbolsRegex = /[!+#$%^*(),?":{}|<>]/;
      const validateSearchResult = await CommonHelper.validateStringRegex(search as string, disallowedSymbolsRegex, RES_MSG.ERROR_MSG.DISALLOWED_SYMBOLS);

      // If validation fails, return the error response immediately
      if (validateSearchResult.error) {
        return ResponseHandler.error(res, {
          status: validateSearchResult.status,
          error: true,
          message: validateSearchResult.message,
        });
      }
      const filters = {
        isKyc: true,
        ...(issuerStatus
          ? { issuerStatus }
          : {
              issuerStatus: { $ne: IssuerStatus.NOT_APPLIED },
            }),
        ...(userType && { userType }),
        ...(countryCode && { countryCode }),
      };

      const sortCriteria = JSON.parse(sort as string);
      const projection = ['name', 'email', 'userImage', 'kycStatus', 'mobile', 'isIssuer', 'issuerStatus', 'issuerReason', 'mainInformation.nationality', 'isActive', 'userType', 'countryCode', 'institutions.companyInformation', 'sumSubKycStatus'];

      const userDetails = await UserService.fetchUserList(filters, projection, {
        page: Number(page),
        limit: Number(limit),
        sort: sortCriteria,
        search,
      });

      return ResponseHandler.success(res, {
        status: 200,
        error: false,
        message: RES_MSG?.SUCCESS_MSG.ISSUER_LIST,
        data: userDetails.data,
      });
    } catch (error: any) {
      logger.error(error, 'getIssuerList Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Approves an issuer account for platform access.
   * Updates issuer status and sends notification.
   *
   * @param {Request} req Express request object containing issuer approval data
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to issuer approval result
   * @memberof UserAuthenticationController
   */
  public approveIssuer = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { email, issuerStatus, issuerReason } = req.body;

      // Fetch specific user details
      const userDetails = await UserService.fetchUserDetails({ email }, ['name', 'email', 'kycStatus', 'isActive', 'issuerStatus']);
      const name = userDetails?.data?.name;
      if (userDetails?.data?.issuerStatus === issuerStatus) {
        throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.BAD_REQUEST);
      }

      const payload = { email, issuerStatus, issuerReason };
      // gRPC call to approve issuer
      userClient.client.approveIssuer(payload, async (error: any, response: any) => {
        if (error || response?.error) {
          const errorMessage = error?.message || response?.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR;
          const errorStatus = error?.status || RESPONSES.INTERNAL_SERVER_ERROR;

          logger.error('gRPC Error:', error || response);
          return ResponseHandler.error(res, {
            status: errorStatus,
            error: true,
            message: errorMessage,
          });
        }

        // Update user's issuer status
        await userSchema.findOneAndUpdate(
          { email },
          {
            issuerStatus,
            isIssuer: issuerStatus === IssuerStatus.APPROVED ? true : false,
            issuerReason,
          },
          { new: true }, // Return updated document
        );
        if (issuerStatus == IssuerStatus.REJECTED) {
          const emailDetail = {
            name,
            reason: issuerReason,
            date: moment().format('YYYY-MM-DD'),
          };
          emailHelper.sendEmailTemplate(email, 'rejectIssuer', emailDetail);
        }
        if (issuerStatus == IssuerStatus.APPROVED) {
          const emailDetail = {
            name,
            reason: issuerReason,
            date: moment().format('YYYY-MM-DD'),
            baseurl: process.env.BASEURL,
          };
          emailHelper.sendEmailTemplate(email, 'becomeanissuer', emailDetail);
        }
        return ResponseHandler.success(res, {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.SUCCESS_MSG.UPDATE_USER,
          data: issuerStatus,
        });
      });
    } catch (error: any) {
      logger.error(error, 'approveIssuer Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Retrieves user data for profile display and management.
   * Provides authenticated user's profile information.
   *
   * @param {Request} req Express request object with user context
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to user profile data
   * @memberof UserAuthenticationController
   */
  public getUserdata = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { userId }: any = req.query;
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return ResponseHandler.error(res, {
          message: 'invalid id',
          status: RESPONSES.BAD_REQUEST,
          error: true,
        });
      }

      const objectId = new mongoose.Types.ObjectId(userId);
      const user = await userSchema.findById(objectId).exec();
      if (!user) {
        return ResponseHandler.error(res, {
          message: RES_MSG.ERROR_MSG.USER_NOT_FOUND,
          status: 400,
          error: true,
        });
      }

      // Fetch user detail data based_id on userId
      // const userDetail = await UserDetailsSchema.findById(objectId).exec();

      // Combine or structure the data as needed
      const combinedData = {
        user: user?.toObject(),
        // details: userDetail ? userDetail.toObject() : null
      };
      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: combinedData,
      });
    } catch (error: any) {
      logger.error(error, 'getUserslist Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Initiates the forgot password process for a user.
   * Sends password reset instructions via email.
   *
   * @param {Request} req Express request object containing user email
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to forgot password process result
   * @memberof UserAuthenticationController
   */
  public forgotPasswordController = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { email } = req.body;
      const userDetails: PromiseResolve = await UserService.fetchUserDetails({
        email,
      });
      if (userDetails.error)
        return ResponseHandler.success(res, {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.ERROR_MSG.FORGOT_CRED,
        });
      const otpResponse = await CommonHelper.sendOTP(userDetails.data._id, otpType.FORGOT, userDetails.data.email);
      if (otpResponse.error) throw new CustomError(otpResponse.message, otpResponse.status);
      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.ERROR_MSG.FORGOT_CRED,
      });
    } catch (error: any) {
      logger.error(error, 'Error in forgotPasswordController');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Changes user password when user is authenticated.
   * Validates current password and updates to new password.
   *
   * @param {Request} req Express request object containing current and new password
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to password change result
   * @memberof UserAuthenticationController
   */
  public changePasswordController = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const email = req.userInfo.email;
      const { password, oldPassword } = req.body;
      if (password == oldPassword) throw new CustomError(RES_MSG.ERROR_MSG.NEW_PASSWORD_SAME_AS_CURRENT_PASSWORD, RESPONSES.BAD_REQUEST);
      const userDetails: PromiseResolve = await UserService.fetchUserDetails({
        email,
      });
      if (userDetails.error) throw new CustomError(RES_MSG.COMMON.NO_USER, RESPONSES.UN_AUTHORIZED);

      const verifyPassResp: PromiseResolve = await CommonHelper.verifyPassword(oldPassword, userDetails.data.password);
      if (verifyPassResp.error) throw new CustomError(RES_MSG.ERROR_MSG.INCORRECT_CURRENT_PASSWORD, RESPONSES.BAD_REQUEST);

      const recentPasswordsResp: PromiseResolve = await UserService.fetchRecentPasswords(userDetails.data._id);

      // Check if new password matches any of the recent passwords
      if (!recentPasswordsResp?.error) {
        for (const recentPassword of recentPasswordsResp.data.passwords) {
          const isMatch = await bcrypt.compare(password, recentPassword);
          if (isMatch) {
            throw new CustomError(RES_MSG.ERROR_MSG.PASSWORD_RECENTLY_USED, RESPONSES.BAD_REQUEST);
          }
        }
      }
      const updateUserResp: PromiseResolve = await UserService.updateUserDetails({ password }, { email });

      if (!updateUserResp.error) {
        // Add the new password to the password history
        await UserService.addPassword(userDetails.data._id, updateUserResp.data.password);
        await RedisHelper.deleteKey(`accessToken:${email}`);
        return ResponseHandler.success(res, {
          status: RESPONSES.SUCCESS,
          error: false,
          message: RES_MSG.SUCCESS_MSG.PASSWORD_RESET_SUCCESS,
        });
      }
    } catch (error) {
      logger.error(error, 'changePasswordController Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Resets user password using a password reset token.
   * Validates token and sets new password for the user.
   *
   * @param {Request} req Express request object containing reset token and new password
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to password reset result
   * @memberof UserAuthenticationController
   */
  public resetPasswordController = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { newPassword } = req.body;
      const { email } = req.body.user;
      const userDetails: PromiseResolve = await UserService.fetchUserDetails({ email }, ['email', '_id'], []);
      if (userDetails.error) throw new CustomError(RES_MSG.COMMON.NO_USER, RESPONSES.UN_AUTHORIZED);
      const recentPasswordsResp: PromiseResolve = await UserService.fetchRecentPasswords(userDetails.data._id);
      if (!recentPasswordsResp.error) {
        for (const recentPassword of recentPasswordsResp.data.passwords) {
          const isMatch = await bcrypt.compare(newPassword, recentPassword);
          if (isMatch) {
            throw new CustomError(RES_MSG.ERROR_MSG.PASSWORD_RECENTLY_USED, RESPONSES.BAD_REQUEST);
          }
        }
      }
      const updateUserResp = await UserService.updateUserDetails({ password: newPassword }, { email });
      if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);
      await RedisHelper.deleteKey(`forgetToken:${email}`);
      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.PASSWORD_RESET_SUCCESS,
      });
    } catch (error: any) {
      logger.error(error, 'Error in resetPassword');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Handles user logout process by invalidating authentication tokens.
   * Updates user session and removes active tokens.
   *
   * @param {Request} req Express request object containing user session data
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to logout result
   * @memberof UserAuthenticationController
   */
  public logOutController = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { email, userId } = req.userInfo;
      await RedisHelper.deleteKey(`accessToken:${email}`);
      await RedisHelper.deleteKey(`2FA_${userId}`);

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.LOGOUT,
      });
    } catch (error: any) {
      logger.error(error, 'logOut Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Enables two-factor authentication for a user account.
   * Generates and stores 2FA secret and provides setup instructions.
   *
   * @param {Request} req Express request object containing user authentication data
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to 2FA setup data
   * @memberof UserAuthenticationController
   */
  public enable2FAController = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { userId, email } = req.userInfo;
      const secret = authenticator.generateSecret();
      const otpAuth = authenticator.keyuri(email, CONFIG.PROJECT.NAME, secret);
      const qrCode = await toDataURL(otpAuth);
      // await RedisHelper.setString(twoFactorSecretKey, secret, CONFIG.REDIS.OTP_EXPIRY)
      const userDetails: PromiseResolve = await UserService.fetchUserDetails({
        email,
      });
      if (userDetails.data.is2FAActive) throw new CustomError(RES_MSG.COMMON.BAD_REQUEST, RESPONSES.BAD_REQUEST);
      const updateUserResp = await UserService.updateUserDetails({ twoFASecret: secret }, { _id: userId });
      if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);
      return ResponseHandler.success(res, {
        status: RESPONSES.CREATED,
        error: false,
        message: RES_MSG.TWO_FA.CREATED,
        data: { qrCode, secret },
      });
    } catch (error) {
      logger.error(error, 'enable2FAController Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Handles forgotten 2FA recovery process.
   * Initiates verification flow for 2FA reset.
   *
   * @param {Request} req Express request object containing user data
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to 2FA recovery initialization
   * @memberof UserAuthenticationController
   */
  public forgot2FAController = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { userId } = req.body.user;
      const userDetails: PromiseResolve = await UserService.fetchUserDetails({ _id: userId }, [], ['kycDetails', 'wallets', 'isIssuer', 'issuerStatus', 'createdAt', 'updatedAt']);
      if (userDetails.error) throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
      const user = userDetails?.data;
      const secret = authenticator.generateSecret();
      // await RedisHelper.setString(twoFactorSecretKey, secret, CONFIG.REDIS.OTP_EXPIRY)
      const updateUserResp = await UserService.updateUserDetails({ twoFASecret: secret }, { _id: userId });
      if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);
      const otpAuth = authenticator.keyuri(user?.email, CONFIG.PROJECT.NAME, secret);
      const qrCode = await toDataURL(otpAuth);
      return ResponseHandler.success(res, {
        status: RESPONSES.CREATED,
        error: false,
        message: RES_MSG.TWO_FA.FORGOT_2FA,
        data: { qrCode, secret },
      });
    } catch (error) {
      logger.error(error, 'forgot2FAController Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Resets two-factor authentication setup for a user account.
   * Verifies user identity and removes existing 2FA configuration.
   *
   * @param {Request} req Express request object containing verification data
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to 2FA reset result
   * @memberof UserAuthenticationController
   */
  public reSet2FA = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { token } = req.body;

      const isTokenValid: PromiseResolve = await CommonHelper.isValidToken(token);
      if (isTokenValid.error) {
        throw new CustomError(RES_MSG.TWO_FA.TOKEN_EXPIRE, RESPONSES.UN_AUTHORIZED);
      }

      const { userId } = isTokenValid.data;

      const userDetails: PromiseResolve = await UserService.fetchUserDetails({ _id: userId }, [], ['kycDetails', 'wallets', 'isIssuer', 'issuerStatus', 'createdAt', 'updatedAt']);

      if (userDetails.error) {
        throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
      }

      const updateData = {
        is2FAActive: false,
        twoFASecret: '',
      };

      const updateUserResp = await UserService.updateUserDetails(updateData, {
        _id: userId,
      });

      if (updateUserResp.error) {
        throw new CustomError(updateUserResp.message, updateUserResp.status);
      }

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.TWO_FA.RESET_SUCCESS,
        data: {},
      });
    } catch (error) {
      logger.error(error, 'reSet2FA Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Verifies 2FA setup during enablement process.
   * Validates submitted 2FA code against stored secret.
   *
   * @param {Request} req Express request object containing 2FA verification code
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to 2FA verification result
   * @memberof UserAuthenticationController
   */
  public verify2FAController = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { token } = req.body;
      const { userId } = req.userInfo;
      const userDetails: PromiseResolve = await UserService.fetchUserDetails({ _id: userId }, [], ['kycDetails', 'wallets', 'isIssuer', 'issuerStatus', 'createdAt', 'updatedAt']);
      if (userDetails.error) throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
      const isValidTwoFAToken = authenticator.check(token, userDetails.data.twoFASecret);
      if (!isValidTwoFAToken) {
        throw new CustomError(RES_MSG.TWO_FA.TOKEN_INVALID, RESPONSES.BAD_REQUEST);
      }
      const updateUserResp = await UserService.updateUserDetails({ is2FAActive: true }, { _id: userId });
      if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);
      await RedisHelper.setString(`2FA_${userId}`, 'true', CONFIG.REDIS.REDIS_AUTH_EXPIRE);
      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,

        message: RES_MSG.TWO_FA.VERIFIED_SUCCESS,
        data: null,
      });
    } catch (error) {
      logger.error(error, 'verify2FAController Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Verifies 2FA code during login process.
   * Validates submitted 2FA code for authentication.
   *
   * @param {Request} req Express request object containing 2FA code and authentication data
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to 2FA login verification result
   * @memberof UserAuthenticationController
   */
  public verify2FALoginController = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { code } = req.body;
      const { userId } = req.body.user;
      const userDetails: PromiseResolve = await UserService.fetchUserDetails({ _id: userId }, [], ['kycDetails', 'wallets', 'isIssuer', 'issuerStatus', 'createdAt', 'updatedAt']);
      if (userDetails.error) throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
      const isValidTwoFAToken = authenticator.check(code, userDetails.data.twoFASecret);
      if (!isValidTwoFAToken) {
        throw new CustomError(RES_MSG.TWO_FA.TOKEN_INVALID, RESPONSES.BAD_REQUEST);
      }
      const updateUserResp = await UserService.updateUserDetails({ is2FAActive: true }, { _id: userId });
      if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);
      await RedisHelper.setString(`2FA_${userId}`, 'true', CONFIG.REDIS.REDIS_AUTH_EXPIRE);
      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.TWO_FA.VERIFIED_SUCCESS,
        data: null,
      });
    } catch (error) {
      logger.error(error, 'verify2FAController Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Disables two-factor authentication for a user account.
   * Verifies user identity and removes 2FA requirement.
   *
   * @param {Request} req Express request object containing 2FA verification data
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to 2FA disablement result
   * @memberof UserAuthenticationController
   */
  public disable2FAController = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { token } = req.body;
      const { userId, email } = req.userInfo;
      const userDetails: PromiseResolve = await UserService.fetchUserDetails({
        email,
      });
      if (userDetails.error) throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
      const isValidTwoFAToken = authenticator.check(token, userDetails.data.twoFASecret);
      if (!isValidTwoFAToken) {
        throw new CustomError(RES_MSG.TWO_FA.TOKEN_INVALID, RESPONSES.BAD_REQUEST);
      }
      const updateUserResp = await UserService.updateUserDetails({ is2FAActive: false }, { _id: userId });
      if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);
      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.TWO_FA.DISABLE_SUCCESS,
        data: null,
      });
    } catch (error) {
      logger.error(error, 'disable2FAController Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Verifies 2FA code during login when 2FA is enabled.
   * Completes authentication process with 2FA verification.
   *
   * @param {Request} req Express request object containing 2FA code
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to login authentication result
   * @memberof UserAuthenticationController
   */
  public verifyLogin2FAController = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { otp } = req.body;
      const { userId } = req.body.user;
      const userDetails: PromiseResolve = await UserService.fetchUserDetails({ _id: userId }, [], ['kycDetails', 'wallets', 'isIssuer', 'issuerStatus', 'createdAt', 'updatedAt']);
      const user = userDetails.data;

      if (userDetails.error) throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.UN_AUTHORIZED);
      const isValidTwoFAToken = authenticator.check(otp, user.twoFASecret);
      if (!isValidTwoFAToken) {
        throw new CustomError(RES_MSG.TWO_FA.TOKEN_INVALID, RESPONSES.BAD_REQUEST);
      }
      const updateUserResp = await UserService.updateUserDetails({ is2FAActive: true }, { _id: userId });
      if (updateUserResp.error) throw new CustomError(updateUserResp.message, updateUserResp.status);

      const tokenResp = await CommonHelper.createJWTAuth({ userId: user._id, email: user.email, userType: user.userType }, true);
      if (tokenResp.error) throw new CustomError(tokenResp.message, tokenResp.status);
      await RedisHelper.setString(`2FA_${userId}`, 'true', CONFIG.REDIS.REDIS_AUTH_EXPIRE);
      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,

        message: RES_MSG.TWO_FA.VERIFIED_SUCCESS,
        data: { ...tokenResp.data, walletAddress },
      });
    } catch (error) {
      logger.error(error, 'verify2FAController Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Approves user requests or applications in the system.
   * Processes approval workflows with notification.
   *
   * @param {Request} req Express request object containing approval data
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to approval process result
   * @memberof UserAuthenticationController
   */
  public approve = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      let message: any;
      const { email, kycStatus, kycReason } = req.body;
      const payload = { email, kycStatus, kycReason };

      const userDetails = await UserService.fetchUserDetails({ email }, ['name', 'email', 'kycStatus', 'isActive', 'kycCount', 'userType']);

      if (userDetails.error) throw new CustomError(RES_MSG.COMMON.NO_USER, RESPONSES.UN_AUTHORIZED);

      const user = userDetails?.data;

      if (user?.isActive == false) {
        throw new CustomError(RES_MSG.COMMON.BLOCK_USER, RESPONSES.BAD_REQUEST);
      }

      if (user?.kycStatus === kycStatus) {
        throw new CustomError(`User is already ${kycStatus}`, RESPONSES.BAD_REQUEST);
      }

      // Prepare the update object with all conditions
      const updateData: any = {
        kycStatus: kycStatus,
        kycReason: kycReason,
        isKyc: false, // Default false, will update later based on gRPC response
      };

      if (kycStatus === 'REJECTED' && user?.kycCount === kycCount - 1) {
        updateData.isActive = false; // Mark user inactive if rejected
        updateData.kycCount = kycCount;
      }

      // Call gRPC service
      userClient.client.approveKyc(payload, async (error: any, response: any) => {
        if (error) {
          logger.error('gRPC Error:', error);
          return ResponseHandler.error(res, {
            status: RESPONSES.INTERNAL_SERVER_ERROR,
            error: true,
            message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
          });
        }

        if (response.error) {
          return ResponseHandler.error(res, {
            message: response.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
            status: RESPONSES.BAD_REQUEST,
            error: true,
          });
        }

        // Update isKyc status based on gRPC response
        updateData.isKyc = response.error ? false : true;

        // Base emailDetail object
        const emailDetail: { name: string; kycReason?: string } = {
          name: user.name,
        };

        if (kycStatus === 'APPROVED') {
          if (user.userType == UserType.Investor) {
            message = RES_MSG.SUCCESS_MSG.KYC_APPROVED;
            emailHelper.sendEmailTemplate(email, 'KYCApproved', emailDetail);
          } else if (user.userType == UserType.Institution) {
            message = RES_MSG.SUCCESS_MSG.KYB_APPROVED;
            emailHelper.sendEmailTemplate(email, 'KYBApproved', emailDetail);
          }
        } else if (kycStatus === 'REJECTED') {
          if (user.userType == UserType.Investor) {
            message = RES_MSG.SUCCESS_MSG.KYC_REJECTED;
            emailDetail.kycReason = kycReason; // Conditionally add kycReason
            emailHelper.sendEmailTemplate(email, 'KYCRejected', emailDetail);
          } else if (user.userType == UserType.Institution) {
            message = RES_MSG.SUCCESS_MSG.KYB_REJECTED;
            emailDetail.kycReason = kycReason; // Conditionally add kycReason
            emailHelper.sendEmailTemplate(email, 'KYBRejected', emailDetail);
          }
        }

        // Single database update call
        await userSchema.findOneAndUpdate({ email: email }, updateData, { new: true });

        return ResponseHandler.success(res, {
          status: RESPONSES.SUCCESS,
          error: false,
          message: message,
          data: kycStatus,
        });
      });
    } catch (error: any) {
      logger.error(error, 'logOut Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Retrieves a list of fees configured in the system.
   * Provides fee structure data for admin management.
   *
   * @param {Request} req Express request object containing filter parameters
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to fee list data
   * @memberof UserAuthenticationController
   */
  public getFeeList = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const getFeeList = await feeSchema.find().sort({ createdAt: -1 });

      const role = req?.userInfo.userType;
      if (role != UserType.Admin) {
        throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.CONFLICT);
      }
      let count: any = 0;
      let maxId;

      if (getFeeList.length > 0) {
        maxId = getFeeList.reduce((max, obj) => (parseInt(obj?._id) > parseInt(max) ? obj?._id : max), getFeeList[0]._id);
        count = maxId;
      }

      const user = {
        count,
        getFeeList,
      };
      return ResponseHandler.success(res, {
        status: 200,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
        data: user,
      });
    } catch (error) {
      logger.error(error, 'getFeeList Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Retrieves current active fee configuration.
   * Provides data about currently applied fee structure.
   *
   * @param {Request} req Express request object
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to current fee data
   * @memberof UserAuthenticationController
   */
  public currentFee = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const getWalletAddress = await userSchema.findOne(
        { email: process.env.EMAIL },
        { walletAddress: 1 }, // Only fetch walletAddress, exclude _id
      );

      const role = req?.userInfo.userType;
      if (role !== UserType.Admin) {
        throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.CONFLICT);
      }

      const details = {
        walletAddress: getWalletAddress?.walletAddress,
      };
      return ResponseHandler.success(res, {
        status: 200,
        error: false,
        message: RES_MSG.USER.DETAILS_FETCH,
        data: details,
      });
    } catch (error) {
      logger.error(error, 'getFeeList Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Unblocks a previously blocked user account.
   * Restores user access with appropriate notifications.
   *
   * @param {Request} req Express request object containing unblock data
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to account unblock result
   * @memberof UserAuthenticationController
   */
  public unblock = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { email, isActive } = req.body;

      // Fetch user details and return specific fields
      const userDetails = await UserService.fetchUserDetails({ email }, ['name', 'email', 'kycStatus', 'isActive']);

      if (!userDetails.data) {
        throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.BAD_REQUEST);
      }

      const currentIsActive = userDetails.data.isActive;
      if (String(currentIsActive) === String(isActive)) {
        throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.BAD_REQUEST);
      }

      const payload = {
        email,
        isActive,
        kycCount: 0,
      };

      // gRPC call to unblock the user
      userClient.client.unblockUser(payload, async (error: any, response: any) => {
        if (error) {
          logger.error('gRPC Error:', error);
          return ResponseHandler.error(res, {
            status: RESPONSES.INTERNAL_SERVER_ERROR,
            error: true,
            message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
          });
        }

        if (response.error) {
          return ResponseHandler.error(res, {
            status: RESPONSES.BAD_REQUEST,
            error: true,
            message: response.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
          });
        }

        // Update user's active status and reset kycCount if activated
        await userSchema.findOneAndUpdate(
          { email },
          {
            isActive,
            ...(isActive === 'true' && { kycCount: 0 }), // Conditionally reset kycCount
          },
          { new: true }, // Return the updated document
        );

        const emailDetails = { name: userDetails.data.name };
        let message;

        if (isActive === 'true') {
          emailHelper.sendEmailTemplate(email, 'accountUnBlock', emailDetails);
          message = RES_MSG.COMMON.ADMIN_UNBLOCK_USER;
        } else {
          // emailHelper.sendEmailTemplate(email, 'accountBlocked', emailDetails);
          emailHelper.sendEmailTemplate(email, 'AccountBlockedNotification', emailDetails);
          await RedisHelper.deleteKey(`accessToken:${email}`);
          message = RES_MSG.COMMON.ADMIN_BLOCK_USER;
        }

        return ResponseHandler.success(res, {
          status: RESPONSES.SUCCESS,
          error: false,
          message,
          data: isActive,
        });
      });
    } catch (error: any) {
      logger.error(error, 'unblock Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Handles document uploads for users and issuers.
   * Processes file uploads, validates, and stores document references.
   *
   * @param {Request} req Express request object containing upload files and metadata
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to document upload result
   * @memberof UserAuthenticationController
   */
  public uploadDocs = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { userId, userType } = req.userInfo;
      const file: Express.Multer.File = req.file;
      let currentOffering;
      const { documentType, offeringId: rowOfferingId } = req.body;
      const offeringId = rowOfferingId ? new Types.ObjectId(rowOfferingId) : null;
      if (offeringId) {
        // Fetch current offering if offeringId exists
        currentOffering = await OfferingService.fetchOfferingDetails({
          _id: new Types.ObjectId(offeringId),
        });
        if (currentOffering.error) {
          throw new CustomError(currentOffering.message, currentOffering.status);
        }
      }
      if (!file) {
        return ResponseHandler.error(res, {
          status: RESPONSES.BAD_REQUEST,
          error: true,
          message: RES_MSG.ERROR_MSG.INVALID_FILE,
        });
      }
      let uploadFileRes: PromiseResolve;
      if (offeringId) {
        uploadFileRes = await CloudHelper.uploadFiles(currentOffering.data.userId.toString(), file, documentType, DocumentFolderTypesEnum.OFFERING, offeringId.toString());
      } else {
        uploadFileRes = await CloudHelper.uploadFiles(userId.toString(), file, documentType, userType);
      }

      if (uploadFileRes.error) throw new CustomError(uploadFileRes.message || RES_MSG.COMMON.BAD_REQUEST, uploadFileRes.status || RESPONSES.BAD_GATEWAY);

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DOCS_UPLOADED_SUCCESS,
        data: uploadFileRes.data,
      });
    } catch (error: any) {
      logger.error(error, 'uploadDocsController Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Generates a report for a single offering with detailed metrics.
   * Provides comprehensive data about an offering's performance.
   *
   * @param {Request} req Express request object containing offering identifier
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to offering report data
   * @memberof UserAuthenticationController
   */
  public singleOfferingReportController = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { offeringId, page = 1, limit = 10, sort = JSON.stringify({ createdAt: -1 }), search = '' }: any = req.query;
      const sortCriteria = JSON.parse(sort as string);
      // const offeringId = new Types.ObjectId(req.query.offeringId);
      const result: PromiseResolve = await UserService.singleOfferingReport(
        { offeringId },
        {
          page: Number(page),
          limit: Number(limit),
          sort: sortCriteria,
          search,
        },
      );
      if (result.error) {
        throw new CustomError(result.message, result.status);
      }
      return ResponseHandler.success(res, {
        status: result.status || RESPONSES.SUCCESS,
        error: false,
        message: result.message || RES_MSG.USER.USERS_FETCH,
        data: result.data || [],
      });
    } catch (error) {
      logger.error(error, 'singleOfferingReportController Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };
}

export default new UserAuthenticationController();
