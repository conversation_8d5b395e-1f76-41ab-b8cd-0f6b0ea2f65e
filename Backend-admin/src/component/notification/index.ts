/* eslint-disable @typescript-eslint/no-unused-vars */
import { Request, Response } from 'express';
import { PromiseResolve } from '../../utils/common.interface';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { ResponseHandler } from '../../helpers/response.helper';
import logger from '../../helpers/logging/logger.helper';
class NotificationController {
  /**
   * Retrieves offering-related notifications for a user.
   * Fetches paginated notification data from the notification service
   * based on user ID.
   *
   * @param {Request} req Express request object containing pagination parameters and user context
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to user's offering notifications
   * @memberof NotificationController
   */
  public getOfferingNotification = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { userInfo } = req;
      const { userId } = userInfo;
      const payload = {
        page: req.query.page,
        limit: req.query.limit,
        userId: userId,
      };
      // await notificationClient.client.getOfferingRequestNotification(payload, async (error: any, response: any) => {
      //   if (error || response?.error) {
      //     const errorMessage = error?.message || response?.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR;
      //     const errorStatus = error?.status || RESPONSES.INTERNAL_SERVER_ERROR;

      //     console.error('gRPC Error:', error || response);
      //     return ResponseHandler.error(res, {
      //       status: errorStatus,
      //       error: true,
      //       message: errorMessage,
      //     });
      //   }

      //   return ResponseHandler.success(res, {
      //     status: response.status || RESPONSES.SUCCESS,
      //     error: false,
      //     message: response.message || RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
      //     data: {
      //       data: JSON.parse(response.data),
      //       totalCount: response.totalCount,
      //     },
      //   });
      // });
    } catch (error: any) {
      logger.error(error, 'getOffering Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Marks offering notifications as seen/read for a user.
   * Updates notification status to indicate user has viewed the notification.
   *
   * @param {Request} req Express request object containing notification ID and user context
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to notification update result
   * @memberof NotificationController
   */
  public seenOfferingNotification = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { userInfo } = req;
      const { userId } = userInfo;
      const payload = {
        _id: req.query._id,
        userId: userId,
      };
      // await notificationClient.client.seenOfferingRequestNotification(payload, async (error: any, response: any) => {
      //   if (error || response?.error) {
      //     const errorMessage = error?.message || response?.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR;
      //     const errorStatus = error?.status || RESPONSES.INTERNAL_SERVER_ERROR;

      //     console.error('gRPC Error:', error || response);
      //     return ResponseHandler.error(res, {
      //       status: errorStatus,
      //       error: true,
      //       message: errorMessage,
      //     });
      //   }

      //   return ResponseHandler.success(res, {
      //     status: response.status || RESPONSES.SUCCESS,
      //     error: false,
      //     message: response.message || RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
      //     data: JSON.parse(response.data),
      //   });
      // });
    } catch (error: any) {
      logger.error(error, 'getOffering Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };
}
export default new NotificationController();
