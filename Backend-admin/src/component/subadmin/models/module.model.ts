import mongoose, { Schema, Document } from 'mongoose';

export interface IModule extends Document {
  _id: string;
  title: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IUpdateModule {
  _id: string;
  title: string;
  createdAt?: Date;
  updatedAt?: Date;
}

const moduleSchema: Schema<IModule> = new Schema(
  {
    title: { type: String, required: true, unique: true },
  },
  {
    versionKey: false,
    timestamps: true,
  },
);

const Module = mongoose.model<IModule>('Module', moduleSchema);

export { Module };
