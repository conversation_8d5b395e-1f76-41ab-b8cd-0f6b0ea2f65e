import mongoose, { Schema, Document, Types } from 'mongoose';

export interface frontendroutes extends Document {
  _id: Types.ObjectId;
  moduelsId?: string;
  routePath?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IUpdatefrontendroutes {
  _id: Types.ObjectId;
  routePath?: string;
  moduelsId?: Types.ObjectId | string;
  createdAt?: Date;
  updatedAt?: Date;
}

const frontendroute: Schema<frontendroutes> = new Schema(
  {
    routePath: { type: String, required: true, unique: true },
    moduelsId: {
      type: String,
      ref: 'Module',
      required: false,
    },
  },
  {
    versionKey: false,
    timestamps: true,
  },
);

const frontendroutesSchema = mongoose.model<frontendroutes>('frontendroute', frontendroute);

export { frontendroutesSchema };
