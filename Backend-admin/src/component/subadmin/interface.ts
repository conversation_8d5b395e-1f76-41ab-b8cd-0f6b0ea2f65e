import { FilterQuery } from 'mongoose';
import { IPagination, PromiseResolve } from '../../utils/common.interface';
import { IUpdatepermission } from './models/permission.model';
import { IUpdateUserModel, IUserModel } from '../userAuthentications/models/user.model';
export interface ISubAdminService {
  /**
   * @param {IOffering} searchDetails
   * @param {IOffering} fields
   * @param {IOffering} excludeFields
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */

  fetchUserDetails(searchDetails: FilterQuery<IUserModel>, fields?: string[], excludeFields?: string[], isKyc?: boolean): Promise<PromiseResolve>;

  fetchSubAdminDetails(searchDetails: FilterQuery<IUserModel>, fields?: string[], excludeFields?: string[], isKyc?: boolean): Promise<PromiseResolve>;

  fetchModuleList(projection?: string[], pagination?: IPagination): Promise<PromiseResolve>;

  /**
   * @param {IUpdateOffering} body
   * @param {IOffering} filter
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  createsubadmin(body: any): Promise<PromiseResolve>;

  updateSubadmin(body: any): Promise<PromiseResolve>;

  /**
   * @param {IOffering} searchDetails
   * @param {Array} projection
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */

  fetchUserListcsv(searchDetails: FilterQuery<IUpdatepermission>, projection?: string[]): Promise<PromiseResolve>;

  /**
   * @param {IOffering} searchDetails
   * @param {Array} projection
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof UserService
   */
  fetchSubaminlist(searchDetails: FilterQuery<IUpdateUserModel>, projection?: string[], pagination?: IPagination): Promise<PromiseResolve>;
}
