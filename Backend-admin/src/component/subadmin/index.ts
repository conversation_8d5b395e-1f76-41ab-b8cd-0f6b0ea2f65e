import { Request, Response } from 'express';
import { PromiseResolve, UserType } from '../../utils/common.interface';
// import OfferingService from "./service";
import transferService from './service';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import CustomError from '../../helpers/customError.helper';
import { ResponseHandler } from '../../helpers/response.helper';
import logger from '../../helpers/logging/logger.helper';
import UserService from '../userAuthentications/service';
import emailHelper from '../../helpers/email.helper';
import { Module } from './models/module.model';
import SubAdminService from './service';
import RedisHelper from '../../helpers/redis.helper';
import { permissionSchema } from './models/permission.model';
import { userSchema } from '../userAuthentications/models/user.model';
const adminurl = process.env.ADMINURL;

class SubAdminController {
  /**
   * Creates a new subadmin user in the system with appropriate permissions.
   * Validates email and wallet address uniqueness, hashes password, and sends
   * invitation email to the new subadmin.
   *
   * @param {Request} req Express request object containing subadmin details (name, email, password, walletAddress)
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to the created subadmin data
   * @memberof SubAdminController
   */
  public createSubadmin = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { name, email: Email, password, confirmPassword, permission, walletAddress: WalletAddress } = req.body;
      const walletAddress = WalletAddress?.toLowerCase();
      const email = Email?.toLowerCase();

      // const searchQuery = { email: email };
      const emailExists: PromiseResolve = await UserService.fetchUserDetails({ email: email });
      const walletAddressExists: PromiseResolve = await UserService.fetchUserDetails({ walletAddress: walletAddress });
      if (!emailExists.error) {
        throw new CustomError(RES_MSG.USER.USER_EXIST, RESPONSES.CONFLICT);
      }
      if (!walletAddressExists.error) {
        throw new CustomError(RES_MSG.USER.WALLET_EXIST, RESPONSES.CONFLICT);
      }
      const isTaWalletAddressExists = await UserService.searchUsersAndAgents(walletAddress);
      if (isTaWalletAddressExists.error) {
        throw new CustomError(RES_MSG.USER.WALLET_EXIST, RESPONSES.CONFLICT);
      }
      if (password !== confirmPassword) {
        throw new CustomError(RES_MSG.USER.PASSWORD, RESPONSES.BAD_REQUEST);
      }
      // const hashedPassword = await bcrypt.hash(password, 10);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      // const data = {
      //   name,
      //   email,
      //   password: hashedPassword,
      //   UserType: 'Subadmin',
      //   walletAddress: walletAddress,
      // };
      const createUserResp: PromiseResolve = await SubAdminService.createsubadmin({ ...req.body, name, email, password, confirmPassword, permission, walletAddress });
      if (createUserResp.error) {
        throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.CONFLICT);
      }
      const capitalizeFirstLetter = (str: string): string => {
        return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
      };

      const emailDetail = {
        name: capitalizeFirstLetter(name),
        email,
        adminurl,
        password,
      };
      emailHelper.sendEmailTemplate(email, 'SubAdminInvite', emailDetail);
      return ResponseHandler.success(res, {
        status: createUserResp.status || RESPONSES.SUCCESS,
        error: false,
        message: createUserResp.message || RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: createUserResp.data,
      });
    } catch (error: any) {
      logger.error(error, 'getOffering Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Retrieves a list of system modules with user-specific permissions.
   * Determines module access rights based on user role (admin or subadmin)
   * and returns appropriately formatted permission data.
   *
   * @param {Request} req Express request object containing user context
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to module list with permissions
   * @memberof SubAdminController
   */
  public moduleList = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const modulelist = await Module.find();
      const email = req?.userInfo.email;
      const role = req?.userInfo.userType;
      const searchDetails = { email: email };
      const permission = await permissionSchema.findOne(searchDetails);
      const pr = permission?.permission;

      // If role is admin, set read and write to true for all modules
      const updatedModules = modulelist.map((module) => {
        if (role === 'admin') {
          return {
            id: module.id,
            title: module.title,
            read: true,
            write: true,
          };
        }

        // For non-admin users, check permissions
        const modulePermission = pr.find((perm: any) => perm.moduelsId.equals(module._id));
        return {
          id: module.id,
          title: module.title,
          read: module.title === 'Manage SubAdmin' ? false : module.title === 'Dashboard' ? true : modulePermission ? modulePermission.read : false,
          write: module.title === 'Manage SubAdmin' ? false : module.title === 'Dashboard' ? true : modulePermission ? modulePermission.write : false,
        };
      });

      return ResponseHandler.success(res, {
        status: 200,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
        data: updatedModules,
      });
    } catch (error) {
      logger.error(error, 'getUserslist Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Retrieves the complete list of system modules by ID for admin users only.
   * Used for module management and permission configuration.
   *
   * @param {Request} req Express request object containing user context
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to a list of all system modules
   * @memberof SubAdminController
   */
  public getModuleListById = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const modulelist = await Module.find();

      const role = req?.userInfo.userType;
      if (role != UserType.Admin) {
        throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.CONFLICT);
      }

      return ResponseHandler.success(res, {
        status: 200,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
        data: modulelist,
      });
    } catch (error) {
      logger.error(error, 'getUserslist Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Exports transfer agent list data to CSV format.
   * Retrieves filtered transfer agent data with optional date filtering
   * and formats it for CSV download.
   *
   * @param {Request} req Express request object containing filter parameters (startDate, endDate)
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to CSV export data
   * @memberof SubAdminController
   */
  public transferAgentListCsv = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { startDate = '', endDate = '' } = req.query;

      // Initialize filters object
      const filters: any = {};

      // Add date filtering based on startDate and endDate
      if (startDate && endDate) {
        filters.createdAt = {
          $gte: new Date(startDate as string),
          $lte: new Date(endDate as string),
        };
      } else if (startDate) {
        filters.createdAt = { $gte: new Date(startDate as string) };
      } else if (endDate) {
        filters.createdAt = { $lte: new Date(endDate as string) };
      }

      // Parse sort criteria
      // let sortCriteria = JSON.parse(sort as string);

      // Define the projection fields
      const projection = ['name', 'email', 'isActive', 'noOfOfferings', 'offeringId', 'status', 'walletAddress', 'createdAt'];

      // Fetch user list with the applied filters and sorting
      const userDetails = await transferService.fetchUserListcsv(filters, projection);

      return ResponseHandler.success(res, {
        status: 200,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
        data: userDetails.data,
      });
    } catch (error) {
      logger.error(error, 'getUserslist Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Unblocks a previously blocked subadmin account.
   * Updates account status to active and sends notification.
   *
   * @param {Request} req Express request object containing subadmin ID
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to account unblock result
   * @memberof SubAdminController
   */
  public unblock = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { email } = req.body;
      // Fetch user details and return specific fields
      const userDetails = await UserService.fetchUserDetails({ email }, ['name', 'email', 'isActive']);
      if (userDetails.error) {
        throw new CustomError(userDetails.message || RES_MSG.COMMON.SOMETHING_WRONG, userDetails.status || RESPONSES.BAD_REQUEST);
      }

      const currentIsActive = userDetails.data.isActive;

      // gRPC call to unblock the user
      // Update user's active status and reset kycCount if activated
      const updatedUser = await userSchema.findOneAndUpdate(
        { email },
        {
          isActive: currentIsActive ? false : true,
        },
        { new: true }, // Return the updated document
      );
      const emailDetails = { name: userDetails.data.name };
      let message;
      if (updatedUser.isActive) {
        emailHelper.sendEmailTemplate(email, 'subadminAccountUnBlock', emailDetails);
        message = RES_MSG.COMMON.ADMIN_UNBLOCK_SUBADMIN;
      } else {
        emailHelper.sendEmailTemplate(email, 'SubadminAccountBlock', emailDetails);
        message = RES_MSG.COMMON.ADMIN_BLOCK_SUBADMIN;
        await RedisHelper.deleteKey(`accessToken:${email}`);
      }

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message,
        data: updatedUser.isActive,
      });
    } catch (error: any) {
      logger.error(error, 'unblock Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Retrieves a paginated list of subadmins with optional filtering and sorting.
   * Used for admin dashboard to manage subadmin accounts.
   *
   * @param {Request} req Express request object containing pagination, filter, and sort parameters
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to a paginated list of subadmins
   * @memberof SubAdminController
   */
  public subAdminList = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const {
        page = 1,
        limit = 10,
        sort = JSON.stringify({ createdAt: -1 }),
        search = '',

        isActive,
      } = req.query;

      const filters = {
        ...(isActive === 'true' || isActive === 'false' ? { isActive: isActive === 'true' } : {}),
      };
      const sortCriteria = JSON.parse(sort as string);
      const projection = ['name', 'email', 'isActive', 'createdAt', 'lastLogin', 'walletAddress', 'onchainID'];

      const userDetails = await SubAdminService.fetchSubaminlist(filters, projection, {
        page: Number(page),
        limit: Number(limit),
        sort: sortCriteria,
        search,
      });

      return ResponseHandler.success(res, {
        status: 200,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
        data: userDetails.data,
      });
    } catch (error) {
      logger.error(error, 'getUserslist Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Exports subadmin list data to CSV format.
   * Retrieves filtered subadmin data and formats it for CSV download.
   *
   * @param {Request} req Express request object containing filter parameters
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to CSV export data
   * @memberof SubAdminController
   */
  public getSubadminListControllerCsv = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { startDate = '', endDate = '' } = req.query;

      // Initialize filters object
      const filters: any = {
        ...(startDate && endDate
          ? { createdAt: { $gte: new Date(startDate as string), $lte: new Date(endDate as string) } }
          : startDate
            ? { createdAt: { $gte: new Date(startDate as string) } }
            : endDate
              ? { createdAt: { $lte: new Date(endDate as string) } }
              : {}),
      };

      // Parse sort criteria
      // let sortCriteria = JSON.parse(sort as string);

      // Define the projection fields
      const projection = ['name', 'email', 'isActive', 'createdAt', 'lastLogin', 'walletAddress', 'onchainID', 'status'];

      // Fetch user list with the applied filters and sorting
      const userDetails = await SubAdminService.fetchUserListcsv(filters, projection);

      return ResponseHandler.success(res, {
        status: 200,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
        data: userDetails.data,
      });
    } catch (error) {
      logger.error(error, 'getUserslist Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Retrieves permission settings for a specific subadmin by ID.
   * Used for viewing and managing individual subadmin permissions.
   *
   * @param {Request} req Express request object containing subadmin ID
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to subadmin permission data
   * @memberof SubAdminController
   */
  public getPermissionListById = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const modulelist = await Module.find();
      const id = req.query.userId;
      const searchDetails = { userId: id };
      let role: string;
      const permission = await permissionSchema.findOne(searchDetails);
      const details = { _id: id };
      const user = await userSchema.findOne(details);
      const pr = permission?.permission;

      // If role is admin, set read and write to true for all modules
      const updatedModules = modulelist.map((module) => {
        if (role === UserType.Admin) {
          return {
            id: module.id,
            title: module.title,
            read: true,
            write: true,
          };
        }
        // For non-admin users, check permissions
        const modulePermission = pr.find((perm: any) => perm.moduelsId.equals(module._id));
        return {
          id: module?.id,
          title: module?.title,
          read: module.title === 'Manage SubAdmin' ? false : module.title === 'Dashboard' ? true : module.title === 'Settings' ? true : modulePermission ? modulePermission.read : false,

          write: module.title === 'Manage SubAdmin' ? false : module.title === 'Dashboard' ? true : module.title === 'Settings' ? true : modulePermission ? modulePermission.write : false,
        };
      });
      const list = {
        name: user?.name,
        email: user?.email,
        walletAddress: user?.walletAddress,
        onchainID: user?.onchainID,
        updatedModules,
      };

      return ResponseHandler.success(res, {
        status: 200,
        error: false,
        message: RES_MSG.USER.USERS_FETCH,
        data: list,
      });
    } catch (error) {
      logger.error(error, 'getUserslist Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };

  /**
   * Updates the active status of a subadmin account.
   * Enables or disables account access and sends appropriate notification.
   *
   * @param {Request} req Express request object containing subadmin ID and status information
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve>} A promise that resolves to status update result
   * @memberof SubAdminController
   */
  public updateStatus = async (req: Request, res: Response): Promise<PromiseResolve> => {
    try {
      const { userId } = req.body;
      // const searchQuery = { email: email };
      const emailExists: PromiseResolve = await SubAdminService.fetchSubAdminDetails({ userId: userId });
      if (emailExists.error) {
        throw new CustomError(RES_MSG.USER.USER_ALREADY_EXIST, RESPONSES.CONFLICT);
      }

      const createUserResp: PromiseResolve = await SubAdminService.updateSubadmin(req?.body);

      await RedisHelper.deleteKey(`accessToken:${createUserResp?.data?.email}`);

      return ResponseHandler.success(res, {
        status: createUserResp.status || RESPONSES.SUCCESS,
        error: false,
        message: createUserResp.message || RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: createUserResp.data,
      });
    } catch (error: any) {
      logger.error(error, 'getOffering Error');
      return ResponseHandler.error(res, {
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
      });
    }
  };
}

export default new SubAdminController();
