import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { PromiseResolve, IPagination } from '../../utils/common.interface';
// import EventLogSchemaModel from './model/EventLogSchema.model';
import { ITransactionsService } from './interface';
import logger from '../../helpers/logging/logger.helper';
import { OrderSchema } from '../offerings/models/order.model';

class TransactionsService implements ITransactionsService {
  /**
   * @param {EventLog} filters
   * @param {IPagination} pagination
   * @returns {Promise<PromiseResolve>}
   * @memberof OfferingService
   */
  fetchTranscations = async (filters: any, pagination: IPagination): Promise<PromiseResolve> => {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 }, search } = pagination;
      const skip = (page - 1) * limit;

      // Prepare the match object with the offeringId and status
      const match: any = {
        offeringId: filters.offeringId,
      };

      // If filters.status is 'ALL', include MINTED, FREEZE, UNFREEZE
      if (filters.status === 'ALL') {
        match.orderType = { $in: ['MINTED', 'FREEZE', 'UNFREEZE'] }; // Include all these statuses
      } else if (filters.status !== 'ALL') {
        match.orderType = filters.status; // Add the specific orderType filter
      }

      // Filter out 'PENDING' and 'APPROVED' statuses
      match.status = { $nin: ['PENDING', 'APPROVED'] };

      // Prepare the base pipeline
      const pipeline: any = [
        {
          $match: match,
        },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'userDetails',
          },
        },
        {
          $unwind: {
            path: '$userDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            name: '$userDetails.name',
            email: '$userDetails.email',
            userImage: '$userDetails.userImage',
          },
        },
        {
          $match: search
            ? {
                $or: [{ name: { $regex: search, $options: 'i' } }, { email: { $regex: search, $options: 'i' } }],
              }
            : {},
        },
        {
          $project: {
            userDetails: 0,
          },
        },
        {
          $sort: sort,
        },
        {
          $facet: {
            metadata: [{ $count: 'totalCount' }],
            orders: [{ $skip: skip }, { $limit: limit }],
          },
        },
      ];

      // First, get the results with pagination and metadata
      const results = await OrderSchema.aggregate(pipeline);

      // Extract metadata and orders from the results
      const metadata = results[0]?.metadata[0] || { totalCount: 0 };
      const orders = results[0]?.orders || [];
      const totalCount = metadata.totalCount;
      const totalPages = Math.ceil(totalCount / limit);
      const nextPage = page < totalPages ? page + 1 : null;
      const previousPage = page > 1 ? page - 1 : null;

      // If no search is applied, return the results as is
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.COMMON.RECORD_FETCH,
        data: {
          transactions: orders,
          currentPage: page,
          totalPages,
          totalCount,
          nextPage,
          previousPage,
        },
      };
    } catch (error) {
      logger.error(error, 'fetchTransactions error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };

  fetchTransacationsCsv = async (filters: any): Promise<PromiseResolve> => {
    try {
      // Prepare the match object with the offeringId and status
      const match: any = {
        offeringId: filters.offeringId,
      };

      // If filters.status is 'ALL', include MINTED, FREEZE, UNFREEZE
      if (filters.status === 'ALL') {
        match.orderType = { $in: ['MINTED', 'FREEZE', 'UNFREEZE'] }; // Include all these statuses
      } else if (filters.status !== 'ALL') {
        match.orderType = filters.status; // Add the specific orderType filter
      }

      // Filter out 'PENDING' and 'APPROVED' statuses
      match.status = { $nin: ['PENDING', 'APPROVED'] };

      // Prepare the base pipeline without pagination and search
      const pipeline: any = [
        {
          $match: match,
        },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'userDetails',
          },
        },
        {
          $unwind: {
            path: '$userDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            name: '$userDetails.name',
            email: '$userDetails.email',
            userImage: '$userDetails.userImage',
          },
        },
        {
          $project: {
            userDetails: 0,
          },
        },
        {
          $sort: { createdAt: -1 }, // Assuming we want to sort by 'createdAt'
        },
      ];

      // Execute the aggregation pipeline to get the results
      const results = await OrderSchema.aggregate(pipeline);

      // Extract orders from the results
      const orders = results || [];

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.COMMON.RECORD_FETCH,
        data: {
          transactions: orders,
        },
      };
    } catch (error) {
      logger.error(error, 'fetchTransactions error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  };
}

export default new TransactionsService();
