import { Request, Response } from 'express';
import { PromiseResolve } from '../../utils/common.interface';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { ResponseHandler } from '../../helpers/response.helper';

import { Types } from 'mongoose';
import TransactionsService from './service';
import logger from '../../helpers/logging/logger.helper';
import CommonHelper from '../../helpers/common.helper';
import BondingClient from '../../_grpc/clients/bonding.client';
// import {validateStringRegex}  '../../helpers/common.helper'

class TransactionController {
  /**
   * Retrieves transactions for a specific offering with filtering and pagination.
   * Validates search parameters, applies filters by transaction type and user context,
   * and returns formatted transaction data.
   *
   * @param {Request} req Express request object containing offering ID, pagination, and filter parameters
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve|void>} A promise that resolves to filtered transaction data
   * @memberof TransactionController
   */
  public getTransactions = async (req: Request, res: Response): Promise<PromiseResolve | void> => {
    try {
      const { page = 1, limit = 10, sort = '', search = '', type = '' } = req.query;
      // Define the regex for disallowed symbols
      const disallowedSymbolsRegex = /[!+#$%^*(),?":{}|<>]/;

      // Validate the search parameter using the CommonHelper's validateStringRegex
      const validateSearchResult = await CommonHelper.validateStringRegex(search as string, disallowedSymbolsRegex, RES_MSG.ERROR_MSG.DISALLOWED_SYMBOLS);

      // If validation fails, return the error response immediately
      if (validateSearchResult.error) {
        return ResponseHandler.error(res, {
          status: validateSearchResult.status,
          error: true,
          message: validateSearchResult.message,
        });
      }

      const userId = new Types.ObjectId(req.userInfo.userId);
      const offeringId = new Types.ObjectId(req.params.offeringId);

      const filters = {
        ...{ status: type?.toString().toUpperCase() },
        userId,
        offeringId,
      };

      const result: PromiseResolve = await TransactionsService.fetchTranscations(filters, {
        page: Number(page),
        limit: Number(limit),
        ...(search && { search: search }),
        ...(sort && { sort: sort }),
      });

      return ResponseHandler.success(res, {
        status: result.status || RESPONSES.SUCCESS,
        error: false,
        message: result.message || RES_MSG.USER.USERS_FETCH,
        data: result.data || [],
      });
    } catch (error) {
      logger.error(error, 'getTransactions Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Exports transaction data to CSV format for a specific offering.
   * Retrieves all transactions matching the specified filters without pagination
   * and formats the data for CSV download.
   *
   * @param {Request} req Express request object containing offering ID and type filter
   * @param {Response} res Express response object
   * @returns {Promise<PromiseResolve|void>} A promise that resolves to transaction data for CSV export
   * @memberof TransactionController
   */
  public getTransactionsCsv = async (req: Request, res: Response): Promise<PromiseResolve | void> => {
    try {
      const { type = '' } = req.query; // Only keep the type parameter

      const userId = new Types.ObjectId(req.userInfo.userId);
      const offeringId = new Types.ObjectId(req.params.offeringId);

      // Filters object
      const filters = {
        ...{ status: type?.toString().toUpperCase() },
        userId,
        offeringId,
      };

      // Prepare pagination with default values for 'page' and 'limit'

      // Fetch transactions without pagination, search, and regex validation
      const result: PromiseResolve = await TransactionsService.fetchTransacationsCsv(filters);

      return ResponseHandler.success(res, {
        status: result.status || RESPONSES.SUCCESS,
        error: false,
        message: result.message || RES_MSG.USER.USERS_FETCH,
        data: result.data || [],
      });
    } catch (error) {
      logger.error(error, 'getTransactions Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Retrieves staking transactions for a specific token with pagination.
   * Calls bonding service via gRPC to fetch staking transaction data.
   * @param {Request} req - The Express request object containing token and pagination parameters
   * @param {Response} res - The Express response object
   * @returns {Promise<PromiseResolve|void>} A promise that resolves with the staking transaction data or error response
   */
  public getStakingTransactions = async (req: Request, res: Response): Promise<PromiseResolve | void> => {
    try {
      const { page, limit, search, userAddress, type } = req.query;
      const { token } = req.params;

      const pagination = {
        page: Number(page),
        limit: Number(limit),
        search: String(search || ''),
      };

      // Call bonding service via gRPC using class-based client
      const result: any = await BondingClient.getStakingTransactions(token as string, pagination, userAddress as string, type as string);

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: 'Staking transactions fetched successfully',
        data: result,
      });
    } catch (error) {
      logger.error(error, 'getStakingTransactions Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };

  /**
   * Retrieves user staking summary for a specific token with pagination.
   * Calls bonding service via gRPC to fetch aggregated user staking data.
   * @param {Request} req - The Express request object containing token and pagination parameters
   * @param {Response} res - The Express response object
   * @returns {Promise<PromiseResolve|void>} A promise that resolves with the user staking summary data or error response
   */
  public getUserStakingSummary = async (req: Request, res: Response): Promise<PromiseResolve | void> => {
    try {
      const { page, limit, search } = req.query;
      const { token } = req.params;

      const pagination = {
        page: Number(page),
        limit: Number(limit),
        search: String(search || ''),
      };

      // Call bonding service via gRPC using class-based client
      const result: any = await BondingClient.getUserStakingSummary(token as string, pagination);

      return ResponseHandler.success(res, {
        status: RESPONSES.SUCCESS,
        error: false,
        message: 'User staking summary fetched successfully',
        data: result,
      });
    } catch (error) {
      logger.error(error, 'getUserStakingSummary Error');
      return ResponseHandler.error(res, {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      });
    }
  };
}

export default new TransactionController();
