export const swaggerDefinition: any = {
  openapi: '3.0.0',
  info: {
    title: 'Libertum Tokenization Admin API',
    description:
      'Comprehensive API documentation for Libertum Tokenization Admin Service. This service handles tokenization processes, converting rights to assets into digital tokens on blockchain. It includes user management, offering management, transaction processing, and administrative functions.',
    version: '1.0.0',
    contact: {
      name: 'Libertum Support',
      email: '<EMAIL>',
    },
    license: {
      name: 'MIT',
      url: 'https://opensource.org/licenses/MIT',
    },
  },
  servers: [
    {
      url: 'http://localhost:7002',
      description: 'Development server',
    },
    {
      url: 'https://api.libertum.com',
      description: 'Production server',
    },
  ],
  tags: [
    {
      name: 'Authentication',
      description: 'User authentication and authorization endpoints',
    },
    {
      name: 'User Management',
      description: 'User account management and administration',
    },
    {
      name: 'Offering Management',
      description: 'Investment offering creation and management',
    },
    {
      name: 'Transfer Agent',
      description: 'Transfer agent management and operations',
    },
    {
      name: 'Sub-Admin',
      description: 'Sub-administrator management and permissions',
    },
    {
      name: 'Transactions',
      description: 'Transaction processing and history',
    },
    {
      name: 'Notifications',
      description: 'Notification management and delivery',
    },
    {
      name: 'NFT',
      description: 'Non-Fungible Token collection management',
    },
    {
      name: 'Dashboard',
      description: 'Dashboard and analytics endpoints',
    },
    {
      name: 'System',
      description: 'System health and status endpoints',
    },
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'Enter JWT token in the format: Bearer <token>',
      },
    },
    schemas: {
      Error: {
        type: 'object',
        properties: {
          message: {
            type: 'string',
            description: 'Error message',
          },
          status: {
            type: 'integer',
            description: 'HTTP status code',
          },
          error: {
            type: 'boolean',
            description: 'Error flag',
          },
        },
        required: ['message', 'status', 'error'],
      },
      Success: {
        type: 'object',
        properties: {
          message: {
            type: 'string',
            description: 'Success message',
          },
          status: {
            type: 'integer',
            description: 'HTTP status code',
          },
          error: {
            type: 'boolean',
            description: 'Error flag (always false for success)',
          },
          data: {
            type: 'object',
            description: 'Response data',
          },
        },
        required: ['message', 'status', 'error'],
      },
    },
  },
  security: [
    {
      bearerAuth: [],
    },
  ],
  paths: {
    '/admin/v1/health-check': {
      get: {
        summary: 'Health Check',
        description: 'Check the health status of the API service',
        tags: ['System'],
        responses: {
          '200': {
            description: 'Service is healthy',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Success',
                },
                example: {
                  message: 'System health is OK.',
                  status: 200,
                  error: false,
                  data: {
                    timestamp: '2024-01-01T00:00:00.000Z',
                  },
                },
              },
            },
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
        },
      },
    },
    '/admin/v1/login': {
      post: {
        summary: 'Admin Login',
        description: 'Authenticate admin user with email and password. Returns authentication tokens and user status.',
        tags: ['Authentication'],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  email: {
                    type: 'string',
                    format: 'email',
                    description: 'Admin email address',
                    example: '<EMAIL>',
                  },
                  password: {
                    type: 'string',
                    description: 'Admin password',
                    example: 'Admin@123',
                  },
                },
                required: ['email', 'password'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'Login successful with OTP required',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    status: { type: 'integer' },
                    data: {
                      type: 'object',
                      properties: {
                        isOtpActive: { type: 'boolean' },
                      },
                    },
                    error: { type: 'boolean' },
                  },
                },
                example: {
                  message: 'OTP sent successfully',
                  status: 200,
                  data: { isOtpActive: true },
                  error: false,
                },
              },
            },
          },
          '201': {
            description: 'Login successful without OTP',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    status: { type: 'integer' },
                    data: {
                      type: 'object',
                      properties: {
                        refreshToken: { type: 'string' },
                        accessToken: { type: 'string' },
                        is2FAActive: { type: 'boolean' },
                        role: { type: 'string' },
                        walletAddress: { type: 'string' },
                        isWalletPublished: { type: 'boolean' },
                      },
                    },
                    error: { type: 'boolean' },
                  },
                },
                example: {
                  message: 'You have logged in successfully. Welcome back!',
                  status: 201,
                  data: {
                    refreshToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
                    accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
                    is2FAActive: false,
                    role: 'admin',
                    walletAddress: '******************************************',
                    isWalletPublished: true,
                  },
                  error: false,
                },
              },
            },
          },
          '401': {
            description: 'Invalid credentials',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
                example: {
                  message: 'The credentials you provided are invalid.',
                  status: 401,
                  error: true,
                },
              },
            },
          },
          '423': {
            description: 'Account locked',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
                example: {
                  message: 'Your account is locked due to multiple unsuccessful login attempts.',
                  status: 423,
                  error: true,
                },
              },
            },
          },
        },
      },
    },
    '/admin/v1/verify': {
      post: {
        summary: 'Verify OTP',
        description: 'Verify One-Time Password (OTP) for user authentication during login or other operations.',
        tags: ['Authentication'],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  otp: {
                    type: 'string',
                    description: 'One-Time Password received via email',
                    example: '123456',
                  },
                  email: {
                    type: 'string',
                    format: 'email',
                    description: 'User email address',
                    example: '<EMAIL>',
                  },
                  type: {
                    type: 'string',
                    enum: ['login', 'registration', 'reset-password'],
                    description: 'Type of OTP verification',
                    example: 'login',
                  },
                },
                required: ['otp', 'email', 'type'],
              },
            },
          },
        },
        responses: {
          '201': {
            description: 'OTP verified successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    status: { type: 'integer' },
                    data: {
                      type: 'object',
                      properties: {
                        refreshToken: { type: 'string' },
                        accessToken: { type: 'string' },
                        role: { type: 'string' },
                        is2FAActive: { type: 'boolean' },
                        walletAddress: { type: 'string' },
                        isWalletPublished: { type: 'boolean' },
                      },
                    },
                    error: { type: 'boolean' },
                  },
                },
                example: {
                  message: 'OTP verified successfully.',
                  status: 201,
                  data: {
                    refreshToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
                    accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
                    role: 'admin',
                    is2FAActive: false,
                    walletAddress: '******************************************',
                    isWalletPublished: true,
                  },
                  error: false,
                },
              },
            },
          },
          '400': {
            description: 'Invalid OTP or expired',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
                example: {
                  message: 'The OTP entered is invalid. Please check and try again.',
                  status: 400,
                  error: true,
                },
              },
            },
          },
          '404': {
            description: 'User not found',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
                example: {
                  message: 'No user found with the provided information.',
                  status: 404,
                  error: true,
                },
              },
            },
          },
        },
      },
    },
    '/admin/v1/resend-otp': {
      post: {
        summary: 'Resend OTP',
        description: 'Resend One-Time Password (OTP) for user authentication. Used when the original OTP expires or is not received.',
        tags: ['Authentication'],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  email: {
                    type: 'string',
                    format: 'email',
                    description: 'User email address',
                    example: '<EMAIL>',
                  },
                  type: {
                    type: 'string',
                    enum: ['login', 'registration', 'reset-password'],
                    description: 'Type of OTP to resend',
                    example: 'login',
                  },
                },
                required: ['email', 'type'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'OTP resent successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    status: { type: 'integer' },
                    data: { type: 'null' },
                    error: { type: 'boolean' },
                  },
                },
                example: {
                  message: 'OTP has been sent successfully.',
                  status: 200,
                  data: null,
                  error: false,
                },
              },
            },
          },
          '400': {
            description: 'Bad Request - Invalid email or missing type',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
                example: {
                  message: 'The request is invalid. Please check parameters and ensure all required fields are filled out.',
                  status: 400,
                  error: true,
                },
              },
            },
          },
          '404': {
            description: 'User not found',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
                example: {
                  message: 'No user found with the provided information.',
                  status: 404,
                  error: true,
                },
              },
            },
          },
        },
      },
    },
    '/admin/v1/forgot-password': {
      post: {
        summary: 'Forgot Password',
        description: 'Initiate password reset process by sending OTP to user email address.',
        tags: ['Authentication'],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  email: {
                    type: 'string',
                    format: 'email',
                    description: 'User email address for password reset',
                    example: '<EMAIL>',
                  },
                },
                required: ['email'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'Password reset OTP sent successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    status: { type: 'integer' },
                    data: { type: 'null' },
                    error: { type: 'boolean' },
                  },
                },
                example: {
                  message: 'Please verify the email address entered. If it is registered, an OTP will be sent to you.',
                  status: 200,
                  data: null,
                  error: false,
                },
              },
            },
          },
          '400': {
            description: 'Invalid email format',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
                example: {
                  message: 'The request is invalid. Please check parameters and ensure all required fields are filled out.',
                  status: 400,
                  error: true,
                },
              },
            },
          },
          '404': {
            description: 'User not found',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
                example: {
                  message: 'No user found with the provided information.',
                  status: 404,
                  error: true,
                },
              },
            },
          },
        },
      },
    },
    '/admin/v1/reset-password': {
      post: {
        summary: 'Reset Password',
        description: 'Reset user password using a valid reset token and new password.',
        tags: ['Authentication'],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  token: {
                    type: 'string',
                    description: 'Password reset token received via email',
                    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
                  },
                  newPassword: {
                    type: 'string',
                    description: 'New password for the user (minimum 8 characters)',
                    example: 'NewPassword@123',
                  },
                },
                required: ['token', 'newPassword'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'Password reset successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    status: { type: 'integer' },
                    data: { type: 'null' },
                    error: { type: 'boolean' },
                  },
                },
                example: {
                  message: 'Password has been changed successfully.',
                  status: 200,
                  data: null,
                  error: false,
                },
              },
            },
          },
          '400': {
            description: 'Invalid or expired token',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
                example: {
                  message: 'The request token has expired. Please try again.',
                  status: 400,
                  error: true,
                },
              },
            },
          },
          '422': {
            description: 'Password validation failed',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
                example: {
                  message: 'The new password must be different from the current password.',
                  status: 422,
                  error: true,
                },
              },
            },
          },
        },
      },
    },
    '/admin/v1/auth/approve': {
      post: {
        summary: 'Approve or Reject User KYC',
        description: 'Approve or reject user KYC (Know Your Customer) verification based on submitted documents and information.',
        tags: ['User Management'],
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  email: {
                    type: 'string',
                    format: 'email',
                    description: 'User email address',
                    example: '<EMAIL>',
                  },
                  kycStatus: {
                    type: 'string',
                    enum: ['APPROVED', 'REJECTED'],
                    description: 'KYC approval status',
                    example: 'APPROVED',
                  },
                  kycReason: {
                    type: 'string',
                    description: 'Reason for rejection (required if status is REJECTED)',
                    example: 'Documents are not clear',
                    nullable: true,
                  },
                },
                required: ['email', 'kycStatus'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'KYC status updated successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    status: { type: 'integer' },
                    data: { type: 'null' },
                    error: { type: 'boolean' },
                  },
                },
                example: {
                  message: 'KYC has been approved successfully.',
                  status: 200,
                  data: null,
                  error: false,
                },
              },
            },
          },
          '400': {
            description: 'Bad request or validation error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
                example: {
                  message: 'User KYC is already approved.',
                  status: 400,
                  error: true,
                },
              },
            },
          },
          '401': {
            description: 'Unauthorized access',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          '404': {
            description: 'User not found',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
                example: {
                  message: 'No user found with the provided information.',
                  status: 404,
                  error: true,
                },
              },
            },
          },
        },
      },
    },
    '/admin/v1/auth/getUserslist': {
      get: {
        summary: 'Get Users List',
        description: 'Retrieve a paginated list of users with optional filtering and sorting. Supports filtering by user type, active status, KYC status, and search functionality.',
        tags: ['User Management'],
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'page',
            in: 'query',
            required: false,
            schema: {
              type: 'integer',
              minimum: 1,
              default: 1,
            },
            description: 'Page number for pagination',
            example: 1,
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            schema: {
              type: 'integer',
              minimum: 1,
              maximum: 100,
              default: 10,
            },
            description: 'Number of users to return per page',
            example: 10,
          },
          {
            name: 'search',
            in: 'query',
            required: false,
            schema: {
              type: 'string',
            },
            description: 'Search term for user name or email',
            example: 'john',
          },
          {
            name: 'userType',
            in: 'query',
            required: false,
            schema: {
              type: 'string',
              enum: ['investor', 'issuer', 'admin', 'subadmin'],
            },
            description: 'Filter by user type',
            example: 'investor',
          },
          {
            name: 'isActive',
            in: 'query',
            required: false,
            schema: {
              type: 'boolean',
            },
            description: 'Filter by active status',
            example: true,
          },
          {
            name: 'kycStatus',
            in: 'query',
            required: false,
            schema: {
              type: 'string',
              enum: ['PENDING', 'APPROVED', 'REJECTED'],
            },
            description: 'Filter by KYC status',
            example: 'APPROVED',
          },
        ],
        responses: {
          '200': {
            description: 'Users list retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    status: { type: 'integer' },
                    data: {
                      type: 'object',
                      properties: {
                        users: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              _id: { type: 'string' },
                              email: { type: 'string' },
                              name: { type: 'string' },
                              userType: { type: 'string' },
                              isActive: { type: 'boolean' },
                              kycStatus: { type: 'string' },
                              walletAddress: { type: 'string' },
                              createdAt: { type: 'string', format: 'date-time' },
                              lastLogin: { type: 'string', format: 'date-time' },
                            },
                          },
                        },
                        totalCount: { type: 'integer' },
                        currentPage: { type: 'integer' },
                        totalPages: { type: 'integer' },
                      },
                    },
                    error: { type: 'boolean' },
                  },
                },
                example: {
                  message: 'Users fetched successfully.',
                  status: 200,
                  data: {
                    users: [
                      {
                        _id: '66f109fabfffada3bec13652',
                        email: '<EMAIL>',
                        name: 'John Doe',
                        userType: 'investor',
                        isActive: true,
                        kycStatus: 'APPROVED',
                        walletAddress: '******************************************',
                        createdAt: '2024-01-01T00:00:00.000Z',
                        lastLogin: '2024-01-15T10:30:00.000Z',
                      },
                    ],
                    totalCount: 1,
                    currentPage: 1,
                    totalPages: 1,
                  },
                  error: false,
                },
              },
            },
          },
          '401': {
            description: 'Unauthorized access',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
        },
      },
    },
    '/admin/v1/auth/dashboard': {
      get: {
        summary: 'Get Dashboard Data',
        description: 'Retrieve dashboard analytics and summary data including user counts, offering statistics, and recent activities.',
        tags: ['Dashboard'],
        security: [{ bearerAuth: [] }],
        responses: {
          '200': {
            description: 'Dashboard data retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    status: { type: 'integer' },
                    data: {
                      type: 'object',
                      properties: {
                        userStats: {
                          type: 'object',
                          properties: {
                            totalUsers: { type: 'integer' },
                            activeUsers: { type: 'integer' },
                            pendingKyc: { type: 'integer' },
                            approvedKyc: { type: 'integer' },
                          },
                        },
                        offeringStats: {
                          type: 'object',
                          properties: {
                            totalOfferings: { type: 'integer' },
                            activeOfferings: { type: 'integer' },
                            pendingOfferings: { type: 'integer' },
                            totalValueLocked: { type: 'number' },
                          },
                        },
                        transactionStats: {
                          type: 'object',
                          properties: {
                            totalTransactions: { type: 'integer' },
                            totalVolume: { type: 'number' },
                            todayTransactions: { type: 'integer' },
                            todayVolume: { type: 'number' },
                          },
                        },
                      },
                    },
                    error: { type: 'boolean' },
                  },
                },
                example: {
                  message: 'Dashboard data fetched successfully.',
                  status: 200,
                  data: {
                    userStats: {
                      totalUsers: 1250,
                      activeUsers: 1100,
                      pendingKyc: 45,
                      approvedKyc: 1050,
                    },
                    offeringStats: {
                      totalOfferings: 25,
                      activeOfferings: 18,
                      pendingOfferings: 3,
                      totalValueLocked: 5500000,
                    },
                    transactionStats: {
                      totalTransactions: 8750,
                      totalVolume: 12500000,
                      todayTransactions: 125,
                      todayVolume: 250000,
                    },
                  },
                  error: false,
                },
              },
            },
          },
          '401': {
            description: 'Unauthorized access',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
        },
      },
    },
    '/admin/v1/auth/offering': {
      post: {
        summary: 'Create/Update Offering',
        description: 'Create a new offering or update an existing offering. This endpoint supports multi-step offering creation process.',
        tags: ['Offering Management'],
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  currentStep: {
                    type: 'integer',
                    description: 'Current step in the offering creation process (1-5)',
                    example: 1,
                  },
                  overview: {
                    type: 'object',
                    description: 'Overview information for step 1',
                    properties: {
                      title: { type: 'string', example: 'Real Estate Investment' },
                      subTitle: { type: 'string', example: 'Invest in prime locations' },
                      description: { type: 'string', example: 'This offering allows investors to participate in premium real estate assets.' },
                      entityName: { type: 'string', example: 'Real Estate Corp' },
                      entityType: { type: 'string', example: 'LLC' },
                      webUrl: { type: 'string', example: 'https://realestatecorp.com' },
                      lineOfBusiness: { type: 'string', example: 'Real Estate' },
                      sourceOfFunds: { type: 'string', example: 'Investor Funds' },
                      location: { type: 'string', example: 'New York, USA' },
                      companyDescription: { type: 'string', example: 'Real Estate Corp is a premier provider of real estate investments.' },
                    },
                  },
                  projectDetails: {
                    type: 'object',
                    description: 'Project details for step 2',
                    properties: {
                      assetType: { type: 'string', example: 'Real Estate' },
                      blockChainType: { type: 'string', example: 'Ethereum' },
                      offeringType: { type: 'string', example: 'Equity' },
                      tokenStandard: { type: 'string', example: 'ERC-20' },
                      offeringName: { type: 'string', example: 'Real Estate Tokens' },
                      minInvestment: { type: 'integer', example: 10000 },
                      maxInvestment: { type: 'integer', example: 500000 },
                      tokenSupply: { type: 'integer', example: 1000000 },
                      projectYield: { type: 'number', example: 8.5 },
                    },
                  },
                },
                required: ['currentStep'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'Offering created/updated successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    status: { type: 'integer' },
                    data: {
                      type: 'object',
                      properties: {
                        offeringId: { type: 'string' },
                        currentStep: { type: 'integer' },
                      },
                    },
                    error: { type: 'boolean' },
                  },
                },
                example: {
                  message: 'Offering created successfully.',
                  status: 200,
                  data: {
                    offeringId: '66f109fabfffada3bec13652',
                    currentStep: 1,
                  },
                  error: false,
                },
              },
            },
          },
          '400': {
            description: 'Bad request or validation error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          '401': {
            description: 'Unauthorized access',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
        },
      },
      get: {
        summary: 'Get Offerings List',
        description: 'Retrieve a paginated list of offerings with optional filtering and search functionality.',
        tags: ['Offering Management'],
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'page',
            in: 'query',
            required: false,
            schema: {
              type: 'integer',
              minimum: 1,
              default: 1,
            },
            description: 'Page number for pagination',
            example: 1,
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            schema: {
              type: 'integer',
              minimum: 1,
              maximum: 100,
              default: 10,
            },
            description: 'Number of offerings to return per page',
            example: 10,
          },
          {
            name: 'search',
            in: 'query',
            required: false,
            schema: {
              type: 'string',
            },
            description: 'Search term for offering name or description',
            example: 'real estate',
          },
          {
            name: 'status',
            in: 'query',
            required: false,
            schema: {
              type: 'string',
              enum: ['DRAFT', 'PENDING', 'APPROVED', 'REJECTED', 'ACTIVE', 'COMPLETED'],
            },
            description: 'Filter by offering status',
            example: 'ACTIVE',
          },
          {
            name: 'assetType',
            in: 'query',
            required: false,
            schema: {
              type: 'string',
            },
            description: 'Filter by asset type',
            example: 'Real Estate',
          },
        ],
        responses: {
          '200': {
            description: 'Offerings retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    status: { type: 'integer' },
                    data: {
                      type: 'object',
                      properties: {
                        offerings: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              _id: { type: 'string' },
                              overview: {
                                type: 'object',
                                properties: {
                                  title: { type: 'string' },
                                  description: { type: 'string' },
                                  entityName: { type: 'string' },
                                },
                              },
                              projectDetails: {
                                type: 'object',
                                properties: {
                                  assetType: { type: 'string' },
                                  offeringType: { type: 'string' },
                                  minInvestment: { type: 'integer' },
                                  maxInvestment: { type: 'integer' },
                                  tokenSupply: { type: 'integer' },
                                },
                              },
                              status: { type: 'string' },
                              createdAt: { type: 'string', format: 'date-time' },
                              updatedAt: { type: 'string', format: 'date-time' },
                            },
                          },
                        },
                        totalCount: { type: 'integer' },
                        currentPage: { type: 'integer' },
                        totalPages: { type: 'integer' },
                      },
                    },
                    error: { type: 'boolean' },
                  },
                },
                example: {
                  message: 'Offerings fetched successfully.',
                  status: 200,
                  data: {
                    offerings: [
                      {
                        _id: '66f109fabfffada3bec13652',
                        overview: {
                          title: 'Real Estate Investment',
                          description: 'Premium real estate investment opportunity',
                          entityName: 'Real Estate Corp',
                        },
                        projectDetails: {
                          assetType: 'Real Estate',
                          offeringType: 'Equity',
                          minInvestment: 10000,
                          maxInvestment: 500000,
                          tokenSupply: 1000000,
                        },
                        status: 'ACTIVE',
                        createdAt: '2024-01-01T00:00:00.000Z',
                        updatedAt: '2024-01-15T10:30:00.000Z',
                      },
                    ],
                    totalCount: 1,
                    currentPage: 1,
                    totalPages: 1,
                  },
                  error: false,
                },
              },
            },
          },
          '401': {
            description: 'Unauthorized access',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
        },
      },
    },
    '/admin/v1/auth/transactions/{offeringId}': {
      get: {
        summary: 'Get Transactions by Offering',
        description: 'Retrieve a paginated list of transactions for a specific offering with optional filtering by transaction type.',
        tags: ['Transactions'],
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'offeringId',
            in: 'path',
            required: true,
            schema: {
              type: 'string',
              pattern: '^[0-9a-fA-F]{24}$',
            },
            description: 'Unique identifier of the offering',
            example: '66f109fabfffada3bec13652',
          },
          {
            name: 'page',
            in: 'query',
            required: false,
            schema: {
              type: 'integer',
              minimum: 1,
              default: 1,
            },
            description: 'Page number for pagination',
            example: 1,
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            schema: {
              type: 'integer',
              minimum: 1,
              maximum: 100,
              default: 10,
            },
            description: 'Number of transactions to return per page',
            example: 10,
          },
          {
            name: 'type',
            in: 'query',
            required: false,
            schema: {
              type: 'string',
              enum: ['BUY', 'SELL', 'TRANSFER', 'DIVIDEND'],
            },
            description: 'Filter by transaction type',
            example: 'BUY',
          },
        ],
        responses: {
          '200': {
            description: 'Transactions retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    status: { type: 'integer' },
                    data: {
                      type: 'object',
                      properties: {
                        transactions: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              _id: { type: 'string' },
                              transactionHash: { type: 'string' },
                              type: { type: 'string' },
                              amount: { type: 'number' },
                              tokenAmount: { type: 'number' },
                              status: { type: 'string' },
                              fromAddress: { type: 'string' },
                              toAddress: { type: 'string' },
                              createdAt: { type: 'string', format: 'date-time' },
                              user: {
                                type: 'object',
                                properties: {
                                  name: { type: 'string' },
                                  email: { type: 'string' },
                                },
                              },
                            },
                          },
                        },
                        totalCount: { type: 'integer' },
                        currentPage: { type: 'integer' },
                        totalPages: { type: 'integer' },
                      },
                    },
                    error: { type: 'boolean' },
                  },
                },
                example: {
                  message: 'Transactions fetched successfully.',
                  status: 200,
                  data: {
                    transactions: [
                      {
                        _id: '66f109fabfffada3bec13652',
                        transactionHash: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890',
                        type: 'BUY',
                        amount: 1000,
                        tokenAmount: 100,
                        status: 'COMPLETED',
                        fromAddress: '******************************************',
                        toAddress: '0x9876543210fedcba9876543210fedcba98765432',
                        createdAt: '2024-01-01T00:00:00.000Z',
                        user: {
                          name: 'John Doe',
                          email: '<EMAIL>',
                        },
                      },
                    ],
                    totalCount: 1,
                    currentPage: 1,
                    totalPages: 1,
                  },
                  error: false,
                },
              },
            },
          },
          '401': {
            description: 'Unauthorized access',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          '404': {
            description: 'Offering not found',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
        },
      },
    },
    '/admin/v1/auth/notification': {
      get: {
        summary: 'Get Notifications',
        description: 'Retrieve a paginated list of offering-related notifications for the authenticated user.',
        tags: ['Notifications'],
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'page',
            in: 'query',
            required: false,
            schema: {
              type: 'integer',
              minimum: 1,
              default: 1,
            },
            description: 'Page number for pagination',
            example: 1,
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            schema: {
              type: 'integer',
              minimum: 1,
              maximum: 100,
              default: 10,
            },
            description: 'Number of notifications to return per page',
            example: 10,
          },
        ],
        responses: {
          '200': {
            description: 'Notifications retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    status: { type: 'integer' },
                    data: {
                      type: 'object',
                      properties: {
                        notifications: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              _id: { type: 'string' },
                              title: { type: 'string' },
                              message: { type: 'string' },
                              type: { type: 'string' },
                              isRead: { type: 'boolean' },
                              offeringId: { type: 'string' },
                              createdAt: { type: 'string', format: 'date-time' },
                            },
                          },
                        },
                        totalCount: { type: 'integer' },
                        unreadCount: { type: 'integer' },
                      },
                    },
                    error: { type: 'boolean' },
                  },
                },
                example: {
                  message: 'Notifications fetched successfully.',
                  status: 200,
                  data: {
                    notifications: [
                      {
                        _id: '66f109fabfffada3bec13652',
                        title: 'New Offering Request',
                        message: 'A new offering request has been submitted for review.',
                        type: 'OFFERING_REQUEST',
                        isRead: false,
                        offeringId: '66f109fabfffada3bec13653',
                        createdAt: '2024-01-01T00:00:00.000Z',
                      },
                    ],
                    totalCount: 1,
                    unreadCount: 1,
                  },
                  error: false,
                },
              },
            },
          },
          '401': {
            description: 'Unauthorized access',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
        },
      },
    },
    '/admin/v1/auth/notification/seen': {
      post: {
        summary: 'Mark Notification as Seen',
        description: 'Mark one or more notifications as read/seen by the user.',
        tags: ['Notifications'],
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  notificationIds: {
                    type: 'array',
                    items: {
                      type: 'string',
                      pattern: '^[0-9a-fA-F]{24}$',
                    },
                    description: 'Array of notification IDs to mark as seen',
                    example: ['66f109fabfffada3bec13652', '66f109fabfffada3bec13653'],
                  },
                },
                required: ['notificationIds'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'Notifications marked as seen successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    status: { type: 'integer' },
                    data: { type: 'null' },
                    error: { type: 'boolean' },
                  },
                },
                example: {
                  message: 'Notifications marked as seen successfully.',
                  status: 200,
                  data: null,
                  error: false,
                },
              },
            },
          },
          '400': {
            description: 'Invalid notification IDs',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          '401': {
            description: 'Unauthorized access',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
        },
      },
    },
    '/admin/v1/auth/nft/getCollection': {
      get: {
        summary: 'Get NFT Collections',
        description: 'Retrieve a paginated list of NFT collections for the authenticated user with optional search functionality.',
        tags: ['NFT'],
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'page',
            in: 'query',
            required: false,
            schema: {
              type: 'integer',
              minimum: 1,
              default: 1,
            },
            description: 'Page number for pagination',
            example: 1,
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            schema: {
              type: 'integer',
              minimum: 1,
              maximum: 100,
              default: 10,
            },
            description: 'Number of collections to return per page',
            example: 10,
          },
          {
            name: 'search',
            in: 'query',
            required: false,
            schema: {
              type: 'string',
            },
            description: 'Search term for collection name or description',
            example: 'art',
          },
        ],
        responses: {
          '200': {
            description: 'NFT collections retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    status: { type: 'integer' },
                    data: {
                      type: 'object',
                      properties: {
                        collections: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              _id: { type: 'string' },
                              name: { type: 'string' },
                              description: { type: 'string' },
                              symbol: { type: 'string' },
                              contractAddress: { type: 'string' },
                              totalSupply: { type: 'integer' },
                              isActive: { type: 'boolean' },
                              imageUrl: { type: 'string' },
                              createdAt: { type: 'string', format: 'date-time' },
                              owner: {
                                type: 'object',
                                properties: {
                                  name: { type: 'string' },
                                  email: { type: 'string' },
                                },
                              },
                            },
                          },
                        },
                        totalCount: { type: 'integer' },
                        currentPage: { type: 'integer' },
                        totalPages: { type: 'integer' },
                      },
                    },
                    error: { type: 'boolean' },
                  },
                },
                example: {
                  message: 'Collections fetched successfully.',
                  status: 200,
                  data: {
                    collections: [
                      {
                        _id: '66f109fabfffada3bec13652',
                        name: 'Digital Art Collection',
                        description: 'A collection of unique digital artworks',
                        symbol: 'DAC',
                        contractAddress: '******************************************',
                        totalSupply: 1000,
                        isActive: true,
                        imageUrl: 'https://example.com/collection-image.jpg',
                        createdAt: '2024-01-01T00:00:00.000Z',
                        owner: {
                          name: 'John Doe',
                          email: '<EMAIL>',
                        },
                      },
                    ],
                    totalCount: 1,
                    currentPage: 1,
                    totalPages: 1,
                  },
                  error: false,
                },
              },
            },
          },
          '401': {
            description: 'Unauthorized access',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
        },
      },
    },
    '/admin/v1/auth/createTransferAgent': {
      post: {
        summary: 'Create Transfer Agent',
        description: 'Create a new transfer agent account with specified permissions and details.',
        tags: ['Transfer Agent'],
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  name: {
                    type: 'string',
                    description: 'Transfer agent full name',
                    example: 'John Smith',
                  },
                  email: {
                    type: 'string',
                    format: 'email',
                    description: 'Transfer agent email address',
                    example: '<EMAIL>',
                  },
                  walletAddress: {
                    type: 'string',
                    description: 'Ethereum wallet address',
                    example: '******************************************',
                  },
                  permissions: {
                    type: 'array',
                    items: {
                      type: 'string',
                    },
                    description: 'Array of permission IDs',
                    example: ['66f109fabfffada3bec13652', '66f109fabfffada3bec13653'],
                  },
                },
                required: ['name', 'email', 'walletAddress'],
              },
            },
          },
        },
        responses: {
          '201': {
            description: 'Transfer agent created successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    status: { type: 'integer' },
                    data: {
                      type: 'object',
                      properties: {
                        transferAgentId: { type: 'string' },
                        name: { type: 'string' },
                        email: { type: 'string' },
                        walletAddress: { type: 'string' },
                        isActive: { type: 'boolean' },
                        createdAt: { type: 'string', format: 'date-time' },
                      },
                    },
                    error: { type: 'boolean' },
                  },
                },
                example: {
                  message: 'Transfer agent added successfully.',
                  status: 201,
                  data: {
                    transferAgentId: '66f109fabfffada3bec13652',
                    name: 'John Smith',
                    email: '<EMAIL>',
                    walletAddress: '******************************************',
                    isActive: true,
                    createdAt: '2024-01-01T00:00:00.000Z',
                  },
                  error: false,
                },
              },
            },
          },
          '400': {
            description: 'Bad request or validation error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
                example: {
                  message: 'A user with this email already exists.',
                  status: 400,
                  error: true,
                },
              },
            },
          },
          '401': {
            description: 'Unauthorized access',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
        },
      },
    },
    '/admin/v1/auth/transferAgentList': {
      get: {
        summary: 'Get Transfer Agent List',
        description: 'Retrieve a paginated list of transfer agents with optional filtering and search functionality.',
        tags: ['Transfer Agent'],
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'page',
            in: 'query',
            required: false,
            schema: {
              type: 'integer',
              minimum: 1,
              default: 1,
            },
            description: 'Page number for pagination',
            example: 1,
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            schema: {
              type: 'integer',
              minimum: 1,
              maximum: 100,
              default: 10,
            },
            description: 'Number of transfer agents to return per page',
            example: 10,
          },
          {
            name: 'search',
            in: 'query',
            required: false,
            schema: {
              type: 'string',
            },
            description: 'Search term for name or email',
            example: 'john',
          },
          {
            name: 'status',
            in: 'query',
            required: false,
            schema: {
              type: 'string',
              enum: ['active', 'inactive'],
            },
            description: 'Filter by status',
            example: 'active',
          },
        ],
        responses: {
          '200': {
            description: 'Transfer agents retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    status: { type: 'integer' },
                    data: {
                      type: 'object',
                      properties: {
                        transferAgents: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              _id: { type: 'string' },
                              name: { type: 'string' },
                              email: { type: 'string' },
                              walletAddress: { type: 'string' },
                              isActive: { type: 'boolean' },
                              noOfOfferings: { type: 'integer' },
                              status: { type: 'string' },
                              createdAt: { type: 'string', format: 'date-time' },
                            },
                          },
                        },
                        totalCount: { type: 'integer' },
                        currentPage: { type: 'integer' },
                        totalPages: { type: 'integer' },
                      },
                    },
                    error: { type: 'boolean' },
                  },
                },
                example: {
                  message: 'Transfer agents fetched successfully.',
                  status: 200,
                  data: {
                    transferAgents: [
                      {
                        _id: '66f109fabfffada3bec13652',
                        name: 'John Smith',
                        email: '<EMAIL>',
                        walletAddress: '******************************************',
                        isActive: true,
                        noOfOfferings: 5,
                        status: 'ACTIVE',
                        createdAt: '2024-01-01T00:00:00.000Z',
                      },
                    ],
                    totalCount: 1,
                    currentPage: 1,
                    totalPages: 1,
                  },
                  error: false,
                },
              },
            },
          },
          '401': {
            description: 'Unauthorized access',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
        },
      },
    },
    '/admin/v1/auth/subadmin': {
      post: {
        summary: 'Create Sub-Admin',
        description: 'Create a new sub-administrator account with specified permissions and access levels.',
        tags: ['Sub-Admin'],
        security: [{ bearerAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  name: {
                    type: 'string',
                    description: 'Sub-admin full name',
                    example: 'Jane Doe',
                  },
                  email: {
                    type: 'string',
                    format: 'email',
                    description: 'Sub-admin email address',
                    example: '<EMAIL>',
                  },
                  permissions: {
                    type: 'array',
                    items: {
                      type: 'string',
                    },
                    description: 'Array of permission IDs',
                    example: ['66f109fabfffada3bec13652', '66f109fabfffada3bec13653'],
                  },
                  modules: {
                    type: 'array',
                    items: {
                      type: 'string',
                    },
                    description: 'Array of module IDs the sub-admin can access',
                    example: ['66f109fabfffada3bec13654', '66f109fabfffada3bec13655'],
                  },
                },
                required: ['name', 'email'],
              },
            },
          },
        },
        responses: {
          '201': {
            description: 'Sub-admin created successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    status: { type: 'integer' },
                    data: {
                      type: 'object',
                      properties: {
                        subAdminId: { type: 'string' },
                        name: { type: 'string' },
                        email: { type: 'string' },
                        isActive: { type: 'boolean' },
                        createdAt: { type: 'string', format: 'date-time' },
                      },
                    },
                    error: { type: 'boolean' },
                  },
                },
                example: {
                  message: 'Sub-admin created successfully.',
                  status: 201,
                  data: {
                    subAdminId: '66f109fabfffada3bec13652',
                    name: 'Jane Doe',
                    email: '<EMAIL>',
                    isActive: true,
                    createdAt: '2024-01-01T00:00:00.000Z',
                  },
                  error: false,
                },
              },
            },
          },
          '400': {
            description: 'Bad request or validation error',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
                example: {
                  message: 'A user with this email already exists.',
                  status: 400,
                  error: true,
                },
              },
            },
          },
          '401': {
            description: 'Unauthorized access',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
        },
      },
      get: {
        summary: 'Get Sub-Admin List',
        description: 'Retrieve a paginated list of sub-administrators with their permissions and access levels.',
        tags: ['Sub-Admin'],
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'page',
            in: 'query',
            required: false,
            schema: {
              type: 'integer',
              minimum: 1,
              default: 1,
            },
            description: 'Page number for pagination',
            example: 1,
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            schema: {
              type: 'integer',
              minimum: 1,
              maximum: 100,
              default: 10,
            },
            description: 'Number of sub-admins to return per page',
            example: 10,
          },
          {
            name: 'search',
            in: 'query',
            required: false,
            schema: {
              type: 'string',
            },
            description: 'Search term for name or email',
            example: 'jane',
          },
          {
            name: 'isActive',
            in: 'query',
            required: false,
            schema: {
              type: 'boolean',
            },
            description: 'Filter by active status',
            example: true,
          },
        ],
        responses: {
          '200': {
            description: 'Sub-admins retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    status: { type: 'integer' },
                    data: {
                      type: 'object',
                      properties: {
                        subAdmins: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              _id: { type: 'string' },
                              name: { type: 'string' },
                              email: { type: 'string' },
                              isActive: { type: 'boolean' },
                              permissions: {
                                type: 'array',
                                items: { type: 'string' },
                              },
                              modules: {
                                type: 'array',
                                items: { type: 'string' },
                              },
                              createdAt: { type: 'string', format: 'date-time' },
                              lastLogin: { type: 'string', format: 'date-time' },
                            },
                          },
                        },
                        totalCount: { type: 'integer' },
                        currentPage: { type: 'integer' },
                        totalPages: { type: 'integer' },
                      },
                    },
                    error: { type: 'boolean' },
                  },
                },
                example: {
                  message: 'Sub-admins fetched successfully.',
                  status: 200,
                  data: {
                    subAdmins: [
                      {
                        _id: '66f109fabfffada3bec13652',
                        name: 'Jane Doe',
                        email: '<EMAIL>',
                        isActive: true,
                        permissions: ['66f109fabfffada3bec13652'],
                        modules: ['66f109fabfffada3bec13654'],
                        createdAt: '2024-01-01T00:00:00.000Z',
                        lastLogin: '2024-01-15T10:30:00.000Z',
                      },
                    ],
                    totalCount: 1,
                    currentPage: 1,
                    totalPages: 1,
                  },
                  error: false,
                },
              },
            },
          },
          '401': {
            description: 'Unauthorized access',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
        },
      },
    },
    '/admin/v1/auth/topInvestors': {
      get: {
        summary: 'Get Top Investors',
        description: 'Retrieve a list of top investors based on investment volume and activity.',
        tags: ['Dashboard'],
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'limit',
            in: 'query',
            required: false,
            schema: {
              type: 'integer',
              minimum: 1,
              maximum: 50,
              default: 10,
            },
            description: 'Number of top investors to return',
            example: 10,
          },
        ],
        responses: {
          '200': {
            description: 'Top investors retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: { type: 'string' },
                    status: { type: 'integer' },
                    data: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          _id: { type: 'string' },
                          name: { type: 'string' },
                          email: { type: 'string' },
                          totalInvestment: { type: 'number' },
                          numberOfInvestments: { type: 'integer' },
                          portfolioValue: { type: 'number' },
                          joinedDate: { type: 'string', format: 'date-time' },
                        },
                      },
                    },
                    error: { type: 'boolean' },
                  },
                },
                example: {
                  message: 'Top investors fetched successfully.',
                  status: 200,
                  data: [
                    {
                      _id: '66f109fabfffada3bec13652',
                      name: 'John Doe',
                      email: '<EMAIL>',
                      totalInvestment: 500000,
                      numberOfInvestments: 12,
                      portfolioValue: 650000,
                      joinedDate: '2023-06-15T00:00:00.000Z',
                    },
                  ],
                  error: false,
                },
              },
            },
          },
          '401': {
            description: 'Unauthorized access',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
        },
      },
    },
    '/admin/v1/staking-transactions/{token}': {
      get: {
        summary: 'Get Staking Transactions',
        description: 'Retrieve paginated staking transactions for a specific token. Returns transaction history including stake and unstake operations.',
        tags: ['Transactions'],
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'token',
            in: 'path',
            required: true,
            description: 'Token contract address',
            schema: {
              type: 'string',
              example: '******************************************',
            },
          },
          {
            name: 'page',
            in: 'query',
            required: false,
            description: 'Page number for pagination',
            schema: {
              type: 'integer',
              minimum: 1,
              default: 1,
              example: 1,
            },
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            description: 'Number of items per page',
            schema: {
              type: 'integer',
              minimum: 1,
              maximum: 100,
              default: 10,
              example: 10,
            },
          },
          {
            name: 'search',
            in: 'query',
            required: false,
            description: 'Search string for filtering by user address, transaction hash, or action ID',
            schema: {
              type: 'string',
              example: '0x87...12a5',
            },
          },
        ],
        responses: {
          '200': {
            description: 'Staking transactions retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    status: { type: 'integer', example: 200 },
                    error: { type: 'boolean', example: false },
                    message: { type: 'string', example: 'Staking transactions fetched successfully' },
                    data: {
                      type: 'object',
                      properties: {
                        transactions: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              type: { type: 'string', example: 'TokenStaked', enum: ['TokenStaked', 'TokenUnstaked'] },
                              token: { type: 'string', example: '******************************************' },
                              userAddress: { type: 'string', example: '0x87...12a5' },
                              stakedAmount: { type: 'string', example: '564.00' },
                              unstakedAmount: { type: 'string', example: '0' },
                              userTotalStaked: { type: 'string', example: '564.00' },
                              totalTokenStaked: { type: 'string', example: '5000.00' },
                              timestamp: { type: 'integer', example: 1712345678 },
                              actionID: { type: 'string', example: 'action123' },
                              transactionHash: { type: 'string', example: '0x87...12a5' },
                            },
                          },
                        },
                        currentPage: { type: 'integer', example: 1 },
                        totalPages: { type: 'integer', example: 25 },
                        totalCount: { type: 'integer', example: 250 },
                        nextPage: { type: 'integer', example: 2, nullable: true },
                        previousPage: { type: 'integer', example: null, nullable: true },
                      },
                    },
                  },
                },
              },
            },
          },
          '400': {
            description: 'Bad request - invalid parameters',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Error' },
              },
            },
          },
          '401': {
            description: 'Unauthorized - invalid or missing authentication token',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Error' },
              },
            },
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Error' },
              },
            },
          },
        },
      },
    },
    '/admin/v1/staking-users/{token}': {
      get: {
        summary: 'Get User Staking Summary',
        description: 'Retrieve paginated user staking summary for a specific token. Returns aggregated staking data per user including total staked amounts and activity.',
        tags: ['Transactions'],
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'token',
            in: 'path',
            required: true,
            description: 'Token contract address',
            schema: {
              type: 'string',
              example: '******************************************',
            },
          },
          {
            name: 'page',
            in: 'query',
            required: false,
            description: 'Page number for pagination',
            schema: {
              type: 'integer',
              minimum: 1,
              default: 1,
              example: 1,
            },
          },
          {
            name: 'limit',
            in: 'query',
            required: false,
            description: 'Number of items per page',
            schema: {
              type: 'integer',
              minimum: 1,
              maximum: 100,
              default: 10,
              example: 10,
            },
          },
          {
            name: 'search',
            in: 'query',
            required: false,
            description: 'Search string for filtering by user address',
            schema: {
              type: 'string',
              example: '0x87...12a5',
            },
          },
        ],
        responses: {
          '200': {
            description: 'User staking summary retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    status: { type: 'integer', example: 200 },
                    error: { type: 'boolean', example: false },
                    message: { type: 'string', example: 'User staking summary fetched successfully' },
                    data: {
                      type: 'object',
                      properties: {
                        users: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              userAddress: { type: 'string', example: '0x87...12a5' },
                              lastActivity: { type: 'string', example: 'Stake', enum: ['Stake', 'Unstake'] },
                              lastActivityTime: { type: 'integer', example: 1712345678 },
                              totalStakedAmount: { type: 'string', example: '564.00' },
                              averageAmount: { type: 'string', example: '564.00' },
                            },
                          },
                        },
                        currentPage: { type: 'integer', example: 1 },
                        totalPages: { type: 'integer', example: 25 },
                        totalCount: { type: 'integer', example: 250 },
                        nextPage: { type: 'integer', example: 2, nullable: true },
                        previousPage: { type: 'integer', example: null, nullable: true },
                      },
                    },
                  },
                },
              },
            },
          },
          '400': {
            description: 'Bad request - invalid parameters',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Error' },
              },
            },
          },
          '401': {
            description: 'Unauthorized - invalid or missing authentication token',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Error' },
              },
            },
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Error' },
              },
            },
          },
        },
      },
    },
  },
};
