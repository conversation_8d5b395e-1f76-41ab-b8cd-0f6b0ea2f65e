/* eslint-disable no-useless-escape */
/**
 * Length of OTP (One-Time Password)
 */
export const otpLength = 6;

/**
 * Regular expression pattern for validating passwords
 * Requires at least:
 * - One lowercase letter
 * - One uppercase letter
 * - One number
 * - One special character
 * - Minimum length of 8 characters
 */
export const passwordPattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;

/**
 * Regular expression pattern for validating names
 * Allows only alphabetic characters and spaces
 */
export const namePattern = /^[A-Za-z\s]+$/;

/**
 * Regular expression pattern for validating OTP
 * Requires exactly 6 digits
 */
export const otpPattern = /^[0-9]{6}$/;

/**
 * Maximum number of previous passwords to store for password history validation
 */
export const maxPasswordHistory = 5;

/**
 * Array of valid document types for offerings
 */
export const offeringDocs = [
  { title: 'E-Signature document', name: 'eSign' },
  { title: 'Pitch Deck', name: 'pitchDeck' },
  {
    title: 'Confidential Information Memorendum',
    name: 'confidentialInformationMemorendum',
  },
  { title: 'Land Registration', name: 'landRegistration' },
  { title: 'Title document', name: 'titleDocs' },
  { title: 'Bank approval', name: 'bankApproval' },
  { title: 'Encumbrance certificate', name: 'encumbranceCertificate' },
  { title: 'Property tax receipt', name: 'propertyTaxReceipt' },
  { title: 'Article Of Association', name: 'articlesOfAssociation' },
  { title: 'Operating Agreement', name: 'operatingAgreement' },
  { title: 'Tax Assignement Letter', name: 'taxAssignmentLetter' },
  { title: 'Certificate Of Registration', name: 'certificateOfRegistration' },
  { title: 'Register Of Managers', name: 'registerOfManagers' },
  { title: 'Property Images', name: 'propertyImages' },
];

/**
 * Cool down period in seconds between OTP requests
 */
export const coolDownTimeInSeconds = 60;

/**
 * Maximum length allowed for name fields
 */
export const nameMaxLength = 70;

/**
 * Minimum length required for name fields
 */
export const nameMinLength = 3;

/**
 * Maximum length allowed for email addresses
 */
export const emailMaxLength = 200;

/**
 * Maximum length allowed for passwords
 */
export const passwordMaxLength = 50;

/**
 * Minimum length required for passwords
 */
export const passwordMinLength = 2;

/**
 * Regular expression pattern for validating alphanumeric input
 * Allows letters, numbers, spaces, hyphens, and special characters
 */
export const alphanumericPattern = /^[A-Za-z0-9À-ÖØ-öø-ÿ\s'’\-]+$/;

/**
 * Maximum number of KYC verification attempts allowed
 */
export const maxKycAttempt = 3;

/**
 * Regular expression pattern for validating URLs
 * Supports both HTTP and HTTPS protocols
 */
export const urlPattern = /^(https?:\/\/)?([a-zA-Z0-9-]+\.)?[a-zA-Z0-9-]{1,}\.[a-zA-Z]{2,}(\/.*)?$/;
