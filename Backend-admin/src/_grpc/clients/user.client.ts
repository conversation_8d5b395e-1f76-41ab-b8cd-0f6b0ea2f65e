/* eslint-disable @typescript-eslint/no-require-imports */
import CONFIG from '../../config/env';
const grpc = require('@grpc/grpc-js');
const protoLoader = require('@grpc/proto-loader');
// import path from 'path';
const path = require('path');
// Path to the .proto file defining the User service
const PROTO_PATH = path.join(__dirname, '../proto/user.proto');
// Define options for loading the .proto file
const PROTO_OPTIONS = {
  keepCase: true, // Preserve the case of field names as defined in the .proto file
  longs: String, // Convert longs to strings to avoid precision loss
  enums: String, // Convert enums to strings
  arrays: true, // Convert arrays properly
};

class UserClient {
  public client: any;

  constructor() {
    this.connectUserClient();
  }

  public async connectUserClient() {
    // Get the host and port for the gRPC service from the config
    const host = CONFIG.GRPC.USER_SERVICE_GRPC_CONTAINER_NAME;
    const port = CONFIG.GRPC.USER_SERVICE_GRPC_PORT;
    // Check if SSL should be used
    const isSsl = process.env.GRPC_SSL;

    // Load the .proto file and get the gRPC object
    const packageDefinition = protoLoader.loadSync(PROTO_PATH, PROTO_OPTIONS);
    const grpcObject: any = grpc.loadPackageDefinition(packageDefinition);

    // Access the UserService from the 'user' package
    const userGrpcService = grpcObject.user.UserGrpcService;

    // Initialize the gRPC client with the host, port, and SSL configuration
    this.client = new userGrpcService(`${host}:${port}`, isSsl === 'True' ? grpc.credentials.createSsl() : grpc.credentials.createInsecure());

    console.log(`User Service gRPC Client is running at ${host}:${port}`);
  }
}

export default new UserClient();
