syntax = "proto3";

package user;

// Define the UserGrpcService with RPC methods
service UserGrpcService {
  rpc getUser (GetUserRequest) returns (CommonResponse);
  rpc approveKyc (ApproveRequest) returns (CommonResponse);
  rpc approveWhitelist (ApproveWhitelistRequest) returns (CommonResponse);
  rpc unblockUser (UnblockRequest) returns (CommonResponse);
  rpc approveIssuer (ApproveIssuerRequest) returns (CommonResponse);
  rpc rejectOffering (RejectRequest) returns (CommonResponse);
  rpc sendOffering (sendOfferingRequest) returns (CommonResponse);
  rpc scheduleOffering (sendOfferingRequest) returns (CommonResponse);
  rpc updateNftStatus (updateNftStatusRequest) returns (updateNftStatusResponse);
  rpc checkDividendStatus (checkDividendStatus) returns (checkDividendStatusResponse);
}

// Admin-related gRPC service
service AdminGrpcService {
  rpc getTransferAgent (TARequest) returns (CommonResponse);
  rpc getTransferAgentDetails (getTADetailsRequest) returns (CommonResponse);
  rpc getExistingUser (getExistingUserRequest) returns (CommonResponse);
  rpc getOfferingRequestNotification (getOfferingRequestNotification) returns (NotificationResponse);
  rpc seenOfferingRequestNotification (seenOfferingRequestNotification) returns (CommonResponse);
  rpc getIssuerNotification (getOfferingRequestNotification) returns (NotificationResponse);
  rpc seenIssuerNotification (seenOfferingRequestNotification) returns (CommonResponse);
}

message checkDividendStatus {
  string _id = 1;
}

message checkDividendStatusResponse {
  bool success = 1;
  string message = 2;
}

// General response message for all services
message CommonResponse {
  int32 status = 1; // Status code
  bool error = 2;   // Indicates if there was an error
  string message = 3; // Detailed message
  string data = 4; // Flexible data type for object or array
}

// General response message for all services
message NotificationResponse {
  int32 status = 1; // Status code
  bool error = 2;   // Indicates if there was an error
  string message = 3; // Detailed message
  string data = 4; // Flexible data type for object or array
  int32 totalCount = 5; // Total count of objects
}


message getOfferingRequestNotification {
  string page = 1;
  string limit = 2;
  string userId = 3;
}

message seenOfferingRequestNotification {
  string _id = 1;
  string userId = 2;
}

// Specific request messages
message GetUserRequest {
  string userId = 1; // The user ID to fetch
}

message ApproveRequest {
  string email = 1;
  string kycStatus = 2;
  string kycReason = 3;
}

message ApproveWhitelistRequest {
  string id = 1;
  string taStatus = 2;
  string whiteListReason = 3;
}

message ApproveIssuerRequest {
  string email = 1;
  string issuerStatus = 2;
  string issuerReason = 3;
}

message UnblockRequest {
  string email = 1;
  string isActive = 2;
}

message RejectRequest {
  string id = 1;
  string status = 2;
  string reason = 3;
}

message sendOfferingRequest {
  string data = 1;
}

message TARequest {
  string page = 1;
  string limit = 2;
  string sort = 3;
  string search = 4;
}

message getTADetailsRequest {
  string taId = 1;
}

message getExistingUserRequest {
  string search = 1;
}

message getTransactionRequest {
  string page = 1;
  string limit = 2;
  string sort = 3;
  string search = 4;
  string status = 5;
  string type = 6;
}

message updateNftStatusRequest {
  string id = 1;
}

message updateNftStatusResponse {
  string status = 1;
  string message = 2;
}
