import * as http from 'http';
import * as serverHandlers from './serverHandlers';
import server from './server';
import { initSentry } from '../../helpers/sentry.helper';

const Server: http.Server = http.createServer(server);

/**
 * Initialize Sentry
 */
initSentry();

/**
 * Server Events
 */
Server.on('error', (error: Error) => serverHandlers.onError(error, server.get('port')));
Server.on('listening', serverHandlers.onListening.bind(Server));
