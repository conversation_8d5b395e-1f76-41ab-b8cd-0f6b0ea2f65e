import { NextFunction, Request } from 'express';
import * as express from 'express';
import { HttpError } from './index';

interface CustomResponse extends express.Response {
  sendHttpError: (error: HttpError | Error, message?: string) => void;
}

/**
 * Generates HTML content for error responses
 * @param {HttpError} error - The error object containing status, name, and message
 * @returns {string} HTML string containing formatted error information
 */
const generateHTML = (error: HttpError): string => {
  if (error) {
    return "<div style='text-align: center;'>" + `<p>Status: ${error.status}</p>` + `<p>Name: ${error.name}</p>` + `<p>${error}</p>` + '</div>';
  }

  return '';
};

/**
 * Middleware to handle HTTP errors and send appropriate responses
 * @param {Request} req - Express request object
 * @param {CustomResponse} res - Custom response object
 * @param {NextFunction} next - Express next function
 * @returns {void}
 *
 * @swagger
 * components:
 *  schemas:
 *    Error:
 *      type: object
 *      required:
 *        - status
 *        - message
 *      properties:
 *        status:
 *          type: integer
 *          description: HTTP status code
 *          example: 200
 *        message:
 *          type: string
 *          description: Error description
 *          example: User created
 */
export function sendHttpErrorModule(req: Request, res: CustomResponse, next: NextFunction): void {
  res.sendHttpError = (error: HttpError): void => {
    res.status(error.status);

    /**
     * if this looks like an AJAX request
     * if this request has a 'json' content-type AND ALSO has its 'Accept' header set
     * if this request DOESN'T explicitly want HTML
     */
    if (req.xhr || req.is('json') || (req.is('json') && req.get('Accept')) || !(req.get('Accept') && req.get('Accept').indexOf('html') !== -1)) {
      res.json({
        status: error.status,
        name: error.name,
        message: error.message,
      });
    } else {
      res.send(generateHTML(error));
    }
  };

  next();
}
