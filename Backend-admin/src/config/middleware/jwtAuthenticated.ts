import { NextFunction, Request, Response } from 'express';
import RedisHelper from '../../helpers/redis.helper';
import { PromiseResolve, UserType } from '../../utils/common.interface';
import CustomError from '../../helpers/customError.helper';
import { RESPONSES, RES_MSG } from '../../utils/responseUtils';
import { ResponseHandler } from '../../helpers/response.helper';
import CommonHelper from '../../helpers/common.helper';
import UserService from '../../component/userAuthentications/service';
import logger from '../../helpers/logging/logger.helper';

/**
 * Middleware to authenticate JWT tokens and validate user access
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 * @returns {Promise<any>} Promise resolving to the next middleware or error response
 */
export async function isAuthenticated(req: Request | any, res: Response, next: NextFunction): Promise<any> {
  try {
    let token;
    const authorization: string = req.headers['authorization'] || null;
    if (authorization) {
      token = authorization.toLowerCase().startsWith('bearer') ? authorization.slice('bearer'.length).trim() : authorization;
    }
    if (!token) throw new CustomError(RES_MSG.COMMON.UNAUTHORIZED_ACCESS, RESPONSES.UN_AUTHORIZED);
    const isTokenValid: PromiseResolve = await CommonHelper.isValidToken(token);
    if (isTokenValid.error) {
      throw new CustomError(RES_MSG.COMMON.UNAUTHORIZED_ACCESS, RESPONSES.UN_AUTHORIZED);
    }
    const profileGetResponse: PromiseResolve = await UserService.fetchUserDetails({ email: isTokenValid.data.email }, [], ['kycDetails', 'wallets']);
    if (profileGetResponse.error) throw new CustomError(profileGetResponse.message, profileGetResponse.status);
    const authenticatedUserToken: string | null = await RedisHelper.getString(`accessToken:${isTokenValid.data.email}`);
    const FAToken: string | null = await RedisHelper.getString(`twoFA:${isTokenValid.data.email}`);

    if (authenticatedUserToken !== token && FAToken !== token) throw new CustomError(RES_MSG.TWO_FA.TOKEN_EXPIRE, RESPONSES.UN_AUTHORIZED);
    if (authenticatedUserToken !== token) {
      throw new CustomError(RES_MSG.TWO_FA.PENDING, RESPONSES.UN_AUTHORIZED);
    }
    req.userInfo = {
      userId: isTokenValid.data.userId,
      email: isTokenValid.data.email,
      userType: isTokenValid.data.userType,
      exp: isTokenValid.data.exp,
    };
    const data = {
      userId: isTokenValid.data.userId,
      email: isTokenValid.data.email,
      userType: isTokenValid.data.userType,
    };

    const is2FAVerified: boolean | string | null = await RedisHelper.getString(`2FA_${isTokenValid.data.userId}`);
    if (profileGetResponse.data.is2FAActive && !is2FAVerified) {
      return ResponseHandler.error(res, {
        error: true,
        status: RESPONSES.FORBIDDEN,
        message: RES_MSG.TWO_FA.PENDING,
      });
    }
    next();
    return data;
  } catch (error: any) {
    logger.error(error, 'verifyJWTToken error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.UN_AUTHORIZED,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * Middleware to authenticate users for 2FA forgot flow
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 * @returns {Promise<PromiseResolve | void>} Promise resolving to the next middleware or error response
 */
export async function isAuthenticatedforgot2Fa(req: Request | any, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    let token;
    const authorization: string = req.headers['authorization'] || null;
    if (authorization) {
      token = authorization.toLowerCase().startsWith('bearer') ? authorization.slice('bearer'.length).trim() : authorization;
    }
    if (!token) throw new CustomError(RES_MSG.COMMON.UNAUTHORIZED_ACCESS, RESPONSES.UN_AUTHORIZED);
    const isTokenValid: PromiseResolve = await CommonHelper.isValidToken(token);
    if (isTokenValid.error) {
      throw new CustomError(RES_MSG.COMMON.UNAUTHORIZED_ACCESS, RESPONSES.UN_AUTHORIZED);
    }
    const profileGetResponse: PromiseResolve = await UserService.fetchUserDetails({ email: isTokenValid.data.email }, [], ['kycDetails', 'wallets']);
    if (profileGetResponse.error) throw new CustomError(profileGetResponse.message, profileGetResponse.status);
    const authenticatedUserToken: string | null = await RedisHelper.getString(`accessToken:${isTokenValid.data.email}`);
    const FAToken: string | null = await RedisHelper.getString(`twoFA:${isTokenValid.data.email}`);
    if (authenticatedUserToken !== token && FAToken !== token) throw new CustomError(RES_MSG.TWO_FA.TOKEN_EXPIRE, RESPONSES.UN_AUTHORIZED);
    req.userInfo = {
      userId: isTokenValid.data.userId,
      email: isTokenValid.data.email,
      userType: isTokenValid.data.userType,
      exp: isTokenValid.data.exp,
    };
    const is2FAVerified: boolean | string | null = await RedisHelper.getString(`2FA_${isTokenValid.data.userId}`);
    if (profileGetResponse.data.is2FAActive && !is2FAVerified) {
      return ResponseHandler.error(res, {
        error: true,
        status: RESPONSES.FORBIDDEN,
        message: RES_MSG.TWO_FA.PENDING,
      });
    }

    next();
  } catch (error: any) {
    logger.error(error, 'verifyJWTToken error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.UN_AUTHORIZED,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * Middleware to verify if the authenticated user is an investor
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 * @returns {Promise<PromiseResolve | void>} Promise resolving to the next middleware or error response
 */
export async function isInvestor(req: Request | any, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const { userInfo } = req;

    if (userInfo?.userType !== UserType.Investor) {
      return ResponseHandler.error(res, {
        status: RESPONSES.FORBIDDEN,
        error: true,
        message: RES_MSG.COMMON.FORBIDDEN_ACCESS,
      });
    }
    next();
  } catch (error) {
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * Middleware to verify if the authenticated user is an institution
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 * @returns {Promise<PromiseResolve | void>} Promise resolving to the next middleware or error response
 */
export async function isInstitution(req: Request | any, res: Response, next: NextFunction): Promise<PromiseResolve | void> {
  try {
    const { userInfo } = req;
    if (userInfo?.userType !== UserType.Institution) {
      return ResponseHandler.error(res, {
        status: RESPONSES.FORBIDDEN,
        error: true,
        message: RES_MSG.COMMON.FORBIDDEN_ACCESS,
      });
    }

    next();
  } catch (error) {
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}

/**
 * Middleware to generate a refresh token for authentication
 * Currently commented out but prepared for future implementation
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 * @returns {Promise<any>} Promise resolving to the next middleware or error response
 */
export async function generateRefreshToken(req: Request, res: Response, next: NextFunction): Promise<any> {
  try {
    // const token: string = req.header('x-refresh-token') || null;
    // if (!token) throw new Error(RES_MSG.COMMON.UNAUTHORIZED_ACCESS);

    // const isTokenValid: PromiseResolve = await CommonHelper.isValidToken(token, false);

    // if (!isTokenValid || isTokenValid.error) {
    //     throw new Error(RES_MSG.COMMON.UNAUTHORIZED_ACCESS);
    // }

    // req.email = isTokenValid.data

    next();
  } catch (error: any) {
    logger.error(error, 'generateRefreshToken error');
    return ResponseHandler.error(res, {
      status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
      error: true,
      message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
    });
  }
}
