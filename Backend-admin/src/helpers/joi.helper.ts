import * as <PERSON><PERSON> from 'joi';
import CommonHelper from './common.helper';
export const options = { errors: { wrap: { label: '' } } };

export const paginationSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1).required(),
  limit: Joi.number().integer().min(1).default(10).required(),
  sort: Joi.object().pattern(Joi.string(), Joi.number().valid(1, -1)).default({ createdAt: -1 }).optional(),
  search: Joi.string()
    .allow('')
    .optional()
    .custom((value) => CommonHelper.escapeRegExp(value), 'Sanitize search string'),
});
