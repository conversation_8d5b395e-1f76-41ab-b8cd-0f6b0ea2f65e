import { createLogger, format, transports } from 'winston';
// import * as fs from 'fs';
// import * as path from 'path';
import 'winston-daily-rotate-file';

import CONFIG from '../../config/env/index';

// const logDir = path.resolve(__dirname, '../../../logs');
// if (!fs.existsSync(logDir)) {
//   fs.mkdirSync(logDir, { recursive: true });
// }

// const dailyRotateFileTransport = new transports.DailyRotateFile({ filename: `${logDir}/%DATE%-app.log`, datePattern: 'YYYY-MM-DD', zippedArchive: true, maxSize: '20m', maxFiles: '14d', level: CONFIG.PROJECT.LOG_LEVEL });

// const exceptionsFilePath = path.join(logDir, 'exceptions.log');
// const rejectionsFilePath = path.join(logDir, 'rejections.log');

// const isProduction = CONFIG.ENVIRONMENT === 'prod';

const logger = createLogger({
  level: CONFIG.PROJECT.LOG_LEVEL,
  format: format.combine(
    format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    format.errors({ stack: true }), // Ensures stack traces are included
    // format.metadata({ fillExcept: ['message', 'level', 'timestamp', 'stack'] }),
    format.json(),
  ),
  defaultMeta: { service: CONFIG.PROJECT.NAME, environment: CONFIG.ENVIRONMENT || 'development' },
  // transports: [dailyRotateFileTransport, new transports.File({ filename: path.join(logDir, 'app.log'), handleExceptions: true, level: CONFIG.PROJECT.LOG_LEVEL })],
  // exceptionHandlers: [new transports.File({ filename: exceptionsFilePath })],
  // rejectionHandlers: [new transports.File({ filename: rejectionsFilePath })],
});

// Add conditional logging for non-production environments
// if (!isProduction) {
logger.add(
  new transports.Console({
    format: format.combine(
      format.colorize(),
      format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
      format.printf(({ timestamp, level, message, stack }) => {
        let logOutput = `[${timestamp}] ${level}: ${message}`;
        if (stack) {
          logOutput += `\n${stack}`;
        }

        return logOutput;
      }),
    ),
    handleExceptions: true,
    level: 'debug',
  }),
);
// }

export default logger;
