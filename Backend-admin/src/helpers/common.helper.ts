/* eslint-disable @typescript-eslint/no-explicit-any */
import * as jwt from 'jsonwebtoken';
import * as bcrypt from 'bcrypt';
import { createJWTPayload, PromiseResolve } from '../utils/common.interface';
import CONFIG from '../config/env';
import RedisHelper from './redis.helper';
import CustomError from './customError.helper';
import { RES_MSG, RESPONSES } from '../utils/responseUtils';
import { otpLength } from '../utils/constant';
import logger from './logging/logger.helper';
import { disposableDomains } from '../utils/disposableDomains';
import emailHelper from './email.helper';
import axios from 'axios';
import * as crypto from 'crypto';
import * as moment from 'moment';

const { LOGIN_MAX_ATTEMPT } = CONFIG.REDIS;

const { AUTH_EXPIRE_TIME, TOKEN, REFRESH_EXPIRE_TIME, REFRESH_TOKEN } = CONFIG.JWT_AUTH;

export interface AuthTokenResponseType {
  iat: number;
  exp: number;
  aud: string;
  iss: string;
}

/**
 * CommonHelper class providing utility functions for authentication, validation, and other common operations
 */
const CommonHelper = {
  /**
   * Creates a JWT token for authentication
   * @param {createJWTPayload} data - Payload data for the token
   * @param {boolean} [isRefreshToken=false] - Whether to create a refresh token
   * @param {string} [tokenType] - Type of token
   * @param {string} [expiresIn=AUTH_EXPIRE_TIME] - Token expiration time
   * @returns {Promise<PromiseResolve>} Promise resolving to token response
   */
  async createJWTAuth(data: createJWTPayload, isRefreshToken: boolean = false, tokenType?: string, expiresIn: string = AUTH_EXPIRE_TIME): Promise<PromiseResolve> {
    try {
      const expiresIN = this.parseExpirationTime(expiresIn);
      const refReshExpiresIN = this.parseExpirationTime(REFRESH_EXPIRE_TIME);
      const payload = data;
      const options = {
        expiresIn: expiresIN,
        issuer: CONFIG.PROJECT.NAME,
        audience: data.email,
      };

      const refreshOptions = {
        expiresIn: refReshExpiresIN,
        issuer: CONFIG.PROJECT.NAME,
        audience: data.email,
      };

      const accessToken: string = jwt.sign(payload, TOKEN, options);
      let refreshToken: string;
      if (isRefreshToken) {
        refreshToken = jwt.sign(payload, REFRESH_TOKEN, refreshOptions);
      }

      const accessTokenKey = tokenType ? `${tokenType}:${data.email}` : `accessToken:${data.email}`;

      await RedisHelper.setString(accessTokenKey, accessToken, Number(this.convertToMilliseconds(expiresIn)));

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: { refreshToken, accessToken },
      };
    } catch (error: any) {
      logger.error(error, 'createJWTAuth Error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message,
      };
    }
  },
  /**
   * Parses expiration time string into appropriate format
   * @param {string | number} expiration - Time string or number to parse
   * @returns {number | string} Parsed expiration time
   */
  parseExpirationTime(expiration: string | number): number | string {
    if (typeof expiration === 'string' && /^\d+$/.test(expiration)) {
      return Number(expiration); // Convert numeric strings to number
    }
    return expiration; // Return the original string if it's like "1h", "30m", etc.
  },
  /**
   * Gets date range based on specified period
   * @param {string} period - Time period (1Y, 2Y, 3Y, 1M, 15D, 7D, 1D)
   * @returns {Object} Object containing start and end dates as Unix timestamps
   */
  getDateRange(period: string) {
    let startDate;
    const endDate = moment();

    switch (period) {
      case '1Y':
        // startDate = moment().subtract(1, 'years');
        startDate = moment().add(1, 'months').subtract(1, 'years').startOf('month');
        // startDate =moment().startOf('year');
        break;
      case '2Y':
        startDate = moment().subtract(2, 'years');
        break;
      case '3Y':
        startDate = moment().subtract(2, 'years');
        break;
      case '1M':
        startDate = moment().subtract(1, 'month');
        break;
      case '15D':
        startDate = moment().subtract(15, 'days');
        break;
      case '7D':
        startDate = moment().subtract(7, 'days');
        break;
      case '1D':
        startDate = moment().subtract(1, 'day');
        break;
      default:
        startDate = moment().subtract(1, 'year'); // default to last 1 year if no valid period
    }

    return {
      startDate: startDate.unix(), // Convert startDate to timestamp
      endDate: endDate.unix(), // Convert endDate to timestamp
    };
  },

  /**
   * Validates if a token is valid
   * @param {string} accessToken - Token to validate
   * @param {boolean} [isAccessToken=true] - Whether the token is an access token
   * @returns {Promise<PromiseResolve>} Promise resolving to validation result
   */
  async isValidToken(accessToken: string, isAccessToken: boolean = true): Promise<PromiseResolve> {
    try {
      let secretToken: any = TOKEN;
      if (!isAccessToken) {
        secretToken = REFRESH_TOKEN;
      }
      const aud = jwt.verify(accessToken, secretToken) as AuthTokenResponseType;
      if (!aud) throw new CustomError(RES_MSG.COMMON.UNAUTHORIZED_ACCESS, RESPONSES.UN_AUTHORIZED);

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: aud,
      };
    } catch (error: any) {
      logger.error(error, ' invalidToken Error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  /**
   * Generates a one-time password (OTP)
   * @param {number} [length=otpLength] - Length of the OTP
   * @returns {Promise<PromiseResolve>} Promise resolving to generated OTP
   */
  async generateOTP(length: number = otpLength): Promise<PromiseResolve> {
    try {
      const characters = '0123456789';
      const charactersLength = characters.length;
      let otp: string = '';
      for (let i = 0; i < length; i++) {
        otp += characters.charAt(Math.floor(Math.random() * charactersLength));
      }

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: otp,
      };
    } catch (error: any) {
      logger.error(error, ' generateOTP Error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  /**
   * Formats a date to DD-MM-YYYY format
   * @param {Date | string} date - Date to format
   * @returns {Promise<PromiseResolve>} Promise resolving to formatted date
   */
  async formatDates(date: Date | string): Promise<PromiseResolve> {
    try {
      const formattedDate = moment(date).format('DD-MM-YYYY');
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: formattedDate,
      };
    } catch (error: any) {
      logger.error(error, 'formatDates Error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  /**
   * Sends OTP to user's email
   * @param {string} key - User identifier
   * @param {string} otpType - Type of OTP
   * @param {string} email - User's email address
   * @returns {Promise<PromiseResolve>} Promise resolving to OTP sending result
   */
  async sendOTP(key: string, otpType: string, email: string): Promise<PromiseResolve> {
    try {
      const otpResult = await CommonHelper.generateOTP(otpLength);
      if (otpResult.error) {
        throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.BAD_REQUEST);
      }
      const otp = otpResult.data;
      const otpKey = `otp_${otpType}_${key}`;

      const setOtpResult = await RedisHelper.setString(otpKey, otp, CONFIG.REDIS.OTP_EXPIRY);
      if (!setOtpResult) {
        throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.BAD_REQUEST);
      }
      const emailDetails = {
        otp,
      };
      // sending otp
      const sendMailResult = await emailHelper.sendEmailTemplate(email, 'verify-otp', emailDetails);
      if (!sendMailResult) {
        throw new CustomError(RES_MSG.COMMON.SOMETHING_WRONG, RESPONSES.BAD_REQUEST);
      }
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.USER.OTP_SUCCESS,
        data: otp,
      };
    } catch (error: any) {
      logger.error(error, 'sendOTP');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  /**
   * Retrieves stored OTP
   * @param {string} key - User identifier
   * @param {string} otpType - Type of OTP
   * @returns {Promise<PromiseResolve>} Promise resolving to stored OTP
   */
  async getOTP(key: string, otpType: string): Promise<PromiseResolve> {
    try {
      const otpKey = `otp_${otpType}_${key}`;

      const storedOTP = await RedisHelper.getString(otpKey);
      if (storedOTP === null || storedOTP === '') {
        throw new CustomError(RES_MSG.ERROR_MSG.OTP_EXP, RESPONSES.BAD_REQUEST);
      }
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: { otp: storedOTP, key: otpKey },
      };
    } catch (error: any) {
      logger.error(error, 'getOTP error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  /**
   * Validates a string against a regex pattern
   * @param {string} input - String to validate
   * @param {RegExp} regex - Regular expression pattern
   * @param {string} errorMessage - Error message to throw if validation fails
   * @returns {Promise<PromiseResolve>} Promise resolving to validation result
   */
  async validateStringRegex(input: string, regex: RegExp, errorMessage: string): Promise<PromiseResolve> {
    try {
      // Check if the input matches the disallowed symbols regex
      if (regex.test(input)) {
        throw new CustomError(errorMessage, RESPONSES.BAD_REQUEST);
      }

      // If validation passes, return success
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: { input },
      };
    } catch (error: any) {
      logger.error(error, 'validateStringRegex error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  /**
   * Verifies if a password matches the hashed password
   * @param {string} password - Plain text password to verify
   * @param {string} comparePassword - Hashed password to compare against
   * @returns {Promise<PromiseResolve>} Promise resolving to verification result
   */
  async verifyPassword(password: string, comparePassword?: string): Promise<PromiseResolve> {
    try {
      const validPassword: boolean = await bcrypt.compare(password, comparePassword);

      if (!validPassword) throw new CustomError(RES_MSG.ERROR_MSG.INVALID_CREDENTIALS, RESPONSES.NOTFOUND);

      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
      };
    } catch (error) {
      logger.error(error, 'verifyPassword error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  /**
   * Locks a user account by setting a lock key in Redis
   * @param {string} key - User identifier for the lock key
   * @returns {Promise<PromiseResolve>} Promise resolving to lock status
   */
  async userLock(key: string): Promise<PromiseResolve> {
    try {
      const lockKey: string = `locked:${key}`;
      const attemptsKey: string = `otp_attempts:${key}`;
      const { LOGIN_MAX_ATTEMPT, LOGIN_BLOCK_TIME } = CONFIG.REDIS;

      const attempts: number | null = await RedisHelper.incrementKey(attemptsKey, 0, LOGIN_BLOCK_TIME);
      if (attempts && attempts >= LOGIN_MAX_ATTEMPT) {
        await RedisHelper.setString(lockKey, 'locked', LOGIN_BLOCK_TIME);
        throw new CustomError(RES_MSG.USER.USER_LOCKED_TIME, RESPONSES.RESOURCE_LOCKED);
      }
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
      };
    } catch (error) {
      logger.error(error, 'userLock error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        data: LOGIN_MAX_ATTEMPT,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  /**
   * @param {string} key
   * @returns {Promise<PromiseResolve>}
   * @memberof CommonHelper
   */
  async isLocked(key: string): Promise<PromiseResolve> {
    try {
      const lockKey: string = `locked:${key}`;
      const isLoked = await RedisHelper.getString(lockKey);
      if (!isLoked) {
        return {
          status: RESPONSES.SUCCESS,
          error: true,
          message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
          data: isLoked,
        };
      }
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: isLoked,
      };
    } catch (error) {
      logger.error(error, 'userLock error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  /**
   * Validates if an email is from a disposable domain
   * @param {string} email - Email address to validate
   * @returns {Promise<PromiseResolve>} Promise resolving to validation result
   */
  async isValidEmail(email: string): Promise<PromiseResolve> {
    try {
      const domain = email.split('@')[1];
      const isInvalid = disposableDomains?.includes(domain);
      if (isInvalid) {
        return {
          status: RESPONSES.BAD_REQUEST,
          error: true,
          message: RES_MSG.ERROR_MSG.INVALID_EMAIL,
        };
      }
      return {
        status: RESPONSES.SUCCESS,
        error: false,
        message: RES_MSG.SUCCESS_MSG.DATA_SUCCESS,
        data: {
          isInvalid,
        },
      };
    } catch (error) {
      logger.error(error, 'isValidEmail error');
      return {
        status: error.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
      };
    }
  },

  /**
   * Merges arrays while removing duplicates based on specified keys
   * @param {any[]} newItems - Array of new items to merge
   * @param {any[]} existingItems - Array of existing items
   * @param {any[]} keys - Array of keys to check for duplicates
   * @returns {any[]} Merged array without duplicates
   */
  mergeWithoutDuplicates(newItems: any[], existingItems: any[] = [], keys: any[] = []): any[] {
    if (keys.length === 0) {
      const uniqueItems = [...new Set([...existingItems, ...newItems])];
      return uniqueItems;
    }

    // If keys are provided, process the merging logic based on keys
    newItems.forEach((newItem) => {
      const existingIndex = existingItems.findIndex((existingItem) =>
        keys.every((key) => {
          const existingValue = key.split('.').reduce((o: any, k: string) => (o || {})[k], existingItem);
          const newValue = key.split('.').reduce((o: any, k: string) => (o || {})[k], newItem);
          return existingValue === newValue;
        }),
      );

      if (existingIndex > -1) {
        // Merge the items if a match is found based on keys
        existingItems[existingIndex] = {
          ...existingItems[existingIndex],
          ...newItem,
        };
      } else {
        // Push newItem to existingItems if no match is found
        existingItems.push(newItem);
      }
    });

    // Return the updated existingItems array
    return existingItems;
  },

  /**
   * Converts time string to milliseconds
   * @param {string | number} timeString - Time string (e.g., "1h", "30m") or number
   * @returns {number} Time in milliseconds
   */
  convertToMilliseconds(timeString: string | number): number {
    if (typeof timeString === 'number') {
      return timeString;
    }

    const regex = /^(\d+)(ms|s|m|hr)$/;
    const match = timeString.match(regex);

    if (!match) {
      throw new Error('Invalid time format');
    }

    const value = parseInt(match[1], 10);
    const unit = match[2];

    switch (unit) {
      case 's':
        return value;
      case 'm':
        return value * 60;
      case 'hr':
        return value * 60 * 60;
      default:
        throw new Error('Unknown time unit');
    }
  },

  /**
   * Generates a random key
   * @returns {string} Random key string
   */
  async generateRandomKey() {
    return Math.random().toString().slice(2, 6);
  },

  /**
   * Escapes special characters in a string for use in regular expressions
   * @param {string} string - String to escape
   * @returns {string} Escaped string
   */
  escapeRegExp(string: string) {
    return string.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
  },

  /**
   * Creates a bonding dex entry
   * @param {any} createOfferingDto - Data for creating the bonding dex
   * @returns {Promise<any>} Promise resolving to the created bonding dex
   */
  async createBondingDex(createOfferingDto: any): Promise<any> {
    try {
      const payload = {
        data: createOfferingDto,
        timestamp: Date.now(),
      };
      const payloadString = JSON.stringify(payload);
      const SECRET_KEY = process.env.HMAC_SECRET;
      console.log('============>>>>', SECRET_KEY);
      const signature = crypto.createHmac('sha256', SECRET_KEY).update(payloadString).digest('hex');
      const bondingApiUrl = `${process.env.BONDING_API}/bonding/api/bondingdex/create`;
      console.log('bondingApiUrl----', { bondingApiUrl, signature, payload });
      const response = await axios.post(bondingApiUrl, payload, {
        headers: {
          'Content-Type': 'application/json',
          'x-hmac-signature': signature,
        },
      });
      return response.data;
    } catch (error: any) {
      logger.error('error->signatureUrl', error?.response?.data || error);
      const errorData = error?.response?.data;
      return {
        status: error.response?.status || RESPONSES.INTERNAL_SERVER_ERROR,
        error: true,
        message: errorData?.message || error.message || RES_MSG.ERROR_MSG.INTERNAL_SERVER_ERROR,
        errors: errorData?.errors || null,
      };
    }
  },
};

export default CommonHelper;
