import * as sgMail from '@sendgrid/mail';
import config from '../config/env';

import * as ejs from 'ejs';
import * as path from 'path';
import { RES_MSG } from '../utils/responseUtils';
import logger from './logging/logger.helper';
const sender = process.env.SENDER;

/**
 * EmailService class to handle sending emails using SendGrid.
 */
class EmailService {
  private sgMail: any;

  /**
   * Constructor to initialize the EmailService.
   * Sets up SendGrid with the API key from the configuration.
   */
  constructor() {
    // Set the SendGrid API key
    this.sgMail = sgMail.setApiKey(config.SENDGRID.API_KEY);
  }

  /**
   * Sends an email using SendGrid.
   * @param {string} to - Recipient email address(es), comma-separated for multiple recipients
   * @param {string} subject - Subject of the email
   * @param {string} text - Plain text content of the email
   * @param {string} html - HTML content of the email
   * @param {string} [type] - Optional type of email (e.g., 'investor' for BCC sending)
   * @returns {Promise<boolean>} Promise resolving to true if email sent successfully, false otherwise
   */
  public async sendMail(to: string, subject: string, text: string, html: string, type?: string): Promise<boolean> {
    try {
      const emailArray = to.split(',').map((email) => email.trim());
      let msg;
      if (type == 'investor') {
        msg = {
          to: sender,
          bcc: emailArray,
          from: {
            email: config.SENDGRID.SENDER,
            name: 'libertum', // Set the display name here
          },
          subject,
          text,
          html,
        };
      } else {
        msg = {
          to: emailArray,
          from: {
            email: config.SENDGRID.SENDER,
            name: 'libertum', // Set the display name here
          },
          subject,
          text,
          html,
        };
      }
      // Send the email using SendGrid
      return this.sgMail
        .send(msg)
        .then(() => {
          return true;
        })
        .catch((error: any) => {
          logger.error(error, 'error in SendGrid');
          return false;
        });
    } catch (error) {
      logger.error(error, 'Error of sendMail');
      return false;
    }
  }

  /**
   * Sends an email using a predefined template.
   * @param {string} to - Recipient email address(es)
   * @param {string} templateName - Name of the template to use (e.g., 'reset-password', 'verify-otp', etc.)
   * @param {any} details - Dynamic data to be used in the template
   * @returns {Promise<boolean>} Promise resolving to true if email sent successfully, false otherwise
   */
  public async sendEmailTemplate(to: string, templateName: string, details: any): Promise<boolean> {
    try {
      let templatePath, subject, text;
      if (templateName === 'reset-password') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.FORGOT_PASSWORD_SUBJECT;
        text = RES_MSG.EMAIL.FORGOT_PASSWORD_TEXT;
      } else if (templateName === 'verify-otp') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.OTP_SUBJECT;
        text = RES_MSG.EMAIL.OTP_TEXT;
      } else if (templateName === 'login') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.ADMIN_EMAIL;
        text = RES_MSG.EMAIL.ADMIN_EMAIL;
      } else if (templateName === 'transferAgent') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.TRANSFERAGENT;
        text = RES_MSG.EMAIL.TRANSFERAGENT;
      } else if (templateName === 'becomeanissuer') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.APPROVE_ISSUER;
        text = RES_MSG.EMAIL.APPROVE_ISSUER;
      } else if (templateName === 'rejectIssuer') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.REJECT_ISSUER;
        text = RES_MSG.EMAIL.REJECT_ISSUER;
      } else if (templateName === 'rejectoffering') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.OFFERING_REJECTED;
        text = RES_MSG.EMAIL.OFFERING_REJECTED;
      } else if (templateName === 'SubAdminInvite') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.SUBADMIN_INVITE;
        text = RES_MSG.EMAIL.SUBADMIN_INVITE;
      } else if (templateName === 'accountBlocked') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.ACCOUNT_BLOCKED;
        text = RES_MSG.EMAIL.ACCOUNT_BLOCKED;
      } else if (templateName === 'AccountBlockedNotification') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.ACCOUNT_BLOCKED;
        text = RES_MSG.EMAIL.ACCOUNT_BLOCKED;
      } else if (templateName === 'accountUnBlock') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.ACCONT_UNBLOCKED;
        text = RES_MSG.EMAIL.ACCONT_UNBLOCKED;
      } else if (templateName === 'SubadminAccountBlock') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.ACCOUNT_BLOCKED;
        text = RES_MSG.EMAIL.ACCOUNT_BLOCKED;
      } else if (templateName === 'subadminAccountUnBlock') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.ACCONT_UNBLOCKED;
        text = RES_MSG.EMAIL.ACCONT_UNBLOCKED;
      } else if (templateName === 'KYCApproved') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.KYC_APPROVED;
        text = RES_MSG.EMAIL.KYC_APPROVED;
      } else if (templateName === 'KYBApproved') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.KYB_APPROVED;
        text = RES_MSG.EMAIL.KYB_APPROVED;
      } else if (templateName === 'KYBRejected') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.KYB_REJECTED;
        text = RES_MSG.EMAIL.KYB_REJECTED;
      } else if (templateName === 'Adminemail') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.ADMIN_EMAIL;
        text = RES_MSG.EMAIL.ADMIN_EMAIL;
      } else if (templateName === 'KYCRejected') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.KYC_REJECTED;
        text = RES_MSG.EMAIL.KYC_REJECTED;
      } else if (templateName === 'KYCReceived') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.KYC_RECEIVED;
        text = RES_MSG.EMAIL.KYC_RECEIVED;
      } else if (templateName === 'investor') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = `Exclusive Invitation: Private Launch of ${details?.offeringName}`;
        text = `Exclusive Invitation: Private Launch of ${details?.offeringName}`;
      } else if (templateName === 'newOfferingRequest') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.NEW_OFFERING_REQUEST_ADMIN;
        text = RES_MSG.EMAIL.NEW_OFFERING_REQUEST_ADMIN;
      } else if (templateName === 'newOfferingRequestIssure') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.NEW_OFFERING_REQUEST_ISSUER;
        text = RES_MSG.EMAIL.NEW_OFFERING_REQUEST_ISSUER;
      } else if (templateName === 'newOfferingRequestIssureSubadmin') {
        templatePath = path.join(__dirname, `../utils/emailTemplate/${templateName}.ejs`);
        subject = RES_MSG.EMAIL.NEW_OFFERING_REQUEST_SUB_ADMIN;
        text = RES_MSG.EMAIL.NEW_OFFERING_REQUEST_SUB_ADMIN;
      } else {
        logger.error(templateName, 'Error: no matching template found');
        return false;
      }

      const htmlDetails = await ejs.renderFile(templatePath, { detail: { ...details, baseUrl: config.API_HOST_URL } }, { async: true });
      if (templateName === 'investor') {
        return await this.sendMail(to, subject, text, htmlDetails, 'investor');
      } else {
        return await this.sendMail(to, subject, text, htmlDetails);
      }
    } catch (error: any) {
      logger.error(error, 'Error in sendEmailTemplate');
      return false;
    }
  }
}

// Export an instance of the EmailService class
export default new EmailService();
