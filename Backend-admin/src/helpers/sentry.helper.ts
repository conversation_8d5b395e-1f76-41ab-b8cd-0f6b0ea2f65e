import * as Sentry from '@sentry/node';
import { RewriteFrames } from '@sentry/integrations';

export const initSentry = () => {
  if (!process.env.SENTRY_DSN) {
    console.warn('SENTRY_DSN is not set. Sentry will not be initialized.');
    return;
  }

  Sentry.init({
    dsn: process.env.SENTRY_DSN,
    integrations: [
      new RewriteFrames({
        root: process.cwd(),
      }),
    ],
    // Performance Monitoring
    tracesSampleRate: 1.0, // Capture 100% of the transactions
    environment: process.env.NODE_ENV || 'development',
  });
};

export default Sentry;
