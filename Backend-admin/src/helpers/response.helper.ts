import express from 'express';
import { ResponsePayLoad, PromiseResolve } from '../utils/common.interface';
import { capitalizeString } from './messageHelper';

/**
 * ResponseHandler class for handling API responses
 */
export class ResponseHandler {
  /**
   * Handles successful API responses
   * @param {express.Response} response - Express response object
   * @param {ResponsePayLoad<T>} responseData - Response data including message, status, and data
   * @returns {PromiseResolve} Formatted response object
   */
  static success<T>(response: express.Response, responseData: ResponsePayLoad<T>): PromiseResolve {
    const { message: messages, status, data = null, error } = responseData;
    const message = capitalizeString(messages);
    response.status(status || 200).json({ message, status, data, error });
    return {
      message,
      status: status || 200,
      data,
      error,
    };
  }

  /**
   * Handles error API responses
   * @param {express.Response} response - Express response object
   * @param {ResponsePayLoad<T>} responseData - Response data including error message, status, and data
   * @returns {PromiseResolve} Formatted error response object
   */
  static error<T>(response: express.Response, responseData: ResponsePayLoad<T>): PromiseResolve {
    const { message, status, data = null, error } = responseData;
    response.status(status || 500).json({ message, status, data, error });
    return {
      message,
      status: status || 500,
      data,
      error,
    };
  }
}
