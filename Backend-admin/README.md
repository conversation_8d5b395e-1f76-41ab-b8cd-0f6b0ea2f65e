# Libertum Tokenization Admin Panel

A comprehensive and secure admin panel designed for managing the Libertum Tokenization platform. This backend system provides robust APIs and services for token management, user administration, and platform operations. Built with modern technologies and best practices, it ensures high performance, security, and scalability.

## Features

### Authentication & Security

- **Secure authentication with JWT and 2FA support**: Implements JSON Web Tokens for secure authentication and Two-Factor Authentication (2FA) for enhanced security
- **Role-based access control**: Granular permission system allowing different levels of access based on user roles
- **User management**: Complete user lifecycle management including registration, profile management, and account recovery

### Token Management

- **Token operations**: Create, update, and manage digital tokens
- **Token tracking**: Monitor token transactions and ownership
- **Token verification**: Validate token authenticity and ownership

### Infrastructure

- **Real-time notifications**: Instant updates for important events and transactions
- **File storage integration**: Secure file handling with Google Cloud Storage
- **Email notifications**: Automated email communications via SendGrid
- **Redis caching**: High-performance caching for improved response times
- **MongoDB database**: Scalable NoSQL database for flexible data storage
- **gRPC service integration**: High-performance RPC framework for microservices communication

### Documentation & Monitoring

- **Swagger API documentation**: Interactive API documentation for easy integration
- **Comprehensive logging system**: Detailed logging for debugging and monitoring

## Tech Stack

### Backend

- **Node.js**: JavaScript runtime for server-side execution
- **Express**: Fast, unopinionated web framework for Node.js
- **TypeScript**: Typed superset of JavaScript for better development experience

### Database & Caching

- **MongoDB**: Document-based NoSQL database for flexible data storage
- **Redis**: In-memory data structure store for caching and session management

### Security & Authentication

- **JWT**: JSON Web Tokens for secure authentication
- **2FA**: Two-Factor Authentication for enhanced security

### Cloud Services

- **Google Cloud Storage**: Secure and scalable cloud storage solution
- **SendGrid**: Reliable email delivery service

### Development Tools

- **Swagger**: API documentation and testing
- **Winston & Pino**: Advanced logging solutions
- **Mocha & Chai**: Testing framework and assertion library
- **ESLint & Prettier**: Code quality and formatting tools
- **SonarQube**: Code quality and security analysis

## Prerequisites

### System Requirements

- **Node.js (v14 or higher)**: Required for running the application
- **MongoDB**: Database server for data storage
- **Redis**: Cache server for performance optimization
- **Google Cloud Storage account**: For file storage
- **SendGrid account**: For email services

### Development Environment

- Code editor (VS Code recommended)
- Git for version control
- Docker (optional, for containerized deployment)

## Installation

### 1. Clone the Repository

```bash
git clone https://github.com/Libertum-Project/Backend-admin.git
cd libertum-admin-backend
```

### 2. Install Dependencies

```bash
npm install
```

This will install all required dependencies including:

- Express and related middleware
- Database drivers
- Authentication libraries
- Testing frameworks
- Development tools

### 3. Environment Configuration

```bash
cp env.example .env
```

Edit the `.env` file with your specific configuration:

- Database credentials
- API keys
- Service endpoints
- Security settings

### 4. Build the Project

```bash
npm run build
```

This command:

- Compiles TypeScript to JavaScript
- Copies necessary assets
- Generates API documentation

### 5. Start the Server

```bash
npm start
```

For development with hot-reload:

```bash
npm run dev
```

## Environment Variables

### Application Configuration

- `NODE_ENV`: Environment mode (development/production)
- `PORT`: Server port number
- `LOG_LEVEL`: Logging verbosity level

### Database Configuration

- `MONGODB_HOST`: MongoDB server hostname
- `MONGODB_PORT`: MongoDB server port
- `MONGODB_DATABASE`: Database name

### Cache Configuration

- `REDIS_HOST`: Redis server hostname
- `LOGIN_MAX_ATTEMPT`: Maximum login attempts
- `LOGIN_BLOCK_TIME`: Account lock duration after failed attempts

### Security Configuration

- `JWT_AUTH_SECRET`: Secret key for JWT generation
- `JWT_AUTH_EXPIRE`: JWT token expiration time
- `JWT_REFRESH_SECRET`: Secret key for refresh tokens
- `JWT_REFRESH_EXPIRE`: Refresh token expiration time

### Service Configuration

- `SENDGRID_API_KEY`: SendGrid API key for email services
- `GOOGLE_CLIENT_ID`: Google Cloud client ID
- `GOOGLE_PROJECT_ID`: Google Cloud project ID
- `BUCKET_NAME`: Google Cloud Storage bucket name

## API Documentation

The API documentation is available at `/api-docs` when the server is running. It includes:

- Detailed endpoint descriptions
- Request/response schemas
- Authentication requirements
- Example requests
- Response codes
- Error handling

## Development

### Testing

```bash
npm test
```

- Unit tests
- Integration tests
- API tests
- Security tests

### Code Quality

```bash
npm run format
```

- Code formatting
- Linting
- Type checking

### Analysis

```bash
npm run sonar
```

- Code quality analysis
- Security vulnerability scanning
- Technical debt assessment

## Docker Support

The project includes Docker configuration for containerized deployment:

```bash
docker-compose up
```

This will:

- Build the application container
- Set up required services
- Configure networking
- Mount volumes
- Set environment variables

## Security Features

### Authentication

- JWT-based authentication
- Two-factor authentication
- Session management
- Password policies

### Protection

- Rate limiting
- CORS protection
- Helmet security headers
- Input validation
- SQL injection prevention
- XSS protection

### Data Security

- Secure password hashing with bcrypt
- Encrypted data storage
- Secure file uploads
- API key management

## Logging

The application uses a comprehensive logging system:

### File Logging (Winston)

- Daily log rotation
- Multiple log levels
- Structured logging
- Error tracking

### Request Logging (Pino)

- HTTP request logging
- Response time tracking
- Error logging
- Performance monitoring

## Contributing

### 1. Fork the Repository

Create your own copy of the repository

### 2. Create Feature Branch

```bash
git checkout -b feature/your-feature-name
```

### 3. Make Changes

- Follow coding standards
- Write tests
- Update documentation

### 4. Commit Changes

```bash
git commit -m "Description of changes"
```

### 5. Push to Branch

```bash
git push origin feature/your-feature-name
```

### 6. Create Pull Request

- Describe changes
- Link related issues
- Request reviews

## License

[Add your license information here]

## Project Structure

```
.
├── .github/                    # GitHub configuration files
│   └── workflows/             # GitHub Actions workflows
├── src/                       # Source code directory
│   ├── config/               # Application configuration
│   │   ├── server/          # Server configuration
│   │   ├── connection/      # Database and service connections
│   │   ├── env/            # Environment configuration
│   │   ├── middleware/     # Global middleware configuration
│   │   └── error/          # Error handling configuration
│   │
│   ├── component/           # Reusable components
│   │   ├── userAuthentications/  # User authentication components
│   │   ├── transfer/            # Transfer related components
│   │   ├── subadmin/            # Subadmin management components
│   │   ├── notification/        # Notification components
│   │   ├── offerings/           # Offering management components
│   │   ├── transactions/        # Transaction components
│   │   ├── transferagent/       # Transfer agent components
│   │   └── nft/                 # NFT management components
│   │
│   ├── helpers/             # Helper functions and utilities
│   │   ├── common.helper.ts         # Common helper functions
│   │   ├── joiError.filter.helper.ts # Joi validation error handling
│   │   ├── response.helper.ts       # Response formatting
│   │   ├── redis.helper.ts          # Redis operations
│   │   ├── email.helper.ts          # Email operations
│   │   ├── bigMath.ts               # Big number calculations
│   │   ├── cloud.helper.ts          # Cloud storage operations
│   │   ├── logging/                 # Logging utilities
│   │   ├── messageHelper.ts         # Message handling
│   │   ├── joi.helper.ts            # Joi validation setup
│   │   ├── kafka.helper.ts          # Kafka operations
│   │   └── customError.helper.ts    # Custom error handling
│   │
│   ├── middleware/          # Express middleware
│   │   ├── validateGetUsersList.ts     # User list validation
│   │   ├── checkAdminPermissions.ts    # Admin permission checks
│   │   ├── notification.middleware.ts   # Notification handling
│   │   ├── user.middleware.ts          # User related middleware
│   │   ├── offering.middleware.ts      # Offering validation
│   │   ├── googleCloud.middleware.ts   # Google Cloud operations
│   │   ├── nft.middleware.ts           # NFT validation
│   │   └── transactions.middleware.ts  # Transaction validation
│   │
│   ├── routes/             # API routes
│   │   ├── notification.router.ts   # Notification routes
│   │   ├── transferagent.router.ts  # Transfer agent routes
│   │   ├── user.router.ts           # User management routes
│   │   ├── auth.router.ts           # Authentication routes
│   │   ├── subadmin.router.ts       # Subadmin routes
│   │   ├── offering.router.ts       # Offering routes
│   │   ├── nft.router.ts            # NFT routes
│   │   └── transactions.router.ts   # Transaction routes
│   │
│   ├── services/           # Business logic services
│   │   └── kafkaService.ts         # Kafka service implementation
│   │
│   ├── utils/              # Utility functions
│   │   ├── constant.ts            # Application constants
│   │   ├── swaggerDef.ts          # Swagger API documentation
│   │   ├── common.interface.ts    # Common interfaces
│   │   ├── emailTemplate/         # Email templates
│   │   ├── responseUtils.ts       # Response utilities
│   │   └── disposableDomains.ts   # Disposable email domains list
│   │
│   ├── _grpc/             # gRPC service definitions
│   │   ├── index.ts              # gRPC service setup
│   │   ├── clients/              # gRPC clients
│   │   └── proto/                # Protocol buffer definitions
│   │
│   ├── public/            # Public assets
│   └── seed/              # Database seed data
│
├── .eslintignore          # ESLint ignore patterns
├── .eslintrc.json         # ESLint configuration
├── .gitignore             # Git ignore patterns
├── .prettierrc            # Prettier configuration
├── docker-compose.yaml    # Docker Compose configuration
├── Dockerfile             # Docker build configuration
├── env.example            # Environment variables template
├── eslint.config.mjs      # ESLint configuration
├── nodemon.json           # Nodemon configuration
├── package.json           # Project dependencies and scripts
├── package-lock.json      # NPM lock file
├── README.md              # Project documentation
├── sonar.js               # SonarQube configuration
└── tsconfig.json          # TypeScript configuration
```
