# Libertum Tokenization Admin API - Endpoints Reference

## Authentication Endpoints (Public)

| Method | Endpoint                    | Description         | Auth Required |
| ------ | --------------------------- | ------------------- | ------------- |
| GET    | `/admin/v1/health-check`    | System health check | No            |
| POST   | `/admin/v1/login`           | Admin login         | No            |
| POST   | `/admin/v1/verify`          | Verify OTP          | No            |
| POST   | `/admin/v1/resend-otp`      | Resend OTP          | No            |
| POST   | `/admin/v1/forgot-password` | Forgot password     | No            |
| POST   | `/admin/v1/reset-password`  | Reset password      | No            |

## User Management Endpoints (Protected)

| Method | Endpoint                      | Description                    | Auth Required |
| ------ | ----------------------------- | ------------------------------ | ------------- |
| POST   | `/admin/v1/auth/approve`      | Approve/reject user KYC        | Yes           |
| GET    | `/admin/v1/auth/getUserslist` | Get users list with pagination | Yes           |

## Offering Management Endpoints (Protected)

| Method | Endpoint                  | Description            | Auth Required |
| ------ | ------------------------- | ---------------------- | ------------- |
| POST   | `/admin/v1/auth/offering` | Create/update offering | Yes           |
| GET    | `/admin/v1/auth/offering` | Get offerings list     | Yes           |

## Transaction Management Endpoints (Protected)

| Method | Endpoint                                   | Description                  | Auth Required |
| ------ | ------------------------------------------ | ---------------------------- | ------------- |
| GET    | `/admin/v1/auth/transactions/{offeringId}` | Get transactions by offering | Yes           |

## Notification Endpoints (Protected)

| Method | Endpoint                           | Description                | Auth Required |
| ------ | ---------------------------------- | -------------------------- | ------------- |
| GET    | `/admin/v1/auth/notification`      | Get notifications list     | Yes           |
| POST   | `/admin/v1/auth/notification/seen` | Mark notifications as seen | Yes           |

## NFT Management Endpoints (Protected)

| Method | Endpoint                           | Description         | Auth Required |
| ------ | ---------------------------------- | ------------------- | ------------- |
| GET    | `/admin/v1/auth/nft/getCollection` | Get NFT collections | Yes           |

## Transfer Agent Endpoints (Protected)

| Method | Endpoint                             | Description              | Auth Required |
| ------ | ------------------------------------ | ------------------------ | ------------- |
| POST   | `/admin/v1/auth/createTransferAgent` | Create transfer agent    | Yes           |
| GET    | `/admin/v1/auth/transferAgentList`   | Get transfer agents list | Yes           |

## Sub-Admin Endpoints (Protected)

| Method | Endpoint                  | Description         | Auth Required |
| ------ | ------------------------- | ------------------- | ------------- |
| POST   | `/admin/v1/auth/subadmin` | Create sub-admin    | Yes           |
| GET    | `/admin/v1/auth/subadmin` | Get sub-admins list | Yes           |

## Dashboard & Analytics Endpoints (Protected)

| Method | Endpoint                      | Description        | Auth Required |
| ------ | ----------------------------- | ------------------ | ------------- |
| GET    | `/admin/v1/auth/dashboard`    | Get dashboard data | Yes           |
| GET    | `/admin/v1/auth/topInvestors` | Get top investors  | Yes           |

## Common Query Parameters

### Pagination Parameters

- `page` (integer): Page number (default: 1)
- `limit` (integer): Items per page (default: 10, max: 100)

### Search Parameters

- `search` (string): Search term for name/email/title
- `sort` (string): Sorting criteria in JSON format

### Filter Parameters

- `userType`: Filter by user type (investor, issuer, admin, subadmin)
- `isActive`: Filter by active status (true/false)
- `kycStatus`: Filter by KYC status (PENDING, APPROVED, REJECTED)
- `status`: Filter by general status
- `type`: Filter by transaction type (BUY, SELL, TRANSFER, DIVIDEND)
- `assetType`: Filter by asset type

## Authentication

All protected endpoints require JWT Bearer token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Response Format

All endpoints return responses in the following format:

### Success Response

```json
{
  "message": "Success message",
  "status": 200,
  "data": {
    /* response data */
  },
  "error": false
}
```

### Error Response

```json
{
  "message": "Error message",
  "status": 400,
  "error": true
}
```

## Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `423` - Account Locked
- `500` - Internal Server Error

## Getting Started

1. Start with `/admin/v1/health-check` to verify API is running
2. Use `/admin/v1/login` to authenticate and get JWT token
3. Include JWT token in Authorization header for protected endpoints
4. Access Swagger documentation at `/api-docs` for interactive testing

## Notes

- All timestamps are in ISO 8601 format
- MongoDB ObjectIds are 24-character hexadecimal strings
- Wallet addresses are Ethereum format (0x...)
- Email addresses must be valid format
- Pagination starts from page 1
