# Libertum Tokenization Admin API - Swagger Documentation

## Overview

I have successfully created comprehensive Swagger documentation for your Libertum Tokenization Admin API. The documentation has been implemented using OpenAPI 3.0 specification and includes detailed descriptions, request/response schemas, examples, and proper error handling for all major API endpoints.

## What Has Been Implemented

### 1. **Complete API Documentation Structure**

- **OpenAPI 3.0 Specification**: Modern, standardized API documentation format
- **Comprehensive Metadata**: Title, description, version, contact information, and license
- **Server Configuration**: Development and production server endpoints
- **Security Schemes**: JWT Bearer token authentication
- **Reusable Components**: Error and Success response schemas

### 2. **API Categories Documented**

#### **Authentication APIs** (`/admin/v1/`)

- `POST /admin/v1/login` - Admin login with email/password
- `POST /admin/v1/verify` - OTP verification for authentication
- `POST /admin/v1/resend-otp` - Resend OTP for various operations
- `POST /admin/v1/forgot-password` - Initiate password reset process
- `POST /admin/v1/reset-password` - Reset password with token
- `GET /admin/v1/health-check` - System health check

#### **User Management APIs** (`/admin/v1/auth/`)

- `POST /admin/v1/auth/approve` - Approve/reject user KYC
- `GET /admin/v1/auth/getUserslist` - Get paginated users list with filtering
- `GET /admin/v1/auth/dashboard` - Dashboard analytics and statistics

#### **Offering Management APIs** (`/admin/v1/auth/`)

- `POST /admin/v1/auth/offering` - Create/update investment offerings
- `GET /admin/v1/auth/offering` - Get offerings list with filtering

#### **Transaction Management APIs** (`/admin/v1/auth/`)

- `GET /admin/v1/auth/transactions/{offeringId}` - Get transactions by offering

#### **Notification APIs** (`/admin/v1/auth/notification/`)

- `GET /admin/v1/auth/notification` - Get notifications list
- `POST /admin/v1/auth/notification/seen` - Mark notifications as read

#### **NFT Management APIs** (`/admin/v1/auth/nft/`)

- `GET /admin/v1/auth/nft/getCollection` - Get NFT collections

#### **Transfer Agent APIs** (`/admin/v1/auth/`)

- `POST /admin/v1/auth/createTransferAgent` - Create transfer agent
- `GET /admin/v1/auth/transferAgentList` - Get transfer agents list

#### **Sub-Admin APIs** (`/admin/v1/auth/`)

- `POST /admin/v1/auth/subadmin` - Create sub-administrator
- `GET /admin/v1/auth/subadmin` - Get sub-admins list

#### **Dashboard APIs** (`/admin/v1/auth/`)

- `GET /admin/v1/auth/topInvestors` - Get top investors analytics

### 3. **Key Features Implemented**

#### **Comprehensive Request/Response Documentation**

- Detailed parameter descriptions with examples
- Request body schemas with validation requirements
- Multiple response scenarios (success, error, validation failures)
- Proper HTTP status codes and error messages

#### **Advanced Query Parameters**

- Pagination support (page, limit)
- Search functionality
- Filtering options (status, type, active state)
- Sorting capabilities

#### **Security Implementation**

- JWT Bearer token authentication
- Protected endpoints with security requirements
- Proper authorization error responses

#### **Data Models and Schemas**

- Reusable component schemas for common responses
- Detailed object properties with types and examples
- Validation requirements and constraints

### 4. **Documentation Quality Features**

#### **Detailed Descriptions**

- Clear, professional API descriptions
- Business context for each endpoint
- Usage examples and scenarios

#### **Proper Examples**

- Realistic request/response examples
- Sample data that reflects actual use cases
- Multiple response scenarios

#### **Error Handling**

- Comprehensive error response documentation
- Standard error formats across all endpoints
- Specific error messages for different scenarios

## File Location

The complete Swagger documentation has been implemented in:

```
src/utils/swaggerDef.ts
```

## How to Access the Documentation

Once your server is running properly (after resolving MongoDB connection issues), the Swagger documentation should be accessible at:

```
http://localhost:7002/api-docs
```

## Next Steps

1. **Resolve Server Issues**: Fix MongoDB connection and port conflicts
2. **Test Documentation**: Verify Swagger UI loads correctly
3. **Add Missing Endpoints**: If there are additional endpoints not covered
4. **Customize Styling**: Optionally customize Swagger UI appearance
5. **Add More Examples**: Enhance with additional request/response examples

## Benefits of This Implementation

- **Developer-Friendly**: Easy to understand and use API documentation
- **Interactive**: Swagger UI allows testing endpoints directly
- **Maintainable**: OpenAPI 3.0 standard ensures long-term compatibility
- **Professional**: Comprehensive documentation suitable for external developers
- **Searchable**: Well-organized with tags and categories
- **Validation**: Built-in request/response validation schemas

The documentation now provides a complete reference for all major API functionality in your Libertum Tokenization Admin service, making it easy for developers to understand and integrate with your APIs.
