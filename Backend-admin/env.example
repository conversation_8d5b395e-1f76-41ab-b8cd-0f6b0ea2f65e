# Application Configuration
NODE_ENV=development
PROJECT_NAME=
LOG_LEVEL="debug"

PORT=

# MongoDB Configuration
MONGODB_HOST=
MONGODB_PORT=
MONGODB_DATABASE=

# Redis Configuration
REDIS_HOST=
LOGIN_MAX_ATTEMPT=
LOGIN_BLOCK_TIME=           #IN SECONDS
REDIS_AUTH_EXPIRE=            # "Set REDIS_AUTH_EXPIRE time in seconds equal to JWT_AUTH_EXPIRE."
OTP_EXPIRY=

# JWT Configuration
JWT_AUTH_SECRET=
JWT_AUTH_EXPIRE=                        # 1 hour
JWT_REFRESH_SECRET=
JWT_REFRESH_EXPIRE=                   # 1 day
JWT_FORGOT_EXPIRE=                      # 10 minutes
JWT_2FA_EXPIRE=                         # 10 minutes
JWT_EMAIL_FORGOT_EXPIRE=                # 10 minutes
JWT_EMAIL_LOGIN_EXPIRE=                 # 10 minutes

# Email Configuration
SENDGRID_API_KEY=
SENDER=

# Google Cloud Configuration
GOOGLE_CLIENT_ID=
GOOGLE_PROJECT_ID=
GOOGLE_KEY_FILE_NAME=
BUCKET_NAME=
MAX_SIZE=5

# Azure Cloud Configuration
CONNECTION_STRING=
ACCOUNT_NAME=
BUCKET_NAME=
MAX_SIZE=5

# Kafka Configuration
KAFKA_BROKER=localhost
KAFKA_BROKER_PORT=9092

# gRPC Configuration
USER_SERVICE_GRPC_CONTAINER_NAME=0.0.0.0
USER_SERVICE_GRPC_PORT=40001
ADMIN_SERVICE_GRPC_CONTAINER_NAME=localhost
ADMIN_SERVICE_GRPC_PORT=40002
NOTIFICATION_SERVICE_GRPC_CONTAINER_NAME=localhost
NOTIFICATION_SERVICE_GRPC_PORT=40004
GRPC_SSL=false

# API Configuration
API_HOST_URL=http://localhost:7001

SONAR_HOST=http://localhost:9000
SONAR_TOKEN=sqa_716ddb46061fca0cbe1c38a764771c67d84e1a60