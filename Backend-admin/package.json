{"name": "Libertum Tokenization", "version": "0.0.1", "main": "./build/config/server/index.js", "description": "Power Libertum platform admin operations, manage users/tokens, and orchestrate workflows via Kafka and gRPC.", "scripts": {"build": "tsc && npm run copy-ejs --skipLibCheck && npm run copy-proto --skipLibCheck && npm run copy-public --skipLibCheck", "copy-ejs": "cp -r ./src/utils/emailTemplate build/utils/emailTemplate", "copy-proto": "cp -r ./src/_grpc/proto build/_grpc/proto", "copy-public": "cp -r ./src/public build/public", "start": "node ./build/config/server/index.js", "dev": "npm run format && nodemon ./src/server.ts", "start:dev": "concurrently \"nodemon --ext ts --exec ts-node ./src/config/server/index.ts\"", "format": "npx prettier --write . './src/**/*.{ts,tsx,js,jsx,json,ejs}' && npx eslint --fix './src/**/*.{ts,tsx,js}'", "sonar": "dotenv -e .env -- sh -c 'sonar-scanner -Dsonar.host.url=\"$SONAR_HOST\" -Dsonar.token=\"$SONAR_TOKEN\"'"}, "dependencies": {"@azure/storage-blob": "^12.26.0", "@google-cloud/storage": "^7.12.1", "@grpc/grpc-js": "^1.12.2", "@grpc/proto-loader": "^0.7.13", "@sendgrid/mail": "^8.1.3", "@sentry/integrations": "^7.114.0", "@sentry/node": "^7.114.0", "@types/bcrypt": "^5.0.2", "@types/moment": "^2.13.0", "axios": "^1.7.3", "bcrypt": "^5.1.1", "big.js": "^6.2.2", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.5", "ejs": "^3.1.10", "eslint": "^9.4.0", "express": "^4.19.2", "fs": "^0.0.1-security", "geoip-lite": "^1.4.10", "helmet": "^7.2.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "libphonenumber-js": "^1.11.4", "moment": "^2.30.1", "mongoose": "^8.5.0", "multer": "^1.4.5-lts.1", "otplib": "^12.0.1", "qrcode": "^1.5.3", "redis": "^4.6.14", "swagger-ui-express": "^5.0.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@eslint/js": "^9.20.0", "@types/big.js": "^6.2.2", "@types/chai": "^4.3.16", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.17", "@types/debug": "^4.1.12", "@types/ejs": "^3.1.5", "@types/express": "^4.17.21", "@types/geoip-lite": "^1.4.4", "@types/jsonwebtoken": "^9.0.6", "@types/mocha": "^10.0.6", "@types/multer": "^1.4.11", "@types/node": "^22.4.2", "@types/qrcode": "^1.5.5", "@types/supertest": "^6.0.2", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@typescript-eslint/eslint-plugin": "^8.25.0", "@typescript-eslint/parser": "^8.25.0", "chai": "^5.1.1", "concurrently": "^8.2.2", "dotenv-cli": "^8.0.0", "eslint": "^8.57.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.31.0", "jsdoc": "^4.0.3", "mocha": "^10.4.0", "nodemon": "^3.1.4", "sonarqube-scanner": "^4.3.0", "supertest": "^7.0.0", "ts-node": "^10.9.2", "typescript": "^5.7.3", "typescript-eslint": "^8.24.0"}}