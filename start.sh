#!/bin/bash

# Function to run a service in a new terminal
run_service() {
    local service_name=$1
    local service_dir=$2
    
    echo "Starting $service_name..."
    # xterm -title "$service_name" -e "cd $service_dir && npm run sonar; exec bash && npm run dev; exec bash" &
    xterm -title "$service_name" -e "cd $service_dir && npm run dev; exec bash" &
}

# Print header
echo "Starting all backend services in separate terminals..."
echo "--------------------------------"

# Check if xterm is installed
if ! command -v xterm &> /dev/null; then
    echo "Error: xterm is not installed. Please install it using:"
    echo "sudo apt-get install xterm"
    exit 1
fi

# Run each service in a new terminal
run_service "Event Service" "Backend-event-service"
run_service "Bonding DEX Service" "backend-bonding-dex-service"
run_service "Dividend Service" "Backend-dividend-service"
run_service "Transfer Agent" "Backend-transfer-agent"
run_service "Admin Service" "Backend-admin"
run_service "Marketplace Service" "Backend-marketplace"

echo "--------------------------------"
echo "All services started in separate terminals"
echo "Each service is running in its own window"
echo "You can close individual terminals to stop specific services" 